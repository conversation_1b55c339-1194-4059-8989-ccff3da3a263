// ==========================================================================
// GFast自动生成model操作代码。
// 生成日期：2025-08-20 11:00:00
// 生成路径: internal/app/ad/model/ks_advertiser_program_creative_report.go
// 生成人：cyao
// desc:创意数据
// company:云南奇讯科技有限公司
// ==========================================================================

package model

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
	comModel "github.com/tiger1103/gfast/v3/internal/app/common/model"
)

// KsAdvertiserProgramCreativeReportInfoRes is the golang structure for table ks_advertiser_program_creative_report.
type KsAdvertiserProgramCreativeReportInfoRes struct {
	gmeta.Meta                                       `orm:"table:ks_advertiser_program_creative_report"`
	CreativeId                                       int64       `orm:"creative_id,primary" json:"creativeId" dc:"创意 id"`                                                                                   // 创意 id
	StatDate                                         string      `orm:"stat_date,primary" json:"statDate" dc:"统计日期"`                                                                                        // 统计日期
	AdvertiserId                                     int64       `orm:"advertiser_id" json:"advertiserId" dc:"广告id"`                                                                                        // 广告id
	Charge                                           float64     `orm:"charge" json:"charge" dc:"消耗"`                                                                                                       // 消耗
	Show                                             int         `orm:"show" json:"show" dc:"封面曝光数"`                                                                                                        // 封面曝光数
	Aclick                                           int         `orm:"aclick" json:"aclick" dc:"素材曝光数"`                                                                                                    // 素材曝光数
	Bclick                                           int         `orm:"bclick" json:"bclick" dc:"行为数"`                                                                                                      // 行为数
	Share                                            int         `orm:"share" json:"share" dc:"分享数"`                                                                                                        // 分享数
	Comment                                          int         `orm:"comment" json:"comment" dc:"评论数"`                                                                                                    // 评论数
	Like                                             int         `orm:"like" json:"like" dc:"点赞数"`                                                                                                          // 点赞数
	Follow                                           int         `orm:"follow" json:"follow" dc:"关注数"`                                                                                                      // 关注数
	Report                                           int         `orm:"report" json:"report" dc:"举报数"`                                                                                                      // 举报数
	Block                                            int         `orm:"block" json:"block" dc:"拉黑数"`                                                                                                        // 拉黑数
	Negative                                         int         `orm:"negative" json:"negative" dc:"减少此类作品数"`                                                                                              // 减少此类作品数
	Activation                                       int         `orm:"activation" json:"activation" dc:"激活数"`                                                                                              // 激活数
	Submit                                           int         `orm:"submit" json:"submit" dc:"表单提交数"`                                                                                                    // 表单提交数
	AdPhotoPlayed10S                                 int         `orm:"ad_photo_played_10s" json:"adPhotoPlayed10S" dc:"10s播放数"`                                                                            // 10s播放数
	AdPhotoPlayed2S                                  int         `orm:"ad_photo_played_2s" json:"adPhotoPlayed2S" dc:"2s播放数"`                                                                               // 2s播放数
	AdPhotoPlayed75Percent                           int         `orm:"ad_photo_played_75percent" json:"adPhotoPlayed75Percent" dc:"75%进度播放数"`                                                              // 75%进度播放数
	CancelLike                                       int         `orm:"cancel_like" json:"cancelLike" dc:"取消点赞"`                                                                                            // 取消点赞
	ClickConversionRatio                             float64     `orm:"click_conversion_ratio" json:"clickConversionRatio" dc:"点击激活率 (激活数/点击数)"`                                                            // 点击激活率 (激活数/点击数)
	ConversionCost                                   float64     `orm:"conversion_cost" json:"conversionCost" dc:"单次激活成本"`                                                                                  // 单次激活成本
	ConversionCostByImpression7D                     float64     `orm:"conversion_cost_by_impression_7d" json:"conversionCostByImpression7D" dc:"7日激活成本"`                                                   // 7日激活成本
	ConversionNum                                    int         `orm:"conversion_num" json:"conversionNum" dc:"转化数"`                                                                                       // 转化数
	ConversionNumByImpression7D                      int         `orm:"conversion_num_by_impression_7d" json:"conversionNumByImpression7D" dc:"七日转化数"`                                                      // 七日转化数
	ConversionNumCost                                float64     `orm:"conversion_num_cost" json:"conversionNumCost" dc:"单次转换成本"`                                                                           // 单次转换成本
	ConversionRatio                                  float64     `orm:"conversion_ratio" json:"conversionRatio" dc:"转化率"`                                                                                   // 转化率
	ConversionRatioByImpression7D                    float64     `orm:"conversion_ratio_by_impression_7d" json:"conversionRatioByImpression7D" dc:"7日转化率"`                                                  // 7日转化率
	LivePlayed3S                                     int         `orm:"live_played_3s" json:"livePlayed3S" dc:"3s播放数量"`                                                                                     // 3s播放数量
	PlayedEnd                                        int         `orm:"played_end" json:"playedEnd" dc:"完播数量"`                                                                                              // 完播数量
	PlayedFiveSeconds                                int         `orm:"played_five_seconds" json:"playedFiveSeconds" dc:"5s数量"`                                                                             // 5s数量
	PlayedThreeSeconds                               int         `orm:"played_three_seconds" json:"playedThreeSeconds" dc:"3s数量"`                                                                           // 3s数量
	AdScene                                          string      `orm:"ad_scene" json:"adScene" dc:"广告场景"`                                                                                                  // 广告场景
	PlacementType                                    string      `orm:"placement_type" json:"placementType" dc:"位置类型"`                                                                                      // 位置类型
	CancelFollow                                     int         `orm:"cancel_follow" json:"cancelFollow" dc:"取消关注"`                                                                                        // 取消关注
	Play3SRatio                                      float64     `orm:"play_3s_ratio" json:"play3SRatio" dc:"3s播放率"`                                                                                        // 3s播放率
	Play5SRatio                                      float64     `orm:"play_5s_ratio" json:"play5SRatio" dc:"5s播放率"`                                                                                        // 5s播放率
	PlayEndRatio                                     float64     `orm:"play_end_ratio" json:"playEndRatio" dc:"完播率"`                                                                                        // 完播率
	DirectSubmit1DCost                               float64     `orm:"direct_submit_1d_cost" json:"directSubmit1DCost" dc:"表单提交成本"`                                                                        // 表单提交成本
	MinigameIaaPurchaseAmountFirstDay                float64     `orm:"minigame_iaa_purchase_amount_first_day" json:"minigameIaaPurchaseAmountFirstDay" dc:"当日广告LTV"`                                       // 当日广告LTV
	MinigameIaaPurchaseAmountThreeDayByConversion    float64     `orm:"minigame_iaa_purchase_amount_three_day_by_conversion" json:"minigameIaaPurchaseAmountThreeDayByConversion" dc:"激活后三日广告LTV"`          // 激活后三日广告LTV
	MinigameIaaPurchaseAmountWeekByConversion        float64     `orm:"minigame_iaa_purchase_amount_week_by_conversion" json:"minigameIaaPurchaseAmountWeekByConversion" dc:"激活后七日广告LTV"`                   // 激活后七日广告LTV
	MinigameIaaPurchaseAmountFirstDayRoi             float64     `orm:"minigame_iaa_purchase_amount_first_day_roi" json:"minigameIaaPurchaseAmountFirstDayRoi" dc:"当日广告变现ROI"`                              // 当日广告变现ROI
	MinigameIaaPurchaseAmountThreeDayByConversionRoi float64     `orm:"minigame_iaa_purchase_amount_three_day_by_conversion_roi" json:"minigameIaaPurchaseAmountThreeDayByConversionRoi" dc:"激活后三日广告变现ROI"` // 激活后三日广告变现ROI
	MinigameIaaPurchaseAmountWeekByConversionRoi     float64     `orm:"minigame_iaa_purchase_amount_week_by_conversion_roi" json:"minigameIaaPurchaseAmountWeekByConversionRoi" dc:"激活后七日广告变现ROI"`          // 激活后七日广告变现ROI
	MinigameIaaPurchaseAmount                        float64     `orm:"minigame_iaa_purchase_amount" json:"minigameIaaPurchaseAmount" dc:"IAA广告变现LTV"`                                                      // IAA广告变现LTV
	MinigameIaaPurchaseRoi                           float64     `orm:"minigame_iaa_purchase_roi" json:"minigameIaaPurchaseRoi" dc:"IAA广告变现ROI"`                                                            // IAA广告变现ROI
	UnitId                                           int64       `orm:"unit_id" json:"unitId" dc:"广告组id"`                                                                                                   // 广告组id
	EffectiveCustomerAcquisition7DCnt                int         `orm:"effective_customer_acquisition_7d_cnt" json:"effectiveCustomerAcquisition7DCnt" dc:"有效获客数（计费）"`                                      // 有效获客数（计费）
	EffectiveCustomerAcquisition7DCost               float64     `orm:"effective_customer_acquisition_7d_cost" json:"effectiveCustomerAcquisition7DCost" dc:"有效获客成本（计费）"`                                   // 有效获客成本（计费）
	EffectiveCustomerAcquisition7DRatio              float64     `orm:"effective_customer_acquisition_7d_ratio" json:"effectiveCustomerAcquisition7DRatio" dc:"有效获客率（计费）"`                                  // 有效获客率（计费）
	MmuEffectiveCustomerAcquisitionCnt               int         `orm:"mmu_effective_customer_acquisition_cnt" json:"mmuEffectiveCustomerAcquisitionCnt" dc:"MMU识别产生的有效获客数（回传）"`                            // MMU识别产生的有效获客数（回传）
	MmuEffectiveCustomerAcquisition7DCnt             int         `orm:"mmu_effective_customer_acquisition_7d_cnt" json:"mmuEffectiveCustomerAcquisition7DCnt" dc:"MMU识别产生的有效获客数（计费）"`                       // MMU识别产生的有效获客数（计费）
	PlayedNum                                        int         `orm:"played_num" json:"playedNum" dc:"播放数"`                                                                                               // 播放数
	LeadsSubmitCnt                                   int         `orm:"leads_submit_cnt" json:"leadsSubmitCnt" dc:"直接私信留资数"`                                                                                // 直接私信留资数
	LeadsSubmitCntRatio                              float64     `orm:"leads_submit_cnt_ratio" json:"leadsSubmitCntRatio" dc:"直接私信留资率"`                                                                     // 直接私信留资率
	LeadsSubmitCost                                  float64     `orm:"leads_submit_cost" json:"leadsSubmitCost" dc:"直接私信留资成本"`                                                                             // 直接私信留资成本
	PrivateMessageSentCnt                            int         `orm:"private_message_sent_cnt" json:"privateMessageSentCnt" dc:"私信消息数"`                                                                   // 私信消息数
	PrivateMessageSentRatio                          float64     `orm:"private_message_sent_ratio" json:"privateMessageSentRatio" dc:"私信消息转化率"`                                                             // 私信消息转化率
	PrivateMessageSentCost                           float64     `orm:"private_message_sent_cost" json:"privateMessageSentCost" dc:"私信消息转化成本"`                                                              // 私信消息转化成本
	EventFormSubmit                                  int         `orm:"event_form_submit" json:"eventFormSubmit" dc:"表单提交数（回传时间）"`                                                                          // 表单提交数（回传时间）
	DirectSubmit1DCnt                                int         `orm:"direct_submit_1d_cnt" json:"directSubmit1DCnt" dc:"表单提交数(计费时间)"`                                                                     // 表单提交数(计费时间)
	EventFormSubmitRatio                             float64     `orm:"event_form_submit_ratio" json:"eventFormSubmitRatio" dc:"表单提交率（回传时间）"`                                                               // 表单提交率（回传时间）
	EventFormSubmitCost                              float64     `orm:"event_form_submit_cost" json:"eventFormSubmitCost" dc:"表单提交成本（回传时间）"`                                                                // 表单提交成本（回传时间）
	EventAudition                                    int         `orm:"event_audition" json:"eventAudition" dc:"首次试听到课（归因）"`                                                                                // 首次试听到课（归因）
	EventAudition30DCnt                              int         `orm:"event_audition_30d_cnt" json:"eventAudition30DCnt" dc:"首次试听到课（归因）"`                                                                  // 首次试听到课（归因）
	EventAuditionCost                                float64     `orm:"event_audition_cost" json:"eventAuditionCost" dc:"首次试听到课成本"`                                                                         // 首次试听到课成本
	AllLessonFinishCnt                               int         `orm:"all_lesson_finish_cnt" json:"allLessonFinishCnt" dc:"全部试听完课（回传）"`                                                                    // 全部试听完课（回传）
	AllLessonFinish30DCnt                            int         `orm:"all_lesson_finish_30d_cnt" json:"allLessonFinish30DCnt" dc:"全部试听完课（归因）"`                                                             // 全部试听完课（归因）
	HighPriceClassPayCnt                             int         `orm:"high_price_class_pay_cnt" json:"highPriceClassPayCnt" dc:"成交付费（回传）"`                                                                 // 成交付费（回传）
	HighPriceClassPay30DCnt                          int         `orm:"high_price_class_pay_30d_cnt" json:"highPriceClassPay30DCnt" dc:"成交付费（归因）"`                                                          // 成交付费（归因）
	CampaignId                                       int64       `orm:"campaign_id" json:"campaignId" dc:"广告计划Id"`                                                                                          // 广告计划Id
	CampaignName                                     string      `orm:"campaign_name" json:"campaignName" dc:"计划名称"`                                                                                        // 计划名称
	UnitName                                         string      `orm:"unit_name" json:"unitName" dc:"广告组名称"`                                                                                               // 广告组名称
	PhotoId                                          string      `orm:"photo_id" json:"photoId" dc:"视频 id"`                                                                                                 // 视频 id
	PhotoUrl                                         string      `orm:"photo_url" json:"photoUrl" dc:"视频链接"`                                                                                                // 视频链接
	CoverUrl                                         string      `orm:"cover_url" json:"coverUrl" dc:"封面链接"`                                                                                                // 封面链接
	ImageToken                                       string      `orm:"image_token" json:"imageToken" dc:"封面 id"`                                                                                           // 封面 id
	Description                                      string      `orm:"description" json:"description" dc:"作品广告语"`                                                                                          // 作品广告语
	PicId                                            int         `orm:"pic_id" json:"picId" dc:"图片库图片ID"`                                                                                                   // 图片库图片ID
	PhotoMd5                                         string      `orm:"photo_md5" json:"photoMd5" dc:"视频 md5"`                                                                                              // 视频 md5
	UnitType                                         int         `orm:"unit_type" json:"unitType" dc:"单元组的创意类型，7=程序化2.0创意，10=智能创意；当值为空时，代表不区分创意类型取全部创意； 默认为空"`                                              // 单元组的创意类型，7=程序化2.0创意，10=智能创意；当值为空时，代表不区分创意类型取全部创意； 默认为空
	CreatedAt                                        *gtime.Time `orm:"created_at" json:"createdAt" dc:"创建时间"`                                                                                              // 创建时间
}

type KsAdvertiserProgramCreativeReportListRes struct {
	CreativeId int64 `json:"creativeId" dc:"创意 id"`
	//StatDate                                         string      `json:"statDate" dc:"统计日期"`
	AdvertiserId int64   `json:"advertiserId" dc:"广告id"`
	Charge       float64 `json:"charge" dc:"消耗"`
	Show         int     `json:"show" dc:"封面曝光数"`
	Aclick       int     `json:"aclick" dc:"素材曝光数"`
	//素材点击率
	AClickRate                                       float64 `json:"aClickRate" dc:"素材点击率"`
	Bclick                                           int     `json:"bclick" dc:"行为数"`
	Share                                            int     `json:"share" dc:"分享数"`
	Comment                                          int     `json:"comment" dc:"评论数"`
	Like                                             int     `json:"like" dc:"点赞数"`
	Follow                                           int     `json:"follow" dc:"关注数"`
	Report                                           int     `json:"report" dc:"举报数"`
	Block                                            int     `json:"block" dc:"拉黑数"`
	Negative                                         int     `json:"negative" dc:"减少此类作品数"`
	Activation                                       int     `json:"activation" dc:"激活数"`
	Submit                                           int     `json:"submit" dc:"表单提交数"`
	AdPhotoPlayed10S                                 int     `json:"adPhotoPlayed10S" dc:"10s播放数"`
	AdPhotoPlayed2S                                  int     `json:"adPhotoPlayed2S" dc:"2s播放数"`
	AdPhotoPlayed75Percent                           int     `json:"adPhotoPlayed75Percent" dc:"75%进度播放数"`
	CancelLike                                       int     `json:"cancelLike" dc:"取消点赞"`
	ClickConversionRatio                             float64 `json:"clickConversionRatio" dc:"点击激活率 (激活数/点击数)"`
	ConversionCost                                   float64 `json:"conversionCost" dc:"单次激活成本"`
	ConversionCostByImpression7D                     float64 `json:"conversionCostByImpression7D" dc:"7日激活成本"`
	ConversionNum                                    int     `json:"conversionNum" dc:"转化数"`
	ConversionNumByImpression7D                      int     `json:"conversionNumByImpression7D" dc:"七日转化数"`
	ConversionNumCost                                float64 `json:"conversionNumCost" dc:"单次转换成本"`
	ConversionRatio                                  float64 `json:"conversionRatio" dc:"转化率"`
	ConversionRatioByImpression7D                    float64 `json:"conversionRatioByImpression7D" dc:"7日转化率"`
	LivePlayed3S                                     int     `json:"livePlayed3S" dc:"3s播放数量"`
	PlayedEnd                                        int     `json:"playedEnd" dc:"完播数量"`
	PlayedFiveSeconds                                int     `json:"playedFiveSeconds" dc:"5s数量"`
	PlayedThreeSeconds                               int     `json:"playedThreeSeconds" dc:"3s数量"`
	AdScene                                          string  `json:"adScene" dc:"广告场景"`
	PlacementType                                    string  `json:"placementType" dc:"位置类型"`
	CancelFollow                                     int     `json:"cancelFollow" dc:"取消关注"`
	Play3SRatio                                      float64 `json:"play3SRatio" dc:"3s播放率"`
	Play5SRatio                                      float64 `json:"play5SRatio" dc:"5s播放率"`
	PlayEndRatio                                     float64 `json:"playEndRatio" dc:"完播率"`
	DirectSubmit1DCost                               float64 `json:"directSubmit1DCost" dc:"表单提交成本"`
	MinigameIaaPurchaseAmountFirstDay                float64 `json:"minigameIaaPurchaseAmountFirstDay" dc:"当日广告LTV"`
	MinigameIaaPurchaseAmountThreeDayByConversion    float64 `json:"minigameIaaPurchaseAmountThreeDayByConversion" dc:"激活后三日广告LTV"`
	MinigameIaaPurchaseAmountWeekByConversion        float64 `json:"minigameIaaPurchaseAmountWeekByConversion" dc:"激活后七日广告LTV"`
	MinigameIaaPurchaseAmountFirstDayRoi             float64 `json:"minigameIaaPurchaseAmountFirstDayRoi" dc:"当日广告变现ROI"`
	MinigameIaaPurchaseAmountThreeDayByConversionRoi float64 `json:"minigameIaaPurchaseAmountThreeDayByConversionRoi" dc:"激活后三日广告变现ROI"`
	MinigameIaaPurchaseAmountWeekByConversionRoi     float64 `json:"minigameIaaPurchaseAmountWeekByConversionRoi" dc:"激活后七日广告变现ROI"`
	MinigameIaaPurchaseAmount                        float64 `json:"minigameIaaPurchaseAmount" dc:"IAA广告变现LTV"`
	MinigameIaaPurchaseRoi                           float64 `json:"minigameIaaPurchaseRoi" dc:"IAA广告变现ROI"`
	UnitId                                           int64   `json:"unitId" dc:"广告组id"`
	EffectiveCustomerAcquisition7DCnt                int     `json:"effectiveCustomerAcquisition7DCnt" dc:"有效获客数（计费）"`
	EffectiveCustomerAcquisition7DCost               float64 `json:"effectiveCustomerAcquisition7DCost" dc:"有效获客成本（计费）"`
	EffectiveCustomerAcquisition7DRatio              float64 `json:"effectiveCustomerAcquisition7DRatio" dc:"有效获客率（计费）"`
	MmuEffectiveCustomerAcquisitionCnt               int     `json:"mmuEffectiveCustomerAcquisitionCnt" dc:"MMU识别产生的有效获客数（回传）"`
	MmuEffectiveCustomerAcquisition7DCnt             int     `json:"mmuEffectiveCustomerAcquisition7DCnt" dc:"MMU识别产生的有效获客数（计费）"`
	PlayedNum                                        int     `json:"playedNum" dc:"播放数"`
	LeadsSubmitCnt                                   int     `json:"leadsSubmitCnt" dc:"直接私信留资数"`
	LeadsSubmitCntRatio                              float64 `json:"leadsSubmitCntRatio" dc:"直接私信留资率"`
	LeadsSubmitCost                                  float64 `json:"leadsSubmitCost" dc:"直接私信留资成本"`
	PrivateMessageSentCnt                            int     `json:"privateMessageSentCnt" dc:"私信消息数"`
	PrivateMessageSentRatio                          float64 `json:"privateMessageSentRatio" dc:"私信消息转化率"`
	PrivateMessageSentCost                           float64 `json:"privateMessageSentCost" dc:"私信消息转化成本"`
	EventFormSubmit                                  int     `json:"eventFormSubmit" dc:"表单提交数（回传时间）"`
	DirectSubmit1DCnt                                int     `json:"directSubmit1DCnt" dc:"表单提交数(计费时间)"`
	EventFormSubmitRatio                             float64 `json:"eventFormSubmitRatio" dc:"表单提交率（回传时间）"`
	EventFormSubmitCost                              float64 `json:"eventFormSubmitCost" dc:"表单提交成本（回传时间）"`
	EventAudition                                    int     `json:"eventAudition" dc:"首次试听到课（归因）"`
	EventAudition30DCnt                              int     `json:"eventAudition30DCnt" dc:"首次试听到课（归因）"`
	EventAuditionCost                                float64 `json:"eventAuditionCost" dc:"首次试听到课成本"`
	AllLessonFinishCnt                               int     `json:"allLessonFinishCnt" dc:"全部试听完课（回传）"`
	AllLessonFinish30DCnt                            int     `json:"allLessonFinish30DCnt" dc:"全部试听完课（归因）"`
	HighPriceClassPayCnt                             int     `json:"highPriceClassPayCnt" dc:"成交付费（回传）"`
	HighPriceClassPay30DCnt                          int     `json:"highPriceClassPay30DCnt" dc:"成交付费（归因）"`
	CampaignId                                       int64   `json:"campaignId" dc:"广告计划Id"`
	CampaignName                                     string  `json:"campaignName" dc:"计划名称"`
	UnitName                                         string  `json:"unitName" dc:"广告组名称"`
	PhotoId                                          string  `json:"photoId" dc:"视频 id"`
	PhotoUrl                                         string  `json:"photoUrl" dc:"视频链接"`
	CoverUrl                                         string  `json:"coverUrl" dc:"封面链接"`
	ImageToken                                       string  `json:"imageToken" dc:"封面 id"`
	Description                                      string  `json:"description" dc:"作品广告语"`
	PicId                                            int     `json:"picId" dc:"图片库图片ID"`
	PhotoMd5                                         string  `json:"photoMd5" dc:"视频 md5"`
	UnitType                                         int     `json:"unitType" dc:"单元组的创意类型，7=程序化2.0创意，10=智能创意；当值为空时，代表不区分创意类型取全部创意； 默认为空"`
}

// KsAdvertiserProgramCreativeReportSearchReq 分页请求参数
type KsAdvertiserProgramCreativeReportSearchReq struct {
	comModel.PageReq
	UnitId       string `p:"unitId" v:"unitId@integer#广告组id需为整数" dc:"广告组id"` //广告组id
	UnitName     string `p:"unitName" dc:"广告组名称"`                            //广告组名称
	StartTime    string `p:"startTime"   dc:"开始时间"`
	EndTime      string `p:"endTime"   dc:"结束时间"`
	UnitType     string `p:"unitType" v:"unitType@integer#单元组的创意类型，7=程序化2.0创意，10=智能创意；当值为空时，代表不区分创意类型取全部创意； 默认为空需为整数" dc:"单元组的创意类型，7=程序化2.0创意，10=智能创意；当值为空时，代表不区分创意类型取全部创意； 默认为空"` //单元组的创意类型，7=程序化2.0创意，10=智能创意；当值为空时，代表不区分创意类型取全部创意； 默认为空
	CreativeId   string `p:"creativeId" dc:"创意 id"`                                                                                                                                //创意 id
	StatDate     string `p:"statDate" dc:"统计日期"`                                                                                                                                   //统计日期
	AdvertiserId string `p:"advertiserId" v:"advertiserId@integer#广告id需为整数" dc:"广告id"`                                                                                             //广告id
	CreatedAt    string `p:"createdAt" v:"createdAt@datetime#创建时间需为YYYY-MM-DD hh:mm:ss格式" dc:"创建时间"`                                                                               //创建时间
}

// KsAdvertiserProgramCreativeReportSearchRes 列表返回结果
type KsAdvertiserProgramCreativeReportSearchRes struct {
	comModel.ListRes
	List []*KsAdvertiserProgramCreativeReportListRes `json:"list"`
}
type KsAdvertiserProgramCreativeReportPullReq struct {
	StartTime string `p:"startTime" v:"required#开始时间不能为空" dc:"开始时间"`
	EndTime   string `p:"endTime" v:"required#结束时间不能为空" dc:"结束时间"`
}

// KsAdvertiserProgramCreativeReportAddReq 添加操作请求参数
type KsAdvertiserProgramCreativeReportAddReq struct {
	CreativeId                                       int64   `p:"creativeId" v:"required#主键ID不能为空" dc:"创意 id"`
	StatDate                                         string  `p:"statDate" v:"required#统计日期不能为空" dc:"统计日期"`
	AdvertiserId                                     int64   `p:"advertiserId"  dc:"广告id"`
	Charge                                           float64 `p:"charge"  dc:"消耗"`
	Show                                             int     `p:"show"  dc:"封面曝光数"`
	Aclick                                           int     `p:"aclick"  dc:"素材曝光数"`
	Bclick                                           int     `p:"bclick"  dc:"行为数"`
	Share                                            int     `p:"share"  dc:"分享数"`
	Comment                                          int     `p:"comment"  dc:"评论数"`
	Like                                             int     `p:"like"  dc:"点赞数"`
	Follow                                           int     `p:"follow"  dc:"关注数"`
	Report                                           int     `p:"report"  dc:"举报数"`
	Block                                            int     `p:"block"  dc:"拉黑数"`
	Negative                                         int     `p:"negative"  dc:"减少此类作品数"`
	Activation                                       int     `p:"activation"  dc:"激活数"`
	Submit                                           int     `p:"submit"  dc:"表单提交数"`
	AdPhotoPlayed10S                                 int     `p:"adPhotoPlayed10S"  dc:"10s播放数"`
	AdPhotoPlayed2S                                  int     `p:"adPhotoPlayed2S"  dc:"2s播放数"`
	AdPhotoPlayed75Percent                           int     `p:"adPhotoPlayed75Percent"  dc:"75%进度播放数"`
	CancelLike                                       int     `p:"cancelLike"  dc:"取消点赞"`
	ClickConversionRatio                             float64 `p:"clickConversionRatio"  dc:"点击激活率 (激活数/点击数)"`
	ConversionCost                                   float64 `p:"conversionCost"  dc:"单次激活成本"`
	ConversionCostByImpression7D                     float64 `p:"conversionCostByImpression7D"  dc:"7日激活成本"`
	ConversionNum                                    int     `p:"conversionNum"  dc:"转化数"`
	ConversionNumByImpression7D                      int     `p:"conversionNumByImpression7D"  dc:"七日转化数"`
	ConversionNumCost                                float64 `p:"conversionNumCost"  dc:"单次转换成本"`
	ConversionRatio                                  float64 `p:"conversionRatio"  dc:"转化率"`
	ConversionRatioByImpression7D                    float64 `p:"conversionRatioByImpression7D"  dc:"7日转化率"`
	LivePlayed3S                                     int     `p:"livePlayed3S"  dc:"3s播放数量"`
	PlayedEnd                                        int     `p:"playedEnd"  dc:"完播数量"`
	PlayedFiveSeconds                                int     `p:"playedFiveSeconds"  dc:"5s数量"`
	PlayedThreeSeconds                               int     `p:"playedThreeSeconds"  dc:"3s数量"`
	AdScene                                          string  `p:"adScene"  dc:"广告场景"`
	PlacementType                                    string  `p:"placementType"  dc:"位置类型"`
	CancelFollow                                     int     `p:"cancelFollow"  dc:"取消关注"`
	Play3SRatio                                      float64 `p:"play3SRatio"  dc:"3s播放率"`
	Play5SRatio                                      float64 `p:"play5SRatio"  dc:"5s播放率"`
	PlayEndRatio                                     float64 `p:"playEndRatio"  dc:"完播率"`
	DirectSubmit1DCost                               float64 `p:"directSubmit1DCost"  dc:"表单提交成本"`
	MinigameIaaPurchaseAmountFirstDay                float64 `p:"minigameIaaPurchaseAmountFirstDay"  dc:"当日广告LTV"`
	MinigameIaaPurchaseAmountThreeDayByConversion    float64 `p:"minigameIaaPurchaseAmountThreeDayByConversion"  dc:"激活后三日广告LTV"`
	MinigameIaaPurchaseAmountWeekByConversion        float64 `p:"minigameIaaPurchaseAmountWeekByConversion"  dc:"激活后七日广告LTV"`
	MinigameIaaPurchaseAmountFirstDayRoi             float64 `p:"minigameIaaPurchaseAmountFirstDayRoi"  dc:"当日广告变现ROI"`
	MinigameIaaPurchaseAmountThreeDayByConversionRoi float64 `p:"minigameIaaPurchaseAmountThreeDayByConversionRoi"  dc:"激活后三日广告变现ROI"`
	MinigameIaaPurchaseAmountWeekByConversionRoi     float64 `p:"minigameIaaPurchaseAmountWeekByConversionRoi"  dc:"激活后七日广告变现ROI"`
	MinigameIaaPurchaseAmount                        float64 `p:"minigameIaaPurchaseAmount"  dc:"IAA广告变现LTV"`
	MinigameIaaPurchaseRoi                           float64 `p:"minigameIaaPurchaseRoi"  dc:"IAA广告变现ROI"`
	UnitId                                           int64   `p:"unitId"  dc:"广告组id"`
	EffectiveCustomerAcquisition7DCnt                int     `p:"effectiveCustomerAcquisition7DCnt"  dc:"有效获客数（计费）"`
	EffectiveCustomerAcquisition7DCost               float64 `p:"effectiveCustomerAcquisition7DCost"  dc:"有效获客成本（计费）"`
	EffectiveCustomerAcquisition7DRatio              float64 `p:"effectiveCustomerAcquisition7DRatio"  dc:"有效获客率（计费）"`
	MmuEffectiveCustomerAcquisitionCnt               int     `p:"mmuEffectiveCustomerAcquisitionCnt"  dc:"MMU识别产生的有效获客数（回传）"`
	MmuEffectiveCustomerAcquisition7DCnt             int     `p:"mmuEffectiveCustomerAcquisition7DCnt"  dc:"MMU识别产生的有效获客数（计费）"`
	PlayedNum                                        int     `p:"playedNum"  dc:"播放数"`
	LeadsSubmitCnt                                   int     `p:"leadsSubmitCnt"  dc:"直接私信留资数"`
	LeadsSubmitCntRatio                              float64 `p:"leadsSubmitCntRatio"  dc:"直接私信留资率"`
	LeadsSubmitCost                                  float64 `p:"leadsSubmitCost"  dc:"直接私信留资成本"`
	PrivateMessageSentCnt                            int     `p:"privateMessageSentCnt"  dc:"私信消息数"`
	PrivateMessageSentRatio                          float64 `p:"privateMessageSentRatio"  dc:"私信消息转化率"`
	PrivateMessageSentCost                           float64 `p:"privateMessageSentCost"  dc:"私信消息转化成本"`
	EventFormSubmit                                  int     `p:"eventFormSubmit"  dc:"表单提交数（回传时间）"`
	DirectSubmit1DCnt                                int     `p:"directSubmit1DCnt"  dc:"表单提交数(计费时间)"`
	EventFormSubmitRatio                             float64 `p:"eventFormSubmitRatio"  dc:"表单提交率（回传时间）"`
	EventFormSubmitCost                              float64 `p:"eventFormSubmitCost"  dc:"表单提交成本（回传时间）"`
	EventAudition                                    int     `p:"eventAudition"  dc:"首次试听到课（归因）"`
	EventAudition30DCnt                              int     `p:"eventAudition30DCnt"  dc:"首次试听到课（归因）"`
	EventAuditionCost                                float64 `p:"eventAuditionCost"  dc:"首次试听到课成本"`
	AllLessonFinishCnt                               int     `p:"allLessonFinishCnt"  dc:"全部试听完课（回传）"`
	AllLessonFinish30DCnt                            int     `p:"allLessonFinish30DCnt"  dc:"全部试听完课（归因）"`
	HighPriceClassPayCnt                             int     `p:"highPriceClassPayCnt"  dc:"成交付费（回传）"`
	HighPriceClassPay30DCnt                          int     `p:"highPriceClassPay30DCnt"  dc:"成交付费（归因）"`
	CampaignId                                       int64   `p:"campaignId"  dc:"广告计划Id"`
	CampaignName                                     string  `p:"campaignName" v:"required#计划名称不能为空" dc:"计划名称"`
	UnitName                                         string  `p:"unitName" v:"required#广告组名称不能为空" dc:"广告组名称"`
	PhotoId                                          string  `p:"photoId"  dc:"视频 id"`
	PhotoUrl                                         string  `p:"photoUrl"  dc:"视频链接"`
	CoverUrl                                         string  `p:"coverUrl"  dc:"封面链接"`
	ImageToken                                       string  `p:"imageToken"  dc:"封面 id"`
	Description                                      string  `p:"description"  dc:"作品广告语"`
	PicId                                            int     `p:"picId"  dc:"图片库图片ID"`
	PhotoMd5                                         string  `p:"photoMd5"  dc:"视频 md5"`
	UnitType                                         int     `p:"unitType"  dc:"单元组的创意类型，7=程序化2.0创意，10=智能创意；当值为空时，代表不区分创意类型取全部创意； 默认为空"`
}

// KsAdvertiserProgramCreativeReportEditReq 修改操作请求参数
type KsAdvertiserProgramCreativeReportEditReq struct {
	CreativeId                                       int64   `p:"creativeId" v:"required#主键ID不能为空" dc:"创意 id"`
	StatDate                                         string  `p:"statDate" v:"required#统计时间不能为空" dc:"统计时间"`
	AdvertiserId                                     int64   `p:"advertiserId"  dc:"广告id"`
	Charge                                           float64 `p:"charge"  dc:"消耗"`
	Show                                             int     `p:"show"  dc:"封面曝光数"`
	Aclick                                           int     `p:"aclick"  dc:"素材曝光数"`
	Bclick                                           int     `p:"bclick"  dc:"行为数"`
	Share                                            int     `p:"share"  dc:"分享数"`
	Comment                                          int     `p:"comment"  dc:"评论数"`
	Like                                             int     `p:"like"  dc:"点赞数"`
	Follow                                           int     `p:"follow"  dc:"关注数"`
	Report                                           int     `p:"report"  dc:"举报数"`
	Block                                            int     `p:"block"  dc:"拉黑数"`
	Negative                                         int     `p:"negative"  dc:"减少此类作品数"`
	Activation                                       int     `p:"activation"  dc:"激活数"`
	Submit                                           int     `p:"submit"  dc:"表单提交数"`
	AdPhotoPlayed10S                                 int     `p:"adPhotoPlayed10S"  dc:"10s播放数"`
	AdPhotoPlayed2S                                  int     `p:"adPhotoPlayed2S"  dc:"2s播放数"`
	AdPhotoPlayed75Percent                           int     `p:"adPhotoPlayed75Percent"  dc:"75%进度播放数"`
	CancelLike                                       int     `p:"cancelLike"  dc:"取消点赞"`
	ClickConversionRatio                             float64 `p:"clickConversionRatio"  dc:"点击激活率 (激活数/点击数)"`
	ConversionCost                                   float64 `p:"conversionCost"  dc:"单次激活成本"`
	ConversionCostByImpression7D                     float64 `p:"conversionCostByImpression7D"  dc:"7日激活成本"`
	ConversionNum                                    int     `p:"conversionNum"  dc:"转化数"`
	ConversionNumByImpression7D                      int     `p:"conversionNumByImpression7D"  dc:"七日转化数"`
	ConversionNumCost                                float64 `p:"conversionNumCost"  dc:"单次转换成本"`
	ConversionRatio                                  float64 `p:"conversionRatio"  dc:"转化率"`
	ConversionRatioByImpression7D                    float64 `p:"conversionRatioByImpression7D"  dc:"7日转化率"`
	LivePlayed3S                                     int     `p:"livePlayed3S"  dc:"3s播放数量"`
	PlayedEnd                                        int     `p:"playedEnd"  dc:"完播数量"`
	PlayedFiveSeconds                                int     `p:"playedFiveSeconds"  dc:"5s数量"`
	PlayedThreeSeconds                               int     `p:"playedThreeSeconds"  dc:"3s数量"`
	AdScene                                          string  `p:"adScene"  dc:"广告场景"`
	PlacementType                                    string  `p:"placementType"  dc:"位置类型"`
	CancelFollow                                     int     `p:"cancelFollow"  dc:"取消关注"`
	Play3SRatio                                      float64 `p:"play3SRatio"  dc:"3s播放率"`
	Play5SRatio                                      float64 `p:"play5SRatio"  dc:"5s播放率"`
	PlayEndRatio                                     float64 `p:"playEndRatio"  dc:"完播率"`
	DirectSubmit1DCost                               float64 `p:"directSubmit1DCost"  dc:"表单提交成本"`
	MinigameIaaPurchaseAmountFirstDay                float64 `p:"minigameIaaPurchaseAmountFirstDay"  dc:"当日广告LTV"`
	MinigameIaaPurchaseAmountThreeDayByConversion    float64 `p:"minigameIaaPurchaseAmountThreeDayByConversion"  dc:"激活后三日广告LTV"`
	MinigameIaaPurchaseAmountWeekByConversion        float64 `p:"minigameIaaPurchaseAmountWeekByConversion"  dc:"激活后七日广告LTV"`
	MinigameIaaPurchaseAmountFirstDayRoi             float64 `p:"minigameIaaPurchaseAmountFirstDayRoi"  dc:"当日广告变现ROI"`
	MinigameIaaPurchaseAmountThreeDayByConversionRoi float64 `p:"minigameIaaPurchaseAmountThreeDayByConversionRoi"  dc:"激活后三日广告变现ROI"`
	MinigameIaaPurchaseAmountWeekByConversionRoi     float64 `p:"minigameIaaPurchaseAmountWeekByConversionRoi"  dc:"激活后七日广告变现ROI"`
	MinigameIaaPurchaseAmount                        float64 `p:"minigameIaaPurchaseAmount"  dc:"IAA广告变现LTV"`
	MinigameIaaPurchaseRoi                           float64 `p:"minigameIaaPurchaseRoi"  dc:"IAA广告变现ROI"`
	UnitId                                           int64   `p:"unitId"  dc:"广告组id"`
	EffectiveCustomerAcquisition7DCnt                int     `p:"effectiveCustomerAcquisition7DCnt"  dc:"有效获客数（计费）"`
	EffectiveCustomerAcquisition7DCost               float64 `p:"effectiveCustomerAcquisition7DCost"  dc:"有效获客成本（计费）"`
	EffectiveCustomerAcquisition7DRatio              float64 `p:"effectiveCustomerAcquisition7DRatio"  dc:"有效获客率（计费）"`
	MmuEffectiveCustomerAcquisitionCnt               int     `p:"mmuEffectiveCustomerAcquisitionCnt"  dc:"MMU识别产生的有效获客数（回传）"`
	MmuEffectiveCustomerAcquisition7DCnt             int     `p:"mmuEffectiveCustomerAcquisition7DCnt"  dc:"MMU识别产生的有效获客数（计费）"`
	PlayedNum                                        int     `p:"playedNum"  dc:"播放数"`
	LeadsSubmitCnt                                   int     `p:"leadsSubmitCnt"  dc:"直接私信留资数"`
	LeadsSubmitCntRatio                              float64 `p:"leadsSubmitCntRatio"  dc:"直接私信留资率"`
	LeadsSubmitCost                                  float64 `p:"leadsSubmitCost"  dc:"直接私信留资成本"`
	PrivateMessageSentCnt                            int     `p:"privateMessageSentCnt"  dc:"私信消息数"`
	PrivateMessageSentRatio                          float64 `p:"privateMessageSentRatio"  dc:"私信消息转化率"`
	PrivateMessageSentCost                           float64 `p:"privateMessageSentCost"  dc:"私信消息转化成本"`
	EventFormSubmit                                  int     `p:"eventFormSubmit"  dc:"表单提交数（回传时间）"`
	DirectSubmit1DCnt                                int     `p:"directSubmit1DCnt"  dc:"表单提交数(计费时间)"`
	EventFormSubmitRatio                             float64 `p:"eventFormSubmitRatio"  dc:"表单提交率（回传时间）"`
	EventFormSubmitCost                              float64 `p:"eventFormSubmitCost"  dc:"表单提交成本（回传时间）"`
	EventAudition                                    int     `p:"eventAudition"  dc:"首次试听到课（归因）"`
	EventAudition30DCnt                              int     `p:"eventAudition30DCnt"  dc:"首次试听到课（归因）"`
	EventAuditionCost                                float64 `p:"eventAuditionCost"  dc:"首次试听到课成本"`
	AllLessonFinishCnt                               int     `p:"allLessonFinishCnt"  dc:"全部试听完课（回传）"`
	AllLessonFinish30DCnt                            int     `p:"allLessonFinish30DCnt"  dc:"全部试听完课（归因）"`
	HighPriceClassPayCnt                             int     `p:"highPriceClassPayCnt"  dc:"成交付费（回传）"`
	HighPriceClassPay30DCnt                          int     `p:"highPriceClassPay30DCnt"  dc:"成交付费（归因）"`
	CampaignId                                       int64   `p:"campaignId"  dc:"广告计划Id"`
	CampaignName                                     string  `p:"campaignName" v:"required#计划名称不能为空" dc:"计划名称"`
	UnitName                                         string  `p:"unitName" v:"required#广告组名称不能为空" dc:"广告组名称"`
	PhotoId                                          string  `p:"photoId"  dc:"视频 id"`
	PhotoUrl                                         string  `p:"photoUrl"  dc:"视频链接"`
	CoverUrl                                         string  `p:"coverUrl"  dc:"封面链接"`
	ImageToken                                       string  `p:"imageToken"  dc:"封面 id"`
	Description                                      string  `p:"description"  dc:"作品广告语"`
	PicId                                            int     `p:"picId"  dc:"图片库图片ID"`
	PhotoMd5                                         string  `p:"photoMd5"  dc:"视频 md5"`
	UnitType                                         int     `p:"unitType"  dc:"单元组的创意类型，7=程序化2.0创意，10=智能创意；当值为空时，代表不区分创意类型取全部创意； 默认为空"`
}
