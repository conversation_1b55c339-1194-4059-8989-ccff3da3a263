// ==========================================================================
// GFast自动生成model操作代码。
// 生成日期：2025-08-22 11:51:48
// 生成路径: internal/app/ad/model/ks_advertiser_strategy_config.go
// 生成人：cq
// desc:快手广告搭建-策略配置
// company:云南奇讯科技有限公司
// ==========================================================================

package model

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
	commonConsts "github.com/tiger1103/gfast/v3/internal/app/common/consts"
	comModel "github.com/tiger1103/gfast/v3/internal/app/common/model"
)

// KsAdvertiserStrategyConfigInfoRes is the golang structure for table ks_advertiser_strategy_config.
type KsAdvertiserStrategyConfigInfoRes struct {
	gmeta.Meta       `orm:"table:ks_advertiser_strategy_config"`
	Id               int64       `orm:"id,primary" json:"id" dc:""`                                                                      //
	StrategyId       string      `orm:"strategy_id" json:"strategyId" dc:"策略组ID"`                                                        // 策略组ID
	StrategyName     string      `orm:"strategy_name" json:"strategyName" dc:"策略组名称"`                                                    // 策略组名称
	StrategyDescribe string      `orm:"strategy_describe" json:"strategyDescribe" dc:"描述"`                                               // 描述
	TaskId           string      `orm:"task_id" json:"taskId" dc:"任务ID"`                                                                 // 任务ID
	AdvertiserIds    []int64     `orm:"advertiser_ids" json:"advertiserIds" dc:"广告主ID列表"`                                                // 广告主ID列表
	AccountBatchRule string      `orm:"account_batch_rule" json:"accountBatchRule" dc:"多账户分配规则 按需分配：ON_DEMAND"`                          // 多账户分配规则 按需分配：ON_DEMAND
	AdGroupRule      string      `orm:"ad_group_rule" json:"adGroupRule" dc:"广告分组规则 按定向包分组：AUDIENCE_PACKAGE 按创意分组：CREATIVE 按文案分组：TITLE"` // 广告分组规则 按定向包分组：AUDIENCE_PACKAGE 按创意分组：CREATIVE 按文案分组：TITLE
	AdType           int         `orm:"ad_type" json:"adType" dc:"广告计划类型 0:信息流（展示广告） 1:搜索（搜索广告）"`                                        // 广告计划类型 0:信息流（展示广告） 1:搜索（搜索广告）
	UserId           int         `orm:"user_id" json:"userId" dc:"归属人员"`                                                                 // 归属人员
	CreatedAt        *gtime.Time `orm:"created_at" json:"createdAt" dc:"创建时间"`                                                           // 创建时间
	UpdatedAt        *gtime.Time `orm:"updated_at" json:"updatedAt" dc:"更新时间"`                                                           // 更新时间
	DeletedAt        *gtime.Time `orm:"deleted_at" json:"deletedAt" dc:"删除时间"`                                                           // 删除时间
}

type KsAdvertiserStrategyConfigListRes struct {
	Id               int64       `json:"id" dc:""`
	StrategyId       string      `json:"strategyId" dc:"策略组ID"`
	StrategyName     string      `json:"strategyName" dc:"策略组名称"`
	StrategyDescribe string      `json:"strategyDescribe" dc:"描述"`
	TaskId           string      `json:"taskId" dc:"任务ID"`
	AdvertiserIds    []int64     `json:"advertiserIds" dc:"广告主ID列表"`
	AccountBatchRule string      `json:"accountBatchRule" dc:"多账户分配规则 按需分配：ON_DEMAND"`
	AdGroupRule      string      `json:"adGroupRule" dc:"广告分组规则 按定向包分组：AUDIENCE_PACKAGE 按创意分组：CREATIVE 按文案分组：TITLE"`
	AdType           int         `json:"adType" dc:"广告计划类型 0:信息流（展示广告） 1:搜索（搜索广告）"`
	UserId           int         `json:"userId" dc:"归属人员"`
	CreatedAt        *gtime.Time `json:"createdAt" dc:"创建时间"`
}

// KsAdvertiserStrategyConfigSearchReq 分页请求参数
type KsAdvertiserStrategyConfigSearchReq struct {
	comModel.PageReq
	StrategyName string `p:"strategyName" dc:"策略组名称"`
	AdType       string `p:"adType" dc:"广告计划类型 0:信息流（展示广告） 1:搜索（搜索广告）"`
}

// KsAdvertiserStrategyConfigSearchRes 列表返回结果
type KsAdvertiserStrategyConfigSearchRes struct {
	comModel.ListRes
	List []*KsAdvertiserStrategyConfigListRes `json:"list"`
}

// KsAdvertiserStrategyConfigAddReq 添加操作请求参数
type KsAdvertiserStrategyConfigAddReq struct {
	StrategyId       string                          `p:"-"  dc:"策略组ID"`
	StrategyName     string                          `p:"strategyName"  dc:"策略组名称"`
	StrategyDescribe string                          `p:"strategyDescribe"  dc:"描述"`
	TaskId           string                          `p:"-"  dc:"任务ID"`
	AdvertiserIds    []int64                         `p:"advertiserIds"  dc:"广告主ID列表"`
	AccountBatchRule commonConsts.KsAccountBatchRule `p:"accountBatchRule"  dc:"多账户分配规则 按需分配：ON_DEMAND"`
	AdGroupRule      commonConsts.KsAdGroupRuleType  `p:"adGroupRule"  dc:"广告分组规则 按定向包分组：AUDIENCE_PACKAGE 按创意分组：CREATIVE 按文案分组：TITLE"`
	AdType           int                             `p:"adType"  dc:"广告计划类型 0:信息流（展示广告） 1:搜索（搜索广告）"`
	UserId           int                             `p:"userId"  dc:"归属人员"`
	DataType         commonConsts.AdGenerateDataType `p:"dataType"  dc:"数据类型 预估生成：TOTAL 生成预览：PREVIEW"`
}

// KsAdvertiserStrategyConfigEditReq 修改操作请求参数
type KsAdvertiserStrategyConfigEditReq struct {
	StrategyId       string  `p:"strategyId"  v:"required#策略组ID不能为空" dc:"策略组ID"`
	StrategyName     string  `p:"strategyName" v:"required#策略组名称不能为空" dc:"策略组名称"`
	StrategyDescribe string  `p:"strategyDescribe"  dc:"描述"`
	TaskId           string  `p:"taskId"  dc:"任务ID"`
	AdvertiserIds    []int64 `p:"advertiserIds"  dc:"广告主ID列表"`
	AccountBatchRule string  `p:"accountBatchRule"  dc:"多账户分配规则 按需分配：ON_DEMAND"`
	AdGroupRule      string  `p:"adGroupRule"  dc:"广告分组规则 按定向包分组：AUDIENCE_PACKAGE 按创意分组：CREATIVE 按文案分组：TITLE"`
	AdType           int     `p:"adType"  dc:"广告计划类型 0:信息流（展示广告） 1:搜索（搜索广告）"`
}

type KsAdvertiserStrategyAddReq struct {
	RuleConfig     *KsAdvertiserStrategyConfigAddReq   `p:"ruleConfig"  dc:"规则配置"`
	CampaignConfig *KsAdvertiserStrategyCampaignAddReq `p:"campaignConfig"  dc:"广告计划"`
	UnitConfig     *KsAdvertiserStrategyUnitAddReq     `p:"adUnitConfig"  dc:"广告组"`
	CreativeConfig *KsAdvertiserStrategyCreativeAddReq `p:"creativeConfig"  dc:"创意"`
	MaterialConfig *KsAdvertiserStrategyMaterialAddReq `p:"materialConfig"  dc:"素材"`
	TitleConfig    *KsAdvertiserStrategyTitleAddReq    `p:"titleConfig"  dc:"文案"`
}

type KsAdvertiserStrategyEditReq struct {
	RuleConfig     *KsAdvertiserStrategyConfigEditReq   `p:"ruleConfig"  dc:"规则配置"`
	CampaignConfig *KsAdvertiserStrategyCampaignEditReq `p:"campaignConfig"  dc:"广告计划"`
	UnitConfig     *KsAdvertiserStrategyUnitEditReq     `p:"adUnitConfig"  dc:"广告组"`
	CreativeConfig *KsAdvertiserStrategyCreativeEditReq `p:"creativeConfig"  dc:"创意"`
	MaterialConfig *KsAdvertiserStrategyMaterialEditReq `p:"materialConfig"  dc:"素材"`
	TitleConfig    *KsAdvertiserStrategyTitleEditReq    `p:"titleConfig"  dc:"文案"`
}

type KsAdvertiserStrategyRes struct {
	RuleConfig     *KsAdvertiserStrategyConfigInfoRes   `json:"ruleConfig"  dc:"规则配置"`
	CampaignConfig *KsAdvertiserStrategyCampaignInfoRes `json:"campaignConfig"  dc:"广告计划"`
	UnitConfig     *KsAdvertiserStrategyUnitInfoRes     `json:"adUnitConfig"  dc:"广告组"`
	CreativeConfig *KsAdvertiserStrategyCreativeInfoRes `json:"creativeConfig"  dc:"创意"`
	MaterialConfig *KsAdvertiserStrategyMaterialInfoRes `json:"materialConfig"  dc:"素材"`
	TitleConfig    *KsAdvertiserStrategyTitleInfoRes    `json:"titleConfig"  dc:"文案"`
}
