// ==========================================================================
// GFast自动生成dao操作代码。
// 生成日期：2025-08-22 11:52:17
// 生成路径: internal/app/ad/dao/ks_advertiser_strategy_creative.go
// 生成人：cq
// desc:快手策略组-广告创意
// company:云南奇讯科技有限公司
// ==========================================================================

package dao

import (
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao/internal"
)

// ksAdvertiserStrategyCreativeDao is the manager for logic model data accessing and custom defined data operations functions management.
// You can define custom methods on it to extend its functionality as you wish.
type ksAdvertiserStrategyCreativeDao struct {
	*internal.KsAdvertiserStrategyCreativeDao
}

var (
	// KsAdvertiserStrategyCreative is globally public accessible object for table tools_gen_table operations.
	KsAdvertiserStrategyCreative = ksAdvertiserStrategyCreativeDao{
		internal.NewKsAdvertiserStrategyCreativeDao(),
	}
)

// Fill with you ideas below.
