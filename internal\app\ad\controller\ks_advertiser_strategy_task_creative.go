// ==========================================================================
// GFast自动生成controller操作代码。
// 生成日期：2025-08-23 17:40:21
// 生成路径: internal/app/ad/controller/ks_advertiser_strategy_task_creative.go
// 生成人：cyao
// desc:快手广告搭建-任务-广告创意
// company:云南奇讯科技有限公司
// ==========================================================================

package controller

import (
	"context"

	"github.com/tiger1103/gfast/v3/api/v1/ad"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	systemController "github.com/tiger1103/gfast/v3/internal/app/system/controller"
)

type ksAdvertiserStrategyTaskCreativeController struct {
	systemController.BaseController
}

var KsAdvertiserStrategyTaskCreative = new(ksAdvertiserStrategyTaskCreativeController)

// List 列表
func (c *ksAdvertiserStrategyTaskCreativeController) List(ctx context.Context, req *ad.KsAdvertiserStrategyTaskCreativeSearchReq) (res *ad.KsAdvertiserStrategyTaskCreativeSearchRes, err error) {
	res = new(ad.KsAdvertiserStrategyTaskCreativeSearchRes)
	res.KsAdvertiserStrategyTaskCreativeSearchRes, err = service.KsAdvertiserStrategyTaskCreative().List(ctx, &req.KsAdvertiserStrategyTaskCreativeSearchReq)
	return
}

// Get 获取快手广告搭建-任务-广告创意
func (c *ksAdvertiserStrategyTaskCreativeController) Get(ctx context.Context, req *ad.KsAdvertiserStrategyTaskCreativeGetReq) (res *ad.KsAdvertiserStrategyTaskCreativeGetRes, err error) {
	res = new(ad.KsAdvertiserStrategyTaskCreativeGetRes)
	res.KsAdvertiserStrategyTaskCreativeInfoRes, err = service.KsAdvertiserStrategyTaskCreative().GetByTaskCreativeId(ctx, req.TaskCreativeId)
	return
}

// Add 添加快手广告搭建-任务-广告创意
func (c *ksAdvertiserStrategyTaskCreativeController) Add(ctx context.Context, req *ad.KsAdvertiserStrategyTaskCreativeAddReq) (res *ad.KsAdvertiserStrategyTaskCreativeAddRes, err error) {
	err = service.KsAdvertiserStrategyTaskCreative().Add(ctx, req.KsAdvertiserStrategyTaskCreativeAddReq)
	return
}

// Edit 修改快手广告搭建-任务-广告创意
func (c *ksAdvertiserStrategyTaskCreativeController) Edit(ctx context.Context, req *ad.KsAdvertiserStrategyTaskCreativeEditReq) (res *ad.KsAdvertiserStrategyTaskCreativeEditRes, err error) {
	err = service.KsAdvertiserStrategyTaskCreative().Edit(ctx, req.KsAdvertiserStrategyTaskCreativeEditReq)
	return
}

// Delete 删除快手广告搭建-任务-广告创意
func (c *ksAdvertiserStrategyTaskCreativeController) Delete(ctx context.Context, req *ad.KsAdvertiserStrategyTaskCreativeDeleteReq) (res *ad.KsAdvertiserStrategyTaskCreativeDeleteRes, err error) {
	err = service.KsAdvertiserStrategyTaskCreative().Delete(ctx, req.TaskCreativeIds)
	return
}
