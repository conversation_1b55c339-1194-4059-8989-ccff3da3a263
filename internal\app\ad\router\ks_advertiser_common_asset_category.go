// ==========================================================================
// GFast自动生成router操作代码。
// 生成日期：2025-08-19 10:36:49
// 生成路径: internal/app/ad/router/ks_advertiser_common_asset_category.go
// 生成人：cq
// desc:快手通用资产-标题分类
// company:云南奇讯科技有限公司
// ==========================================================================

package router

import (
	"context"

	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/tiger1103/gfast/v3/internal/app/ad/controller"
)

func (router *Router) BindKsAdvertiserCommonAssetCategoryController(ctx context.Context, group *ghttp.RouterGroup) {
	group.Group("/ksAdvertiserCommonAssetCategory", func(group *ghttp.RouterGroup) {
		group.Bind(
			controller.KsAdvertiserCommonAssetCategory,
		)
	})
}
