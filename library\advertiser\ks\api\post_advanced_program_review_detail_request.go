package api

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
)

// AdvancedProgramReviewDetailService 获取程序化创意/智能创意审核信息
type AdvancedProgramReviewDetailService struct {
	ctx     context.Context
	cfg     *Configuration
	token   string
	Request *AdvancedProgramReviewDetailRequest
}

// AdvancedProgramReviewDetailRequest 获取程序化创意2.0审核信息
type AdvancedProgramReviewDetailRequest struct {
	// AdvertiserID 广告主ID
	AdvertiserID uint64 `json:"advertiser_id,omitempty"`
	// UnitIDs 广告组ID;数量小于等于20个
	UnitIDs []uint64 `json:"unit_ids,omitempty"`
}

type AdvancedProgramReview struct {
	//Details
	Details []*AdvancedProgramReviewDetail `json:"details,omitempty"`
}

// AdvancedProgramReviewDetail 获取程序化创意2.0审核信息
type AdvancedProgramReviewDetail struct {
	// UnitID 当前的程序化创意的广告组id
	UnitID uint64 `json:"unit_id,omitempty"`
	// Slogans 审核不通过的封面广告语
	Slogans []string `json:"slogans,omitempty"`
	// CombineDetailViews 审核不通过和正在审核的创意组合
	CombineDetailViews []CombineDetailView `json:"combine_detail_views,omitempty"`
}

// CombineDetailView 审核不通过和正在审核的创意组合
type CombineDetailView struct {
	// ID 创意id
	ID uint64 `json:"id,omitempty"`
	// PhotoID 视频id; 已加密
	PhotoID uint64 `json:"photo_id,omitempty"`
	// CoverUrl 封面url
	CoverUrl string `json:"cover_url,omitempty"`
	// Caption 作品广告语
	Caption string `json:"caption,omitempty"`
	// ReviewStatus 审核状态;1：审核中2：审核通过3：不通过
	ReviewStatus int `json:"review_status,omitempty"`
	// ReviewDetail 审核信息;里面是一个String类型数据，是审核信息
	ReviewDetail []string `json:"review_detail,omitempty"`
	// PutStatus 程序化创意审核状态; 程序化创意操作状态，1：投放，2：暂停，3：删除
	PutStatus int `json:"put_status,omitempty"`
}

func (r *AdvancedProgramReviewDetailService) SetCfg(cfg *Configuration) *AdvancedProgramReviewDetailService {
	r.cfg = cfg
	return r
}

func (r *AdvancedProgramReviewDetailService) SetReq(req AdvancedProgramReviewDetailRequest) *AdvancedProgramReviewDetailService {
	r.Request = &req
	return r
}

func (r *AdvancedProgramReviewDetailService) AccessToken(accessToken string) *AdvancedProgramReviewDetailService {
	r.token = accessToken
	return r
}

func (r *AdvancedProgramReviewDetailService) Do() (data *KsBaseResp[AdvancedProgramReview], err error) {
	localBasePath := r.cfg.BasePath
	localVarPath := localBasePath + "/rest/openapi/v2/creative/advanced/program/review_detail"
	response, err := r.cfg.HTTPClient.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("Access-Token", r.token).
		SetBody(r.Request).
		SetResult(&KsBaseResp[AdvancedProgramReview]{}).
		Post(localVarPath)
	if err != nil {
		return nil, err
	}
	resp := new(KsBaseResp[AdvancedProgramReview])
	// 将 JSON 响应解码到结构体中
	err = json.Unmarshal(response.Body(), &resp)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("/rest/openapi/v2/creative/advanced/program/review_detail解析响应出错: %v\n", err))
	}
	if resp.Code == 0 && resp.Data != nil {
		return resp, nil
	} else {
		return resp, errors.New(resp.Message)
	}
}
