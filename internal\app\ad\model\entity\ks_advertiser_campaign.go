// ==========================================================================
// GFast自动生成model entity操作代码。
// 生成日期：2025-08-13 16:34:00
// 生成路径: internal/app/ad/model/entity/ks_advertiser_campaign.go
// 生成人：cyao
// desc:快手广告计划
// company:云南奇讯科技有限公司
// ==========================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
)

// KsAdvertiserCampaign is the golang structure for table ks_advertiser_campaign.
type KsAdvertiserCampaign struct {
	gmeta.Meta                     `orm:"table:ks_advertiser_campaign"`
	CampaignId                     int64       `orm:"campaign_id,primary" json:"campaignId"`                                    // 广告计划 ID
	AdvertiserId                   int         `orm:"advertiser_id" json:"advertiserId"`                                        // 广告id
	PhotoPackageDetails            string      `orm:"photo_package_details" json:"photoPackageDetails"`                         // 图片包详情（可能为 null）
	AdType                         int         `orm:"ad_type" json:"adType"`                                                    // 广告类型
	CampaignType                   int         `orm:"campaign_type" json:"campaignType"`                                        // 广告系列类型
	CampaignDeepConversionType     int         `orm:"campaign_deep_conversion_type" json:"campaignDeepConversionType"`          // 深度转化类型
	BidType                        int         `orm:"bid_type" json:"bidType"`                                                  // 出价类型
	PutStatus                      int         `orm:"put_status" json:"putStatus"`                                              // 投放状态
	CampaignOcpxActionTypeName     string      `orm:"campaign_ocpx_action_type_name" json:"campaignOcpxActionTypeName"`         // 智投计划优化目标名称 可能为null
	CapRoiRatio                    float64     `orm:"cap_roi_ratio" json:"capRoiRatio"`                                         // ROI 上限比例
	CampaignOcpxActionType         int         `orm:"campaign_ocpx_action_type" json:"campaignOcpxActionType"`                  // OCPX 动作类型
	CampaignName                   string      `orm:"campaign_name" json:"campaignName"`                                        // 广告系列名称
	UpdateTime                     *gtime.Time `orm:"update_time" json:"updateTime"`                                            // 更新时间
	DspVersion                     int         `orm:"dsp_version" json:"dspVersion"`                                            // DSP 版本
	PeriodicDeliveryType           int         `orm:"periodic_delivery_type" json:"periodicDeliveryType"`                       // 周期投放类型
	CampaignDeepConversionTypeName string      `orm:"campaign_deep_conversion_type_name" json:"campaignDeepConversionTypeName"` // 智投计划深度优化目标名称
	CampaignSubType                int         `orm:"campaign_sub_type" json:"campaignSubType"`                                 // 广告系列子类型
	ConstraintCpa                  int         `orm:"constraint_cpa" json:"constraintCpa"`                                      // CPA 限制
	AutoAdjust                     int         `orm:"auto_adjust" json:"autoAdjust"`                                            // 是否自动调节
	ContinuePeriodType             int         `orm:"continue_period_type" json:"continuePeriodType"`                           // 连续周期类型
	ConstraintActionType           int         `orm:"constraint_action_type" json:"constraintActionType"`                       // 动作类型限制
	DayBudget                      int         `orm:"day_budget" json:"dayBudget"`                                              // 每日预算
	AutoManage                     int         `orm:"auto_manage" json:"autoManage"`                                            // 是否自动管理
	AutoPhotoScope                 int         `orm:"auto_photo_scope" json:"autoPhotoScope"`                                   // 自动图片范围
	CreateTime                     *gtime.Time `orm:"create_time" json:"createTime"`                                            // 创建时间
	AutoBuild                      int         `orm:"auto_build" json:"autoBuild"`                                              // 是否自动构建
	PeriodicDays                   int         `orm:"periodic_days" json:"periodicDays"`                                        // 周期天数
	CapBid                         int         `orm:"cap_bid" json:"capBid"`                                                    // 出价上限
	RangeBudget                    int         `orm:"range_budget" json:"rangeBudget"`                                          // 区间预算
	DayBudgetSchedule              string      `orm:"day_budget_schedule" json:"dayBudgetSchedule"`                             // 分日预算
	Status                         int         `orm:"status" json:"status"`                                                     // 状态
	UnitNameRule                   string      `orm:"unit_name_rule" json:"unitNameRule"`                                       // 单元命名规则
	CreativeNameRule               string      `orm:"creative_name_rule" json:"creativeNameRule"`                               // 创意命名规则
	CreatedAt                      *gtime.Time `orm:"created_at" json:"createdAt"`                                              // 创建时间
	DeletedAt                      *gtime.Time `orm:"deleted_at" json:"deletedAt"`                                              // 删除时间
}
