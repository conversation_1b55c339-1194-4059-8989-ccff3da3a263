// ==========================================================================
// GFast自动生成model entity操作代码。
// 生成日期：2025-08-22 11:52:17
// 生成路径: internal/app/ad/model/entity/ks_advertiser_strategy_creative.go
// 生成人：cq
// desc:快手策略组-广告创意
// company:云南奇讯科技有限公司
// ==========================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
)

// KsAdvertiserStrategyCreative is the golang structure for table ks_advertiser_strategy_creative.
type KsAdvertiserStrategyCreative struct {
	gmeta.Meta       `orm:"table:ks_advertiser_strategy_creative"`
	Id               uint64      `orm:"id,primary" json:"id"`                      // 主键ID
	StrategyId       string      `orm:"strategy_id" json:"strategyId"`             // 策略组ID
	TaskId           string      `orm:"task_id" json:"taskId"`                     // 任务ID
	CreateMode       int         `orm:"create_mode" json:"createMode"`             // 创意制作方式 1：自动化创意 2：程序化3.0
	ActionBarText    string      `orm:"action_bar_text" json:"actionBarText"`      // 行动号召
	NewExposeTag     []string    `orm:"new_expose_tag" json:"newExposeTag"`        // 推荐理由（选填）
	OuterLoopNative  int         `orm:"outer_loop_native" json:"outerLoopNative"`  // 是否开启原生 1开启、0关闭｜不填则默认为0
	KolUserId        int64       `orm:"kol_user_id" json:"kolUserId"`              // 达人id 开启原生场景下必传，计划 campaignType=30 短剧推广时，值为短剧作者ID
	KolUserType      int         `orm:"kol_user_type" json:"kolUserType"`          // 达人类型 1普通快手号原生，2服务号原生，3聚星达人（需加白使用）
	CreativeCategory int         `orm:"creative_category" json:"creativeCategory"` // 创意分类
	CreativeTag      []string    `orm:"creative_tag" json:"creativeTag"`           // 创意标签
	CreativeName     string      `orm:"creative_name" json:"creativeName"`         // 创意名称
	TrackUrlSwitch   int         `orm:"track_url_switch" json:"trackUrlSwitch"`    // 监测链接开关 1:开启，0:关闭
	CreatedAt        *gtime.Time `orm:"created_at" json:"createdAt"`               // 创建时间
	UpdatedAt        *gtime.Time `orm:"updated_at" json:"updatedAt"`               // 更新时间
	DeletedAt        *gtime.Time `orm:"deleted_at" json:"deletedAt"`               // 删除时间
}
