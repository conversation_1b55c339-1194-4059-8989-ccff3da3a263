package api

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
)

// GetAccountListService  代理商-账户列表（提供账户检索能力）
type GetAccountListService struct {
	ctx     context.Context
	cfg     *Configuration
	token   string
	Request *GetAccountListReq
}

type GetAccountListReq struct {
	//create_time_begin 账户创建时间（开始）
	CreateTimeBegin int64 `json:"create_time_begin"`
	//create_time_end 账户创建时间（结束）
	CreateTimeEnd int64 `json:"create_time_end"`
	//当前页码
	Page int64 `json:"page"` // 当前页码
	// page_size	页码大小
	PageSize int `json:"page_size"`
	// select_type 搜索类型 0-不搜索 1-全部分类(全类型精确匹配，建议明确查询类型) 2-广告主ID 3-快手ID 4-广告主昵称 5-企业名称
	SelectType int `json:"select_type"`
	//select_value 搜索值
	SelectValue string `json:"select_value"`
}

type GetAccountListResp struct {
	Code    int              `json:"code"`    // 状态码
	Message string           `json:"message"` // 返回信息
	Data    *AccountListResp `json:"data"`    // 广告主列表数据
}

// AccountListResp 广告主列表数据
type AccountListResp struct {
	Details    []AccountInfoView `json:"details"`     // 广告主列表
	TotalCount int64             `json:"total_count"` // 总数
	//page_size
	PageSize int64 `json:"page_size"`
	PageNo   int64 `json:"page_no"`
}

func (r *GetAccountListService) SetCfg(cfg *Configuration) *GetAccountListService {
	r.cfg = cfg
	return r
}

func (r *GetAccountListService) SetReq(req GetAccountListReq) *GetAccountListService {
	r.Request = &req
	return r
}

func (r *GetAccountListService) AccessToken(accessToken string) *GetAccountListService {
	r.token = accessToken
	return r
}

func (r *GetAccountListService) Do() (data *GetAccountListResp, err error) {
	localBasePath := r.cfg.BasePath
	localVarPath := localBasePath + "/rest/openapi/gw/agent/v1/account/list"
	response, err := r.cfg.HTTPClient.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("Access-Token", r.token).
		SetBody(r.Request).
		SetResult(&GetAccountListResp{}).
		Post(localVarPath)
	if err != nil {
		return nil, err
	}
	resp := new(GetAccountListResp)
	// 将 JSON 响应解码到结构体中
	err = json.Unmarshal(response.Body(), &resp)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("/rest/openapi/gw/agent/v1/account/list解析响应出错: %v\n", err))
	}
	if resp.Code == 0 && resp.Data != nil {
		return resp, nil
	} else {
		return resp, errors.New(resp.Message)
	}
}
