// ==========================================================================
// GFast自动生成dao操作代码。
// 生成日期：2025-08-12 15:45:06
// 生成路径: internal/app/ad/dao/ks_advertiser_agent_info.go
// 生成人：cyao
// desc:快手代理商信息表
// company:云南奇讯科技有限公司
// ==========================================================================

package dao

import (
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao/internal"
)

// ksAdvertiserAgentInfoDao is the manager for logic model data accessing and custom defined data operations functions management.
// You can define custom methods on it to extend its functionality as you wish.
type ksAdvertiserAgentInfoDao struct {
	*internal.KsAdvertiserAgentInfoDao
}

var (
	// KsAdvertiserAgentInfo is globally public accessible object for table tools_gen_table operations.
	KsAdvertiserAgentInfo = ksAdvertiserAgentInfoDao{
		internal.NewKsAdvertiserAgentInfoDao(),
	}
)

// Fill with you ideas below.
