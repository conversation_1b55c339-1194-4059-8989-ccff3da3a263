// ==========================================================================
// GFast自动生成controller操作代码。
// 生成日期：2025-08-12 15:45:06
// 生成路径: internal/app/ad/controller/ks_advertiser_agent_info.go
// 生成人：cyao
// desc:快手代理商信息表
// company:云南奇讯科技有限公司
// ==========================================================================

package controller

import (
	"context"

	"github.com/tiger1103/gfast/v3/api/v1/ad"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	systemController "github.com/tiger1103/gfast/v3/internal/app/system/controller"
)

type ksAdvertiserAgentInfoController struct {
	systemController.BaseController
}

var KsAdvertiserAgentInfo = new(ksAdvertiserAgentInfoController)

// List 列表
func (c *ksAdvertiserAgentInfoController) List(ctx context.Context, req *ad.KsAdvertiserAgentInfoSearchReq) (res *ad.KsAdvertiserAgentInfoSearchRes, err error) {
	res = new(ad.KsAdvertiserAgentInfoSearchRes)
	res.KsAdvertiserAgentInfoSearchRes, err = service.KsAdvertiserAgentInfo().List(ctx, &req.KsAdvertiserAgentInfoSearchReq)
	return
}

// Get 获取快手代理商信息表
func (c *ksAdvertiserAgentInfoController) Get(ctx context.Context, req *ad.KsAdvertiserAgentInfoGetReq) (res *ad.KsAdvertiserAgentInfoGetRes, err error) {
	res = new(ad.KsAdvertiserAgentInfoGetRes)
	res.KsAdvertiserAgentInfoInfoRes, err = service.KsAdvertiserAgentInfo().GetById(ctx, req.Id)
	return
}

// Add 添加快手代理商信息表
func (c *ksAdvertiserAgentInfoController) Add(ctx context.Context, req *ad.KsAdvertiserAgentInfoAddReq) (res *ad.KsAdvertiserAgentInfoAddRes, err error) {
	err = service.KsAdvertiserAgentInfo().Add(ctx, req.KsAdvertiserAgentInfoAddReq)
	return
}

// Share
func (c *ksAdvertiserAgentInfoController) Share(ctx context.Context, req *ad.KsAdvertiserAgentShareReq) (res *ad.KsAdvertiserAgentShareRes, err error) {
	res = new(ad.KsAdvertiserAgentShareRes)
	res.KsAdvertiserAgentInfoShareRes, err = service.KsAdvertiserAgentInfo().Share(ctx, &req.KsAdvertiserAgentInfoShareReq)
	return
}

// Edit 修改快手代理商信息表
func (c *ksAdvertiserAgentInfoController) Edit(ctx context.Context, req *ad.KsAdvertiserAgentInfoEditReq) (res *ad.KsAdvertiserAgentInfoEditRes, err error) {
	err = service.KsAdvertiserAgentInfo().Edit(ctx, req.KsAdvertiserAgentInfoEditReq)
	return
}

// Delete 删除快手代理商信息表
func (c *ksAdvertiserAgentInfoController) Delete(ctx context.Context, req *ad.KsAdvertiserAgentInfoDeleteReq) (res *ad.KsAdvertiserAgentInfoDeleteRes, err error) {
	err = service.KsAdvertiserAgentInfo().Delete(ctx, req.Ids)
	return
}
