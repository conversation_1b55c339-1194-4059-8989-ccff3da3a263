// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2025-08-13 16:33:59
// 生成路径: api/v1/ad/ks_advertiser_campaign.go
// 生成人：cyao
// desc:快手广告计划相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package ad

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
)

// KsAdvertiserCampaignSearchReq 分页请求参数
type KsAdvertiserCampaignSearchReq struct {
	g.Meta `path:"/list" tags:"快手广告计划" method:"post" summary:"快手广告计划列表"`
	commonApi.Author
	model.KsAdvertiserCampaignSearchReq
}

// KsAdvertiserCampaignSearchRes 列表返回结果
type KsAdvertiserCampaignSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.KsAdvertiserCampaignSearchRes
}

// KsAdvertiserCampaignAddReq 添加操作请求参数
type KsAdvertiserCampaignAddReq struct {
	g.Meta `path:"/add" tags:"快手广告计划" method:"post" summary:"快手广告计划添加"`
	commonApi.Author
	*model.KsAdvertiserCampaignAddReq
}

// KsAdvertiserCampaignAddRes 添加操作返回结果
type KsAdvertiserCampaignAddRes struct {
	commonApi.EmptyRes
}

// KsAdvertiserCampaignEditReq 修改操作请求参数
type KsAdvertiserCampaignEditReq struct {
	g.Meta `path:"/edit" tags:"快手广告计划" method:"put" summary:"快手广告计划修改"`
	commonApi.Author
	*model.KsAdvertiserCampaignEditReq
}

// KsAdvertiserCampaignEditRes 修改操作返回结果
type KsAdvertiserCampaignEditRes struct {
	commonApi.EmptyRes
}

// KsAdvertiserCampaignGetReq 获取一条数据请求
type KsAdvertiserCampaignGetReq struct {
	g.Meta `path:"/get" tags:"快手广告计划" method:"get" summary:"获取快手广告计划信息"`
	commonApi.Author
	CampaignId int64 `p:"campaignId" v:"required#主键必须"` //通过主键获取
}

// KsAdvertiserCampaignGetRes 获取一条数据结果
type KsAdvertiserCampaignGetRes struct {
	g.Meta `mime:"application/json"`
	*model.KsAdvertiserCampaignInfoRes
}

// KsAdvertiserCampaignDeleteReq 删除数据请求
type KsAdvertiserCampaignDeleteReq struct {
	g.Meta `path:"/delete" tags:"快手广告计划" method:"delete" summary:"删除快手广告计划"`
	commonApi.Author
	CampaignIds []int64 `p:"campaignIds" v:"required#主键必须"` //通过主键删除
}

// KsAdvertiserCampaignDeleteRes 删除数据返回
type KsAdvertiserCampaignDeleteRes struct {
	commonApi.EmptyRes
}

// KsAdvertiserCampaignManualSyncReq 手动同步广告计划请求参数
type KsAdvertiserCampaignManualSyncReq struct {
	g.Meta `path:"/manualSync" tags:"快手广告计划" method:"post" summary:"手动同步广告计划"`
	commonApi.Author
	AdvertiserIds []int64 `p:"advertiserIds" v:"required#账户ID必须" dc:"账户ID"`
	StartTime     string  `p:"startTime" v:"required#开始时间必须" dc:"开始时间"`
	EndTime       string  `p:"endTime" v:"required#结束时间必须" dc:"结束时间"`
}

// KsAdvertiserCampaignManualSyncRes 手动同步广告计划返回结果
type KsAdvertiserCampaignManualSyncRes struct {
	commonApi.EmptyRes
}

// KsAdvertiserCampaignEditNameReq 修改广告计划名称请求参数
type KsAdvertiserCampaignEditNameReq struct {
	g.Meta `path:"/editName" tags:"快手广告计划" method:"post" summary:"修改广告计划名称"`
	commonApi.Author
	CampaignId   int64  `p:"campaignId" v:"required#广告计划ID必须" dc:"广告计划ID"`
	CampaignName string `p:"campaignName" v:"required#广告计划名称必须" dc:"广告计划名称"`
}

// KsAdvertiserCampaignEditNameRes 修改广告计划名称返回结果
type KsAdvertiserCampaignEditNameRes struct {
	commonApi.EmptyRes
}
