// ==========================================================================
// GFast自动生成router操作代码。
// 生成日期：2025-08-19 10:36:58
// 生成路径: internal/app/ad/router/ks_advertiser_common_asset_title.go
// 生成人：cq
// desc:快手通用资产-标题库
// company:云南奇讯科技有限公司
// ==========================================================================

package router

import (
	"context"

	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/tiger1103/gfast/v3/internal/app/ad/controller"
)

func (router *Router) BindKsAdvertiserCommonAssetTitleController(ctx context.Context, group *ghttp.RouterGroup) {
	group.Group("/ksAdvertiserCommonAssetTitle", func(group *ghttp.RouterGroup) {
		group.Bind(
			controller.KsAdvertiserCommonAssetTitle,
		)
	})
}
