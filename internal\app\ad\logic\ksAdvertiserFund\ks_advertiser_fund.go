// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2025-08-12 15:45:08
// 生成路径: internal/app/ad/logic/ks_advertiser_fund.go
// 生成人：cyao
// desc:广告主资金信息
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	"github.com/tiger1103/gfast/v3/library/liberr"
)

func init() {
	service.RegisterKsAdvertiserFund(New())
}

func New() service.IKsAdvertiserFund {
	return &sKsAdvertiserFund{}
}

type sKsAdvertiserFund struct{}

func (s *sKsAdvertiserFund) List(ctx context.Context, req *model.KsAdvertiserFundSearchReq) (listRes *model.KsAdvertiserFundSearchRes, err error) {
	listRes = new(model.KsAdvertiserFundSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.KsAdvertiserFund.Ctx(ctx).WithAll()
		if req.AdvertiserId != "" {
			m = m.Where(dao.KsAdvertiserFund.Columns().AdvertiserId+" = ?", req.AdvertiserId)
		}
		if req.ContractRebate != "" {
			m = m.Where(dao.KsAdvertiserFund.Columns().ContractRebate+" = ?", req.ContractRebate)
		}
		if len(req.DateRange) != 0 {
			m = m.Where(dao.KsAdvertiserFund.Columns().CreatedAt+" >=? AND "+dao.KsAdvertiserFund.Columns().CreatedAt+" <=?", req.DateRange[0], req.DateRange[1])
		}
		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "advertiser_id asc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		var res []*model.KsAdvertiserFundListRes
		err = m.Page(req.PageNum, req.PageSize).Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		listRes.List = make([]*model.KsAdvertiserFundListRes, len(res))
		for k, v := range res {
			listRes.List[k] = &model.KsAdvertiserFundListRes{
				AdvertiserId:    v.AdvertiserId,
				DirectRebate:    v.DirectRebate,
				ContractRebate:  v.ContractRebate,
				RechargeBalance: v.RechargeBalance,
				Balance:         v.Balance,
				CreatedAt:       v.CreatedAt,
			}
		}
	})
	return
}

func (s *sKsAdvertiserFund) GetByAdvertiserId(ctx context.Context, advertiserId int64) (res *model.KsAdvertiserFundInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.KsAdvertiserFund.Ctx(ctx).WithAll().Where(dao.KsAdvertiserFund.Columns().AdvertiserId, advertiserId).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sKsAdvertiserFund) Add(ctx context.Context, req *model.KsAdvertiserFundAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.KsAdvertiserFund.Ctx(ctx).Insert(do.KsAdvertiserFund{
			AdvertiserId:    req.AdvertiserId,
			DirectRebate:    req.DirectRebate,
			ContractRebate:  req.ContractRebate,
			RechargeBalance: req.RechargeBalance,
			Balance:         req.Balance,
		})
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sKsAdvertiserFund) Edit(ctx context.Context, req *model.KsAdvertiserFundEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.KsAdvertiserFund.Ctx(ctx).WherePri(req.AdvertiserId).Update(do.KsAdvertiserFund{
			DirectRebate:    req.DirectRebate,
			ContractRebate:  req.ContractRebate,
			RechargeBalance: req.RechargeBalance,
			Balance:         req.Balance,
		})
		liberr.ErrIsNil(ctx, err, "修改失败")
	})
	return
}

func (s *sKsAdvertiserFund) Delete(ctx context.Context, advertiserIds []int64) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.KsAdvertiserFund.Ctx(ctx).Delete(dao.KsAdvertiserFund.Columns().AdvertiserId+" in (?)", advertiserIds)
		liberr.ErrIsNil(ctx, err, "删除失败")
	})
	return
}
