package api

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
)

// QuerySeriesPayModeTypeService 短剧付费模式查询接口
type QuerySeriesPayModeTypeService struct {
	ctx     context.Context
	cfg     *Configuration
	token   string
	Request *QuerySeriesPayModeTypeReq
}

// QuerySeriesPayModeTypeReq 请求结构体
type QuerySeriesPayModeTypeReq struct {
	AdvertiserId int64 `json:"advertiser_id"` // 广告主ID，必填
	SeriesId     int64 `json:"series_id"`     // 短剧id，必填
	UserId       int64 `json:"user_id"`       // 快手号id 必填
}

type MapiSeriesPayModeInfoSnake struct {
	PayMode     int    `json:"pay_mode"` // 目前仅支持payMode=1，即“打包“
	PayModeDesc string `json:"pay_mode_desc"`
}

func (r *QuerySeriesPayModeTypeService) SetCfg(cfg *Configuration) *QuerySeriesPayModeTypeService {
	r.cfg = cfg
	return r
}

func (r *QuerySeriesPayModeTypeService) SetReq(req QuerySeriesPayModeTypeReq) *QuerySeriesPayModeTypeService {
	r.Request = &req
	return r
}

func (r *QuerySeriesPayModeTypeService) AccessToken(accessToken string) *QuerySeriesPayModeTypeService {
	r.token = accessToken
	return r
}

func (r *QuerySeriesPayModeTypeService) Do() (data *KsBaseResp[[]MapiSeriesPayModeInfoSnake], err error) {
	localBasePath := r.cfg.BasePath
	localVarPath := localBasePath + "/rest/openapi/gw/dsp/series/payModeType"
	response, err := r.cfg.HTTPClient.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("Access-Token", r.token).
		SetBody(r.Request).
		SetResult(&KsBaseResp[[]MapiSeriesPayModeInfoSnake]{}).
		Post(localVarPath)
	if err != nil {
		return nil, err
	}
	resp := new(KsBaseResp[[]MapiSeriesPayModeInfoSnake])
	// 将 JSON 响应解码到结构体中
	err = json.Unmarshal(response.Body(), &resp)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("/rest/openapi/gw/dsp/series/payModeType解析响应出错: %v\n", err))
	}
	if resp.Code == 0 && resp.Data != nil {
		return resp, nil
	} else {
		return resp, errors.New(resp.Message)
	}
}
