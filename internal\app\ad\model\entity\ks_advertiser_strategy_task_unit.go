// ==========================================================================
// GFast自动生成model entity操作代码。
// 生成日期：2025-08-23 17:40:24
// 生成路径: internal/app/ad/model/entity/ks_advertiser_strategy_task_unit.go
// 生成人：cyao
// desc:快手广告搭建-任务-广告组
// company:云南奇讯科技有限公司
// ==========================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
)

// KsAdvertiserStrategyTaskUnit is the golang structure for table ks_advertiser_strategy_task_unit.
type KsAdvertiserStrategyTaskUnit struct {
	gmeta.Meta     `orm:"table:ks_advertiser_strategy_task_unit"`
	TaskUnitId     string      `orm:"task_unit_id,primary" json:"taskUnitId"` // 任务广告组ID
	UnitId         int64       `orm:"unit_id" json:"unitId"`                  // 单元 ID
	UnitName       string      `orm:"unit_name" json:"unitName"`              // 广告组名称
	TaskId         string      `orm:"task_id" json:"taskId"`                  // 任务ID
	TaskCampaignId string      `orm:"task_campaign_id" json:"taskCampaignId"` // 任务计划ID
	AdvertiserId   string      `orm:"advertiser_id" json:"advertiserId"`      // 广告主ID
	AdvertiserNick string      `orm:"advertiser_nick" json:"advertiserNick"`  // 广告主名称
	ExternalAction string      `orm:"external_action" json:"externalAction"`  // 优化目标
	ErrMsg         string      `orm:"err_msg" json:"errMsg"`                  // 失败原因
	Status         string      `orm:"status" json:"status"`                   // 项目状态 等待提交：INIT 已终止：TERMINATED 成功：SUCCESS 失败：ERROR
	UnitData       string      `orm:"unit_data" json:"unitData"`              // 创建项目数据
	CreatedAt      *gtime.Time `orm:"created_at" json:"createdAt"`            // 创建时间
	UpdatedAt      *gtime.Time `orm:"updated_at" json:"updatedAt"`            // 更新时间
	DeletedAt      *gtime.Time `orm:"deleted_at" json:"deletedAt"`            // 删除时间
}
