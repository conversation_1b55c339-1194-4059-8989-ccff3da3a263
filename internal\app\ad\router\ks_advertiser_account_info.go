// ==========================================================================
// GFast自动生成router操作代码。
// 生成日期：2025-08-14 14:09:05
// 生成路径: internal/app/ad/router/ks_advertiser_account_info.go
// 生成人：cyao
// desc:快手广告账户表格
// company:云南奇讯科技有限公司
// ==========================================================================

package router

import (
	"context"

	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/tiger1103/gfast/v3/internal/app/ad/controller"
)

func (router *Router) BindKsAdvertiserAccountInfoController(ctx context.Context, group *ghttp.RouterGroup) {
	group.Group("/ksAdvertiserAccountInfo", func(group *ghttp.RouterGroup) {
		group.Bind(
			controller.KsAdvertiserAccountInfo,
		)
	})
}
