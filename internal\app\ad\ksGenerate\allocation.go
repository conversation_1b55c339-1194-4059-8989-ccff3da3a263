package generate

import (
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	commonConst "github.com/tiger1103/gfast/v3/internal/app/common/consts"
	"github.com/tiger1103/gfast/v3/library/libUtils"
)

// AllocationCreativeMaterial 分配创意素材
func AllocationCreativeMaterial(req *model.KsAdvertiserStrategyGenerateReq) map[int64][]*model.CreativeMaterials {
	res := make(map[int64][]*model.CreativeMaterials)
	allocationMethod := req.KsAdvertiserStrategyMaterialConfig.MaterialAllocationMethod
	creativeMaterialData := req.KsAdvertiserStrategyMaterialConfig.CreativeMaterialData
	advertiserIds := req.KsAdvertiserStrategyRuleReq.AdvertiserIds
	switch allocationMethod {
	case commonConst.CreateMaterialAdvertiser, commonConst.CreateMaterialSame:
		// 分账户选择或全账户复用：直接按配置分配
		for _, v := range creativeMaterialData {
			res[v.AdvertiserId] = v.CreativeMaterials
		}
	case commonConst.CreateMaterialAverage:
		// 平均分配：将素材平均分配给各个广告主
		if len(creativeMaterialData) > 0 {
			creativeMaterials := creativeMaterialData[0].CreativeMaterials
			materialNum := len(creativeMaterials)
			advertiserNum := len(advertiserIds)
			if advertiserNum > 0 {
				// 计算每个广告主的基础分配数量
				baseMaterialsPerAdvertiser := materialNum / advertiserNum
				// 剩余需要分配的
				remainingMaterials := materialNum % advertiserNum
				materialIndex := 0
				for i, advertiserId := range advertiserIds {
					// 计算当前广告主应分配的数量
					materialsToAssign := baseMaterialsPerAdvertiser
					if i < remainingMaterials {
						materialsToAssign++
					}
					// 分配创意素材
					for j := 0; j < materialsToAssign && materialIndex < materialNum; j++ {
						res[advertiserId] = append(res[advertiserId], creativeMaterials[materialIndex])
						materialIndex++
					}
				}
			}
		}
	}
	return res
}

// AllocationTitle 分配文案
func AllocationTitle(req *model.KsAdvertiserStrategyGenerateReq) map[int64][]*model.KsAdvertiserCommonAssetTitleInfoRes {
	res := make(map[int64][]*model.KsAdvertiserCommonAssetTitleInfoRes)
	// 所有广告主使用相同的文案列表
	for _, advertiserId := range req.KsAdvertiserStrategyRuleReq.AdvertiserIds {
		res[advertiserId] = req.KsAdvertiserStrategyTitleConfig.TitleData
	}
	return res
}

// AllocationTitleAverage 平均分配文案
func AllocationTitleAverage(req *model.KsAdvertiserStrategyGenerateReq) map[int64][]*model.KsAdvertiserCommonAssetTitleInfoRes {
	res := make(map[int64][]*model.KsAdvertiserCommonAssetTitleInfoRes)
	titleData := req.KsAdvertiserStrategyTitleConfig.TitleData
	advertiserIds := req.KsAdvertiserStrategyRuleReq.AdvertiserIds
	titleNum := len(titleData)
	advertiserNum := len(advertiserIds)
	if titleNum == 0 || advertiserNum == 0 {
		return res
	}
	// 计算每个广告主的基础分配数量
	baseTitlesPerAdvertiser := titleNum / advertiserNum
	// 剩余需要分配的文案数量
	remainingTitles := titleNum % advertiserNum
	titleIndex := 0
	for i, advertiserId := range advertiserIds {
		// 计算当前广告主应分配的数量
		titlesToAssign := baseTitlesPerAdvertiser
		if i < remainingTitles {
			titlesToAssign++
		}
		// 分配文案
		for j := 0; j < titlesToAssign && titleIndex < titleNum; j++ {
			res[advertiserId] = append(res[advertiserId], titleData[titleIndex])
			titleIndex++
		}
	}
	return res
}

// BuildPreviewUnitListByMaterialAndTitle 根据创意素材和文案构建预览广告组列表
func BuildPreviewUnitListByMaterialAndTitle(estimateInfo *model.EstimateInfo, titleAllocationMethod commonConst.KsTitleAllocationMethod) []*model.PreviewUnitBaseInfo {
	var creativeMaterialList = estimateInfo.CreativeMaterialList
	var titleList = estimateInfo.TitleList
	unitList := make([]*model.PreviewUnitBaseInfo, 0)
	if len(creativeMaterialList) == 0 {
		return unitList
	}
	switch titleAllocationMethod {
	case commonConst.KsTitleAllocationMethodAuto:
		// AUTO模式：广告组数量 = 素材数量，文案按顺序循环使用
		titleIndex := 0
		for _, creativeMaterials := range creativeMaterialList {
			unitInfo := &model.PreviewUnitBaseInfo{
				CreativeMaterials: creativeMaterials,
			}
			// 循环使用文案
			if len(titleList) > 0 {
				if titleIndex >= len(titleList) {
					titleIndex = 0
				}
				unitInfo.Title = titleList[titleIndex]
				titleIndex++
			}
			unitList = append(unitList, unitInfo)
		}
	case commonConst.KsTitleAllocationMethodTest:
		// TEST模式：广告组数量 = 素材数量 * 文案数量，每个素材配每个文案
		for _, creativeMaterials := range creativeMaterialList {
			for _, title := range titleList {
				unitInfo := &model.PreviewUnitBaseInfo{
					CreativeMaterials: creativeMaterials,
					Title:             title,
				}
				unitList = append(unitList, unitInfo)
			}
		}
	}
	return unitList
}

// AllocationUnitAverage 将广告组平均分配到广告计划中
func AllocationUnitAverage(unitList []*model.PreviewUnitBaseInfo, campaignNum int) []*model.PreviewCampaignBaseInfo {
	campaignList := make([]*model.PreviewCampaignBaseInfo, 0)
	if len(unitList) == 0 || campaignNum <= 0 {
		return campaignList
	}
	// 计算每个广告计划的基础广告组数量
	baseUnitsPerCampaign := len(unitList) / campaignNum
	// 剩余需要分配的广告组数量
	remainingUnits := len(unitList) % campaignNum
	unitIndex := 0
	for i := 0; i < campaignNum; i++ {
		campaign := &model.PreviewCampaignBaseInfo{
			CampaignId: libUtils.GenerateID(), // 临时ID
			UnitList:   make([]*model.PreviewUnitBaseInfo, 0),
		}
		// 计算当前广告计划应分配的广告组数量
		unitsToAssign := baseUnitsPerCampaign
		if i < remainingUnits {
			unitsToAssign++
		}
		// 分配广告组到当前广告计划
		for j := 0; j < unitsToAssign && unitIndex < len(unitList); j++ {
			campaign.UnitList = append(campaign.UnitList, unitList[unitIndex])
			unitIndex++
		}
		campaignList = append(campaignList, campaign)
	}
	return campaignList
}
