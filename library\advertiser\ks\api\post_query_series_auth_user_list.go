package api

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
)

// QuerySeriesAuthUserListService 获取授权的短剧作者列表
type QuerySeriesAuthUserListService struct {
	ctx     context.Context
	cfg     *Configuration
	token   string
	Request *QuerySeriesAuthUserListReq
}

// QuerySeriesAuthUserListReq 请求结构体
type QuerySeriesAuthUserListReq struct {
	AdvertiserId int64 `json:"advertiser_id"` // 广告主ID，必填
}

type QuerySeriesAuthUserListData struct {
	HeadUrl  string `json:"head_url" dc:"短剧作者快手号头像"`
	UserId   int64  `json:"user_id" dc:"短剧作者快手号id"`
	UserName string `json:"user_name" dc:"短剧作者快手号名称"`
	UserSex  string `json:"user_sex" dc:"短剧作者快手号性别，男性M，女性F，U未知"`
}

func (r *QuerySeriesAuthUserListService) SetCfg(cfg *Configuration) *QuerySeriesAuthUserListService {
	r.cfg = cfg
	return r
}

func (r *QuerySeriesAuthUserListService) SetReq(req QuerySeriesAuthUserListReq) *QuerySeriesAuthUserListService {
	r.Request = &req
	return r
}

func (r *QuerySeriesAuthUserListService) AccessToken(accessToken string) *QuerySeriesAuthUserListService {
	r.token = accessToken
	return r
}

func (r *QuerySeriesAuthUserListService) Do() (data *KsBaseResp[[]QuerySeriesAuthUserListData], err error) {
	localBasePath := r.cfg.BasePath
	localVarPath := localBasePath + "/rest/openapi/gw/dsp/series/auth/user/list"
	response, err := r.cfg.HTTPClient.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("Access-Token", r.token).
		SetBody(r.Request).
		SetResult(&KsBaseResp[[]QuerySeriesAuthUserListData]{}).
		Post(localVarPath)
	if err != nil {
		return nil, err
	}
	resp := new(KsBaseResp[[]QuerySeriesAuthUserListData])
	// 将 JSON 响应解码到结构体中
	err = json.Unmarshal(response.Body(), &resp)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("/rest/openapi/gw/dsp/series/auth/user/list解析响应出错: %v\n", err))
	}
	if resp.Code == 0 && resp.Data != nil {
		return resp, nil
	} else {
		return resp, errors.New(resp.Message)
	}
}
