// ==========================================================================
// GFast自动生成model entity操作代码。
// 生成日期：2025-08-14 14:09:04
// 生成路径: internal/app/ad/model/entity/ks_advertiser_account_info.go
// 生成人：cyao
// desc:快手广告账户表格
// company:云南奇讯科技有限公司
// ==========================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
)

// KsAdvertiserAccountInfo is the golang structure for table ks_advertiser_account_info.
type KsAdvertiserAccountInfo struct {
	gmeta.Meta            `orm:"table:ks_advertiser_account_info"`
	AccountId             int64       `orm:"account_id,primary" json:"accountId"`                   // 广告主ID
	AgentAccountId        int64       `orm:"agent_account_id" json:"agentAccountId"`                // 代理商账户id
	AuthorizeKsAccount    int64       `orm:"authorize_ks_account" json:"authorizeKsAccount"`        // 授权快手账号(代理商)
	UserId                int64       `orm:"user_id" json:"userId"`                                 // 快手账户ID
	AccountName           string      `orm:"account_name" json:"accountName"`                       // 快手账户名称
	ResponsiblePerson     string      `orm:"responsible_person" json:"responsiblePerson"`           // 销售责任人
	UcType                string      `orm:"uc_type" json:"ucType"`                                 // 账户类型
	PaymentType           string      `orm:"payment_type" json:"paymentType"`                       // 付款类型
	Balance               float64     `orm:"balance" json:"balance"`                                // 现金余额
	CreditBalance         float64     `orm:"credit_balance" json:"creditBalance"`                   // 信用账户余额
	ExtendedBalance       float64     `orm:"extended_balance" json:"extendedBalance"`               // 预留账户余额
	Rebate                int64       `orm:"rebate" json:"rebate"`                                  // 后返余额
	PreRebate             int64       `orm:"pre_rebate" json:"preRebate"`                           // 前返余额
	ContractRebate        int64       `orm:"contract_rebate" json:"contractRebate"`                 // 框返余额
	TotalBalance          int64       `orm:"total_balance" json:"totalBalance"`                     // 总余额
	LoLimit               int64       `orm:"lo_limit" json:"loLimit"`                               // 账户最低余额
	SingleOut             int64       `orm:"single_out" json:"singleOut"`                           // 单次转账金额
	AutoOut               int         `orm:"auto_out" json:"autoOut"`                               // 自动转账状态
	BalanceWarn           int         `orm:"balance_warn" json:"balanceWarn"`                       // 余额不足提醒
	ProductName           string      `orm:"product_name" json:"productName"`                       // 产品名称
	FirstCostDay          string      `orm:"first_cost_day" json:"firstCostDay"`                    // 首日消耗日期
	Industry              string      `orm:"industry" json:"industry"`                              // 一级行业
	SecondIndustry        string      `orm:"second_industry" json:"secondIndustry"`                 // 二级行业
	Recharged             int         `orm:"recharged" json:"recharged"`                            // 是否充值
	CorporationName       string      `orm:"corporation_name" json:"corporationName"`               // 企业名称
	ReviewStatus          int         `orm:"review_status" json:"reviewStatus"`                     // 审核状态 1-审核中; 2-审核通过; 3-审核拒绝; 0-待提交
	FrozenStatus          int         `orm:"frozen_status" json:"frozenStatus"`                     // 冻结状态
	TransferAccountStatus int         `orm:"transfer_account_status" json:"transferAccountStatus"`  // 转账状态
	ChildReviewStatusInfo string      `orm:"child_review_status_info" json:"childReviewStatusInfo"` // 审核状态信息（嵌套结构体）
	CopyAccount           int         `orm:"copy_account" json:"copyAccount"`                       // 是否为复制账户
	ReviewDetail          string      `orm:"review_detail" json:"reviewDetail"`                     // 审核详情（数组嵌套结构体）
	DirectRebate          int64       `orm:"direct_rebate" json:"directRebate"`                     // 激励余额
	OptimizerOwner        string      `orm:"optimizer_owner" json:"optimizerOwner"`                 // 优化师责任人
	AccountAutoManage     int         `orm:"account_auto_manage" json:"accountAutoManage"`          // 账户智投开关
	DayBudget             int64       `orm:"day_budget" json:"dayBudget"`                           // 单日预算 单位：厘
	Remark                string      `orm:"remark" json:"remark"`                                  // 备注
	AuthSource            string      `orm:"auth_source" json:"authSource"`                         // 授权来源（如代理商账户授权 1 广告主授权 2）
	AuthStatus            int         `orm:"auth_status" json:"authStatus"`                         // 授权状态（0:待授权, 1:已授权）
	DeliveryStatus        int         `orm:"delivery_status" json:"deliveryStatus"`                 // 投放状态（0:停用, 1:启用）
	Owner                 int         `orm:"owner" json:"owner"`                                    // 归属人员
	DeliveryType          int         `orm:"delivery_type" json:"deliveryType"`                     // 投放方式（0:默认, 1:优先效果）暂时先没用到
	EffectFirst           int         `orm:"effect_first" json:"effectFirst"`                       // 优先效果策略（1:开启, 其他:未开启）暂时先没用到
	CreateTime            int64       `orm:"create_time" json:"createTime"`                         // 创建时间
	CreatedAt             *gtime.Time `orm:"created_at" json:"createdAt"`                           // 创建时间记录产生时间
	UpdatedAt             *gtime.Time `orm:"updated_at" json:"updatedAt"`                           // 更新时间
}
