// ==========================================================================
// GFast自动生成service操作代码。
// 生成日期：2025-08-20 11:00:01
// 生成路径: internal/app/ad/service/ks_advertiser_program_creative_report.go
// 生成人：cyao
// desc:创意数据
// company:云南奇讯科技有限公司
// ==========================================================================

package service

import (
	"context"

	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
)

type IKsAdvertiserProgramCreativeReport interface {
	List(ctx context.Context, req *model.KsAdvertiserProgramCreativeReportSearchReq) (res *model.KsAdvertiserProgramCreativeReportSearchRes, err error)
	Pull(ctx context.Context, startTime, endTime string) (err error)
	GetByStatDate(ctx context.Context, creativeId int64, statDate string) (res *model.KsAdvertiserProgramCreativeReportInfoRes, err error)
	PullCreateByAdId(ctx context.Context, accessToken string, adId int64, startTime, endTime string) (err error)
	Add(ctx context.Context, req *model.KsAdvertiserProgramCreativeReportAddReq) (err error)
	Edit(ctx context.Context, req *model.KsAdvertiserProgramCreativeReportEditReq) (err error)
	Delete(ctx context.Context, CreativeId []int64) (err error)
}

var localKsAdvertiserProgramCreativeReport IKsAdvertiserProgramCreativeReport

func KsAdvertiserProgramCreativeReport() IKsAdvertiserProgramCreativeReport {
	if localKsAdvertiserProgramCreativeReport == nil {
		panic("implement not found for interface IKsAdvertiserProgramCreativeReport, forgot register?")
	}
	return localKsAdvertiserProgramCreativeReport
}

func RegisterKsAdvertiserProgramCreativeReport(i IKsAdvertiserProgramCreativeReport) {
	localKsAdvertiserProgramCreativeReport = i
}
