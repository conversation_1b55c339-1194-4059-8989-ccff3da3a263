package api

type AdKSIClient struct {
	Cfg *Configuration
	*GetTokenService
	*GetAdListService
	*GetReTokenService
	*QueryCoreDataService
	*QueryAdDataService
	*QuerySalerCopyRightDataService
	*QuerySettleDataService
	*QueryAccountInfoService
	*QueryAdDetailService
	*QuerySeriesInfoService
	*QueryOrderDetailService

	*GetAdvertiserInfoService
	*QueryAdvertiserFundService
	*GetFetchAccountListService
	*GetDspUnitListService
	*GetDspCampaignListService

	*AccountReportService
	*CampaignReportService
	*UnitReportService
	*CreativeReviewDetailsService
	*OcpxTypesService
	*ModAccountAutoInfoService
	*QueryAccountAutoInfoService
	*QueryAccountBudgetService
	*UpdateAccountBudgetService
	*ProgramCreativeReportService

	*QueryAccountIncExploreOcpxTypesService
	*AddAccountIncExploreService
	*QueryAccountIncExploreService
	*UpdateAccountIncExploreService
	*DeleteAccountIncExploreService
	*PauseAccountIncExploreService
	*RebootAccountIncExploreService
	*AdImageUploadService
	*UploadTokenGenerateService
	*FileAdVideoUploadService
	*PostAdVideoUploadService

	*UpdateCampaignStatusService
	*UpdateCampaignService
	*UpdateUnitStatusService
	*UpdateUnitService

	*AdvancedCreativeCreateService
	*CreateUnitService
	*CreateCampaignService
	*CreateCreativeService

	*QuerySeriesAuthUserListService
	*QuerySeriesListService
	*QuerySeriesEpisodeListService
	*QuerySeriesPayModeTypeService
	*QuerySeriesPayModeTemplateService
	*QueryProductService
	*QueryProductLibraryListService
	*QueryCreativeActionBarTextService
	*QueryToolExposeTagsService
	*QueryCreativeCategoryListService
}
