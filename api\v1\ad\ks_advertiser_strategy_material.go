// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2025-08-22 11:52:27
// 生成路径: api/v1/ad/ks_advertiser_strategy_material.go
// 生成人：cq
// desc:快手策略组-素材相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package ad

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
)

// KsAdvertiserStrategyMaterialSearchReq 分页请求参数
type KsAdvertiserStrategyMaterialSearchReq struct {
	g.Meta `path:"/list" tags:"快手策略组-素材" method:"get" summary:"快手策略组-素材列表"`
	commonApi.Author
	model.KsAdvertiserStrategyMaterialSearchReq
}

// KsAdvertiserStrategyMaterialSearchRes 列表返回结果
type KsAdvertiserStrategyMaterialSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.KsAdvertiserStrategyMaterialSearchRes
}

// KsAdvertiserStrategyMaterialAddReq 添加操作请求参数
type KsAdvertiserStrategyMaterialAddReq struct {
	g.Meta `path:"/add" tags:"快手策略组-素材" method:"post" summary:"快手策略组-素材添加"`
	commonApi.Author
	*model.KsAdvertiserStrategyMaterialAddReq
}

// KsAdvertiserStrategyMaterialAddRes 添加操作返回结果
type KsAdvertiserStrategyMaterialAddRes struct {
	commonApi.EmptyRes
}

// KsAdvertiserStrategyMaterialEditReq 修改操作请求参数
type KsAdvertiserStrategyMaterialEditReq struct {
	g.Meta `path:"/edit" tags:"快手策略组-素材" method:"put" summary:"快手策略组-素材修改"`
	commonApi.Author
	*model.KsAdvertiserStrategyMaterialEditReq
}

// KsAdvertiserStrategyMaterialEditRes 修改操作返回结果
type KsAdvertiserStrategyMaterialEditRes struct {
	commonApi.EmptyRes
}

// KsAdvertiserStrategyMaterialGetReq 获取一条数据请求
type KsAdvertiserStrategyMaterialGetReq struct {
	g.Meta `path:"/get" tags:"快手策略组-素材" method:"get" summary:"获取快手策略组-素材信息"`
	commonApi.Author
	Id uint64 `p:"id" v:"required#主键必须"` //通过主键获取
}

// KsAdvertiserStrategyMaterialGetRes 获取一条数据结果
type KsAdvertiserStrategyMaterialGetRes struct {
	g.Meta `mime:"application/json"`
	*model.KsAdvertiserStrategyMaterialInfoRes
}

// KsAdvertiserStrategyMaterialDeleteReq 删除数据请求
type KsAdvertiserStrategyMaterialDeleteReq struct {
	g.Meta `path:"/delete" tags:"快手策略组-素材" method:"delete" summary:"删除快手策略组-素材"`
	commonApi.Author
	Ids []uint64 `p:"ids" v:"required#主键必须"` //通过主键删除
}

// KsAdvertiserStrategyMaterialDeleteRes 删除数据返回
type KsAdvertiserStrategyMaterialDeleteRes struct {
	commonApi.EmptyRes
}
