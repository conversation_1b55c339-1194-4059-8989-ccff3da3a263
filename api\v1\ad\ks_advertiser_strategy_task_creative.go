// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2025-08-23 17:40:21
// 生成路径: api/v1/ad/ks_advertiser_strategy_task_creative.go
// 生成人：cyao
// desc:快手广告搭建-任务-广告创意相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package ad

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
)

// KsAdvertiserStrategyTaskCreativeSearchReq 分页请求参数
type KsAdvertiserStrategyTaskCreativeSearchReq struct {
	g.Meta `path:"/list" tags:"快手广告搭建-任务-广告创意" method:"get" summary:"快手广告搭建-任务-广告创意列表"`
	commonApi.Author
	model.KsAdvertiserStrategyTaskCreativeSearchReq
}

// KsAdvertiserStrategyTaskCreativeSearchRes 列表返回结果
type KsAdvertiserStrategyTaskCreativeSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.KsAdvertiserStrategyTaskCreativeSearchRes
}

// KsAdvertiserStrategyTaskCreativeAddReq 添加操作请求参数
type KsAdvertiserStrategyTaskCreativeAddReq struct {
	g.Meta `path:"/add" tags:"快手广告搭建-任务-广告创意" method:"post" summary:"快手广告搭建-任务-广告创意添加"`
	commonApi.Author
	*model.KsAdvertiserStrategyTaskCreativeAddReq
}

// KsAdvertiserStrategyTaskCreativeAddRes 添加操作返回结果
type KsAdvertiserStrategyTaskCreativeAddRes struct {
	commonApi.EmptyRes
}

// KsAdvertiserStrategyTaskCreativeEditReq 修改操作请求参数
type KsAdvertiserStrategyTaskCreativeEditReq struct {
	g.Meta `path:"/edit" tags:"快手广告搭建-任务-广告创意" method:"put" summary:"快手广告搭建-任务-广告创意修改"`
	commonApi.Author
	*model.KsAdvertiserStrategyTaskCreativeEditReq
}

// KsAdvertiserStrategyTaskCreativeEditRes 修改操作返回结果
type KsAdvertiserStrategyTaskCreativeEditRes struct {
	commonApi.EmptyRes
}

// KsAdvertiserStrategyTaskCreativeGetReq 获取一条数据请求
type KsAdvertiserStrategyTaskCreativeGetReq struct {
	g.Meta `path:"/get" tags:"快手广告搭建-任务-广告创意" method:"get" summary:"获取快手广告搭建-任务-广告创意信息"`
	commonApi.Author
	TaskCreativeId string `p:"taskCreativeId" v:"required#主键必须"` //通过主键获取
}

// KsAdvertiserStrategyTaskCreativeGetRes 获取一条数据结果
type KsAdvertiserStrategyTaskCreativeGetRes struct {
	g.Meta `mime:"application/json"`
	*model.KsAdvertiserStrategyTaskCreativeInfoRes
}

// KsAdvertiserStrategyTaskCreativeDeleteReq 删除数据请求
type KsAdvertiserStrategyTaskCreativeDeleteReq struct {
	g.Meta `path:"/delete" tags:"快手广告搭建-任务-广告创意" method:"delete" summary:"删除快手广告搭建-任务-广告创意"`
	commonApi.Author
	TaskCreativeIds []string `p:"taskCreativeIds" v:"required#主键必须"` //通过主键删除
}

// KsAdvertiserStrategyTaskCreativeDeleteRes 删除数据返回
type KsAdvertiserStrategyTaskCreativeDeleteRes struct {
	commonApi.EmptyRes
}
