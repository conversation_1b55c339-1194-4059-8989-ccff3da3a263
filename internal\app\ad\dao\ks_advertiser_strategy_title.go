// ==========================================================================
// GFast自动生成dao操作代码。
// 生成日期：2025-08-22 11:52:42
// 生成路径: internal/app/ad/dao/ks_advertiser_strategy_title.go
// 生成人：cq
// desc:快手策略组-文案
// company:云南奇讯科技有限公司
// ==========================================================================

package dao

import (
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao/internal"
)

// ksAdvertiserStrategyTitleDao is the manager for logic model data accessing and custom defined data operations functions management.
// You can define custom methods on it to extend its functionality as you wish.
type ksAdvertiserStrategyTitleDao struct {
	*internal.KsAdvertiserStrategyTitleDao
}

var (
	// KsAdvertiserStrategyTitle is globally public accessible object for table tools_gen_table operations.
	KsAdvertiserStrategyTitle = ksAdvertiserStrategyTitleDao{
		internal.NewKsAdvertiserStrategyTitleDao(),
	}
)

// Fill with you ideas below.
