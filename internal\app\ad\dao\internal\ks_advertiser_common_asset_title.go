// ==========================================================================
// GFast自动生成dao internal操作代码。
// 生成日期：2025-08-19 10:36:58
// 生成路径: internal/app/ad/dao/internal/ks_advertiser_common_asset_title.go
// 生成人：cq
// desc:快手通用资产-标题库
// company:云南奇讯科技有限公司
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// KsAdvertiserCommonAssetTitleDao is the manager for logic model data accessing and custom defined data operations functions management.
type KsAdvertiserCommonAssetTitleDao struct {
	table   string                              // Table is the underlying table name of the DAO.
	group   string                              // Group is the database configuration group name of current DAO.
	columns KsAdvertiserCommonAssetTitleColumns // Columns is the short type for Columns, which contains all the column names of Table for convenient usage.
}

// KsAdvertiserCommonAssetTitleColumns defines and stores column names for table ks_advertiser_common_asset_title.
type KsAdvertiserCommonAssetTitleColumns struct {
	Id                 string //
	Title              string // 标题
	CategoryId         string // 标题分类ID
	UserId             string // 创建者
	Last3DayCost       string // 近3日消耗
	Last30DayCost      string // 近30日消耗
	Last3DayClickRate  string // 近3日点击率
	Last30DayClickRate string // 近30日点击率
	UnitCount          string // 关联广告组数
	CreatedAt          string // 创建时间
	UpdatedAt          string // 更新时间
	DeletedAt          string // 删除时间
}

var ksAdvertiserCommonAssetTitleColumns = KsAdvertiserCommonAssetTitleColumns{
	Id:                 "id",
	Title:              "title",
	CategoryId:         "category_id",
	UserId:             "user_id",
	Last3DayCost:       "last_3_day_cost",
	Last30DayCost:      "last_30_day_cost",
	Last3DayClickRate:  "last_3_day_click_rate",
	Last30DayClickRate: "last_30_day_click_rate",
	UnitCount:          "unit_count",
	CreatedAt:          "created_at",
	UpdatedAt:          "updated_at",
	DeletedAt:          "deleted_at",
}

// NewKsAdvertiserCommonAssetTitleDao creates and returns a new DAO object for table data access.
func NewKsAdvertiserCommonAssetTitleDao() *KsAdvertiserCommonAssetTitleDao {
	return &KsAdvertiserCommonAssetTitleDao{
		group:   "default",
		table:   "ks_advertiser_common_asset_title",
		columns: ksAdvertiserCommonAssetTitleColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *KsAdvertiserCommonAssetTitleDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *KsAdvertiserCommonAssetTitleDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *KsAdvertiserCommonAssetTitleDao) Columns() KsAdvertiserCommonAssetTitleColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *KsAdvertiserCommonAssetTitleDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *KsAdvertiserCommonAssetTitleDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *KsAdvertiserCommonAssetTitleDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
