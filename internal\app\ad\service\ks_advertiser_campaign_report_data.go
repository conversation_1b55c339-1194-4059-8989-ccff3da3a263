// ==========================================================================
// GFast自动生成service操作代码。
// 生成日期：2025-08-12 10:45:00
// 生成路径: internal/app/ad/service/ks_advertiser_campaign_report_data.go
// 生成人：cq
// desc:快手广告计划报表数据
// company:云南奇讯科技有限公司
// ==========================================================================

package service

import (
	"context"

	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
)

type IKsAdvertiserCampaignReportData interface {
	List(ctx context.Context, req *model.KsAdvertiserCampaignReportDataSearchReq) (res *model.KsAdvertiserCampaignReportDataSearchRes, err error)
	Add(ctx context.Context, req *model.KsAdvertiserCampaignReportDataAddReq) (err error)
	BatchAdd(ctx context.Context, batchReq []*model.KsAdvertiserCampaignReportDataAddReq) (err error)
	RunSyncKsCampaignReportData(ctx context.Context, req *model.KsAdvertiserCampaignReportDataSearchReq) (err error)
	SyncKsCampaignReportDataTask(ctx context.Context)
}

var localKsAdvertiserCampaignReportData IKsAdvertiserCampaignReportData

func KsAdvertiserCampaignReportData() IKsAdvertiserCampaignReportData {
	if localKsAdvertiserCampaignReportData == nil {
		panic("implement not found for interface IKsAdvertiserCampaignReportData, forgot register?")
	}
	return localKsAdvertiserCampaignReportData
}

func RegisterKsAdvertiserCampaignReportData(i IKsAdvertiserCampaignReportData) {
	localKsAdvertiserCampaignReportData = i
}
