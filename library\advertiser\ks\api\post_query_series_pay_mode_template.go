package api

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
)

// QuerySeriesPayModeTemplateService 查询短剧付费模板信息接口
type QuerySeriesPayModeTemplateService struct {
	ctx     context.Context
	cfg     *Configuration
	token   string
	Request *QuerySeriesPayModeTemplateReq
}

// QuerySeriesPayModeTemplateReq 请求结构体
type QuerySeriesPayModeTemplateReq struct {
	AdvertiserId        int64 `json:"advertiser_id" dc:"广告主ID，必填"`
	SeriesId            int64 `json:"series_id" dc:"短剧id，必填"`
	SeriesPayMode       int   `json:"series_pay_mode" dc:"付费模式类型(目前多付费模板只支持“打包“，即payMode=1)"`
	SeriesPayTemplateId int64 `json:"series_pay_template_id" dc:"付费模版id (非必填，填写则代表查询该模板id对应信息）"`
	UserId              int64 `json:"user_id" dc:"快手号id 必填"`
}

type MapiSeriesPayModeTemplateInfoSnake struct {
	SeriesPayMode       int    `json:"series_pay_mode" dc:"付费模式类型"`
	SeriesPayTemplateId int64  `json:"series_pay_template_id" dc:"付费模版id"`
	TemplateName        string `json:"template_name" dc:"付费模版名称"`
	TemplateStatus      int    `json:"template_status" dc:"付费模版状态 1 有效，2已删除"`
}

func (r *QuerySeriesPayModeTemplateService) SetCfg(cfg *Configuration) *QuerySeriesPayModeTemplateService {
	r.cfg = cfg
	return r
}

func (r *QuerySeriesPayModeTemplateService) SetReq(req QuerySeriesPayModeTemplateReq) *QuerySeriesPayModeTemplateService {
	r.Request = &req
	return r
}

func (r *QuerySeriesPayModeTemplateService) AccessToken(accessToken string) *QuerySeriesPayModeTemplateService {
	r.token = accessToken
	return r
}

func (r *QuerySeriesPayModeTemplateService) Do() (data *KsBaseResp[[]MapiSeriesPayModeTemplateInfoSnake], err error) {
	localBasePath := r.cfg.BasePath
	localVarPath := localBasePath + "/rest/openapi/gw/dsp/series/payModeTemplateInfo"
	response, err := r.cfg.HTTPClient.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("Access-Token", r.token).
		SetBody(r.Request).
		SetResult(&KsBaseResp[[]MapiSeriesPayModeTemplateInfoSnake]{}).
		Post(localVarPath)
	if err != nil {
		return nil, err
	}
	resp := new(KsBaseResp[[]MapiSeriesPayModeTemplateInfoSnake])
	// 将 JSON 响应解码到结构体中
	err = json.Unmarshal(response.Body(), &resp)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("/rest/openapi/gw/dsp/series/payModeTemplateInfo解析响应出错: %v\n", err))
	}
	if resp.Code == 0 && resp.Data != nil {
		return resp, nil
	} else {
		return resp, errors.New(resp.Message)
	}
}
