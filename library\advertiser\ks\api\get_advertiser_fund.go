package api

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
)

// QueryAdvertiserFundService 获取广告账户余额信息
type QueryAdvertiserFundService struct {
	ctx     context.Context
	cfg     *Configuration
	token   string
	Request *QueryAdvertiserFundReq
}

type QueryAdvertiserFundReq struct {
	AdvertiserID int64 `json:"advertiser_id"` // 必填, 用户快手号id
}

// QueryAdvertiserFundData 定义返回结果的结构体
type QueryAdvertiserFundData struct {
	Code    int             `json:"code"`
	Message string          `json:"message"`
	Data    *AdvertiserFund `json:"data"` // 数据
}

// AdvertiserFund 定义单个数据项的结构体
type AdvertiserFund struct {
	//direct_rebate 激励余额
	DirectRebate float64 `json:"direct_rebate"`
	//contract_rebate 框返余额
	ContractRebate float64 `json:"contract_rebate"`
	//recharge_balance 充值余额
	RechargeBalance float64 `json:"recharge_balance"`
	//balance 账户总余额
	Balance float64 `json:"balance"`
}

func (r *QueryAdvertiserFundService) SetCfg(cfg *Configuration) *QueryAdvertiserFundService {
	r.cfg = cfg
	return r
}

func (r *QueryAdvertiserFundService) SetReq(req QueryAdvertiserFundReq) *QueryAdvertiserFundService {
	r.Request = &req
	return r
}

func (r *QueryAdvertiserFundService) AccessToken(accessToken string) *QueryAdvertiserFundService {
	r.token = accessToken
	return r
}

func (r *QueryAdvertiserFundService) Do() (data *QueryAdvertiserFundData, err error) {
	localBasePath := r.cfg.BasePath
	localVarPath := localBasePath + "/rest/openapi/v1/advertiser/fund/get"
	// 序列化请求体
	bodyBytes, err := json.Marshal(r.Request)
	if err != nil {
		return nil, fmt.Errorf("序列化请求体失败: %w", err)
	}
	// 手动构建底层请求
	httpReq, err := http.NewRequestWithContext(
		context.Background(),
		"GET",
		localVarPath,
		bytes.NewBuffer(bodyBytes),
	)
	if err != nil {
		return nil, fmt.Errorf("构建请求失败: %w", err)
	}
	httpReq.Header.Add("Content-Type", "application/json")
	httpReq.Header.Add("Access-Token", r.token)
	// 执行请求
	rawResponse, err := r.cfg.HTTPClient.GetClient().Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("网络请求失败: %w", err)
	}
	defer rawResponse.Body.Close()
	// 处理响应
	if rawResponse.StatusCode < 200 || rawResponse.StatusCode >= 300 {
		return nil, fmt.Errorf("HTTP错误状态码: %d", rawResponse.StatusCode)
	}
	body, err := io.ReadAll(rawResponse.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应体失败: %w", err)
	}
	resp := new(QueryAdvertiserFundData)
	// 将 JSON 响应解码到结构体中
	err = json.Unmarshal(body, &resp)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("/rest/openapi/v1/advertiser/fund/get解析响应出错: %v\n", err))
	}
	if resp.Code == 0 && resp.Data != nil {
		return resp, nil
	} else {
		return resp, errors.New(resp.Message)
	}
}
