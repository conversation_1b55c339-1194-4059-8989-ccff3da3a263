// ==========================================================================
// GFast自动生成dao internal操作代码。
// 生成日期：2025-08-12 15:45:06
// 生成路径: internal/app/ad/dao/internal/ks_advertiser_agent_info.go
// 生成人：cyao
// desc:快手代理商信息表
// company:云南奇讯科技有限公司
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// KsAdvertiserAgentInfoDao is the manager for logic model data accessing and custom defined data operations functions management.
type KsAdvertiserAgentInfoDao struct {
	table   string                       // Table is the underlying table name of the DAO.
	group   string                       // Group is the database configuration group name of current DAO.
	columns KsAdvertiserAgentInfoColumns // Columns is the short type for Columns, which contains all the column names of Table for convenient usage.
}

// KsAdvertiserAgentInfoColumns defines and stores column names for table ks_advertiser_agent_info.
type KsAdvertiserAgentInfoColumns struct {
	Id                 string // 自增主键
	AgentAccountId     string // 代理商账户id
	AuthorizeKsAccount string // 授权快手账号
	Owner              string // 归属人员id
	CreatedAt          string // 创建时间
	UpdatedAt          string // 更新时间
	IsShare            string // 0非共享1共享
}

var ksAdvertiserAgentInfoColumns = KsAdvertiserAgentInfoColumns{
	Id:                 "id",
	AgentAccountId:     "agent_account_id",
	AuthorizeKsAccount: "authorize_ks_account",
	Owner:              "owner",
	CreatedAt:          "created_at",
	UpdatedAt:          "updated_at",
	IsShare:            "is_share",
}

// NewKsAdvertiserAgentInfoDao creates and returns a new DAO object for table data access.
func NewKsAdvertiserAgentInfoDao() *KsAdvertiserAgentInfoDao {
	return &KsAdvertiserAgentInfoDao{
		group:   "default",
		table:   "ks_advertiser_agent_info",
		columns: ksAdvertiserAgentInfoColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *KsAdvertiserAgentInfoDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *KsAdvertiserAgentInfoDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *KsAdvertiserAgentInfoDao) Columns() KsAdvertiserAgentInfoColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *KsAdvertiserAgentInfoDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *KsAdvertiserAgentInfoDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *KsAdvertiserAgentInfoDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
