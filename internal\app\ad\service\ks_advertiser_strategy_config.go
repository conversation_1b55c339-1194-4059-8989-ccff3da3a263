// ==========================================================================
// GFast自动生成service操作代码。
// 生成日期：2025-08-22 11:51:48
// 生成路径: internal/app/ad/service/ks_advertiser_strategy_config.go
// 生成人：cq
// desc:快手广告搭建-策略配置
// company:云南奇讯科技有限公司
// ==========================================================================

package service

import (
	"context"

	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
)

type IKsAdvertiserStrategyConfig interface {
	List(ctx context.Context, req *model.KsAdvertiserStrategyConfigSearchReq) (res *model.KsAdvertiserStrategyConfigSearchRes, err error)
	GetById(ctx context.Context, Id int64) (res *model.KsAdvertiserStrategyConfigInfoRes, err error)
	GetInfoById(ctx context.Context, strategyId string, taskId string) (res *model.KsAdvertiserStrategyRes, err error)
	Add(ctx context.Context, req *model.KsAdvertiserStrategyAddReq) (err error)
	Edit(ctx context.Context, req *model.KsAdvertiserStrategyEditReq) (err error)
	Delete(ctx context.Context, strategyIds []string) (err error)
	Copy(ctx context.Context, strategyId string) (err error)
}

var localKsAdvertiserStrategyConfig IKsAdvertiserStrategyConfig

func KsAdvertiserStrategyConfig() IKsAdvertiserStrategyConfig {
	if localKsAdvertiserStrategyConfig == nil {
		panic("implement not found for interface IKsAdvertiserStrategyConfig, forgot register?")
	}
	return localKsAdvertiserStrategyConfig
}

func RegisterKsAdvertiserStrategyConfig(i IKsAdvertiserStrategyConfig) {
	localKsAdvertiserStrategyConfig = i
}
