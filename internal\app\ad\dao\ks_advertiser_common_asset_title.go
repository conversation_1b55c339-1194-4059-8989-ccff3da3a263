// ==========================================================================
// GFast自动生成dao操作代码。
// 生成日期：2025-08-19 10:36:58
// 生成路径: internal/app/ad/dao/ks_advertiser_common_asset_title.go
// 生成人：cq
// desc:快手通用资产-标题库
// company:云南奇讯科技有限公司
// ==========================================================================

package dao

import (
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao/internal"
)

// ksAdvertiserCommonAssetTitleDao is the manager for logic model data accessing and custom defined data operations functions management.
// You can define custom methods on it to extend its functionality as you wish.
type ksAdvertiserCommonAssetTitleDao struct {
	*internal.KsAdvertiserCommonAssetTitleDao
}

var (
	// KsAdvertiserCommonAssetTitle is globally public accessible object for table tools_gen_table operations.
	KsAdvertiserCommonAssetTitle = ksAdvertiserCommonAssetTitleDao{
		internal.NewKsAdvertiserCommonAssetTitleDao(),
	}
)

// Fill with you ideas below.
