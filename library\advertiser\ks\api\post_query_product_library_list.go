package api

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
)

// QueryProductLibraryListService 短剧付费模式查询接口
type QueryProductLibraryListService struct {
	ctx     context.Context
	cfg     *Configuration
	token   string
	Request *LibraryListRequest
}

// LibraryListRequest 获取商品库列表 API Request
type LibraryListRequest struct {
	// PageInfo 分页信息
	PageInfo *PageInfo `json:"page_info,omitempty"`
	// Name 商品库名称
	Name string `json:"name,omitempty"`
	// BizID 商品库业务类型 1-主站, 2-联盟, 3-通用
	BizID []int `json:"biz_id,omitempty"`
	// AdvertiserID 广告主ID
	AdvertiserID uint64 `json:"advertiser_id,omitempty"`
	// LibraryID 商品库ID
	LibraryID uint64 `json:"library_id,omitempty"`
	// Status 商品库状态 1-审核中, 2-使用中, 3-审核失败, 4-暂停使用, 5-XML初始化中, 6-XML初始化失败
	Status int `json:"status,omitempty"`
	// QueryType 商品库权限类型 1-使用权限, 2-编辑权限(含使用权限)
	QueryType int `json:"query_type,omitempty"`
}

// LibraryListResponse 获取商品库列表 API Response
type LibraryListResponse struct {
	// PageInfo 列表页参数
	PageInfo *PageInfo               `json:"page_info,omitempty"`
	Data     []AdDpaLibraryViewSneak `json:"data,omitempty"`
}

// AdDpaLibraryViewSneak 商品库信息
type AdDpaLibraryViewSneak struct {
	// Name 商品库名称
	Name string `json:"name,omitempty"`
	// LibraryDesc 商品库描述
	LibraryDesc string `json:"library_desc,omitempty"`
	// LibraryID 商品库ID
	LibraryID uint64 `json:"library_id,omitempty"`
	// Status 商品库状态 1-审核中, 2-使用中, 3-审核失败, 4-暂停使用, 5-XML初始化中, 6-XML初始化失败
	Status int `json:"status,omitempty"`
	// CreateTime 商品库创建时间 毫秒时间戳
	CreateTime int64 `json:"create_time,omitempty"`
	// BizID 商品库业务类型 0-未知, 1-主站, 2-联盟, 3-通用
	BizID int `json:"biz_id,omitempty"`
}

func (r *QueryProductLibraryListService) SetCfg(cfg *Configuration) *QueryProductLibraryListService {
	r.cfg = cfg
	return r
}

func (r *QueryProductLibraryListService) SetReq(req LibraryListRequest) *QueryProductLibraryListService {
	r.Request = &req
	return r
}

func (r *QueryProductLibraryListService) AccessToken(accessToken string) *QueryProductLibraryListService {
	r.token = accessToken
	return r
}

func (r *QueryProductLibraryListService) Do() (data *KsBaseResp[LibraryListResponse], err error) {
	localBasePath := r.cfg.BasePath
	localVarPath := localBasePath + "/rest/openapi/gw/dsp/dpa/library/list"
	response, err := r.cfg.HTTPClient.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("Access-Token", r.token).
		SetBody(r.Request).
		SetResult(&KsBaseResp[LibraryListResponse]{}).
		Post(localVarPath)
	if err != nil {
		return nil, err
	}
	resp := new(KsBaseResp[LibraryListResponse])
	// 将 JSON 响应解码到结构体中
	err = json.Unmarshal(response.Body(), &resp)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("/rest/openapi/gw/dsp/dpa/library/list解析响应出错: %v\n", err))
	}
	if resp.Code == 0 && resp.Data != nil {
		return resp, nil
	} else {
		return resp, errors.New(resp.Message)
	}
}
