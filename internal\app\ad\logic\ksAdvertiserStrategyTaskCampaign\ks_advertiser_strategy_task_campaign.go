// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2025-08-23 17:40:19
// 生成路径: internal/app/ad/logic/ks_advertiser_strategy_task_campaign.go
// 生成人：cyao
// desc:快手广告搭建-任务-广告计划
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	"github.com/tiger1103/gfast/v3/library/liberr"
)

func init() {
	service.RegisterKsAdvertiserStrategyTaskCampaign(New())
}

func New() service.IKsAdvertiserStrategyTaskCampaign {
	return &sKsAdvertiserStrategyTaskCampaign{}
}

type sKsAdvertiserStrategyTaskCampaign struct{}

func (s *sKsAdvertiserStrategyTaskCampaign) List(ctx context.Context, req *model.KsAdvertiserStrategyTaskCampaignSearchReq) (listRes *model.KsAdvertiserStrategyTaskCampaignSearchRes, err error) {
	listRes = new(model.KsAdvertiserStrategyTaskCampaignSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.KsAdvertiserStrategyTaskCampaign.Ctx(ctx).WithAll()
		if req.TaskCampaignId != "" {
			m = m.Where(dao.KsAdvertiserStrategyTaskCampaign.Columns().TaskCampaignId+" = ?", req.TaskCampaignId)
		}
		if req.CampaignId != "" {
			m = m.Where(dao.KsAdvertiserStrategyTaskCampaign.Columns().CampaignId+" = ?", gconv.Int64(req.CampaignId))
		}
		if req.CampaignName != "" {
			m = m.Where(dao.KsAdvertiserStrategyTaskCampaign.Columns().CampaignName+" like ?", "%"+req.CampaignName+"%")
		}
		if req.TaskId != "" {
			m = m.Where(dao.KsAdvertiserStrategyTaskCampaign.Columns().TaskId+" = ?", req.TaskId)
		}
		if req.AdvertiserId != "" {
			m = m.Where(dao.KsAdvertiserStrategyTaskCampaign.Columns().AdvertiserId+" = ?", req.AdvertiserId)
		}
		if req.AdvertiserNick != "" {
			m = m.Where(dao.KsAdvertiserStrategyTaskCampaign.Columns().AdvertiserNick+" = ?", req.AdvertiserNick)
		}
		if req.Status != "" {
			m = m.Where(dao.KsAdvertiserStrategyTaskCampaign.Columns().Status+" = ?", req.Status)
		}
		if len(req.DateRange) != 0 {
			m = m.Where(dao.KsAdvertiserStrategyTaskCampaign.Columns().CreatedAt+" >=? AND "+dao.KsAdvertiserStrategyTaskCampaign.Columns().CreatedAt+" <=?", req.DateRange[0], req.DateRange[1])
		}
		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "task_campaign_id asc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		var res []*model.KsAdvertiserStrategyTaskCampaignListRes
		err = m.Page(req.PageNum, req.PageSize).Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		listRes.List = make([]*model.KsAdvertiserStrategyTaskCampaignListRes, len(res))
		for k, v := range res {
			listRes.List[k] = &model.KsAdvertiserStrategyTaskCampaignListRes{
				TaskCampaignId: v.TaskCampaignId,
				CampaignId:     v.CampaignId,
				CampaignName:   v.CampaignName,
				TaskId:         v.TaskId,
				AdvertiserId:   v.AdvertiserId,
				AdvertiserNick: v.AdvertiserNick,
				CampaignData:   v.CampaignData,
				Status:         v.Status,
				CreatedAt:      v.CreatedAt,
			}
		}
	})
	return
}

func (s *sKsAdvertiserStrategyTaskCampaign) GetByTaskCampaignId(ctx context.Context, taskCampaignId string) (res *model.KsAdvertiserStrategyTaskCampaignInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.KsAdvertiserStrategyTaskCampaign.Ctx(ctx).WithAll().Where(dao.KsAdvertiserStrategyTaskCampaign.Columns().TaskCampaignId, taskCampaignId).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sKsAdvertiserStrategyTaskCampaign) Add(ctx context.Context, req *model.KsAdvertiserStrategyTaskCampaignAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.KsAdvertiserStrategyTaskCampaign.Ctx(ctx).Insert(do.KsAdvertiserStrategyTaskCampaign{
			TaskCampaignId: req.TaskCampaignId,
			CampaignId:     req.CampaignId,
			CampaignName:   req.CampaignName,
			TaskId:         req.TaskId,
			AdvertiserId:   req.AdvertiserId,
			AdvertiserNick: req.AdvertiserNick,
			CampaignData:   req.CampaignData,
			Status:         req.Status,
		})
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sKsAdvertiserStrategyTaskCampaign) Edit(ctx context.Context, req *model.KsAdvertiserStrategyTaskCampaignEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.KsAdvertiserStrategyTaskCampaign.Ctx(ctx).WherePri(req.TaskCampaignId).Update(do.KsAdvertiserStrategyTaskCampaign{
			CampaignId:     req.CampaignId,
			CampaignName:   req.CampaignName,
			TaskId:         req.TaskId,
			AdvertiserId:   req.AdvertiserId,
			AdvertiserNick: req.AdvertiserNick,
			CampaignData:   req.CampaignData,
			Status:         req.Status,
		})
		liberr.ErrIsNil(ctx, err, "修改失败")
	})
	return
}

func (s *sKsAdvertiserStrategyTaskCampaign) Delete(ctx context.Context, taskCampaignIds []string) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.KsAdvertiserStrategyTaskCampaign.Ctx(ctx).Delete(dao.KsAdvertiserStrategyTaskCampaign.Columns().TaskCampaignId+" in (?)", taskCampaignIds)
		liberr.ErrIsNil(ctx, err, "删除失败")
	})
	return
}
