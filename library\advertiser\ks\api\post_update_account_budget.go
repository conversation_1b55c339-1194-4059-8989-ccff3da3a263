package api

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
)

// UpdateAccountBudgetService 修改账户预算
type UpdateAccountBudgetService struct {
	ctx     context.Context
	cfg     *Configuration
	token   string
	Request *UpdateAccountBudgetReq
}

// UpdateAccountBudgetReq 请求结构体
type UpdateAccountBudgetReq struct {
	AdvertiserId      int64   `json:"advertiser_id"`       // 账号id
	DayBudget         int64   `json:"day_budget"`          // 单日预算金额
	DayBudgetSchedule []int64 `json:"day_budget_schedule"` // 分日预算
}

// UpdateAccountBudgetResp API响应结构体
type UpdateAccountBudgetResp struct {
}

func (r *UpdateAccountBudgetService) SetCfg(cfg *Configuration) *UpdateAccountBudgetService {
	r.cfg = cfg
	return r
}

func (r *UpdateAccountBudgetService) SetReq(req UpdateAccountBudgetReq) *UpdateAccountBudgetService {
	r.Request = &req
	return r
}

func (r *UpdateAccountBudgetService) AccessToken(accessToken string) *UpdateAccountBudgetService {
	r.token = accessToken
	return r
}

func (r *UpdateAccountBudgetService) Do() (data *KsBaseResp[UpdateAccountBudgetResp], err error) {
	localBasePath := r.cfg.BasePath
	localVarPath := localBasePath + "/rest/openapi/v1/advertiser/update/budget"
	response, err := r.cfg.HTTPClient.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("Access-Token", r.token).
		SetBody(r.Request).
		SetResult(&KsBaseResp[UpdateAccountBudgetResp]{}).
		Post(localVarPath)
	if err != nil {
		return nil, err
	}
	resp := new(KsBaseResp[UpdateAccountBudgetResp])
	// 将 JSON 响应解码到结构体中
	err = json.Unmarshal(response.Body(), &resp)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("/rest/openapi/v1/advertiser/update/budget解析响应出错: %v\n", err))
	}
	if resp.Code == 0 {
		return resp, nil
	} else {
		return resp, errors.New(resp.Message)
	}
}
