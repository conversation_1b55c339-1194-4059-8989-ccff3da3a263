// ==========================================================================
// GFast自动生成service操作代码。
// 生成日期：2025-08-23 17:40:22
// 生成路径: internal/app/ad/service/ks_advertiser_strategy_task_creative.go
// 生成人：cyao
// desc:快手广告搭建-任务-广告创意
// company:云南奇讯科技有限公司
// ==========================================================================

package service

import (
	"context"

	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
)

type IKsAdvertiserStrategyTaskCreative interface {
	List(ctx context.Context, req *model.KsAdvertiserStrategyTaskCreativeSearchReq) (res *model.KsAdvertiserStrategyTaskCreativeSearchRes, err error)
	GetByTaskCreativeId(ctx context.Context, TaskCreativeId string) (res *model.KsAdvertiserStrategyTaskCreativeInfoRes, err error)
	Add(ctx context.Context, req *model.KsAdvertiserStrategyTaskCreativeAddReq) (err error)
	Edit(ctx context.Context, req *model.KsAdvertiserStrategyTaskCreativeEditReq) (err error)
	Delete(ctx context.Context, TaskCreativeId []string) (err error)
}

var localKsAdvertiserStrategyTaskCreative IKsAdvertiserStrategyTaskCreative

func KsAdvertiserStrategyTaskCreative() IKsAdvertiserStrategyTaskCreative {
	if localKsAdvertiserStrategyTaskCreative == nil {
		panic("implement not found for interface IKsAdvertiserStrategyTaskCreative, forgot register?")
	}
	return localKsAdvertiserStrategyTaskCreative
}

func RegisterKsAdvertiserStrategyTaskCreative(i IKsAdvertiserStrategyTaskCreative) {
	localKsAdvertiserStrategyTaskCreative = i
}
