// ==========================================================================
// GFast自动生成router操作代码。
// 生成日期：2025-08-13 16:33:57
// 生成路径: internal/app/ad/router/ks_advertiser_unit.go
// 生成人：cyao
// desc:快手广告组
// company:云南奇讯科技有限公司
// ==========================================================================

package router

import (
	"context"

	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/tiger1103/gfast/v3/internal/app/ad/controller"
)

func (router *Router) BindKsAdvertiserUnitController(ctx context.Context, group *ghttp.RouterGroup) {
	group.Group("/ksAdvertiserUnit", func(group *ghttp.RouterGroup) {
		group.Bind(
			controller.KsAdvertiserUnit,
		)
	})
}
