// ==========================================================================
// GFast自动生成service操作代码。
// 生成日期：2025-08-23 17:40:19
// 生成路径: internal/app/ad/service/ks_advertiser_strategy_task_campaign.go
// 生成人：cyao
// desc:快手广告搭建-任务-广告计划
// company:云南奇讯科技有限公司
// ==========================================================================

package service

import (
	"context"

	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
)

type IKsAdvertiserStrategyTaskCampaign interface {
	List(ctx context.Context, req *model.KsAdvertiserStrategyTaskCampaignSearchReq) (res *model.KsAdvertiserStrategyTaskCampaignSearchRes, err error)
	GetByTaskCampaignId(ctx context.Context, TaskCampaignId string) (res *model.KsAdvertiserStrategyTaskCampaignInfoRes, err error)
	Add(ctx context.Context, req *model.KsAdvertiserStrategyTaskCampaignAddReq) (err error)
	Edit(ctx context.Context, req *model.KsAdvertiserStrategyTaskCampaignEditReq) (err error)
	Delete(ctx context.Context, TaskCampaignId []string) (err error)
}

var localKsAdvertiserStrategyTaskCampaign IKsAdvertiserStrategyTaskCampaign

func KsAdvertiserStrategyTaskCampaign() IKsAdvertiserStrategyTaskCampaign {
	if localKsAdvertiserStrategyTaskCampaign == nil {
		panic("implement not found for interface IKsAdvertiserStrategyTaskCampaign, forgot register?")
	}
	return localKsAdvertiserStrategyTaskCampaign
}

func RegisterKsAdvertiserStrategyTaskCampaign(i IKsAdvertiserStrategyTaskCampaign) {
	localKsAdvertiserStrategyTaskCampaign = i
}
