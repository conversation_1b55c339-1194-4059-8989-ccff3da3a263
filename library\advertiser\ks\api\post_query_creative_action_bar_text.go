package api

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
)

// QueryCreativeActionBarTextService 获取行动号召按钮接口
type QueryCreativeActionBarTextService struct {
	ctx     context.Context
	cfg     *Configuration
	token   string
	Request *ActionBarTextListRequest
}

// ActionBarTextListRequest 获取行动号召按钮
type ActionBarTextListRequest struct {
	// AdvertiserID 广告主id
	AdvertiserID uint64 `json:"advertiser_id,omitempty"`
	// CampaignType 计划类型; 必填 2 - 提升应用安装;3 - 获取电商下单;4 - 推广品牌活动;5 - 收集销售线索;13 - 小店商品推广；14：直播推广
	CampaignType int `json:"campaign_type,omitempty"`
	// ConsultType 是否使用了咨询组件；0=未使用，1=使用；注，咨询组件仅在收集销售线索计划(campaign_type=5)下可用，且使用了咨询组件后，可用的行动号召按钮限于接口返回内容
	ConsultType int `json:"consult_type,omitempty"`
}

// ActionBarTextListResponse 获取行动号召按钮 API Response
type ActionBarTextListResponse struct {
	// ActionBarText 行动号召按钮文案
	ActionBarText []string `json:"action_bar_text,omitempty"`
}

func (r *QueryCreativeActionBarTextService) SetCfg(cfg *Configuration) *QueryCreativeActionBarTextService {
	r.cfg = cfg
	return r
}

func (r *QueryCreativeActionBarTextService) SetReq(req ActionBarTextListRequest) *QueryCreativeActionBarTextService {
	r.Request = &req
	return r
}

func (r *QueryCreativeActionBarTextService) AccessToken(accessToken string) *QueryCreativeActionBarTextService {
	r.token = accessToken
	return r
}

func (r *QueryCreativeActionBarTextService) Do() (data *KsBaseResp[ActionBarTextListResponse], err error) {
	localBasePath := r.cfg.BasePath
	localVarPath := localBasePath + "/rest/openapi/v1/creative/action_bar_text/list"
	response, err := r.cfg.HTTPClient.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("Access-Token", r.token).
		SetBody(r.Request).
		SetResult(&KsBaseResp[ActionBarTextListResponse]{}).
		Post(localVarPath)
	if err != nil {
		return nil, err
	}
	resp := new(KsBaseResp[ActionBarTextListResponse])
	// 将 JSON 响应解码到结构体中
	err = json.Unmarshal(response.Body(), &resp)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("/rest/openapi/v1/creative/action_bar_text/list解析响应出错: %v\n", err))
	}
	if resp.Code == 0 && resp.Data != nil {
		return resp, nil
	} else {
		return resp, errors.New(resp.Message)
	}
}
