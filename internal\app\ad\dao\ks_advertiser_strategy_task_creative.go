// ==========================================================================
// GFast自动生成dao操作代码。
// 生成日期：2025-08-23 17:40:21
// 生成路径: internal/app/ad/dao/ks_advertiser_strategy_task_creative.go
// 生成人：cyao
// desc:快手广告搭建-任务-广告创意
// company:云南奇讯科技有限公司
// ==========================================================================

package dao

import (
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao/internal"
)

// ksAdvertiserStrategyTaskCreativeDao is the manager for logic model data accessing and custom defined data operations functions management.
// You can define custom methods on it to extend its functionality as you wish.
type ksAdvertiserStrategyTaskCreativeDao struct {
	*internal.KsAdvertiserStrategyTaskCreativeDao
}

var (
	// KsAdvertiserStrategyTaskCreative is globally public accessible object for table tools_gen_table operations.
	KsAdvertiserStrategyTaskCreative = ksAdvertiserStrategyTaskCreativeDao{
		internal.NewKsAdvertiserStrategyTaskCreativeDao(),
	}
)

// Fill with you ideas below.
