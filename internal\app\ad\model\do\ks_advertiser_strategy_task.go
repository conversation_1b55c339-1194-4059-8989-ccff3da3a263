// ==========================================================================
// GFast自动生成model entity操作代码。
// 生成日期：2025-08-23 17:40:11
// 生成路径: internal/app/ad/model/entity/ks_advertiser_strategy_task.go
// 生成人：cyao
// desc:快手广告搭建-任务
// company:云南奇讯科技有限公司
// ==========================================================================

package do

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
)

// KsAdvertiserStrategyTask is the golang structure for table ks_advertiser_strategy_task.
type KsAdvertiserStrategyTask struct {
	gmeta.Meta    `orm:"table:ks_advertiser_strategy_task, do:true"`
	TaskId        interface{} `orm:"task_id,primary" json:"taskId"`       // 任务ID
	TaskName      interface{} `orm:"task_name" json:"taskName"`           // 任务名称
	TaskStatus    interface{} `orm:"task_status" json:"taskStatus"`       // 任务状态 等待提交：INIT 素材上传中：MATERIAL_PROCESSING 项目等待提交：PROJECT_INIT 项目提交中：PROJECT_PROCESSING 广告等待提交：PROMOTION_INIT 广告提交中：PROMOTION_PROCESSING 已终止：TERMINATED 提交完成：FINISH
	AdvertiserIds interface{} `orm:"advertiser_ids" json:"advertiserIds"` // 广告主ID列表
	CampaignNum   interface{} `orm:"campaign_num" json:"campaignNum"`     // 广告计划数
	UnitNum       interface{} `orm:"unit_num" json:"unitNum"`             // 广告组数
	RuleType      interface{} `orm:"rule_type" json:"ruleType"`           // 提交规则类型 1：立即提交 2：定时提交
	UserId        interface{} `orm:"user_id" json:"userId"`               // 归属人员
	CreatedAt     *gtime.Time `orm:"created_at" json:"createdAt"`         // 创建时间
	UpdatedAt     *gtime.Time `orm:"updated_at" json:"updatedAt"`         // 更新时间
	DeletedAt     *gtime.Time `orm:"deleted_at" json:"deletedAt"`         // 删除时间
}
