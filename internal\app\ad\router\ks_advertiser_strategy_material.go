// ==========================================================================
// GFast自动生成router操作代码。
// 生成日期：2025-08-22 11:52:28
// 生成路径: internal/app/ad/router/ks_advertiser_strategy_material.go
// 生成人：cq
// desc:快手策略组-素材
// company:云南奇讯科技有限公司
// ==========================================================================

package router

import (
	"context"

	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/tiger1103/gfast/v3/internal/app/ad/controller"
)

func (router *Router) BindKsAdvertiserStrategyMaterialController(ctx context.Context, group *ghttp.RouterGroup) {
	group.Group("/ksAdvertiserStrategyMaterial", func(group *ghttp.RouterGroup) {
		group.Bind(
			controller.KsAdvertiserStrategyMaterial,
		)
	})
}
