package api

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"time"
)

// GetDspUnitListService 查询广告组 DspUnitList
type GetDspUnitListService struct {
	ctx     context.Context
	cfg     *Configuration
	token   string
	Request *GetDspUnitListReq
}

// GetDspUnitListReq 请求结构体
type GetDspUnitListReq struct {
	AdvertiserID        int64    `json:"advertiser_id"`                       // 广告主ID（在获取access_token时返回）
	AppName             string   `json:"app_name,omitempty"`                  // 过滤条件：推广应用名称
	CampaignID          int64    `json:"campaign_id,omitempty"`               // 广告计划ID，传则筛选对应计划下的单元
	CampaignType        int64    `json:"campaign_type,omitempty"`             // 过滤条件：推广计划类型
	DeepConversionTypes []string `json:"deep_conversion_type_list,omitempty"` // 过滤条件：深度转化类型
	EndDate             string   `json:"end_date,omitempty"`                  // 结束时间（yyyy-MM-dd），与 start_date 同时传或同时不传
	OcpxActionTypes     []string `json:"ocpx_action_type_list,omitempty"`     // 过滤条件：OCPX行为类型
	Page                int      `json:"page,omitempty"`                      // 页码（默认 1）
	PageSize            int      `json:"page_size,omitempty"`                 // 每页条数（默认 20）
	PutStatusList       []string `json:"put_status_list,omitempty"`           // 单元投放状态筛选：1 启动，2 暂停，3 删除
	ReviewStatusList    []string `json:"review_status_list,omitempty"`        // 审核状态筛选：1 审核中，2 审核通过，3 审核拒绝，7 修改审核
	StartDate           string   `json:"start_date,omitempty"`                // 开始时间（yyyy-MM-dd），与 end_date 同时传或同时不传
	Status              int      `json:"status,omitempty"`                    // 广告组状态：2 不限，10 广告组已暂停，40 广告组已删除
	TimeFilterType      int      `json:"time_filter_type,omitempty"`          // 时间维度类型，1表示按创建时间，2按更新时间
	UnitID              int64    `json:"unit_id,omitempty"`                   // 广告单元ID
	UnitIDs             []string `json:"unit_ids,omitempty"`                  // 广告单元ID列表（最多100个）
	UnitName            string   `json:"unit_name,omitempty"`                 // 广告单元名称（模糊搜索）
}

type GetDspUnitListResp struct {
	Code    int                 `json:"code"`
	Message string              `json:"message"`
	Data    *GetDspUnitListData `json:"data"`
}

type GetDspUnitListData struct {
	//details
	Details    []*UnitDetail `json:"details"`
	TotalCount int           `json:"total_count"`
}

type UnitDetail struct {
	LinkIntegrationType         int        `json:"link_integration_type"`         // 链接整合类型
	AssetMining                 bool       `json:"asset_mining"`                  // 资产挖掘
	SiteType                    int        `json:"site_type"`                     // 预约广告 1:IOS 预约 缺省为不传或传 0
	AdType                      int        `json:"ad_type"`                       // 广告计划类型  0:信息流，1:搜索
	DpaDynamicParamsForURI      string     `json:"dpa_dynamic_params_for_uri"`    // 落地页链接动态参数
	SchemaURI                   string     `json:"schema_uri"`                    // 调起链接、 提升应用活跃营销目标的调起链接
	ProductImage                string     `json:"product_image"`                 // 商品主图
	PackageID                   int        `json:"package_id"`                    // 新版应用中心应用ID
	BidType                     int        `json:"bid_type"`                      // 出价类型 1：CPM，2：CPC，6：OCPC(使用 OCPC 代表 OCPX)，10：OCPM，20：eCPC
	ProductPrice                string     `json:"product_price"`                 // 商品价格 元
	PutStatus                   int        `json:"put_status"`                    // 投放状态 1：投放中；2：暂停 3：删除
	SmartCover                  bool       `json:"smart_cover"`                   // 智能封面 是否开启智能抽帧
	DpaOuterIDs                 []string   `json:"dpa_outer_ids"`                 // DPA外部商品id集合
	SeriesPayTemplateIDMulti    []int64    `json:"series_pay_template_id_multi"`  // 短剧付费模版列表
	DpaUnitSubType              int        `json:"dpa_unit_sub_type"`             //  商品广告类型：1-DPA，2-SDPA，3-动态商品卡
	ULink                       string     `json:"u_link"`                        // ios系统的ulink链接
	AppStore                    []string   `json:"app_store"`                     // 应用商店列表
	AppDownloadType             int        `json:"app_download_type"`             // 应用下载类型 0：直接下载1：落地页下载（仅在提升应用安装计划下生效）
	DspVersion                  int        `json:"dsp_version"`                   // 请补充描述详情
	PlayableID                  int64      `json:"playable_id"`                   // 可试玩 ID 可选字段，开启试玩时存在，否则不存在。当用户上传试玩素材成功时返回，用于之后对于广告组中试玩创意的编辑操作。
	EpisodeID                   int64      `json:"episode_id"`                    // 剧集 ID
	PlayableSwitch              bool       `json:"playable_switch"`               // 可试玩开关
	ProductID                   string     `json:"product_id"`                    // 产品 ID
	SchemaID                    string     `json:"schema_id"`                     // 微信小程序外部调起链接 目前只有收集营销线索计划下的联盟广告位该字段才有效
	UnitSource                  int        `json:"unit_source"`                   // 广告组来源 0:常规（非托管）、1:托管
	SeriesPayTemplateID         int64      `json:"series_pay_template_id"`        // 付费模板  仅在计划 campaignType=30快手号-短剧推广时支持
	AppID                       int64      `json:"app_id"`                        // 应用 ID
	CampaignID                  int64      `json:"campaign_id"`                   // 广告计划 ID
	DayBudget                   int64      `json:"day_budget"`                    // 日预算 单位：厘
	OcpxActionType              int        `json:"ocpx_action_type"`              // 优化目标	0：未知，2：点击转化链接，10：曝光，11：点击，31：下载完成，53：提交线索，109：电话卡激活，137：量房，180：激活，190: 付费，191：首日 ROI，348：有效线索，383: 授信，384: 完件 715：微信复制;739:7 日付费次数;774:7 日ROI;810:激活付费;763:添加企业微信,978:首日变现ROI（iaa专用），985：混变ROI(iaap专用)，1011:7日变现ROI（iaa专用）,1012:7日混变ROI（iaap专用）
	UseAppMarket                int        `json:"use_app_market"`                // 是否使用应用市场 0：未设置 1：优先从系统应用商店下载使用
	TargetExplore               int        `json:"target_explore"`                // 是否开启搜索人群探索
	ComponentID                 int64      `json:"component_id"`                  // 组件 ID
	DpaDynamicParams            int        `json:"dpa_dynamic_params"`            // DPA 动态参数 关闭动态参数-0，开启自动参数-1
	CreateTime                  string     `json:"create_time"`                   // 创建时间
	PlayButton                  string     `json:"play_button"`                   // 试玩按钮文字内容 开启试玩时存在，否则不存在，示例按钮内容如下：1：立即试玩；2：试玩一下；3：立即体验；4：免装试玩；5：免装体验。启用试玩时：默认“立即试玩”，未启用试玩时：内容为空。
	URLType                     int        `json:"url_type"`                      // URL 类型 当计划类型为 3 时（获取电商下单）时有返回。1：淘宝商品短链 2：淘宝商品 itemID
	DpaCategories               []string   `json:"dpa_categories"`                // DPA 类别
	ProductName                 string     `json:"product_name"`                  // 产品名称
	SeriesPayMode               int        `json:"series_pay_mode"`               // 付费模式 仅在计划 campaignType=30快手号-短剧推广时，与付费模板一起返回，1-打包，2-虚拟币
	ShowMode                    int        `json:"show_mode"`                     // 展示模式 0：未知，1：轮播，2：优选
	UnitName                    string     `json:"unit_name"`                     // 广告组名称
	ExtendSearch                bool       `json:"extend_search"`                 // 智能扩词开启状态
	QuickSearch                 int        `json:"quick_search"`                  // 是否开启快投
	SiteID                      int64      `json:"site_id"`                       // 建站ID  当 web_uri_type = 2 时表示建站 ID，必须为数字，通过「/rest/openapi/v2/lp/page/list」 获取。计划类型是2（提升应用安装）且关联应用为安卓时，表示安卓下载中间页ID，通过「/rest/openapi/v2/lp/page/list」 获取 "view_comps = 7" 的建站ID。
	SeriesCardInfo              SeriesCard `json:"series_card_info"`              // 剧集卡片信息
	Status                      int        `json:"status"`                        // 广告组状态 过滤筛选条件；-2：不限(已删除)；10：广告组已删除；40：广告创意已删除.所有不包含已删除 其他值无效
	ConsultID                   int64      `json:"consult_id"`                    // 是否使用了咨询组件 0=未使用，1=使用；注，咨询组件仅在收集销售线索计划(campaign_type=5)下可用，且使用了咨询组件后，可用的行动号召按钮限于接口返回内容
	RoiRatio                    float64    `json:"roi_ratio"`                     // 付费 ROI 系数 优化目标为「首日 ROI」时必填：ROI 系数取值范围 ( 0,100 ] 最多支持到三位小数（如：0.066）
	LiveComponentType           int        `json:"live_component_type"`           // 直播组件类型 0：小铃铛（默认）；1：房产 2：团购组件 3：服务号小钥匙 4: 无组件 5：小程序 6：小手柄 14 : 快聘 ；仅营销目标为粉丝直播推广时可用
	SearchPopulationRetargeting int        `json:"search_population_retargeting"` // 是否开启人群追投
	EnhanceConversionType       int        `json:"enhance_conversion_type"`       // 增强目标
	UnitType                    int        `json:"unit_type"`                     // 创意制作方式
	OuterID                     string     `json:"outer_id"`                      // 外部 ID
	StudyStatus                 int        `json:"study_status"`                  // 学习状态
	SeriesCardType              int        `json:"series_card_type"`              // 剧集卡片类型
	CustomMiniAppData           string     `json:"custom_mini_app_data"`          // 自定义小程序数据
	UpdateTime                  string     `json:"update_time"`                   // 更新时间
	ImMessageMount              bool       `json:"im_message_mount"`              // IM 消息挂载
	LibraryID                   int64      `json:"library_id"`                    // 素材库 ID
	DeepConversionType          int        `json:"deep_conversion_type"`          // 深度转化类型
	DeepConversionBid           int        `json:"deep_conversion_bid"`           // 深度转化出价
	KwaiBookID                  int64      `json:"kwai_book_id"`                  // 快手书籍 ID
	DpaDynamicParamsForDP       string     `json:"dpa_dynamic_params_for_dp"`     // DP 动态参数
	UnitID                      int64      `json:"unit_id"`                       // 单元 ID
	PageAuditStatus             string     `json:"page_audit_status"`             // 页面审核状态
	JingleBellID                int64      `json:"jingle_bell_id"`                // 铃铛 ID
	LiveUserID                  int64      `json:"live_user_id"`                  // 直播用户 ID
	PlayableURL                 string     `json:"playable_url"`                  // 可试玩 URL
	PlayableFileName            string     `json:"playable_file_name"`            // 可试玩文件名
	WebURIType                  int        `json:"web_uri_type"`                  // Web URI 类型
	ReviewDetail                string     `json:"review_detail"`                 // 审核详情
	EndTime                     string     `json:"end_time"`                      // 结束时间
	CompensateStatus            int        `json:"compensate_status"`             // 补偿状态
	SceneID                     []int      `json:"scene_id"`                      // 场景 ID
	BeginTime                   string     `json:"begin_time"`                    // 开始时间
	UnitMaterialType            int        `json:"unit_material_type"`            // 单元素材类型
	ConvertID                   int64      `json:"convert_id"`                    // 转化 ID
	URL                         string     `json:"url"`                           // 链接
	Target                      TargetInfo `json:"target"`                        // 定向信息
	SeriesID                    int64      `json:"series_id"`                     // 系列 ID
	ScheduleTime                string     `json:"schedule_time"`                 // 日程时间
	AdvCardList                 string     `json:"adv_card_list"`                 // 广告卡片列表
	OuterLoopNative             int        `json:"outer_loop_native"`             // 外循环原生
	CpaBid                      int        `json:"cpa_bid"`                       // CPA 出价
	PlayableOrientation         int        `json:"playable_orientation"`          // 可试玩方向
	TemplateID                  int        `json:"template_id"`                   // 模板 ID
	Bid                         int        `json:"bid"`                           // 出价
	AdvCardOption               int        `json:"adv_card_option"`               // 广告卡片选项
}

// 剧集卡片信息
type SeriesCard struct {
	PicID       *string `json:"pic_id"`
	Description *string `json:"description"`
	CoverImage  *string `json:"cover_image"`
	Label       *string `json:"label"`
	Title       *string `json:"title"`
}

// 定向信息
type TargetInfo struct {
	Gender                    int      `json:"gender"`
	FilterConvertedWechatID   []string `json:"filter_converted_wechat_id"`
	Media                     []string `json:"media"`
	Network                   int      `json:"network"`
	DevicePrice               []string `json:"device_price"`
	MediaSourceType           int      `json:"media_source_type"`
	TargetSource              *string  `json:"target_source"`
	UserType                  int      `json:"user_type"`
	Operators                 []string `json:"operators"`
	DistanceShow              []string `json:"distance_show"`
	ExcludePopulation         []string `json:"exclude_population"`
	DeviceBrandIDs            []string `json:"device_brand_ids"`
	AgesRange                 *string  `json:"ages_range"`
	DistrictIDs               *string  `json:"district_ids"`
	AndroidOSV                *string  `json:"android_osv"`
	BehaviorType              int      `json:"behavior_type"`
	DisableInstalledAppSwitch int      `json:"disable_installed_app_switch"`
	AppIDs                    *string  `json:"app_ids"`
	AppNames                  *string  `json:"app_names"`
	IPType                    int      `json:"ip_type"`
	FilterConvertedLevel      int      `json:"filter_converted_level"`
	Population                []string `json:"population"`
	PlatformOS                int      `json:"platform_os"`
	FilterTimeRange           int      `json:"filter_time_range"`
	BusinessInterest          *string  `json:"business_interest"`
	SeedPopulation            *string  `json:"seed_population"`
	IosOSV                    *string  `json:"ios_osv"`
	AutoPopulation            int      `json:"auto_population"`
	AppInterestIDs            []string `json:"app_interest_ids"`
	PaidAudience              []string `json:"paid_audience"`
	IntelliExtendOption       int      `json:"intelli_extend_option"`
	Region                    *string  `json:"region"`
	ExcludeMedia              []string `json:"exclude_media"`
}

func (r *GetDspUnitListService) SetCfg(cfg *Configuration) *GetDspUnitListService {
	r.cfg = cfg
	return r
}

func (r *GetDspUnitListService) SetReq(req GetDspUnitListReq) *GetDspUnitListService {
	r.Request = &req
	return r
}

func (r *GetDspUnitListService) AccessToken(accessToken string) *GetDspUnitListService {
	r.token = accessToken
	return r
}

func (r *GetDspUnitListService) Do() (data *GetDspUnitListResp, err error) {
	localBasePath := r.cfg.BasePath
	localVarPath := localBasePath + "/rest/openapi/gw/dsp/unit/list"
	response, err := r.cfg.HTTPClient.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("Access-Token", r.token).
		SetBody(r.Request).
		SetResult(&GetDspUnitListResp{}).
		Post(localVarPath)
	if err != nil {
		return nil, err
	}
	resp := new(GetDspUnitListResp)
	// 将 JSON 响应解码到结构体中
	err = json.Unmarshal(response.Body(), &resp)
	if err != nil {
		if strings.Contains(err.Error(), "文件操作过于频繁") {
			time.Sleep(200 * time.Millisecond)
			return r.Do()
		}
		return nil, errors.New(fmt.Sprintf("/rest/openapi/gw/dsp/unit/list解析响应出错: %v\n", err))
	}
	if resp.Code == 0 && resp.Data != nil {
		return resp, nil
	} else {
		return resp, errors.New(resp.Message)
	}
}
