// ==========================================================================
// GFast自动生成model操作代码。
// 生成日期：2025-08-23 17:40:21
// 生成路径: internal/app/ad/model/ks_advertiser_strategy_task_creative.go
// 生成人：cyao
// desc:快手广告搭建-任务-广告创意
// company:云南奇讯科技有限公司
// ==========================================================================

package model

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
	comModel "github.com/tiger1103/gfast/v3/internal/app/common/model"
)

// KsAdvertiserStrategyTaskCreativeInfoRes is the golang structure for table ks_advertiser_strategy_task_creative.
type KsAdvertiserStrategyTaskCreativeInfoRes struct {
	gmeta.Meta     `orm:"table:ks_advertiser_strategy_task_creative"`
	TaskCreativeId string      `orm:"task_creative_id,primary" json:"taskCreativeId" dc:"任务创意ID"`                  // 任务创意ID
	CreativeId     int64       `orm:"creative_id" json:"creativeId" dc:"创意 id"`                                    // 创意 id
	CreativeName   string      `orm:"creative_name" json:"creativeName" dc:"创意名称"`                                 // 创意名称
	TaskId         string      `orm:"task_id" json:"taskId" dc:"任务ID"`                                             // 任务ID
	TaskCampaignId string      `orm:"task_campaign_id" json:"taskCampaignId" dc:"任务计划ID"`                          // 任务计划ID
	TaskUnitId     string      `orm:"task_unit_id" json:"taskUnitId" dc:"任务广告组ID"`                                 // 任务广告组ID
	AdvertiserId   string      `orm:"advertiser_id" json:"advertiserId" dc:"广告主ID"`                                // 广告主ID
	AdvertiserNick string      `orm:"advertiser_nick" json:"advertiserNick" dc:"广告主名称"`                            // 广告主名称
	ErrMsg         string      `orm:"err_msg" json:"errMsg" dc:"失败原因"`                                             // 失败原因
	Status         string      `orm:"status" json:"status" dc:"项目状态 等待提交：INIT 已终止：TERMINATED 成功：SUCCESS 失败：ERROR"` // 项目状态 等待提交：INIT 已终止：TERMINATED 成功：SUCCESS 失败：ERROR
	CreativeData   string      `orm:"creative_data" json:"creativeData" dc:"创建创意数据"`                               // 创建创意数据
	CreatedAt      *gtime.Time `orm:"created_at" json:"createdAt" dc:"创建时间"`                                       // 创建时间
	UpdatedAt      *gtime.Time `orm:"updated_at" json:"updatedAt" dc:"更新时间"`                                       // 更新时间
	DeletedAt      *gtime.Time `orm:"deleted_at" json:"deletedAt" dc:"删除时间"`                                       // 删除时间
}

type KsAdvertiserStrategyTaskCreativeListRes struct {
	TaskCreativeId string      `json:"taskCreativeId" dc:"任务创意ID"`
	CreativeId     int64       `json:"creativeId" dc:"创意 id"`
	CreativeName   string      `json:"creativeName" dc:"创意名称"`
	TaskId         string      `json:"taskId" dc:"任务ID"`
	TaskCampaignId string      `json:"taskCampaignId" dc:"任务计划ID"`
	TaskUnitId     string      `json:"taskUnitId" dc:"任务广告组ID"`
	AdvertiserId   string      `json:"advertiserId" dc:"广告主ID"`
	AdvertiserNick string      `json:"advertiserNick" dc:"广告主名称"`
	ErrMsg         string      `json:"errMsg" dc:"失败原因"`
	Status         string      `json:"status" dc:"项目状态 等待提交：INIT 已终止：TERMINATED 成功：SUCCESS 失败：ERROR"`
	CreativeData   string      `json:"creativeData" dc:"创建创意数据"`
	CreatedAt      *gtime.Time `json:"createdAt" dc:"创建时间"`
}

// KsAdvertiserStrategyTaskCreativeSearchReq 分页请求参数
type KsAdvertiserStrategyTaskCreativeSearchReq struct {
	comModel.PageReq
	TaskCreativeId string `p:"taskCreativeId" dc:"任务创意ID"`                                             //任务创意ID
	CreativeId     string `p:"creativeId" v:"creativeId@integer#创意 id需为整数" dc:"创意 id"`                 //创意 id
	CreativeName   string `p:"creativeName" dc:"创意名称"`                                                 //创意名称
	TaskId         string `p:"taskId" dc:"任务ID"`                                                       //任务ID
	TaskCampaignId string `p:"taskCampaignId" dc:"任务计划ID"`                                             //任务计划ID
	TaskUnitId     string `p:"taskUnitId" dc:"任务广告组ID"`                                                //任务广告组ID
	AdvertiserId   string `p:"advertiserId" dc:"广告主ID"`                                                //广告主ID
	AdvertiserNick string `p:"advertiserNick" dc:"广告主名称"`                                              //广告主名称
	ErrMsg         string `p:"errMsg" dc:"失败原因"`                                                       //失败原因
	Status         string `p:"status" dc:"项目状态 等待提交：INIT 已终止：TERMINATED 成功：SUCCESS 失败：ERROR"`          //项目状态 等待提交：INIT 已终止：TERMINATED 成功：SUCCESS 失败：ERROR
	CreativeData   string `p:"creativeData" dc:"创建创意数据"`                                               //创建创意数据
	CreatedAt      string `p:"createdAt" v:"createdAt@datetime#创建时间需为YYYY-MM-DD hh:mm:ss格式" dc:"创建时间"` //创建时间
}

// KsAdvertiserStrategyTaskCreativeSearchRes 列表返回结果
type KsAdvertiserStrategyTaskCreativeSearchRes struct {
	comModel.ListRes
	List []*KsAdvertiserStrategyTaskCreativeListRes `json:"list"`
}

// KsAdvertiserStrategyTaskCreativeAddReq 添加操作请求参数
type KsAdvertiserStrategyTaskCreativeAddReq struct {
	TaskCreativeId string `p:"taskCreativeId" v:"required#主键ID不能为空" dc:"任务创意ID"`
	CreativeId     int64  `p:"creativeId"  dc:"创意 id"`
	CreativeName   string `p:"creativeName" v:"required#创意名称不能为空" dc:"创意名称"`
	TaskId         string `p:"taskId"  dc:"任务ID"`
	TaskCampaignId string `p:"taskCampaignId"  dc:"任务计划ID"`
	TaskUnitId     string `p:"taskUnitId" v:"required#任务广告组ID不能为空" dc:"任务广告组ID"`
	AdvertiserId   string `p:"advertiserId"  dc:"广告主ID"`
	AdvertiserNick string `p:"advertiserNick"  dc:"广告主名称"`
	ErrMsg         string `p:"errMsg"  dc:"失败原因"`
	Status         string `p:"status" v:"required#项目状态 等待提交：INIT 已终止：TERMINATED 成功：SUCCESS 失败：ERROR不能为空" dc:"项目状态 等待提交：INIT 已终止：TERMINATED 成功：SUCCESS 失败：ERROR"`
	CreativeData   string `p:"creativeData"  dc:"创建创意数据"`
}

// KsAdvertiserStrategyTaskCreativeEditReq 修改操作请求参数
type KsAdvertiserStrategyTaskCreativeEditReq struct {
	TaskCreativeId string `p:"taskCreativeId" v:"required#主键ID不能为空" dc:"任务创意ID"`
	CreativeId     int64  `p:"creativeId"  dc:"创意 id"`
	CreativeName   string `p:"creativeName" v:"required#创意名称不能为空" dc:"创意名称"`
	TaskId         string `p:"taskId"  dc:"任务ID"`
	TaskCampaignId string `p:"taskCampaignId"  dc:"任务计划ID"`
	TaskUnitId     string `p:"taskUnitId" v:"required#任务广告组ID不能为空" dc:"任务广告组ID"`
	AdvertiserId   string `p:"advertiserId"  dc:"广告主ID"`
	AdvertiserNick string `p:"advertiserNick"  dc:"广告主名称"`
	ErrMsg         string `p:"errMsg"  dc:"失败原因"`
	Status         string `p:"status" v:"required#项目状态 等待提交：INIT 已终止：TERMINATED 成功：SUCCESS 失败：ERROR不能为空" dc:"项目状态 等待提交：INIT 已终止：TERMINATED 成功：SUCCESS 失败：ERROR"`
	CreativeData   string `p:"creativeData"  dc:"创建创意数据"`
}
