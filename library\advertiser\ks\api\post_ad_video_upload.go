package api

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"strings"
	"sync"
	"time"
)

// PostAdVideoUploadService ad-分片上传 合集 包括分片上传 合并 领用token
type PostAdVideoUploadService struct {
	ctx     context.Context
	cfg     *Configuration
	token   string
	Request *PostAdVideoUploadReq
}

type PostAdVideoUploadResp struct {
	Code    int    `json:"code"`    // 状态码
	Message string `json:"message"` // 响应消息
	Data    *struct {
		BlobStoreKey string `json:"blob_store_key"`
	} `json:"data"`
}

type PostAdVideoUploadReq struct {
	AdvertiserId int64 `json:"advertiser_id"`
	// file_url
	FileUrl string `json:"file_url"`

	UploadToken string   `json:"upload_token"`
	Endpoint    []string `json:"endpoint"`
}

type UploadTokenVerify struct {
	UploadToken  string `json:"upload_token"`
	AdvertiserId int64  `json:"advertiser_id"`
}

func (r *PostAdVideoUploadService) SetCfg(cfg *Configuration) *PostAdVideoUploadService {
	r.cfg = cfg
	return r
}

func (r *PostAdVideoUploadService) SetReq(req PostAdVideoUploadReq) *PostAdVideoUploadService {
	r.Request = &req
	return r
}

func (r *PostAdVideoUploadService) AccessToken(accessToken string) *PostAdVideoUploadService {
	r.token = accessToken
	return r
}

const (
	uploadFragmentURL = "https://%s/api/upload/fragment"
	completeUploadURL = "https://%s/api/upload/complete"
	chunkSize         = 2 * 1024 * 1024 // 5 MB per chunk
)

// uploadFragment 上传单个分片
func uploadFragment(endpoint, uploadToken string, fragmentID int, data []byte) error {
	url := fmt.Sprintf(uploadFragmentURL+"?upload_token=%s&fragment_id=%d", endpoint, uploadToken, fragmentID)
	req, err := http.NewRequest("POST", url, bytes.NewReader(data))
	if err != nil {
		return err
	}
	req.Header.Set("Content-Type", "video/mp4")

	resp, err := http.DefaultClient.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()
	body, _ := io.ReadAll(resp.Body)
	bodyStr := string(body)
	if resp.StatusCode != http.StatusOK || err != nil {
		if strings.Contains(err.Error(), "文件操作过于频繁") {
			time.Sleep(200 * time.Millisecond)
			return uploadFragment(endpoint, uploadToken, fragmentID, data)
		}
		return fmt.Errorf("fragment %d upload failed: %s", fragmentID, bodyStr)
	} else {
		fmt.Println(bodyStr)
	}
	return nil
}

type CompleteUploadResult struct {
	Result int `json:"result"`
}

// completeUpload 通知合并分片
func completeUpload(endpoint, uploadToken string, fragmentCount int) error {
	url := fmt.Sprintf(completeUploadURL+"?upload_token=%s&fragment_count=%d", endpoint, uploadToken, fragmentCount)
	req, err := http.NewRequest("POST", url, bytes.NewReader([]byte{}))
	if err != nil {
		return err
	}

	resp, err := http.DefaultClient.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()
	body, _ := io.ReadAll(resp.Body)
	bodyStr := string(body)
	// 序列化成 CompleteUploadResult 结构
	var result CompleteUploadResult
	err = json.Unmarshal(body, &result)
	if resp.StatusCode != http.StatusOK || err != nil {
		if strings.Contains(err.Error(), "文件操作过于频繁") {
			time.Sleep(200 * time.Millisecond)
			return completeUpload(endpoint, uploadToken, fragmentCount)
		}
		return fmt.Errorf("complete upload failed: %s", bodyStr)
	}
	if result.Result == 1 {
		return nil
	} else {
		return fmt.Errorf(" ks video upload failed: %s", bodyStr)
	}

}

// UploadVideoInChunks 分片上传视频  chunkSize 分片大小  concurrency 并发数
func UploadVideoInChunks(url, endpoint, token string, chunkSize int64, concurrency int, maxBufferedChunks int) error {
	resp, err := http.Get(url)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	// channel 缓冲大小受 maxBufferedChunks 限制
	type fragment struct {
		id   int
		data []byte
	}
	ch := make(chan fragment, maxBufferedChunks)

	var wg sync.WaitGroup
	var mu sync.Mutex
	var uploadErr error

	// 启动 worker 池，数量 = concurrency
	for i := 0; i < concurrency; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			for frag := range ch {
				if e := uploadFragment(endpoint, token, frag.id, frag.data); e != nil {
					mu.Lock()
					if uploadErr == nil {
						uploadErr = e
					}
					mu.Unlock()
				}
			}
		}()
	}

	// 读取文件 -> 分片 -> 丢到 channel
	buf := make([]byte, chunkSize)
	fragmentID := 0
	for {
		n, err := io.ReadFull(resp.Body, buf)
		if n > 0 {
			// 必须拷贝，避免 buf 被复用
			data := make([]byte, n)
			copy(data, buf[:n])

			// 如果 channel 满了，会阻塞等待，避免过度占用内存
			ch <- fragment{id: fragmentID, data: data}
			fragmentID++
		}
		if err == io.EOF || err == io.ErrUnexpectedEOF {
			break
		}
		if err != nil {
			close(ch)
			wg.Wait()
			return err
		}
	}

	close(ch)
	wg.Wait()

	if uploadErr != nil {
		return uploadErr
	}
	return completeUpload(endpoint, token, fragmentID)
}

func (r *PostAdVideoUploadService) Do() (data *PostAdVideoUploadResp, err error) {
	if len(r.Request.Endpoint) == 0 {
		return nil, errors.New("endpoint is null")
	}
	if len(r.Request.FileUrl) == 0 {
		return nil, errors.New("file_url is null")
	}
	if err = UploadVideoInChunks(r.Request.FileUrl, r.Request.Endpoint[0], r.Request.UploadToken, chunkSize, 4, 4); err != nil {
		return
	}
	//领用上传token
	verifyReq := new(UploadTokenVerify)
	verifyReq.UploadToken = r.Request.UploadToken
	verifyReq.AdvertiserId = r.Request.AdvertiserId
	localBasePath := r.cfg.BasePath
	localVarPath := localBasePath + "/rest/openapi/gw/ad/common/upload/token/verify"
	response, err := r.cfg.HTTPClient.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("Access-Token", r.token).
		SetBody(verifyReq).
		SetResult(&PostAdVideoUploadResp{}).
		Post(localVarPath)
	if err != nil {
		return nil, err
	}
	resp := new(PostAdVideoUploadResp)
	// 将 JSON 响应解码到结构体中
	err = json.Unmarshal(response.Body(), &resp)
	if err != nil {
		if strings.Contains(err.Error(), "文件操作过于频繁") {
			time.Sleep(200 * time.Millisecond)
			return r.Do()
		}
		return nil, errors.New(fmt.Sprintf("/rest/openapi/gw/ad/common/upload/token/verify解析响应出错: %v\n", err))
	}
	if resp.Code == 0 && resp.Data != nil {
		return resp, nil
	} else {
		return resp, errors.New(resp.Message)
	}
}
