// ==========================================================================
// GFast自动生成dao internal操作代码。
// 生成日期：2025-08-22 11:52:52
// 生成路径: internal/app/ad/dao/internal/ks_advertiser_strategy_unit.go
// 生成人：cq
// desc:快手策略组-广告组
// company:云南奇讯科技有限公司
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// KsAdvertiserStrategyUnitDao is the manager for logic model data accessing and custom defined data operations functions management.
type KsAdvertiserStrategyUnitDao struct {
	table   string                          // Table is the underlying table name of the DAO.
	group   string                          // Group is the database configuration group name of current DAO.
	columns KsAdvertiserStrategyUnitColumns // Columns is the short type for Columns, which contains all the column names of Table for convenient usage.
}

// KsAdvertiserStrategyUnitColumns defines and stores column names for table ks_advertiser_strategy_unit.
type KsAdvertiserStrategyUnitColumns struct {
	Id                        string // 主键ID
	StrategyId                string // 策略组ID
	TaskId                    string // 任务ID
	SceneCategory             string // 投放版位 0：快手主站
	SceneId                   string // 资源位置数组
	KsUserId                  string // 快手号
	ShortPlayAllocationMethod string // 短剧分配方式 全部相同：SAME 按账户分配：ADVERTISER 按广告计划分配：CAMPAIGN 按广告组分配：UNIT
	ShortPlayData             string // 选择短剧与集数
	ProductAllocationMethod   string // 产品分配方式 全部相同：SAME 按账户分配：ADVERTISER 按广告计划分配：CAMPAIGN 按广告组分配：UNIT
	ProductData               string // 关联商品
	QuickSearch               string // 搜索快投开关 0：不开启（默认填充）；1：开启搜索快投
	TargetExplore             string // 搜索人群探索 0：不开启；1：开启
	NegativeWordParam         string // 搜索广告否词
	BeginTime                 string // 投放开始时间 格式为 yyyy-MM-dd
	EndTime                   string // 投放结束时间 格式为 yyyy-MM-dd
	ScheduleTime              string // 投放时间段 24*7位字符串
	DayBudget                 string // 日预算 不限传0
	OcpxActionType            string // 转化目标（优化目标）
	BidType                   string // 计费方式（优化目标出价类型） 2：CPC；10：OCPM；12:MCB最大转化
	BidAllocationMethod       string // 出价分配规则 全部相同：SAME 按账户分配：ADVERTISER
	BidData                   string // 出价信息
	UnitName                  string // 广告组名称
	PutStatus                 string // 广告组默认状态 1：广告组投放中；2：广告组暂停投放
	CreatedAt                 string // 创建时间
	UpdatedAt                 string // 更新时间
	DeletedAt                 string // 删除时间
}

var ksAdvertiserStrategyUnitColumns = KsAdvertiserStrategyUnitColumns{
	Id:                        "id",
	StrategyId:                "strategy_id",
	TaskId:                    "task_id",
	SceneCategory:             "scene_category",
	SceneId:                   "scene_id",
	KsUserId:                  "ks_user_id",
	ShortPlayAllocationMethod: "short_play_allocation_method",
	ShortPlayData:             "short_play_data",
	ProductAllocationMethod:   "product_allocation_method",
	ProductData:               "product_data",
	QuickSearch:               "quick_search",
	TargetExplore:             "target_explore",
	NegativeWordParam:         "negative_word_param",
	BeginTime:                 "begin_time",
	EndTime:                   "end_time",
	ScheduleTime:              "schedule_time",
	DayBudget:                 "day_budget",
	OcpxActionType:            "ocpx_action_type",
	BidType:                   "bid_type",
	BidAllocationMethod:       "bid_allocation_method",
	BidData:                   "bid_data",
	UnitName:                  "unit_name",
	PutStatus:                 "put_status",
	CreatedAt:                 "created_at",
	UpdatedAt:                 "updated_at",
	DeletedAt:                 "deleted_at",
}

// NewKsAdvertiserStrategyUnitDao creates and returns a new DAO object for table data access.
func NewKsAdvertiserStrategyUnitDao() *KsAdvertiserStrategyUnitDao {
	return &KsAdvertiserStrategyUnitDao{
		group:   "default",
		table:   "ks_advertiser_strategy_unit",
		columns: ksAdvertiserStrategyUnitColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *KsAdvertiserStrategyUnitDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *KsAdvertiserStrategyUnitDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *KsAdvertiserStrategyUnitDao) Columns() KsAdvertiserStrategyUnitColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *KsAdvertiserStrategyUnitDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *KsAdvertiserStrategyUnitDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *KsAdvertiserStrategyUnitDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
