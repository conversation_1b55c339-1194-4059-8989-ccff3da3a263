// ==========================================================================
// GFast自动生成service操作代码。
// 生成日期：2025-08-21 00:00:00
// 生成路径: internal/app/ad/service/ks_advertiser_strategy_generate.go
// 生成人：gfast
// desc:快手广告策略生成
// company:云南奇讯科技有限公司
// ==========================================================================

package service

import (
	"context"
	ksApi "github.com/tiger1103/gfast/v3/library/advertiser/ks/api"

	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
)

type IKsAdvertiserStrategyGenerate interface {
	GenerateAdPreview(ctx context.Context, req *model.KsAdvertiserStrategyGenerateReq) (res *model.KsAdvertiserStrategyGenerateRes, err error)
	ExecuteTask(ctx context.Context, req *model.AdExecuteTaskReq) (err error)
	QuerySeriesAuthUserList(ctx context.Context, advertiserId int64) (res []ksApi.QuerySeriesAuthUserListData, err error)
	QuerySeriesList(ctx context.Context, req *model.QuerySeriesListReq) (res []ksApi.MapiSeriesInfoSnake, err error)
	QuerySeriesEpisodeList(ctx context.Context, req *model.QuerySeriesEpisodeListReq) (res []ksApi.MapiEpisodeInfoSnake, err error)
	QuerySeriesPayModeType(ctx context.Context, req *model.QuerySeriesPayModeTypeReq) (res []ksApi.MapiSeriesPayModeInfoSnake, err error)
	QuerySeriesPayModeTemplate(ctx context.Context, req *model.QuerySeriesPayModeTemplateReq) (res []ksApi.MapiSeriesPayModeTemplateInfoSnake, err error)
	QueryProductList(ctx context.Context, req *model.QueryProductListReq) (res *ksApi.ProductBatchQueryResponse, err error)
	QueryProductLibraryList(ctx context.Context, req *model.QueryProductLibraryListReq) (res *ksApi.LibraryListResponse, err error)
	QueryCreativeActionBarText(ctx context.Context, req *model.QueryCreativeActionBarTextReq) (res []string, err error)
	QueryToolExposeTags(ctx context.Context, req *model.QueryToolExposeTagsReq) (res []string, err error)
	QueryCreativeCategory(ctx context.Context, req *model.QueryCreativeCategoryReq) (res []*model.QueryCreativeCategoryRes, err error)
}

var localKsAdvertiserStrategyGenerate IKsAdvertiserStrategyGenerate

func KsAdvertiserStrategyGenerate() IKsAdvertiserStrategyGenerate {
	if localKsAdvertiserStrategyGenerate == nil {
		panic("implement not found for interface IKsAdvertiserStrategyGenerate, forgot register?")
	}
	return localKsAdvertiserStrategyGenerate
}

func RegisterKsAdvertiserStrategyGenerate(i IKsAdvertiserStrategyGenerate) {
	localKsAdvertiserStrategyGenerate = i
}
