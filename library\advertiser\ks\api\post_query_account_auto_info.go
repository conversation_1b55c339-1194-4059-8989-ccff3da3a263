package api

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
)

// QueryAccountAutoInfoService 查询账户智投配置信息
type QueryAccountAutoInfoService struct {
	ctx     context.Context
	cfg     *Configuration
	token   string
	Request *QueryAccountAutoInfoReq
}

type QueryAccountAutoInfoReq struct {
	AdvertiserId int64 `json:"advertiser_id"` // 广告主id
}

//type QueryAccountAutoInfoResp struct {
//	Code    int                             `json:"code"`    // 状态码
//	Message string                          `json:"message"` // 响应消息
//	Data    *AccountSimpleQueryResp863Snake `json:"data"`    // 优化目标列表视图
//}

// AccountSimpleQueryResp863Snake 账户数据响应结构体
type AccountSimpleQueryResp863Snake struct {
	AccountAutoManage        int                        `json:"account_auto_manage" dc:"账户智投开关"`
	OcpxActionTypeConstraint []OcpxActionTypeConstraint `json:"ocpx_action_type_constraint" dc:"账户智投目标成本配置"`
	AutoCampaignNameRule     string                     `json:"auto_campaign_name_rule" dc:"广告计划命名规则"`
}

// OcpxActionTypeConstraint 账户智投目标成本配置项
type OcpxActionTypeConstraint struct {
	OcpxActionType int     `json:"ocpx_action_type" dc:"优化目标: 191(首日ROI), 190(付费), 180(激活), 394(订单提交), 53(表单优化), 773(关键行为), 324(唤端)"`
	Value          float64 `json:"value" dc:"优化目标成本约束，单位元，精确小数点后三位"`
}

func (r *QueryAccountAutoInfoService) SetCfg(cfg *Configuration) *QueryAccountAutoInfoService {
	r.cfg = cfg
	return r
}

func (r *QueryAccountAutoInfoService) SetReq(req QueryAccountAutoInfoReq) *QueryAccountAutoInfoService {
	r.Request = &req
	return r
}

func (r *QueryAccountAutoInfoService) AccessToken(accessToken string) *QueryAccountAutoInfoService {
	r.token = accessToken
	return r
}

func (r *QueryAccountAutoInfoService) Do() (data *KsBaseResp[AccountSimpleQueryResp863Snake], err error) {
	localBasePath := r.cfg.BasePath
	localVarPath := localBasePath + "/rest/openapi/gw/dsp/account/query/auto/info"
	response, err := r.cfg.HTTPClient.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("Access-Token", r.token).
		SetBody(r.Request).
		SetResult(&KsBaseResp[AccountSimpleQueryResp863Snake]{}).
		Post(localVarPath)
	if err != nil {
		return nil, err
	}
	resp := new(KsBaseResp[AccountSimpleQueryResp863Snake])
	// 将 JSON 响应解码到结构体中
	err = json.Unmarshal(response.Body(), &resp)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("/rest/openapi/gw/dsp/account/query/auto/info解析响应出错: %v\n", err))
	}
	if resp.Code == 0 && resp.Data != nil {
		return resp, nil
	} else {
		return resp, errors.New(resp.Message)
	}
}
