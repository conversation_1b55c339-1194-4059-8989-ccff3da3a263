package api

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
)

// UpdateUnitStatusService 修改广告组状态
type UpdateUnitStatusService struct {
	ctx     context.Context
	cfg     *Configuration
	token   string
	Request *UpdateUnitStatusReq
}

// UpdateUnitStatusReq 请求结构体
type UpdateUnitStatusReq struct {
	AdvertiserId int64   `json:"advertiser_id"` // 广告主ID，必填
	UnitId       int64   `json:"unit_id"`       // 广告组ID，必填
	UnitIds      []int64 `json:"unit_ids"`      // 广告组ID列表，批量操作时使用，最多20个
	PutStatus    int64   `json:"put_status"`    // 要修改的状态：1-投放、2-暂停、3-删除
}

// UpdateUnitStatusData API响应结构体
type UpdateUnitStatusData struct {
	UnitId  int64           `json:"unit_id"`  // 广告组ID
	UnitIds []int64         `json:"unit_ids"` // 广告组ID集合
	Errors  []ErrorMsgSnake `json:"errors"`   // 错误信息列表
}

// ErrorMsgSnake 错误信息结构体
type ErrorMsgSnake struct {
	Id       int64  `json:"id"`        // 出错的广告组ID
	ErrorMsg string `json:"error_msg"` // 错误信息
}

func (r *UpdateUnitStatusService) SetCfg(cfg *Configuration) *UpdateUnitStatusService {
	r.cfg = cfg
	return r
}

func (r *UpdateUnitStatusService) SetReq(req UpdateUnitStatusReq) *UpdateUnitStatusService {
	r.Request = &req
	return r
}

func (r *UpdateUnitStatusService) AccessToken(accessToken string) *UpdateUnitStatusService {
	r.token = accessToken
	return r
}

func (r *UpdateUnitStatusService) Do() (data *KsBaseResp[UpdateUnitStatusData], err error) {
	localBasePath := r.cfg.BasePath
	localVarPath := localBasePath + "/rest/openapi/v1/ad_unit/update/status"
	response, err := r.cfg.HTTPClient.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("Access-Token", r.token).
		SetBody(r.Request).
		SetResult(&KsBaseResp[UpdateUnitStatusData]{}).
		Post(localVarPath)
	if err != nil {
		return nil, err
	}
	resp := new(KsBaseResp[UpdateUnitStatusData])
	// 将 JSON 响应解码到结构体中
	err = json.Unmarshal(response.Body(), &resp)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("/rest/openapi/v1/ad_unit/update/status解析响应出错: %v\n", err))
	}
	if resp.Code == 0 && resp.Data != nil {
		return resp, nil
	} else {
		return resp, errors.New(resp.Message)
	}
}
