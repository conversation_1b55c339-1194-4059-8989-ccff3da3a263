// ==========================================================================
// GFast自动生成controller操作代码。
// 生成日期：2025-08-22 11:52:02
// 生成路径: internal/app/ad/controller/ks_advertiser_strategy_campaign.go
// 生成人：cq
// desc:快手策略组-广告计划
// company:云南奇讯科技有限公司
// ==========================================================================

package controller

import (
	"context"

	"github.com/tiger1103/gfast/v3/api/v1/ad"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	systemController "github.com/tiger1103/gfast/v3/internal/app/system/controller"
)

type ksAdvertiserStrategyCampaignController struct {
	systemController.BaseController
}

var KsAdvertiserStrategyCampaign = new(ksAdvertiserStrategyCampaignController)

// List 列表
func (c *ksAdvertiserStrategyCampaignController) List(ctx context.Context, req *ad.KsAdvertiserStrategyCampaignSearchReq) (res *ad.KsAdvertiserStrategyCampaignSearchRes, err error) {
	res = new(ad.KsAdvertiserStrategyCampaignSearchRes)
	res.KsAdvertiserStrategyCampaignSearchRes, err = service.KsAdvertiserStrategyCampaign().List(ctx, &req.KsAdvertiserStrategyCampaignSearchReq)
	return
}

// Get 获取快手策略组-广告计划
func (c *ksAdvertiserStrategyCampaignController) Get(ctx context.Context, req *ad.KsAdvertiserStrategyCampaignGetReq) (res *ad.KsAdvertiserStrategyCampaignGetRes, err error) {
	res = new(ad.KsAdvertiserStrategyCampaignGetRes)
	res.KsAdvertiserStrategyCampaignInfoRes, err = service.KsAdvertiserStrategyCampaign().GetById(ctx, req.Id)
	return
}

// Add 添加快手策略组-广告计划
func (c *ksAdvertiserStrategyCampaignController) Add(ctx context.Context, req *ad.KsAdvertiserStrategyCampaignAddReq) (res *ad.KsAdvertiserStrategyCampaignAddRes, err error) {
	err = service.KsAdvertiserStrategyCampaign().Add(ctx, req.KsAdvertiserStrategyCampaignAddReq)
	return
}

// Edit 修改快手策略组-广告计划
func (c *ksAdvertiserStrategyCampaignController) Edit(ctx context.Context, req *ad.KsAdvertiserStrategyCampaignEditReq) (res *ad.KsAdvertiserStrategyCampaignEditRes, err error) {
	err = service.KsAdvertiserStrategyCampaign().Edit(ctx, req.KsAdvertiserStrategyCampaignEditReq)
	return
}

// Delete 删除快手策略组-广告计划
func (c *ksAdvertiserStrategyCampaignController) Delete(ctx context.Context, req *ad.KsAdvertiserStrategyCampaignDeleteReq) (res *ad.KsAdvertiserStrategyCampaignDeleteRes, err error) {
	err = service.KsAdvertiserStrategyCampaign().Delete(ctx, req.Ids)
	return
}
