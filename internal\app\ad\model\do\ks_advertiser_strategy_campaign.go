// ==========================================================================
// GFast自动生成model entity操作代码。
// 生成日期：2025-08-22 11:52:02
// 生成路径: internal/app/ad/model/entity/ks_advertiser_strategy_campaign.go
// 生成人：cq
// desc:快手策略组-广告计划
// company:云南奇讯科技有限公司
// ==========================================================================

package do

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
)

// KsAdvertiserStrategyCampaign is the golang structure for table ks_advertiser_strategy_campaign.
type KsAdvertiserStrategyCampaign struct {
	gmeta.Meta       `orm:"table:ks_advertiser_strategy_campaign, do:true"`
	Id               interface{} `orm:"id,primary" json:"id"`                       // 主键ID
	StrategyId       interface{} `orm:"strategy_id" json:"strategyId"`              // 策略组ID
	TaskId           interface{} `orm:"task_id" json:"taskId"`                      // 任务ID
	CampaignType     interface{} `orm:"campaign_type" json:"campaignType"`          // 营销类型 30：快手号-短剧推广
	AdType           interface{} `orm:"ad_type" json:"adType"`                      // 广告计划类型 0:信息流（展示广告） 1:搜索（搜索广告）；不填默认信息流
	AutoAdjust       interface{} `orm:"auto_adjust" json:"autoAdjust"`              // 自动调控开关 0：关闭，1：开启
	AutoBuild        interface{} `orm:"auto_build" json:"autoBuild"`                // 自动基建 0：关闭，1：开启
	UnitNameRule     interface{} `orm:"unit_name_rule" json:"unitNameRule"`         // 单元名称规则
	CreativeNameRule interface{} `orm:"creative_name_rule" json:"creativeNameRule"` // 创意名称规则
	AutoManage       interface{} `orm:"auto_manage" json:"autoManage"`              // 智能投放开关 0：关闭，1：开启
	BidType          interface{} `orm:"bid_type" json:"bidType"`                    // 竞价策略 0：成本优先 1：最大转化
	DayBudget        interface{} `orm:"day_budget" json:"dayBudget"`                // 日预算 不限传0
	CampaignName     interface{} `orm:"campaign_name" json:"campaignName"`          // 广告计划名称
	AdUnitLimit      interface{} `orm:"ad_unit_limit" json:"adUnitLimit"`           // 广告计划内广告组上线
	PutStatus        interface{} `orm:"put_status" json:"putStatus"`                // 广告计划默认状态 1-投放、2-暂停
	CreatedAt        *gtime.Time `orm:"created_at" json:"createdAt"`                // 创建时间
	UpdatedAt        *gtime.Time `orm:"updated_at" json:"updatedAt"`                // 更新时间
	DeletedAt        *gtime.Time `orm:"deleted_at" json:"deletedAt"`                // 删除时间
}
