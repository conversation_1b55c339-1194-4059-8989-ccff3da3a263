// ==========================================================================
// GFast自动生成controller操作代码。
// 生成日期：2025-08-21 00:00:00
// 生成路径: internal/app/ad/controller/ks_advertiser_strategy_generate.go
// 生成人：gfast
// desc:快手广告策略生成
// company:云南奇讯科技有限公司
// ==========================================================================

package controller

import (
	"context"

	"github.com/tiger1103/gfast/v3/api/v1/ad"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	systemController "github.com/tiger1103/gfast/v3/internal/app/system/controller"
)

type ksAdvertiserStrategyGenerateController struct {
	systemController.BaseController
}

var KsAdvertiserStrategyGenerate = new(ksAdvertiserStrategyGenerateController)

// GenerateAdPreview 快手广告搭建-生成广告预览
func (c *ksAdvertiserStrategyGenerateController) GenerateAdPreview(ctx context.Context, req *ad.KsAdvertiserStrategyGenerateReq) (res *ad.KsAdvertiserStrategyGenerateRes, err error) {
	res = new(ad.KsAdvertiserStrategyGenerateRes)
	res.KsAdvertiserStrategyGenerateRes, err = service.KsAdvertiserStrategyGenerate().GenerateAdPreview(ctx, req.KsAdvertiserStrategyGenerateReq)
	return
}

// ExecuteTask(ctx context.Context, req *model.AdExecuteTaskRes) (err error)
func (c *ksAdvertiserStrategyGenerateController) ExecuteTask(ctx context.Context, req *ad.KsAdvertiserStrategyExecuteTaskReq) (res *ad.KsAdvertiserStrategyExecuteTaskRes, err error) {
	err = service.KsAdvertiserStrategyGenerate().ExecuteTask(ctx, req.AdExecuteTaskReq)
	return
}

// QuerySeriesAuthUserList 获取授权的短剧作者列表
func (c *ksAdvertiserStrategyGenerateController) QuerySeriesAuthUserList(ctx context.Context, req *ad.QuerySeriesAuthUserListReq) (res *ad.QuerySeriesAuthUserListRes, err error) {
	res = new(ad.QuerySeriesAuthUserListRes)
	res.Data, err = service.KsAdvertiserStrategyGenerate().QuerySeriesAuthUserList(ctx, req.AdvertiserId)
	return
}

// QuerySeriesList 查询授权短剧列表
func (c *ksAdvertiserStrategyGenerateController) QuerySeriesList(ctx context.Context, req *ad.QuerySeriesListReq) (res *ad.QuerySeriesListRes, err error) {
	res = new(ad.QuerySeriesListRes)
	res.Data, err = service.KsAdvertiserStrategyGenerate().QuerySeriesList(ctx, req.QuerySeriesListReq)
	return
}

// QuerySeriesEpisodeList 查询短剧剧集列表
func (c *ksAdvertiserStrategyGenerateController) QuerySeriesEpisodeList(ctx context.Context, req *ad.QuerySeriesEpisodeListReq) (res *ad.QuerySeriesEpisodeListRes, err error) {
	res = new(ad.QuerySeriesEpisodeListRes)
	res.Data, err = service.KsAdvertiserStrategyGenerate().QuerySeriesEpisodeList(ctx, req.QuerySeriesEpisodeListReq)
	return
}

// QuerySeriesPayModeType 查询短剧付费模式
func (c *ksAdvertiserStrategyGenerateController) QuerySeriesPayModeType(ctx context.Context, req *ad.QuerySeriesPayModeTypeReq) (res *ad.QuerySeriesPayModeTypeRes, err error) {
	res = new(ad.QuerySeriesPayModeTypeRes)
	res.Data, err = service.KsAdvertiserStrategyGenerate().QuerySeriesPayModeType(ctx, req.QuerySeriesPayModeTypeReq)
	return
}

// QuerySeriesPayModeTemplate 查询短剧付费模板
func (c *ksAdvertiserStrategyGenerateController) QuerySeriesPayModeTemplate(ctx context.Context, req *ad.QuerySeriesPayModeTemplateReq) (res *ad.QuerySeriesPayModeTemplateRes, err error) {
	res = new(ad.QuerySeriesPayModeTemplateRes)
	res.Data, err = service.KsAdvertiserStrategyGenerate().QuerySeriesPayModeTemplate(ctx, req.QuerySeriesPayModeTemplateReq)
	return
}

// QueryProductList 查询商品列表
func (c *ksAdvertiserStrategyGenerateController) QueryProductList(ctx context.Context, req *ad.QueryProductListReq) (res *ad.QueryProductListRes, err error) {
	res = new(ad.QueryProductListRes)
	res.ProductBatchQueryResponse, err = service.KsAdvertiserStrategyGenerate().QueryProductList(ctx, req.QueryProductListReq)
	return
}

// QueryProductLibraryList 查询商品库列表
func (c *ksAdvertiserStrategyGenerateController) QueryProductLibraryList(ctx context.Context, req *ad.QueryProductLibraryListReq) (res *ad.QueryProductLibraryListRes, err error) {
	res = new(ad.QueryProductLibraryListRes)
	res.LibraryListResponse, err = service.KsAdvertiserStrategyGenerate().QueryProductLibraryList(ctx, req.QueryProductLibraryListReq)
	return
}

// QueryCreativeActionBarText 查询行动号召按钮
func (c *ksAdvertiserStrategyGenerateController) QueryCreativeActionBarText(ctx context.Context, req *ad.QueryCreativeActionBarTextReq) (res *ad.QueryCreativeActionBarTextRes, err error) {
	res = new(ad.QueryCreativeActionBarTextRes)
	res.Data, err = service.KsAdvertiserStrategyGenerate().QueryCreativeActionBarText(ctx, req.QueryCreativeActionBarTextReq)
	return
}

// QueryToolExposeTags 查询创意推荐理由
func (c *ksAdvertiserStrategyGenerateController) QueryToolExposeTags(ctx context.Context, req *ad.QueryToolExposeTagsReq) (res *ad.QueryToolExposeTagsRes, err error) {
	res = new(ad.QueryToolExposeTagsRes)
	res.Data, err = service.KsAdvertiserStrategyGenerate().QueryToolExposeTags(ctx, req.QueryToolExposeTagsReq)
	return
}

// QueryCreativeCategory 查询创意分类
func (c *ksAdvertiserStrategyGenerateController) QueryCreativeCategory(ctx context.Context, req *ad.QueryCreativeCategoryReq) (res *ad.QueryCreativeCategoryRes, err error) {
	res = new(ad.QueryCreativeCategoryRes)
	res.Data, err = service.KsAdvertiserStrategyGenerate().QueryCreativeCategory(ctx, req.QueryCreativeCategoryReq)
	return
}
