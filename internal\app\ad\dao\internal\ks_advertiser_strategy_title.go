// ==========================================================================
// GFast自动生成dao internal操作代码。
// 生成日期：2025-08-22 11:52:42
// 生成路径: internal/app/ad/dao/internal/ks_advertiser_strategy_title.go
// 生成人：cq
// desc:快手策略组-文案
// company:云南奇讯科技有限公司
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// KsAdvertiserStrategyTitleDao is the manager for logic model data accessing and custom defined data operations functions management.
type KsAdvertiserStrategyTitleDao struct {
	table   string                           // Table is the underlying table name of the DAO.
	group   string                           // Group is the database configuration group name of current DAO.
	columns KsAdvertiserStrategyTitleColumns // Columns is the short type for Columns, which contains all the column names of Table for convenient usage.
}

// KsAdvertiserStrategyTitleColumns defines and stores column names for table ks_advertiser_strategy_title.
type KsAdvertiserStrategyTitleColumns struct {
	Id                    string // 主键ID
	StrategyId            string // 策略组ID
	TaskId                string // 任务ID
	TitleAllocationMethod string // 标题分配方式 AUTO TEST
	TitleData             string // 标题数据，包含标题详细信息、分类、性能数据等
	CreatedAt             string // 创建时间
	UpdatedAt             string // 更新时间
	DeletedAt             string // 删除时间
}

var ksAdvertiserStrategyTitleColumns = KsAdvertiserStrategyTitleColumns{
	Id:                    "id",
	StrategyId:            "strategy_id",
	TaskId:                "task_id",
	TitleAllocationMethod: "title_allocation_method",
	TitleData:             "title_data",
	CreatedAt:             "created_at",
	UpdatedAt:             "updated_at",
	DeletedAt:             "deleted_at",
}

// NewKsAdvertiserStrategyTitleDao creates and returns a new DAO object for table data access.
func NewKsAdvertiserStrategyTitleDao() *KsAdvertiserStrategyTitleDao {
	return &KsAdvertiserStrategyTitleDao{
		group:   "default",
		table:   "ks_advertiser_strategy_title",
		columns: ksAdvertiserStrategyTitleColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *KsAdvertiserStrategyTitleDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *KsAdvertiserStrategyTitleDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *KsAdvertiserStrategyTitleDao) Columns() KsAdvertiserStrategyTitleColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *KsAdvertiserStrategyTitleDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *KsAdvertiserStrategyTitleDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *KsAdvertiserStrategyTitleDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
