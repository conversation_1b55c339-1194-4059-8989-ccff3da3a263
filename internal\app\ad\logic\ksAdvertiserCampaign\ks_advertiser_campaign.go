// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2025-08-13 16:34:00
// 生成路径: internal/app/ad/logic/ks_advertiser_campaign.go
// 生成人：cyao
// desc:快手广告计划
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"
	"errors"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	commonConsts "github.com/tiger1103/gfast/v3/internal/app/common/consts"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	ksApi "github.com/tiger1103/gfast/v3/library/advertiser/ks/api"
	"github.com/tiger1103/gfast/v3/library/liberr"
)

func init() {
	service.RegisterKsAdvertiserCampaign(New())
}

func New() service.IKsAdvertiserCampaign {
	return &sKsAdvertiserCampaign{}
}

type sKsAdvertiserCampaign struct{}

func (s *sKsAdvertiserCampaign) List(ctx context.Context, req *model.KsAdvertiserCampaignSearchReq) (listRes *model.KsAdvertiserCampaignSearchRes, err error) {
	listRes = new(model.KsAdvertiserCampaignSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.KsAdvertiserCampaign.Ctx(ctx).WithAll()
		if req.CampaignId != "" {
			m = m.Where(dao.KsAdvertiserCampaign.Columns().CampaignId+" = ?", req.CampaignId)
		}
		if req.AdvertiserId != "" {
			m = m.Where(dao.KsAdvertiserCampaign.Columns().AdvertiserId+" = ?", gconv.Int(req.AdvertiserId))
		}
		if req.PhotoPackageDetails != "" {
			m = m.Where(dao.KsAdvertiserCampaign.Columns().PhotoPackageDetails+" = ?", req.PhotoPackageDetails)
		}
		if req.AdType != "" {
			m = m.Where(dao.KsAdvertiserCampaign.Columns().AdType+" = ?", gconv.Int(req.AdType))
		}
		if req.CampaignType != "" {
			m = m.Where(dao.KsAdvertiserCampaign.Columns().CampaignType+" = ?", gconv.Int(req.CampaignType))
		}
		if req.CampaignDeepConversionType != "" {
			m = m.Where(dao.KsAdvertiserCampaign.Columns().CampaignDeepConversionType+" = ?", gconv.Int(req.CampaignDeepConversionType))
		}
		if req.BidType != "" {
			m = m.Where(dao.KsAdvertiserCampaign.Columns().BidType+" = ?", gconv.Int(req.BidType))
		}
		if req.PutStatus != "" {
			m = m.Where(dao.KsAdvertiserCampaign.Columns().PutStatus+" = ?", gconv.Int(req.PutStatus))
		}
		if req.CampaignOcpxActionTypeName != "" {
			m = m.Where(dao.KsAdvertiserCampaign.Columns().CampaignOcpxActionTypeName+" like ?", "%"+req.CampaignOcpxActionTypeName+"%")
		}
		if req.CampaignOcpxActionType != "" {
			m = m.Where(dao.KsAdvertiserCampaign.Columns().CampaignOcpxActionType+" = ?", gconv.Int(req.CampaignOcpxActionType))
		}
		if req.CampaignName != "" {
			m = m.Where(dao.KsAdvertiserCampaign.Columns().CampaignName+" like ?", "%"+req.CampaignName+"%")
		}
		if req.UpdateTime != "" {
			m = m.Where(dao.KsAdvertiserCampaign.Columns().UpdateTime+" = ?", gconv.Time(req.UpdateTime))
		}
		if req.DspVersion != "" {
			m = m.Where(dao.KsAdvertiserCampaign.Columns().DspVersion+" = ?", gconv.Int(req.DspVersion))
		}
		if req.PeriodicDeliveryType != "" {
			m = m.Where(dao.KsAdvertiserCampaign.Columns().PeriodicDeliveryType+" = ?", gconv.Int(req.PeriodicDeliveryType))
		}
		if req.CampaignDeepConversionTypeName != "" {
			m = m.Where(dao.KsAdvertiserCampaign.Columns().CampaignDeepConversionTypeName+" like ?", "%"+req.CampaignDeepConversionTypeName+"%")
		}
		if req.CampaignSubType != "" {
			m = m.Where(dao.KsAdvertiserCampaign.Columns().CampaignSubType+" = ?", gconv.Int(req.CampaignSubType))
		}
		if req.ConstraintCpa != "" {
			m = m.Where(dao.KsAdvertiserCampaign.Columns().ConstraintCpa+" = ?", gconv.Int(req.ConstraintCpa))
		}
		if req.AutoAdjust != "" {
			m = m.Where(dao.KsAdvertiserCampaign.Columns().AutoAdjust+" = ?", gconv.Int(req.AutoAdjust))
		}
		if req.ContinuePeriodType != "" {
			m = m.Where(dao.KsAdvertiserCampaign.Columns().ContinuePeriodType+" = ?", gconv.Int(req.ContinuePeriodType))
		}
		if req.ConstraintActionType != "" {
			m = m.Where(dao.KsAdvertiserCampaign.Columns().ConstraintActionType+" = ?", gconv.Int(req.ConstraintActionType))
		}
		if req.DayBudget != "" {
			m = m.Where(dao.KsAdvertiserCampaign.Columns().DayBudget+" = ?", gconv.Int(req.DayBudget))
		}
		if req.AutoManage != "" {
			m = m.Where(dao.KsAdvertiserCampaign.Columns().AutoManage+" = ?", gconv.Int(req.AutoManage))
		}
		if req.AutoPhotoScope != "" {
			m = m.Where(dao.KsAdvertiserCampaign.Columns().AutoPhotoScope+" = ?", gconv.Int(req.AutoPhotoScope))
		}
		if req.CreateTime != "" {
			m = m.Where(dao.KsAdvertiserCampaign.Columns().CreateTime+" = ?", gconv.Time(req.CreateTime))
		}
		if req.AutoBuild != "" {
			m = m.Where(dao.KsAdvertiserCampaign.Columns().AutoBuild+" = ?", gconv.Int(req.AutoBuild))
		}
		if req.PeriodicDays != "" {
			m = m.Where(dao.KsAdvertiserCampaign.Columns().PeriodicDays+" = ?", gconv.Int(req.PeriodicDays))
		}
		if req.CapBid != "" {
			m = m.Where(dao.KsAdvertiserCampaign.Columns().CapBid+" = ?", gconv.Int(req.CapBid))
		}
		if req.RangeBudget != "" {
			m = m.Where(dao.KsAdvertiserCampaign.Columns().RangeBudget+" = ?", gconv.Int(req.RangeBudget))
		}
		if req.DayBudgetSchedule != "" {
			m = m.Where(dao.KsAdvertiserCampaign.Columns().DayBudgetSchedule+" = ?", req.DayBudgetSchedule)
		}
		if req.Status != "" {
			m = m.Where(dao.KsAdvertiserCampaign.Columns().Status+" = ?", gconv.Int(req.Status))
		}
		if req.UnitNameRule != "" {
			m = m.Where(dao.KsAdvertiserCampaign.Columns().UnitNameRule+" = ?", req.UnitNameRule)
		}
		if req.CreativeNameRule != "" {
			m = m.Where(dao.KsAdvertiserCampaign.Columns().CreativeNameRule+" = ?", req.CreativeNameRule)
		}
		if len(req.DateRange) != 0 {
			m = m.Where(dao.KsAdvertiserCampaign.Columns().CreatedAt+" >=? AND "+dao.KsAdvertiserCampaign.Columns().CreatedAt+" <=?", req.DateRange[0], req.DateRange[1])
		}
		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "campaign_id asc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		var res []*model.KsAdvertiserCampaignListRes
		err = m.Page(req.PageNum, req.PageSize).Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		listRes.List = make([]*model.KsAdvertiserCampaignListRes, len(res))
		for k, v := range res {
			listRes.List[k] = &model.KsAdvertiserCampaignListRes{
				CampaignId:                     v.CampaignId,
				AdvertiserId:                   v.AdvertiserId,
				PhotoPackageDetails:            v.PhotoPackageDetails,
				AdType:                         v.AdType,
				CampaignType:                   v.CampaignType,
				CampaignDeepConversionType:     v.CampaignDeepConversionType,
				BidType:                        v.BidType,
				PutStatus:                      v.PutStatus,
				CampaignOcpxActionTypeName:     v.CampaignOcpxActionTypeName,
				CapRoiRatio:                    v.CapRoiRatio,
				CampaignOcpxActionType:         v.CampaignOcpxActionType,
				CampaignName:                   v.CampaignName,
				UpdateTime:                     v.UpdateTime,
				DspVersion:                     v.DspVersion,
				PeriodicDeliveryType:           v.PeriodicDeliveryType,
				CampaignDeepConversionTypeName: v.CampaignDeepConversionTypeName,
				CampaignSubType:                v.CampaignSubType,
				ConstraintCpa:                  v.ConstraintCpa,
				AutoAdjust:                     v.AutoAdjust,
				ContinuePeriodType:             v.ContinuePeriodType,
				ConstraintActionType:           v.ConstraintActionType,
				DayBudget:                      v.DayBudget,
				AutoManage:                     v.AutoManage,
				AutoPhotoScope:                 v.AutoPhotoScope,
				CreateTime:                     v.CreateTime,
				AutoBuild:                      v.AutoBuild,
				PeriodicDays:                   v.PeriodicDays,
				CapBid:                         v.CapBid,
				RangeBudget:                    v.RangeBudget,
				DayBudgetSchedule:              v.DayBudgetSchedule,
				Status:                         v.Status,
				UnitNameRule:                   v.UnitNameRule,
				CreativeNameRule:               v.CreativeNameRule,
				CreatedAt:                      v.CreatedAt,
			}
		}
	})
	return
}

func (s *sKsAdvertiserCampaign) GetByCampaignId(ctx context.Context, campaignId int64) (res *model.KsAdvertiserCampaignInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.KsAdvertiserCampaign.Ctx(ctx).WithAll().Where(dao.KsAdvertiserCampaign.Columns().CampaignId, campaignId).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

// 根据广告id 拉取 广告计划
func (s *sKsAdvertiserCampaign) PullCampaignByAdId(ctx context.Context, accessToken string, adId int64) (err error) {

	err = g.Try(ctx, func(ctx context.Context) {
		page, pageSize := 1, 200
		for {
			campaignListRes, err := ksApi.GetKSApiClient().GetDspCampaignListService.AccessToken(accessToken).SetReq(ksApi.GetDspCampaignListReq{
				AdvertiserID: adId,
				PageSize:     pageSize,
				Page:         page,
			}).Do()
			if err != nil {
				g.Log().Errorf(ctx, "拉取广告计划失败: %v", err)
				break
			}
			if campaignListRes == nil || campaignListRes.Data == nil || campaignListRes.Data.TotalCount == 0 {
				break
			}
			// 循环添加广告计划
			batchReq := make([]*model.KsAdvertiserCampaignAddReq, 0)
			for _, campaign := range campaignListRes.Data.Details {
				req := &model.KsAdvertiserCampaignAddReq{
					CampaignId:   campaign.CampaignID,
					AdvertiserId: gconv.Int(adId),
					//PhotoPackageDetails:            campaign.PhotoPackageDetails,
					AdType:                         campaign.AdType,
					CampaignType:                   campaign.CampaignType,
					CampaignDeepConversionType:     campaign.CampaignDeepConversionType,
					BidType:                        campaign.BidType,
					PutStatus:                      campaign.PutStatus,
					CampaignOcpxActionTypeName:     campaign.CampaignOcpxActionTypeName,
					CapRoiRatio:                    campaign.CapRoiRatio,
					CampaignOcpxActionType:         campaign.CampaignOcpxActionType,
					CampaignName:                   campaign.CampaignName,
					DspVersion:                     campaign.DspVersion,
					PeriodicDeliveryType:           campaign.PeriodicDeliveryType,
					CampaignDeepConversionTypeName: campaign.CampaignDeepConversionTypeName,
					CampaignSubType:                campaign.CampaignSubType,
					ConstraintCpa:                  campaign.ConstraintCpa,
					AutoAdjust:                     campaign.AutoAdjust,
					ContinuePeriodType:             campaign.ContinuePeriodType,
					ConstraintActionType:           campaign.ConstraintActionType,
					DayBudget:                      campaign.DayBudget,
					AutoManage:                     campaign.AutoManage,
					AutoPhotoScope:                 campaign.AutoPhotoScope,
					AutoBuild:                      campaign.AutoBuild,
					PeriodicDays:                   campaign.PeriodicDays,
					CapBid:                         campaign.CapBid,
					RangeBudget:                    campaign.RangeBudget,
					//DayBudgetSchedule:              campaign.DayBudgetSchedule,
					Status:     campaign.Status,
					UpdateTime: gconv.GTime(campaign.UpdateTime),
					CreateTime: gconv.GTime(campaign.CreateTime),
				}
				if len(campaign.DayBudgetSchedule) > 0 {
					req.DayBudgetSchedule = campaign.DayBudgetSchedule
				}

				if campaign.AutoBuildNameRule != nil {
					req.UnitNameRule = campaign.AutoBuildNameRule.UnitNameRule
					req.CreativeNameRule = campaign.AutoBuildNameRule.CreativeNameRule
				}

				batchReq = append(batchReq, req)
			}
			err = s.BatchAdd(ctx, batchReq)
			if err != nil {
				g.Log().Errorf(ctx, "批量新增失败%v", err.Error())
			}
			if campaignListRes.Data.TotalCount < pageSize {
				break
			} else {
				page++
			}
		}

	})
	return
}

func (s *sKsAdvertiserCampaign) Add(ctx context.Context, req *model.KsAdvertiserCampaignAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.KsAdvertiserCampaign.Ctx(ctx).Insert(do.KsAdvertiserCampaign{
			CampaignId:                     req.CampaignId,
			AdvertiserId:                   req.AdvertiserId,
			PhotoPackageDetails:            req.PhotoPackageDetails,
			AdType:                         req.AdType,
			CampaignType:                   req.CampaignType,
			CampaignDeepConversionType:     req.CampaignDeepConversionType,
			BidType:                        req.BidType,
			PutStatus:                      req.PutStatus,
			CampaignOcpxActionTypeName:     req.CampaignOcpxActionTypeName,
			CapRoiRatio:                    req.CapRoiRatio,
			CampaignOcpxActionType:         req.CampaignOcpxActionType,
			CampaignName:                   req.CampaignName,
			UpdateTime:                     req.UpdateTime,
			DspVersion:                     req.DspVersion,
			PeriodicDeliveryType:           req.PeriodicDeliveryType,
			CampaignDeepConversionTypeName: req.CampaignDeepConversionTypeName,
			CampaignSubType:                req.CampaignSubType,
			ConstraintCpa:                  req.ConstraintCpa,
			AutoAdjust:                     req.AutoAdjust,
			ContinuePeriodType:             req.ContinuePeriodType,
			ConstraintActionType:           req.ConstraintActionType,
			DayBudget:                      req.DayBudget,
			AutoManage:                     req.AutoManage,
			AutoPhotoScope:                 req.AutoPhotoScope,
			CreateTime:                     req.CreateTime,
			AutoBuild:                      req.AutoBuild,
			PeriodicDays:                   req.PeriodicDays,
			CapBid:                         req.CapBid,
			RangeBudget:                    req.RangeBudget,
			DayBudgetSchedule:              req.DayBudgetSchedule,
			Status:                         req.Status,
			UnitNameRule:                   req.UnitNameRule,
			CreativeNameRule:               req.CreativeNameRule,
		})
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

// 批量添加
func (s *sKsAdvertiserCampaign) BatchAdd(ctx context.Context, batchReq []*model.KsAdvertiserCampaignAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.KsAdvertiserCampaign.Ctx(ctx).Save(batchReq)
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sKsAdvertiserCampaign) Edit(ctx context.Context, req *model.KsAdvertiserCampaignEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.KsAdvertiserCampaign.Ctx(ctx).WherePri(req.CampaignId).Update(do.KsAdvertiserCampaign{
			AdvertiserId:                   req.AdvertiserId,
			PhotoPackageDetails:            req.PhotoPackageDetails,
			AdType:                         req.AdType,
			CampaignType:                   req.CampaignType,
			CampaignDeepConversionType:     req.CampaignDeepConversionType,
			BidType:                        req.BidType,
			PutStatus:                      req.PutStatus,
			CampaignOcpxActionTypeName:     req.CampaignOcpxActionTypeName,
			CapRoiRatio:                    req.CapRoiRatio,
			CampaignOcpxActionType:         req.CampaignOcpxActionType,
			CampaignName:                   req.CampaignName,
			UpdateTime:                     req.UpdateTime,
			DspVersion:                     req.DspVersion,
			PeriodicDeliveryType:           req.PeriodicDeliveryType,
			CampaignDeepConversionTypeName: req.CampaignDeepConversionTypeName,
			CampaignSubType:                req.CampaignSubType,
			ConstraintCpa:                  req.ConstraintCpa,
			AutoAdjust:                     req.AutoAdjust,
			ContinuePeriodType:             req.ContinuePeriodType,
			ConstraintActionType:           req.ConstraintActionType,
			DayBudget:                      req.DayBudget,
			AutoManage:                     req.AutoManage,
			AutoPhotoScope:                 req.AutoPhotoScope,
			CreateTime:                     req.CreateTime,
			AutoBuild:                      req.AutoBuild,
			PeriodicDays:                   req.PeriodicDays,
			CapBid:                         req.CapBid,
			RangeBudget:                    req.RangeBudget,
			DayBudgetSchedule:              req.DayBudgetSchedule,
			Status:                         req.Status,
			UnitNameRule:                   req.UnitNameRule,
			CreativeNameRule:               req.CreativeNameRule,
		})
		liberr.ErrIsNil(ctx, err, "修改失败")
	})
	return
}

func (s *sKsAdvertiserCampaign) Delete(ctx context.Context, campaignIds []int64) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.KsAdvertiserCampaign.Ctx(ctx).Delete(dao.KsAdvertiserCampaign.Columns().CampaignId+" in (?)", campaignIds)
		liberr.ErrIsNil(ctx, err, "删除失败")
	})
	return
}

func (s *sKsAdvertiserCampaign) UpdateCampaignInfo(ctx context.Context, req *model.KsAdvertiserCampaignUpdateReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		campaignInfo := do.KsAdvertiserCampaign{}
		if req.PutStatus != nil {
			campaignInfo.PutStatus = req.PutStatus
		}
		if req.DayBudget != nil {
			campaignInfo.DayBudget = req.DayBudget
		}
		_, err = dao.KsAdvertiserCampaign.Ctx(ctx).WherePri(req.CampaignId).Update(campaignInfo)
		liberr.ErrIsNil(ctx, err, "修改失败")
	})
	return
}

// ManualSyncCampaign 手动同步广告计划
func (s *sKsAdvertiserCampaign) ManualSyncCampaign(ctx context.Context, advertiserIds []int64, startTime, endTime string) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		for _, advertiserId := range advertiserIds {
			accessToken, _ := service.KsAdvertiserAccountInfo().GetAccessToken(ctx, advertiserId)
			if accessToken == "" {
				continue
			}
			var page = 1
			var pageSize = 200
			campaignListRes, err1 := ksApi.GetKSApiClient().GetDspCampaignListService.
				AccessToken(accessToken).SetReq(ksApi.GetDspCampaignListReq{
				Page:         page,
				PageSize:     pageSize,
				StartDate:    startTime,
				EndDate:      endTime,
				AdvertiserID: advertiserId,
			}).Do()
			if err1 != nil {
				g.Log().Errorf(ctx, "同步广告主%v计划列表失败：%v", advertiserId, err1)
				continue
			}
			if campaignListRes.Data == nil || len(campaignListRes.Data.Details) == 0 {
				continue
			}
			var campaignListAdd []*model.KsAdvertiserCampaignAddReq
			for _, campaign := range campaignListRes.Data.Details {
				var photoPackageDetails []ksApi.PhotoPackageDetail
				if campaign.PhotoPackageDetails == nil {
					photoPackageDetails = []ksApi.PhotoPackageDetail{}
				}
				campaignListAdd = append(campaignListAdd, &model.KsAdvertiserCampaignAddReq{
					CampaignId:                     campaign.CampaignID,
					AdvertiserId:                   gconv.Int(advertiserId),
					PhotoPackageDetails:            photoPackageDetails,
					AdType:                         campaign.AdType,
					CampaignType:                   campaign.CampaignType,
					CampaignDeepConversionType:     campaign.CampaignDeepConversionType,
					BidType:                        campaign.BidType,
					PutStatus:                      campaign.PutStatus,
					CampaignOcpxActionTypeName:     gconv.String(campaign.CampaignOcpxActionTypeName),
					CapRoiRatio:                    campaign.CapRoiRatio,
					CampaignOcpxActionType:         campaign.CampaignOcpxActionType,
					CampaignName:                   campaign.CampaignName,
					DspVersion:                     campaign.DspVersion,
					PeriodicDeliveryType:           campaign.PeriodicDeliveryType,
					CampaignDeepConversionTypeName: gconv.String(campaign.CampaignDeepConversionTypeName),
					CampaignSubType:                campaign.CampaignSubType,
					ConstraintCpa:                  campaign.ConstraintCpa,
					AutoAdjust:                     campaign.AutoAdjust,
					ContinuePeriodType:             campaign.ContinuePeriodType,
					ConstraintActionType:           campaign.ConstraintActionType,
					DayBudget:                      campaign.DayBudget,
					AutoManage:                     campaign.AutoManage,
					AutoPhotoScope:                 campaign.AutoPhotoScope,
					AutoBuild:                      campaign.AutoBuild,
					PeriodicDays:                   campaign.PeriodicDays,
					CapBid:                         campaign.CapBid,
					RangeBudget:                    campaign.RangeBudget,
					DayBudgetSchedule:              campaign.DayBudgetSchedule,
					Status:                         campaign.Status,
					UpdateTime:                     gconv.GTime(campaign.UpdateTime),
					CreateTime:                     gconv.GTime(campaign.CreateTime),
				})
			}
			_ = s.BatchAdd(ctx, campaignListAdd)
			if page*pageSize >= campaignListRes.Data.TotalCount {
				break
			}
			page++
		}
	})
	return
}

// EditCampaignName 修改广告计划名称
func (s *sKsAdvertiserCampaign) EditCampaignName(ctx context.Context, campaignId int64, campaignName string) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		campaignInfo, _ := s.GetByCampaignId(ctx, campaignId)
		if campaignInfo == nil {
			liberr.ErrIsNil(ctx, errors.New("广告计划不存在"))
		}
		advertiserId := gconv.Int64(campaignInfo.AdvertiserId)
		accessToken, _ := service.KsAdvertiserAccountInfo().GetAccessToken(ctx, advertiserId)
		if accessToken == "" {
			liberr.ErrIsNil(ctx, errors.New(commonConsts.ErrMsgGetAccessToken))
		}
		_, err = ksApi.GetKSApiClient().UpdateCampaignService.
			AccessToken(accessToken).SetReq(ksApi.UpdateCampaignReq{
			CampaignId:   campaignId,
			AdvertiserId: advertiserId,
			CampaignName: &campaignName,
		}).Do()
		liberr.ErrIsNil(ctx, err, "修改广告计划名称失败")
		if err == nil {
			_, err = dao.KsAdvertiserCampaign.Ctx(ctx).WherePri(campaignId).Update(do.KsAdvertiserCampaign{
				CampaignName: campaignName,
			})
		}
	})
	return
}
