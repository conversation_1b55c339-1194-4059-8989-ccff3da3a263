// ==========================================================================
// GFast自动生成dao internal操作代码。
// 生成日期：2025-08-23 17:40:19
// 生成路径: internal/app/ad/dao/internal/ks_advertiser_strategy_task_campaign.go
// 生成人：cyao
// desc:快手广告搭建-任务-广告计划
// company:云南奇讯科技有限公司
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// KsAdvertiserStrategyTaskCampaignDao is the manager for logic model data accessing and custom defined data operations functions management.
type KsAdvertiserStrategyTaskCampaignDao struct {
	table   string                                  // Table is the underlying table name of the DAO.
	group   string                                  // Group is the database configuration group name of current DAO.
	columns KsAdvertiserStrategyTaskCampaignColumns // Columns is the short type for Columns, which contains all the column names of Table for convenient usage.
}

// KsAdvertiserStrategyTaskCampaignColumns defines and stores column names for table ks_advertiser_strategy_task_campaign.
type KsAdvertiserStrategyTaskCampaignColumns struct {
	TaskCampaignId string // 任务计划ID
	CampaignId     string // 计划ID
	CampaignName   string // 任务计划名称
	TaskId         string // 任务ID
	AdvertiserId   string // 广告主ID
	AdvertiserNick string // 广告主名称
	CampaignData   string // 创建广告计划数据
	Status         string // 状态 等待提交：INIT 已终止：TERMINATED 成功：SUCCESS 失败：ERROR
	CreatedAt      string // 创建时间
	UpdatedAt      string // 更新时间
	DeletedAt      string // 删除时间
}

var ksAdvertiserStrategyTaskCampaignColumns = KsAdvertiserStrategyTaskCampaignColumns{
	TaskCampaignId: "task_campaign_id",
	CampaignId:     "campaign_id",
	CampaignName:   "campaign_name",
	TaskId:         "task_id",
	AdvertiserId:   "advertiser_id",
	AdvertiserNick: "advertiser_nick",
	CampaignData:   "campaign_data",
	Status:         "status",
	CreatedAt:      "created_at",
	UpdatedAt:      "updated_at",
	DeletedAt:      "deleted_at",
}

// NewKsAdvertiserStrategyTaskCampaignDao creates and returns a new DAO object for table data access.
func NewKsAdvertiserStrategyTaskCampaignDao() *KsAdvertiserStrategyTaskCampaignDao {
	return &KsAdvertiserStrategyTaskCampaignDao{
		group:   "default",
		table:   "ks_advertiser_strategy_task_campaign",
		columns: ksAdvertiserStrategyTaskCampaignColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *KsAdvertiserStrategyTaskCampaignDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *KsAdvertiserStrategyTaskCampaignDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *KsAdvertiserStrategyTaskCampaignDao) Columns() KsAdvertiserStrategyTaskCampaignColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *KsAdvertiserStrategyTaskCampaignDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *KsAdvertiserStrategyTaskCampaignDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *KsAdvertiserStrategyTaskCampaignDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
