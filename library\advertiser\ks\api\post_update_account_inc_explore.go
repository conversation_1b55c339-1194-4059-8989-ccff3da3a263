package api

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
)

// UpdateAccountIncExploreService 编辑增量探索配置
type UpdateAccountIncExploreService struct {
	ctx     context.Context
	cfg     *Configuration
	token   string
	Request *UpdateAccountIncExploreReq
}

// UpdateAccountIncExploreReq 请求结构体
type UpdateAccountIncExploreReq struct {
	IncExploreInfo []GwIncExploreDetailDto `json:"inc_explore_info"` // 增量探索配置列表
	AdvertiserId   int64                   `json:"advertiser_id"`    // 账号id
}

func (r *UpdateAccountIncExploreService) SetCfg(cfg *Configuration) *UpdateAccountIncExploreService {
	r.cfg = cfg
	return r
}

func (r *UpdateAccountIncExploreService) SetReq(req UpdateAccountIncExploreReq) *UpdateAccountIncExploreService {
	r.Request = &req
	return r
}

func (r *UpdateAccountIncExploreService) AccessToken(accessToken string) *UpdateAccountIncExploreService {
	r.token = accessToken
	return r
}

func (r *UpdateAccountIncExploreService) Do() (data *KsBaseResp[[]GwIncExploreDetailDto], err error) {
	localBasePath := r.cfg.BasePath
	localVarPath := localBasePath + "/rest/openapi/gw/dsp/account/incExplore/update"
	response, err := r.cfg.HTTPClient.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("Access-Token", r.token).
		SetBody(r.Request).
		SetResult(&KsBaseResp[[]GwIncExploreDetailDto]{}).
		Post(localVarPath)
	if err != nil {
		return nil, err
	}
	resp := new(KsBaseResp[[]GwIncExploreDetailDto])
	// 将 JSON 响应解码到结构体中
	err = json.Unmarshal(response.Body(), &resp)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("/rest/openapi/gw/dsp/account/incExplore/update解析响应出错: %v\n", err))
	}
	if resp.Code == 0 && resp.Data != nil {
		return resp, nil
	} else {
		return resp, errors.New(resp.Message)
	}
}
