package api

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
)

// DeleteAccountIncExploreService 删除增量探索配置
type DeleteAccountIncExploreService struct {
	ctx     context.Context
	cfg     *Configuration
	token   string
	Request *DeleteAccountIncExploreReq
}

// DeleteAccountIncExploreReq 请求结构体
type DeleteAccountIncExploreReq struct {
	DeepConversionType int64 `json:"deep_conversion_type" dc:"深度转化目标类型"`
	OcpxActionType     int64 `json:"ocpx_action_type" dc:"转化目标类型"`
	AdvertiserId       int64 `json:"advertiser_id" dc:"账号id"`
}

func (r *DeleteAccountIncExploreService) SetCfg(cfg *Configuration) *DeleteAccountIncExploreService {
	r.cfg = cfg
	return r
}

func (r *DeleteAccountIncExploreService) SetReq(req DeleteAccountIncExploreReq) *DeleteAccountIncExploreService {
	r.Request = &req
	return r
}

func (r *DeleteAccountIncExploreService) AccessToken(accessToken string) *DeleteAccountIncExploreService {
	r.token = accessToken
	return r
}

func (r *DeleteAccountIncExploreService) Do() (data *KsBaseResp[[]GwIncExploreDetailView], err error) {
	localBasePath := r.cfg.BasePath
	localVarPath := localBasePath + "/rest/openapi/gw/dsp/account/incExplore/delete"
	response, err := r.cfg.HTTPClient.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("Access-Token", r.token).
		SetBody(r.Request).
		SetResult(&KsBaseResp[[]GwIncExploreDetailView]{}).
		Post(localVarPath)
	if err != nil {
		return nil, err
	}
	resp := new(KsBaseResp[[]GwIncExploreDetailView])
	// 将 JSON 响应解码到结构体中
	err = json.Unmarshal(response.Body(), &resp)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("/rest/openapi/gw/dsp/account/incExplore/delete解析响应出错: %v\n", err))
	}
	if resp.Code == 0 && resp.Data != nil {
		return resp, nil
	} else {
		return resp, errors.New(resp.Message)
	}
}
