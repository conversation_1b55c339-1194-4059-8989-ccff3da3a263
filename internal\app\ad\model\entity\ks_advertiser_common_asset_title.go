// ==========================================================================
// GFast自动生成model entity操作代码。
// 生成日期：2025-08-19 10:36:58
// 生成路径: internal/app/ad/model/entity/ks_advertiser_common_asset_title.go
// 生成人：cq
// desc:快手通用资产-标题库
// company:云南奇讯科技有限公司
// ==========================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
)

// KsAdvertiserCommonAssetTitle is the golang structure for table ks_advertiser_common_asset_title.
type KsAdvertiserCommonAssetTitle struct {
	gmeta.Meta         `orm:"table:ks_advertiser_common_asset_title"`
	Id                 int         `orm:"id,primary" json:"id"`                             //
	Title              string      `orm:"title" json:"title"`                               // 标题
	CategoryId         int         `orm:"category_id" json:"categoryId"`                    // 标题分类ID
	UserId             int         `orm:"user_id" json:"userId"`                            // 创建者
	Last3DayCost       float64     `orm:"last_3_day_cost" json:"last3DayCost"`              // 近3日消耗
	Last30DayCost      float64     `orm:"last_30_day_cost" json:"last30DayCost"`            // 近30日消耗
	Last3DayClickRate  float64     `orm:"last_3_day_click_rate" json:"last3DayClickRate"`   // 近3日点击率
	Last30DayClickRate float64     `orm:"last_30_day_click_rate" json:"last30DayClickRate"` // 近30日点击率
	UnitCount          int         `orm:"unit_count" json:"unitCount"`                      // 关联广告组数
	CreatedAt          *gtime.Time `orm:"created_at" json:"createdAt"`                      // 创建时间
	UpdatedAt          *gtime.Time `orm:"updated_at" json:"updatedAt"`                      // 更新时间
	DeletedAt          *gtime.Time `orm:"deleted_at" json:"deletedAt"`                      // 删除时间
}
