// ==========================================================================
// GFast自动生成service操作代码。
// 生成日期：2025-08-22 11:52:17
// 生成路径: internal/app/ad/service/ks_advertiser_strategy_creative.go
// 生成人：cq
// desc:快手策略组-广告创意
// company:云南奇讯科技有限公司
// ==========================================================================

package service

import (
	"context"

	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
)

type IKsAdvertiserStrategyCreative interface {
	List(ctx context.Context, req *model.KsAdvertiserStrategyCreativeSearchReq) (res *model.KsAdvertiserStrategyCreativeSearchRes, err error)
	GetById(ctx context.Context, Id uint64) (res *model.KsAdvertiserStrategyCreativeInfoRes, err error)
	GetInfoById(ctx context.Context, strategyId string, taskId string) (res *model.KsAdvertiserStrategyCreativeInfoRes, err error)
	Add(ctx context.Context, req *model.KsAdvertiserStrategyCreativeAddReq) (err error)
	Edit(ctx context.Context, req *model.KsAdvertiserStrategyCreativeEditReq) (err error)
	Delete(ctx context.Context, Id []uint64) (err error)
}

var localKsAdvertiserStrategyCreative IKsAdvertiserStrategyCreative

func KsAdvertiserStrategyCreative() IKsAdvertiserStrategyCreative {
	if localKsAdvertiserStrategyCreative == nil {
		panic("implement not found for interface IKsAdvertiserStrategyCreative, forgot register?")
	}
	return localKsAdvertiserStrategyCreative
}

func RegisterKsAdvertiserStrategyCreative(i IKsAdvertiserStrategyCreative) {
	localKsAdvertiserStrategyCreative = i
}
