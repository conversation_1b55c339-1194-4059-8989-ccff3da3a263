package api

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
)

// GetFetchAccountListService  批量拉取代理商下账户列表
type GetFetchAccountListService struct {
	ctx     context.Context
	cfg     *Configuration
	token   string
	Request *GetFetchAccountListReq
}

type GetFetchAccountListReq struct {
	AgentId int64 `json:"agent_id"` // 代理商id
	//begin_account_id
	BeginAccountId int `json:"begin_account_id"` // 起始accountId，第一次传0，后续每次传上次请求最后一个account_id
	// batch_size	单批次拉取数量 单次最多查询1000
	BatchSize int `json:"batch_size"`
	//create_time_begin 账户创建时间（开始）
	CreateTimeBegin int64 `json:"create_time_begin"`
	//create_time_end 账户创建时间（结束）
	CreateTimeEnd int64 `json:"create_time_end"`
	//corporation_name 公司名称
	CorporationName string `json:"corporation_name"`
}

type GetFetchAccountListResp struct {
	Code    int                   `json:"code"`    // 状态码
	Message string                `json:"message"` // 返回信息
	Data    *FetchAccountListResp `json:"data"`    // 广告主列表数据
}

// FetchAccountListResp 广告主列表数据
type FetchAccountListResp struct {
	Details    []AccountInfoView `json:"details"`     // 广告主列表
	TotalCount int64             `json:"total_count"` // 总数
}

// AccountInfoView 广告主详情
type AccountInfoView struct {
	ResponsiblePerson     string                `json:"responsible_person"`       // 销售责任人
	UserID                int64                 `json:"user_id"`                  // 快手ID
	AccountID             int64                 `json:"account_id"`               // 广告主ID
	UCType                string                `json:"uc_type"`                  // 账户类型
	PaymentType           string                `json:"payment_type"`             // 付款类型
	AccountName           string                `json:"account_name"`             // 快手昵称
	CreateTime            int64                 `json:"create_time"`              // 创建时间
	Balance               float64               `json:"balance"`                  // 现金余额
	CreditBalance         float64               `json:"credit_balance"`           // 信用账户余额
	ExtendedBalance       float64               `json:"extended_balance"`         // 预留账户余额
	Rebate                int64                 `json:"rebate"`                   // 后返余额
	PreRebate             int64                 `json:"pre_rebate"`               // 前返余额
	ContractRebate        int64                 `json:"contract_rebate"`          // 框返余额
	TotalBalance          int64                 `json:"total_balance"`            // 总余额
	LoLimit               int64                 `json:"lo_limit"`                 // 账户最低余额
	SingleOut             int64                 `json:"single_out"`               // 单次转账金额
	AutoOut               bool                  `json:"auto_out"`                 // 自动转账状态
	BalanceWarn           bool                  `json:"balance_warn"`             // 余额不足提醒
	ProductName           string                `json:"product_name"`             // 产品名称
	FirstCostDay          string                `json:"first_cost_day"`           // 首日消耗日期
	Industry              string                `json:"industry"`                 // 一级行业
	SecondIndustry        string                `json:"second_industry"`          // 二级行业
	Recharged             bool                  `json:"recharged"`                // 是否充值
	CorporationName       string                `json:"corporation_name"`         // 企业名称
	ReviewStatus          int                   `json:"review_status"`            // 审核状态 1-审核中; 2-审核通过; 3-审核拒绝; 0-待提交
	FrozenStatus          int                   `json:"frozen_status"`            // 冻结状态
	TransferAccountStatus bool                  `json:"transfer_account_status"`  // 转账状态
	ChildReviewStatusInfo ChildReviewStatusInfo `json:"child_review_status_info"` // 审核状态信息
	CopyAccount           bool                  `json:"copy_account"`             // 是否为复制账户
	ReviewDetails         []CertReviewDetail    `json:"review_detail"`            // 审核详情
	DirectRebate          int64                 `json:"direct_rebate"`            // 激励余额
	OptimizerOwner        string                `json:"optimizer_owner"`          // 优化师责任人
}

// 子结构体
type ChildReviewStatusInfo struct {
	// 按文档补充字段
	AuthenticationReviewInfo AuthenticationReviewInfo `json:"authentication_review_info"` // 账号认证状态信息
	ContractReviewInfo       ContractReviewInfo       `json:"contract_review_info"`       // 合同签约状态信息
	UserReviewInfo           UserReviewInfo           `json:"user_review_info"`           // 用户审核状态信息

}

type AuthenticationReviewInfo struct {
	AuthenticationStatus int    `json:"authentication_status"` // 账号认证状态 0-无状态, 1-待认证, 2-认证中, 3-认证通过, 4-认证失败, 5-认证失效
	AuthenticationDetail string `json:"authentication_detail"` // 账号认证状态详情
}

type ContractReviewInfo struct {
	ContractReviewStatus int    `json:"contract_review_status"` // 合同签约状态 0-无效, 1-签约中, 2-已签约, 3-已终止, 4-已过期, 5-已撤销, 6-已拒签, 7-已删除, 8-提前终止
	ContractReviewDetail string `json:"contract_review_detail"` // 合同签约状态详情
}

type UserReviewInfo struct {
	UserReviewStatus int    `json:"review_status"` // 用户审核状态 0-未送审, 1-待审核, 2-审核通过, 3-审核拒绝, 4-封禁
	UserReviewDetail string `json:"review_detail"` // 用户审核状态详情
}

// CertReviewDetail 审核详情
type CertReviewDetail struct {
	ID   int    `json:"id"`   // id
	Desc string `json:"desc"` // 描述
}

func (r *GetFetchAccountListService) SetCfg(cfg *Configuration) *GetFetchAccountListService {
	r.cfg = cfg
	return r
}

func (r *GetFetchAccountListService) SetReq(req GetFetchAccountListReq) *GetFetchAccountListService {
	r.Request = &req
	return r
}

func (r *GetFetchAccountListService) AccessToken(accessToken string) *GetFetchAccountListService {
	r.token = accessToken
	return r
}

func (r *GetFetchAccountListService) Do() (data *GetFetchAccountListResp, err error) {
	localBasePath := r.cfg.BasePath
	localVarPath := localBasePath + "/rest/openapi/gw/agent/v1/fetch/account/list"
	response, err := r.cfg.HTTPClient.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("Access-Token", r.token).
		SetBody(r.Request).
		SetResult(&GetFetchAccountListResp{}).
		Post(localVarPath)
	if err != nil {
		return nil, err
	}
	resp := new(GetFetchAccountListResp)
	// 将 JSON 响应解码到结构体中
	err = json.Unmarshal(response.Body(), &resp)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("/rest/openapi/gw/agent/v1/fetch/account/list解析响应出错: %v\n", err))
	}
	if resp.Code == 0 && resp.Data != nil {
		return resp, nil
	} else {
		return resp, errors.New(resp.Message)
	}
}
