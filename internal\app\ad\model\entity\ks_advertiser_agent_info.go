// ==========================================================================
// GFast自动生成model entity操作代码。
// 生成日期：2025-08-12 15:45:06
// 生成路径: internal/app/ad/model/entity/ks_advertiser_agent_info.go
// 生成人：cyao
// desc:快手代理商信息表
// company:云南奇讯科技有限公司
// ==========================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
)

// KsAdvertiserAgentInfo is the golang structure for table ks_advertiser_agent_info.
type KsAdvertiserAgentInfo struct {
	gmeta.Meta         `orm:"table:ks_advertiser_agent_info"`
	Id                 uint64      `orm:"id,primary" json:"id"`                           // 自增主键
	AgentAccountId     int64       `orm:"agent_account_id" json:"agentAccountId"`         // 代理商账户id
	AuthorizeKsAccount int64       `orm:"authorize_ks_account" json:"authorizeKsAccount"` // 授权快手账号
	Owner              int         `orm:"owner" json:"owner"`                             // 归属人员id
	CreatedAt          *gtime.Time `orm:"created_at" json:"createdAt"`                    // 创建时间
	UpdatedAt          *gtime.Time `orm:"updated_at" json:"updatedAt"`                    // 更新时间
	IsShare            int         `orm:"is_share" json:"isShare"`                        // 0非共享1共享
}
