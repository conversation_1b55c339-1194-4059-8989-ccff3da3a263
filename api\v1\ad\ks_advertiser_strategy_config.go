// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2025-08-22 11:51:48
// 生成路径: api/v1/ad/ks_advertiser_strategy_config.go
// 生成人：cq
// desc:快手广告搭建-策略配置相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package ad

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
)

// KsAdvertiserStrategyConfigSearchReq 分页请求参数
type KsAdvertiserStrategyConfigSearchReq struct {
	g.Meta `path:"/list" tags:"快手广告搭建-策略配置" method:"post" summary:"快手广告搭建-策略配置列表"`
	commonApi.Author
	model.KsAdvertiserStrategyConfigSearchReq
}

// KsAdvertiserStrategyConfigSearchRes 列表返回结果
type KsAdvertiserStrategyConfigSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.KsAdvertiserStrategyConfigSearchRes
}

// KsAdvertiserStrategyConfigAddReq 添加操作请求参数
type KsAdvertiserStrategyConfigAddReq struct {
	g.Meta `path:"/add" tags:"快手广告搭建-策略配置" method:"post" summary:"快手广告搭建-策略配置添加"`
	commonApi.Author
	*model.KsAdvertiserStrategyAddReq
}

// KsAdvertiserStrategyConfigAddRes 添加操作返回结果
type KsAdvertiserStrategyConfigAddRes struct {
	commonApi.EmptyRes
}

// KsAdvertiserStrategyConfigEditReq 修改操作请求参数
type KsAdvertiserStrategyConfigEditReq struct {
	g.Meta `path:"/edit" tags:"快手广告搭建-策略配置" method:"put" summary:"快手广告搭建-策略配置修改"`
	commonApi.Author
	*model.KsAdvertiserStrategyEditReq
}

// KsAdvertiserStrategyConfigEditRes 修改操作返回结果
type KsAdvertiserStrategyConfigEditRes struct {
	commonApi.EmptyRes
}

// KsAdvertiserStrategyConfigGetReq 获取一条数据请求
type KsAdvertiserStrategyConfigGetReq struct {
	g.Meta `path:"/get" tags:"快手广告搭建-策略配置" method:"get" summary:"获取快手广告搭建-策略配置信息"`
	commonApi.Author
	StrategyId string `p:"strategyId" dc:"策略组ID"`
	TaskId     string `p:"taskId" dc:"任务ID"`
}

// KsAdvertiserStrategyConfigGetRes 获取一条数据结果
type KsAdvertiserStrategyConfigGetRes struct {
	g.Meta `mime:"application/json"`
	*model.KsAdvertiserStrategyRes
}

// KsAdvertiserStrategyConfigDeleteReq 删除数据请求
type KsAdvertiserStrategyConfigDeleteReq struct {
	g.Meta `path:"/delete" tags:"快手广告搭建-策略配置" method:"post" summary:"删除快手广告搭建-策略配置"`
	commonApi.Author
	StrategyIds []string `p:"strategyIds" v:"required#策略ID必须"`
}

// KsAdvertiserStrategyConfigDeleteRes 删除数据返回
type KsAdvertiserStrategyConfigDeleteRes struct {
	commonApi.EmptyRes
}

// KsAdvertiserStrategyConfigCopyReq 复制策略组请求
type KsAdvertiserStrategyConfigCopyReq struct {
	g.Meta `path:"/copy" tags:"快手广告搭建-策略配置" method:"post" summary:"复制策略组"`
	commonApi.Author
	StrategyId string `p:"strategyId" v:"required#strategyId必须"`
}

// KsAdvertiserStrategyConfigCopyRes 复制策略组结果
type KsAdvertiserStrategyConfigCopyRes struct {
	g.Meta `mime:"application/json"`
}
