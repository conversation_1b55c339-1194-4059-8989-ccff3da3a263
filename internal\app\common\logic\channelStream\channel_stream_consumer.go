// ==========================================================================
// 通用的 Channel Stream 消费者
// 生成日期：2025-01-15
// desc: 通用的Redis Stream消费者，支持多种channel_stream
// ==========================================================================

package channelStream

import (
	"context"
	"fmt"
	commonConsts "github.com/tiger1103/gfast/v3/internal/app/common/consts"
	"math"
	"strings"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gcron"
	"github.com/gogf/gf/v2/os/gctx"
	"github.com/redis/go-redis/v9"
	commonService "github.com/tiger1103/gfast/v3/internal/app/common/service"
)

// StreamConsumerConfig Stream消费者配置
type StreamConsumerConfig struct {
	StreamName    string        // Stream名称
	GroupName     string        // 消费者组名称
	ConsumerName  string        // 消费者名称
	BlockTime     time.Duration // 阻塞时间
	BatchSize     int64         // 批量处理大小
	LockTimeout   time.Duration // 锁超时时间
	MessageField  string        // 消息字段名
	CleanupCron   string        // 清理任务cron表达式
	MaxStreamSize int64         // Stream最大保留消息数
}

// TaskProcessor 任务处理器接口
type TaskProcessor interface {
	ProcessTask(ctx context.Context, taskData []byte) (success bool, err error)
	UpdateTaskStatus(ctx context.Context, taskId string, success bool) error
	GetTaskId(taskData []byte) (string, error)
}

// StreamConsumer Stream消费者
type StreamConsumer struct {
	config    *StreamConsumerConfig
	processor TaskProcessor
	ctx       context.Context
	cancel    context.CancelFunc
}

// NewStreamConsumer 创建新的Stream消费者
func NewStreamConsumer(config *StreamConsumerConfig, processor TaskProcessor) *StreamConsumer {
	ctx, cancel := context.WithCancel(context.Background())
	return &StreamConsumer{
		config:    config,
		processor: processor,
		ctx:       ctx,
		cancel:    cancel,
	}
}

// Start 启动消费者
func (sc *StreamConsumer) Start() {
	// 1. 初始化消费者组
	if err := sc.initConsumerGroup(); err != nil {
		g.Log().Errorf(sc.ctx, "初始化消费者组失败：%v", err)
		return
	}

	// 2. 启动清理任务
	if sc.config.CleanupCron != "" {
		sc.initCleanupTask()
	}

	// 3. 启动消费循环
	go sc.consumeLoop()
}

// Stop 停止消费者
func (sc *StreamConsumer) Stop() {
	sc.cancel()
}

// initConsumerGroup 初始化消费者组
func (sc *StreamConsumer) initConsumerGroup() error {
	_, err := commonService.GetGoRedis().XInfoGroups(sc.ctx, sc.config.StreamName).Result()
	if err != nil {
		if strings.Contains(err.Error(), "ERR no such key") {
			err = commonService.GetGoRedis().XGroupCreateMkStream(sc.ctx,
				sc.config.StreamName,
				sc.config.GroupName,
				"0-0").Err()
			if err != nil {
				return fmt.Errorf("创建消费者组失败：%v", err)
			}
		} else {
			return fmt.Errorf("检查消费者组失败：%v", err)
		}
	}
	return nil
}

// consumeLoop 消费循环
func (sc *StreamConsumer) consumeLoop() {
	for {
		select {
		case <-sc.ctx.Done():
			return
		default:
			// 1. 处理Pending消息
			sc.processPendingMessages()

			// 2. 读取新消息
			sc.processNewMessages()
		}
	}
}

// processNewMessages 处理新消息
func (sc *StreamConsumer) processNewMessages() {
	xStreams, err := commonService.GetGoRedis().XReadGroup(sc.ctx, &redis.XReadGroupArgs{
		Group:    sc.config.GroupName,
		Consumer: sc.config.ConsumerName,
		Streams:  []string{sc.config.StreamName, ">"},
		Count:    sc.config.BatchSize,
		Block:    sc.config.BlockTime,
	}).Result()

	if err != nil && err.Error() != "redis: nil" {
		g.Log().Errorf(sc.ctx, "读取新消息失败：%v", err)
		return
	}

	if len(xStreams) == 0 {
		return
	}

	for _, msg := range xStreams[0].Messages {
		sc.processMessage(msg)
	}
}

// processPendingMessages 处理Pending消息
func (sc *StreamConsumer) processPendingMessages() {
	// 1. 检查Pending消息数量
	pending, err := commonService.GetGoRedis().XPending(sc.ctx,
		sc.config.StreamName,
		sc.config.GroupName).Result()
	if err != nil {
		g.Log().Errorf(sc.ctx, "检查Pending消息失败: %v", err)
		return
	}

	// 2. 如果有Pending消息
	if pending.Count > 0 {
		pendingMsgs, err := commonService.GetGoRedis().XPendingExt(sc.ctx, &redis.XPendingExtArgs{
			Stream:   sc.config.StreamName,
			Group:    sc.config.GroupName,
			Start:    "-",
			End:      "+",
			Count:    sc.config.BatchSize,
			Consumer: "",
		}).Result()
		if err != nil {
			g.Log().Errorf(sc.ctx, "获取Pending消息详情失败: %v", err)
			return
		}

		// 3. 处理每条Pending消息
		for _, msg := range pendingMsgs {
			sc.claimAndProcessMessage(msg.ID)
		}
	}
}

// claimAndProcessMessage 认领并处理消息
func (sc *StreamConsumer) claimAndProcessMessage(messageID string) {
	lockKey := fmt.Sprintf("%s_lock:%s", sc.config.StreamName, messageID)
	ok, _ := commonService.GetGoRedis().SetNX(sc.ctx, lockKey, 1, sc.config.LockTimeout).Result()
	if !ok {
		return
	}
	defer commonService.GetGoRedis().Del(sc.ctx, lockKey)

	// 认领消息
	claimedMsgs, err := commonService.GetGoRedis().XClaim(sc.ctx, &redis.XClaimArgs{
		Stream:   sc.config.StreamName,
		Group:    sc.config.GroupName,
		Consumer: sc.config.ConsumerName,
		MinIdle:  10 * time.Second,
		Messages: []string{messageID},
	}).Result()
	if err != nil {
		g.Log().Errorf(sc.ctx, "认领消息失败(ID:%s): %v", messageID, err)
		return
	}

	// 处理认领到的消息
	for _, claimedMsg := range claimedMsgs {
		sc.processMessage(claimedMsg)
	}
}

// processMessage 处理单个消息
func (sc *StreamConsumer) processMessage(msg redis.XMessage) {
	lockKey := fmt.Sprintf("%s_lock:%s", sc.config.StreamName, msg.ID)
	ok, _ := commonService.GetGoRedis().SetNX(sc.ctx, lockKey, 1, sc.config.LockTimeout).Result()
	if !ok {
		return
	}
	defer commonService.GetGoRedis().Del(sc.ctx, lockKey)

	// 获取消息数据
	taskDataStr, exists := msg.Values[sc.config.MessageField]
	if !exists {
		g.Log().Errorf(sc.ctx, "消息中缺少字段 %s", sc.config.MessageField)
		return
	}

	taskData := []byte(taskDataStr.(string))

	// 获取任务ID
	taskId, err := sc.processor.GetTaskId(taskData)
	if err != nil {
		g.Log().Errorf(sc.ctx, "获取任务ID失败：%v", err)
		return
	}

	// 最大重试次数
	maxRetries := 3
	retryKey := fmt.Sprintf("%s_retry:%s", sc.config.StreamName, taskId)
	// 获取当前重试次数
	retryCount, _ := commonService.GetGoRedis().Get(sc.ctx, retryKey).Int()

	// 处理任务
	success, err := sc.processor.ProcessTask(sc.ctx, taskData)
	if err != nil {
		// 检查是否为需要重试的错误
		if strings.Contains(err.Error(), commonConsts.RateLimitRetryNeeded) && retryCount < maxRetries {
			// 增加重试次数并设置过期时间
			commonService.GetGoRedis().Set(sc.ctx, retryKey, retryCount+1, 24*time.Hour)
			g.Log().Infof(sc.ctx, "任务需要重试(TaskId:%s, 重试次数:%d/%d)：%v", taskId, retryCount+1, maxRetries, err)
			backoffDelay := time.Duration(math.Pow(2, float64(retryCount))) * 1 * time.Second
			time.Sleep(backoffDelay)
			return
		} else {
			g.Log().Errorf(sc.ctx, "处理任务失败(TaskId:%s)：%v", taskId, err)
		}
	}

	// 更新任务状态
	if err := sc.processor.UpdateTaskStatus(sc.ctx, taskId, success); err != nil {
		g.Log().Errorf(sc.ctx, "更新任务状态失败(TaskId:%s)：%v", taskId, err)
	}

	// 确认消息
	commonService.GetGoRedis().XAck(sc.ctx,
		sc.config.StreamName,
		sc.config.GroupName,
		msg.ID)
}

// initCleanupTask 初始化清理任务
func (sc *StreamConsumer) initCleanupTask() {
	taskName := fmt.Sprintf("Cleanup_%s_StreamData", sc.config.StreamName)
	_, _ = gcron.Add(gctx.New(), sc.config.CleanupCron, func(ctx context.Context) {
		lockKey := fmt.Sprintf("cleanup_%s_task_lock", sc.config.StreamName)
		ok, _ := commonService.GetGoRedis().SetNX(ctx, lockKey, 1, 10*time.Minute).Result()
		if !ok {
			return
		}

		defer func() {
			_, err := commonService.GetGoRedis().Del(ctx, lockKey).Result()
			if err != nil {
				g.Log().Errorf(ctx, "释放分布式锁失败: %v", err)
			}
		}()

		err := sc.CleanupStreamData(ctx)
		if err != nil {
			g.Log().Errorf(ctx, "执行Stream数据清理失败: %v", err)
		}
	}, taskName)
}

// CleanupStreamData 清理Stream数据
func (sc *StreamConsumer) CleanupStreamData(ctx context.Context) error {
	maxStreamSize := sc.config.MaxStreamSize
	if maxStreamSize <= 0 {
		maxStreamSize = 1000
	}

	// 获取消费者组信息，检查是否所有消息都已处理
	groups, err := commonService.GetGoRedis().XInfoGroups(ctx, sc.config.StreamName).Result()
	if err != nil {
		return fmt.Errorf("获取消费者组信息失败: %v", err)
	}

	allProcessed := true
	for _, group := range groups {
		if group.Pending > 0 {
			allProcessed = false
			break
		}
	}

	if allProcessed {
		_, err = commonService.GetGoRedis().XTrimMaxLen(ctx, sc.config.StreamName, maxStreamSize).Result()
		if err != nil {
			return fmt.Errorf("清理Stream数据失败 (保留最近%d条): %v", maxStreamSize, err)
		}
		g.Log().Infof(ctx, "成功清理Stream数据，保留最近%d条消息", maxStreamSize)
	}

	return nil
}
