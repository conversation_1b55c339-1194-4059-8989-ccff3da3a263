// ==========================================================================
// GFast自动生成router操作代码。
// 生成日期：2025-08-12 15:45:08
// 生成路径: internal/app/ad/router/ks_advertiser_fund.go
// 生成人：cyao
// desc:广告主资金信息
// company:云南奇讯科技有限公司
// ==========================================================================

package router

import (
	"context"

	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/tiger1103/gfast/v3/internal/app/ad/controller"
)

func (router *Router) BindKsAdvertiserFundController(ctx context.Context, group *ghttp.RouterGroup) {
	group.Group("/ksAdvertiserFund", func(group *ghttp.RouterGroup) {
		group.Bind(
			controller.KsAdvertiserFund,
		)
	})
}
