// ==========================================================================
// GFast自动生成controller操作代码。
// 生成日期：2025-08-20 11:00:01
// 生成路径: internal/app/ad/controller/ks_advertiser_program_creative_report.go
// 生成人：cyao
// desc:创意数据
// company:云南奇讯科技有限公司
// ==========================================================================

package controller

import (
	"context"

	"github.com/tiger1103/gfast/v3/api/v1/ad"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	systemController "github.com/tiger1103/gfast/v3/internal/app/system/controller"
)

type ksAdvertiserProgramCreativeReportController struct {
	systemController.BaseController
}

var KsAdvertiserProgramCreativeReport = new(ksAdvertiserProgramCreativeReportController)

// List 列表
func (c *ksAdvertiserProgramCreativeReportController) List(ctx context.Context, req *ad.KsAdvertiserProgramCreativeReportSearchReq) (res *ad.KsAdvertiserProgramCreativeReportSearchRes, err error) {
	res = new(ad.KsAdvertiserProgramCreativeReportSearchRes)
	res.KsAdvertiserProgramCreativeReportSearchRes, err = service.KsAdvertiserProgramCreativeReport().List(ctx, &req.KsAdvertiserProgramCreativeReportSearchReq)
	return
}

// KsAdvertiserProgramCreativeReportPull
func (c *ksAdvertiserProgramCreativeReportController) KsAdvertiserProgramCreativeReportPull(ctx context.Context, req *ad.KsAdvertiserProgramCreativeReportPullReq) (res *ad.KsAdvertiserProgramCreativeReportPullRes, err error) {
	err = service.KsAdvertiserProgramCreativeReport().Pull(ctx, req.StartTime, req.EndTime)
	return
}

// Get 获取快手创意数据
func (c *ksAdvertiserProgramCreativeReportController) Get(ctx context.Context, req *ad.KsAdvertiserProgramCreativeReportGetReq) (res *ad.KsAdvertiserProgramCreativeReportGetRes, err error) {
	res = new(ad.KsAdvertiserProgramCreativeReportGetRes)
	res.KsAdvertiserProgramCreativeReportInfoRes, err = service.KsAdvertiserProgramCreativeReport().GetByStatDate(ctx, req.CreativeId, req.StateDate)
	return
}

// Add 添加创意数据
func (c *ksAdvertiserProgramCreativeReportController) Add(ctx context.Context, req *ad.KsAdvertiserProgramCreativeReportAddReq) (res *ad.KsAdvertiserProgramCreativeReportAddRes, err error) {
	err = service.KsAdvertiserProgramCreativeReport().Add(ctx, req.KsAdvertiserProgramCreativeReportAddReq)
	return
}

// Edit 修改创意数据
func (c *ksAdvertiserProgramCreativeReportController) Edit(ctx context.Context, req *ad.KsAdvertiserProgramCreativeReportEditReq) (res *ad.KsAdvertiserProgramCreativeReportEditRes, err error) {
	err = service.KsAdvertiserProgramCreativeReport().Edit(ctx, req.KsAdvertiserProgramCreativeReportEditReq)
	return
}

// Delete 删除创意数据
func (c *ksAdvertiserProgramCreativeReportController) Delete(ctx context.Context, req *ad.KsAdvertiserProgramCreativeReportDeleteReq) (res *ad.KsAdvertiserProgramCreativeReportDeleteRes, err error) {
	err = service.KsAdvertiserProgramCreativeReport().Delete(ctx, req.CreativeIds)
	return
}
