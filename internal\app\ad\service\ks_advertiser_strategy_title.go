// ==========================================================================
// GFast自动生成service操作代码。
// 生成日期：2025-08-22 11:52:42
// 生成路径: internal/app/ad/service/ks_advertiser_strategy_title.go
// 生成人：cq
// desc:快手策略组-文案
// company:云南奇讯科技有限公司
// ==========================================================================

package service

import (
	"context"

	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
)

type IKsAdvertiserStrategyTitle interface {
	List(ctx context.Context, req *model.KsAdvertiserStrategyTitleSearchReq) (res *model.KsAdvertiserStrategyTitleSearchRes, err error)
	GetById(ctx context.Context, Id uint64) (res *model.KsAdvertiserStrategyTitleInfoRes, err error)
	GetInfoById(ctx context.Context, strategyId string, taskId string) (res *model.KsAdvertiserStrategyTitleInfoRes, err error)
	Add(ctx context.Context, req *model.KsAdvertiserStrategyTitleAddReq) (err error)
	Edit(ctx context.Context, req *model.KsAdvertiserStrategyTitleEditReq) (err error)
	Delete(ctx context.Context, Id []uint64) (err error)
}

var localKsAdvertiserStrategyTitle IKsAdvertiserStrategyTitle

func KsAdvertiserStrategyTitle() IKsAdvertiserStrategyTitle {
	if localKsAdvertiserStrategyTitle == nil {
		panic("implement not found for interface IKsAdvertiserStrategyTitle, forgot register?")
	}
	return localKsAdvertiserStrategyTitle
}

func RegisterKsAdvertiserStrategyTitle(i IKsAdvertiserStrategyTitle) {
	localKsAdvertiserStrategyTitle = i
}
