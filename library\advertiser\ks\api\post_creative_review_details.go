package api

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
)

// CreativeReviewDetailsService  Ks re  获取创意审核详情接口
type CreativeReviewDetailsService struct {
	ctx     context.Context
	cfg     *Configuration
	token   string
	Request *CreativeReviewDetailsReq
}

type CreativeReviewDetailsReq struct {
	//advertiser_id accountId
	AdvertiserId int64 `json:"advertiser_id"`
	//ids 自定义创意时为创意ID 程序化创意时为组ID
	Ids []int64 `json:"ids"`
	//creative_mold 创意类型
	CreativeMold int `json:"creative_mold"`
}

type CreativeReviewDetailsResp struct {
	Code    int                          `json:"code"`
	Message string                       `json:"message"`
	Data    []*CreativeReviewDetailsData `json:"data"`
}

type CreativeReviewDetailsData struct {
	//社区审核状态 1审核中 2审核通过 3审核拒绝 5基本通过审核(指视频通过审核)
	CommunityReviewStatus int `json:"community_review_status"`
	// 创意ID
	Id int64 `json:"id"`
	//商业审核状态 1审核中 2审核通过 3审核拒绝 5基本通过审核(指视频通过审核)
	ReviewStatus int `json:"review_status"`
}

//{
//"review_reason": null,
//"adv_creative_community_review_detail": null,
//"limiting_reason": [],
//"community_review_status": 2,
//"id": ************,
//"review_status": 2
//}

func (r *CreativeReviewDetailsService) SetCfg(cfg *Configuration) *CreativeReviewDetailsService {
	r.cfg = cfg
	return r
}

func (r *CreativeReviewDetailsService) SetReq(req CreativeReviewDetailsReq) *CreativeReviewDetailsService {
	r.Request = &req
	return r
}

func (r *CreativeReviewDetailsService) AccessToken(accessToken string) *CreativeReviewDetailsService {
	r.token = accessToken
	return r
}

func (r *CreativeReviewDetailsService) Do() (data *CreativeReviewDetailsResp, err error) {
	localBasePath := r.cfg.BasePath
	localVarPath := localBasePath + "/rest/openapi/gw/dsp/creative/element/reviewDetails"
	response, err := r.cfg.HTTPClient.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("Access-Token", r.token).
		SetBody(r.Request).
		SetResult(&CreativeReviewDetailsResp{}).
		Post(localVarPath)
	if err != nil {
		return nil, err
	}
	resp := new(CreativeReviewDetailsResp)
	// 将 JSON 响应解码到结构体中
	err = json.Unmarshal(response.Body(), &resp)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("/rest/openapi/gw/dsp/creative/element/reviewDetails解析响应出错: %v\n", err))
	}
	if resp.Code == 0 && resp.Data != nil {
		return resp, nil
	} else {
		return resp, errors.New(resp.Message)
	}
}
