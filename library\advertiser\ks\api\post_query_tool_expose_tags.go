package api

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
)

// QueryToolExposeTagsService 查询创意推荐理由
type QueryToolExposeTagsService struct {
	ctx     context.Context
	cfg     *Configuration
	token   string
	Request *ActionBarTextListRequest
}

// ToolExposeTagsListRequest 查询创意推荐理由
type ToolExposeTagsListRequest struct {
	// AdvertiserID 广告主id
	AdvertiserID uint64 `json:"advertiser_id,omitempty"`
	// CampaignType 计划类型; 必填 2 - 提升应用安装;3 - 获取电商下单;4 - 推广品牌活动;5 - 收集销售线索;13 - 小店商品推广；14：直播推广
	CampaignType int `json:"campaign_type,omitempty"`
}

// ToolExposeTagsListResponse 查询创意推荐理由 API Response
type ToolExposeTagsListResponse struct {
	// ExposeTagView 创意推荐理由
	ExposeTagView []ExposeTagView `json:"details,omitempty"`
}

type ExposeTagView struct {
	Text string `json:"text,omitempty"` // 推荐理由
}

func (r *QueryToolExposeTagsService) SetCfg(cfg *Configuration) *QueryToolExposeTagsService {
	r.cfg = cfg
	return r
}

func (r *QueryToolExposeTagsService) SetReq(req ActionBarTextListRequest) *QueryToolExposeTagsService {
	r.Request = &req
	return r
}

func (r *QueryToolExposeTagsService) AccessToken(accessToken string) *QueryToolExposeTagsService {
	r.token = accessToken
	return r
}

func (r *QueryToolExposeTagsService) Do() (data *KsBaseResp[ToolExposeTagsListResponse], err error) {
	localBasePath := r.cfg.BasePath
	localVarPath := localBasePath + "/rest/openapi/v1/tool/expose_tags/list"
	response, err := r.cfg.HTTPClient.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("Access-Token", r.token).
		SetBody(r.Request).
		SetResult(&KsBaseResp[ToolExposeTagsListResponse]{}).
		Post(localVarPath)
	if err != nil {
		return nil, err
	}
	resp := new(KsBaseResp[ToolExposeTagsListResponse])
	// 将 JSON 响应解码到结构体中
	err = json.Unmarshal(response.Body(), &resp)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("/rest/openapi/v1/tool/expose_tags/list解析响应出错: %v\n", err))
	}
	if resp.Code == 0 && resp.Data != nil {
		return resp, nil
	} else {
		return resp, errors.New(resp.Message)
	}
}
