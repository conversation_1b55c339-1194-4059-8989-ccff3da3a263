// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2025-08-12 10:45:09
// 生成路径: api/v1/ad/ks_advertiser_unit_report_data.go
// 生成人：cq
// desc:快手广告组报表数据相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package ad

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
)

// KsAdvertiserUnitReportDataSearchReq 分页请求参数
type KsAdvertiserUnitReportDataSearchReq struct {
	g.Meta `path:"/list" tags:"快手广告组报表数据" method:"post" summary:"快手广告组报表数据列表"`
	commonApi.Author
	model.KsAdvertiserUnitReportDataSearchReq
}

// KsAdvertiserUnitReportDataSearchRes 列表返回结果
type KsAdvertiserUnitReportDataSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.KsAdvertiserUnitReportDataSearchRes
}

// KsAdvertiserUnitReportDataAddReq 添加操作请求参数
type KsAdvertiserUnitReportDataAddReq struct {
	g.Meta `path:"/add" tags:"快手广告组报表数据" method:"post" summary:"快手广告组报表数据添加"`
	commonApi.Author
	*model.KsAdvertiserUnitReportDataAddReq
}

// KsAdvertiserUnitReportDataAddRes 添加操作返回结果
type KsAdvertiserUnitReportDataAddRes struct {
	commonApi.EmptyRes
}

// KsAdvertiserUnitReportDataTaskReq 分页请求参数
type KsAdvertiserUnitReportDataTaskReq struct {
	g.Meta `path:"/task" tags:"快手广告组报表数据" method:"post" summary:"快手广告组报表数据任务"`
	commonApi.Author
	model.KsAdvertiserUnitReportDataSearchReq
}

// KsAdvertiserUnitReportDataTaskRes 列表返回结果
type KsAdvertiserUnitReportDataTaskRes struct {
	g.Meta `mime:"application/json"`
	*model.KsAdvertiserUnitReportDataSearchRes
}
