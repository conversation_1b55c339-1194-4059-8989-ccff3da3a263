// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2025-08-22 11:52:17
// 生成路径: api/v1/ad/ks_advertiser_strategy_creative.go
// 生成人：cq
// desc:快手策略组-广告创意相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package ad

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
)

// KsAdvertiserStrategyCreativeSearchReq 分页请求参数
type KsAdvertiserStrategyCreativeSearchReq struct {
	g.Meta `path:"/list" tags:"快手策略组-广告创意" method:"get" summary:"快手策略组-广告创意列表"`
	commonApi.Author
	model.KsAdvertiserStrategyCreativeSearchReq
}

// KsAdvertiserStrategyCreativeSearchRes 列表返回结果
type KsAdvertiserStrategyCreativeSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.KsAdvertiserStrategyCreativeSearchRes
}

// KsAdvertiserStrategyCreativeAddReq 添加操作请求参数
type KsAdvertiserStrategyCreativeAddReq struct {
	g.Meta `path:"/add" tags:"快手策略组-广告创意" method:"post" summary:"快手策略组-广告创意添加"`
	commonApi.Author
	*model.KsAdvertiserStrategyCreativeAddReq
}

// KsAdvertiserStrategyCreativeAddRes 添加操作返回结果
type KsAdvertiserStrategyCreativeAddRes struct {
	commonApi.EmptyRes
}

// KsAdvertiserStrategyCreativeEditReq 修改操作请求参数
type KsAdvertiserStrategyCreativeEditReq struct {
	g.Meta `path:"/edit" tags:"快手策略组-广告创意" method:"put" summary:"快手策略组-广告创意修改"`
	commonApi.Author
	*model.KsAdvertiserStrategyCreativeEditReq
}

// KsAdvertiserStrategyCreativeEditRes 修改操作返回结果
type KsAdvertiserStrategyCreativeEditRes struct {
	commonApi.EmptyRes
}

// KsAdvertiserStrategyCreativeGetReq 获取一条数据请求
type KsAdvertiserStrategyCreativeGetReq struct {
	g.Meta `path:"/get" tags:"快手策略组-广告创意" method:"get" summary:"获取快手策略组-广告创意信息"`
	commonApi.Author
	Id uint64 `p:"id" v:"required#主键必须"` //通过主键获取
}

// KsAdvertiserStrategyCreativeGetRes 获取一条数据结果
type KsAdvertiserStrategyCreativeGetRes struct {
	g.Meta `mime:"application/json"`
	*model.KsAdvertiserStrategyCreativeInfoRes
}

// KsAdvertiserStrategyCreativeDeleteReq 删除数据请求
type KsAdvertiserStrategyCreativeDeleteReq struct {
	g.Meta `path:"/delete" tags:"快手策略组-广告创意" method:"delete" summary:"删除快手策略组-广告创意"`
	commonApi.Author
	Ids []uint64 `p:"ids" v:"required#主键必须"` //通过主键删除
}

// KsAdvertiserStrategyCreativeDeleteRes 删除数据返回
type KsAdvertiserStrategyCreativeDeleteRes struct {
	commonApi.EmptyRes
}
