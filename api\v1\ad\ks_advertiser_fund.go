// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2025-08-12 15:45:08
// 生成路径: api/v1/ad/ks_advertiser_fund.go
// 生成人：cyao
// desc:广告主资金信息相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package ad

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
)

// KsAdvertiserFundSearchReq 分页请求参数
type KsAdvertiserFundSearchReq struct {
	g.Meta `path:"/list" tags:"广告主资金信息" method:"get" summary:"广告主资金信息列表"`
	commonApi.Author
	model.KsAdvertiserFundSearchReq
}

// KsAdvertiserFundSearchRes 列表返回结果
type KsAdvertiserFundSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.KsAdvertiserFundSearchRes
}

// KsAdvertiserFundAddReq 添加操作请求参数
type KsAdvertiserFundAddReq struct {
	g.Meta `path:"/add" tags:"广告主资金信息" method:"post" summary:"广告主资金信息添加"`
	commonApi.Author
	*model.KsAdvertiserFundAddReq
}

// KsAdvertiserFundAddRes 添加操作返回结果
type KsAdvertiserFundAddRes struct {
	commonApi.EmptyRes
}

// KsAdvertiserFundEditReq 修改操作请求参数
type KsAdvertiserFundEditReq struct {
	g.Meta `path:"/edit" tags:"广告主资金信息" method:"put" summary:"广告主资金信息修改"`
	commonApi.Author
	*model.KsAdvertiserFundEditReq
}

// KsAdvertiserFundEditRes 修改操作返回结果
type KsAdvertiserFundEditRes struct {
	commonApi.EmptyRes
}

// KsAdvertiserFundGetReq 获取一条数据请求
type KsAdvertiserFundGetReq struct {
	g.Meta `path:"/get" tags:"广告主资金信息" method:"get" summary:"获取广告主资金信息信息"`
	commonApi.Author
	AdvertiserId int64 `p:"advertiserId" v:"required#主键必须"` //通过主键获取
}

// KsAdvertiserFundGetRes 获取一条数据结果
type KsAdvertiserFundGetRes struct {
	g.Meta `mime:"application/json"`
	*model.KsAdvertiserFundInfoRes
}

// KsAdvertiserFundDeleteReq 删除数据请求
type KsAdvertiserFundDeleteReq struct {
	g.Meta `path:"/delete" tags:"广告主资金信息" method:"delete" summary:"删除广告主资金信息"`
	commonApi.Author
	AdvertiserIds []int64 `p:"advertiserIds" v:"required#主键必须"` //通过主键删除
}

// KsAdvertiserFundDeleteRes 删除数据返回
type KsAdvertiserFundDeleteRes struct {
	commonApi.EmptyRes
}
