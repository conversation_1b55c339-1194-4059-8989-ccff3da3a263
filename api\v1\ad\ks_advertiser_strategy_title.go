// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2025-08-22 11:52:42
// 生成路径: api/v1/ad/ks_advertiser_strategy_title.go
// 生成人：cq
// desc:快手策略组-文案相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package ad

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
)

// KsAdvertiserStrategyTitleSearchReq 分页请求参数
type KsAdvertiserStrategyTitleSearchReq struct {
	g.Meta `path:"/list" tags:"快手策略组-文案" method:"get" summary:"快手策略组-文案列表"`
	commonApi.Author
	model.KsAdvertiserStrategyTitleSearchReq
}

// KsAdvertiserStrategyTitleSearchRes 列表返回结果
type KsAdvertiserStrategyTitleSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.KsAdvertiserStrategyTitleSearchRes
}

// KsAdvertiserStrategyTitleAddReq 添加操作请求参数
type KsAdvertiserStrategyTitleAddReq struct {
	g.Meta `path:"/add" tags:"快手策略组-文案" method:"post" summary:"快手策略组-文案添加"`
	commonApi.Author
	*model.KsAdvertiserStrategyTitleAddReq
}

// KsAdvertiserStrategyTitleAddRes 添加操作返回结果
type KsAdvertiserStrategyTitleAddRes struct {
	commonApi.EmptyRes
}

// KsAdvertiserStrategyTitleEditReq 修改操作请求参数
type KsAdvertiserStrategyTitleEditReq struct {
	g.Meta `path:"/edit" tags:"快手策略组-文案" method:"put" summary:"快手策略组-文案修改"`
	commonApi.Author
	*model.KsAdvertiserStrategyTitleEditReq
}

// KsAdvertiserStrategyTitleEditRes 修改操作返回结果
type KsAdvertiserStrategyTitleEditRes struct {
	commonApi.EmptyRes
}

// KsAdvertiserStrategyTitleGetReq 获取一条数据请求
type KsAdvertiserStrategyTitleGetReq struct {
	g.Meta `path:"/get" tags:"快手策略组-文案" method:"get" summary:"获取快手策略组-文案信息"`
	commonApi.Author
	Id uint64 `p:"id" v:"required#主键必须"` //通过主键获取
}

// KsAdvertiserStrategyTitleGetRes 获取一条数据结果
type KsAdvertiserStrategyTitleGetRes struct {
	g.Meta `mime:"application/json"`
	*model.KsAdvertiserStrategyTitleInfoRes
}

// KsAdvertiserStrategyTitleDeleteReq 删除数据请求
type KsAdvertiserStrategyTitleDeleteReq struct {
	g.Meta `path:"/delete" tags:"快手策略组-文案" method:"delete" summary:"删除快手策略组-文案"`
	commonApi.Author
	Ids []uint64 `p:"ids" v:"required#主键必须"` //通过主键删除
}

// KsAdvertiserStrategyTitleDeleteRes 删除数据返回
type KsAdvertiserStrategyTitleDeleteRes struct {
	commonApi.EmptyRes
}
