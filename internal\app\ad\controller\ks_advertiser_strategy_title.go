// ==========================================================================
// GFast自动生成controller操作代码。
// 生成日期：2025-08-22 11:52:42
// 生成路径: internal/app/ad/controller/ks_advertiser_strategy_title.go
// 生成人：cq
// desc:快手策略组-文案
// company:云南奇讯科技有限公司
// ==========================================================================

package controller

import (
	"context"

	"github.com/tiger1103/gfast/v3/api/v1/ad"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	systemController "github.com/tiger1103/gfast/v3/internal/app/system/controller"
)

type ksAdvertiserStrategyTitleController struct {
	systemController.BaseController
}

var KsAdvertiserStrategyTitle = new(ksAdvertiserStrategyTitleController)

// List 列表
func (c *ksAdvertiserStrategyTitleController) List(ctx context.Context, req *ad.KsAdvertiserStrategyTitleSearchReq) (res *ad.KsAdvertiserStrategyTitleSearchRes, err error) {
	res = new(ad.KsAdvertiserStrategyTitleSearchRes)
	res.KsAdvertiserStrategyTitleSearchRes, err = service.KsAdvertiserStrategyTitle().List(ctx, &req.KsAdvertiserStrategyTitleSearchReq)
	return
}

// Get 获取快手策略组-文案
func (c *ksAdvertiserStrategyTitleController) Get(ctx context.Context, req *ad.KsAdvertiserStrategyTitleGetReq) (res *ad.KsAdvertiserStrategyTitleGetRes, err error) {
	res = new(ad.KsAdvertiserStrategyTitleGetRes)
	res.KsAdvertiserStrategyTitleInfoRes, err = service.KsAdvertiserStrategyTitle().GetById(ctx, req.Id)
	return
}

// Add 添加快手策略组-文案
func (c *ksAdvertiserStrategyTitleController) Add(ctx context.Context, req *ad.KsAdvertiserStrategyTitleAddReq) (res *ad.KsAdvertiserStrategyTitleAddRes, err error) {
	err = service.KsAdvertiserStrategyTitle().Add(ctx, req.KsAdvertiserStrategyTitleAddReq)
	return
}

// Edit 修改快手策略组-文案
func (c *ksAdvertiserStrategyTitleController) Edit(ctx context.Context, req *ad.KsAdvertiserStrategyTitleEditReq) (res *ad.KsAdvertiserStrategyTitleEditRes, err error) {
	err = service.KsAdvertiserStrategyTitle().Edit(ctx, req.KsAdvertiserStrategyTitleEditReq)
	return
}

// Delete 删除快手策略组-文案
func (c *ksAdvertiserStrategyTitleController) Delete(ctx context.Context, req *ad.KsAdvertiserStrategyTitleDeleteReq) (res *ad.KsAdvertiserStrategyTitleDeleteRes, err error) {
	err = service.KsAdvertiserStrategyTitle().Delete(ctx, req.Ids)
	return
}
