// ==========================================================================
// GFast自动生成dao internal操作代码。
// 生成日期：2025-08-14 14:09:04
// 生成路径: internal/app/ad/dao/internal/ks_advertiser_account_info.go
// 生成人：cyao
// desc:快手广告账户表格
// company:云南奇讯科技有限公司
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// KsAdvertiserAccountInfoDao is the manager for logic model data accessing and custom defined data operations functions management.
type KsAdvertiserAccountInfoDao struct {
	table   string                         // Table is the underlying table name of the DAO.
	group   string                         // Group is the database configuration group name of current DAO.
	columns KsAdvertiserAccountInfoColumns // Columns is the short type for Columns, which contains all the column names of Table for convenient usage.
}

// KsAdvertiserAccountInfoColumns defines and stores column names for table ks_advertiser_account_info.
type KsAdvertiserAccountInfoColumns struct {
	AccountId             string // 广告主ID
	AgentAccountId        string // 代理商账户id
	AuthorizeKsAccount    string // 授权快手账号(代理商)
	UserId                string // 快手账户ID
	AccountName           string // 快手账户名称
	ResponsiblePerson     string // 销售责任人
	UcType                string // 账户类型
	PaymentType           string // 付款类型
	Balance               string // 现金余额
	CreditBalance         string // 信用账户余额
	ExtendedBalance       string // 预留账户余额
	Rebate                string // 后返余额
	PreRebate             string // 前返余额
	ContractRebate        string // 框返余额
	TotalBalance          string // 总余额
	LoLimit               string // 账户最低余额
	SingleOut             string // 单次转账金额
	AutoOut               string // 自动转账状态
	BalanceWarn           string // 余额不足提醒
	ProductName           string // 产品名称
	FirstCostDay          string // 首日消耗日期
	Industry              string // 一级行业
	SecondIndustry        string // 二级行业
	Recharged             string // 是否充值
	CorporationName       string // 企业名称
	ReviewStatus          string // 审核状态 1-审核中; 2-审核通过; 3-审核拒绝; 0-待提交
	FrozenStatus          string // 冻结状态
	TransferAccountStatus string // 转账状态
	ChildReviewStatusInfo string // 审核状态信息（嵌套结构体）
	CopyAccount           string // 是否为复制账户
	ReviewDetail          string // 审核详情（数组嵌套结构体）
	DirectRebate          string // 激励余额
	OptimizerOwner        string // 优化师责任人
	AccountAutoManage     string // 账户智投开关
	DayBudget             string // 单日预算 单位：厘
	Remark                string // 备注
	AuthSource            string // 授权来源（如代理商账户授权 1 广告主授权 2）
	AuthStatus            string // 授权状态（0:待授权, 1:已授权）
	DeliveryStatus        string // 投放状态（0:停用, 1:启用）
	Owner                 string // 归属人员
	DeliveryType          string // 投放方式（0:默认, 1:优先效果）暂时先没用到
	EffectFirst           string // 优先效果策略（1:开启, 其他:未开启）暂时先没用到
	CreateTime            string // 创建时间
	CreatedAt             string // 创建时间记录产生时间
	UpdatedAt             string // 更新时间
}

var ksAdvertiserAccountInfoColumns = KsAdvertiserAccountInfoColumns{
	AccountId:             "account_id",
	AgentAccountId:        "agent_account_id",
	AuthorizeKsAccount:    "authorize_ks_account",
	UserId:                "user_id",
	AccountName:           "account_name",
	ResponsiblePerson:     "responsible_person",
	UcType:                "uc_type",
	PaymentType:           "payment_type",
	Balance:               "balance",
	CreditBalance:         "credit_balance",
	ExtendedBalance:       "extended_balance",
	Rebate:                "rebate",
	PreRebate:             "pre_rebate",
	ContractRebate:        "contract_rebate",
	TotalBalance:          "total_balance",
	LoLimit:               "lo_limit",
	SingleOut:             "single_out",
	AutoOut:               "auto_out",
	BalanceWarn:           "balance_warn",
	ProductName:           "product_name",
	FirstCostDay:          "first_cost_day",
	Industry:              "industry",
	SecondIndustry:        "second_industry",
	Recharged:             "recharged",
	CorporationName:       "corporation_name",
	ReviewStatus:          "review_status",
	FrozenStatus:          "frozen_status",
	TransferAccountStatus: "transfer_account_status",
	ChildReviewStatusInfo: "child_review_status_info",
	CopyAccount:           "copy_account",
	ReviewDetail:          "review_detail",
	DirectRebate:          "direct_rebate",
	OptimizerOwner:        "optimizer_owner",
	AccountAutoManage:     "account_auto_manage",
	DayBudget:             "day_budget",
	Remark:                "remark",
	AuthSource:            "auth_source",
	AuthStatus:            "auth_status",
	DeliveryStatus:        "delivery_status",
	Owner:                 "owner",
	DeliveryType:          "delivery_type",
	EffectFirst:           "effect_first",
	CreateTime:            "create_time",
	CreatedAt:             "created_at",
	UpdatedAt:             "updated_at",
}

// NewKsAdvertiserAccountInfoDao creates and returns a new DAO object for table data access.
func NewKsAdvertiserAccountInfoDao() *KsAdvertiserAccountInfoDao {
	return &KsAdvertiserAccountInfoDao{
		group:   "default",
		table:   "ks_advertiser_account_info",
		columns: ksAdvertiserAccountInfoColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *KsAdvertiserAccountInfoDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *KsAdvertiserAccountInfoDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *KsAdvertiserAccountInfoDao) Columns() KsAdvertiserAccountInfoColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *KsAdvertiserAccountInfoDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *KsAdvertiserAccountInfoDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *KsAdvertiserAccountInfoDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
