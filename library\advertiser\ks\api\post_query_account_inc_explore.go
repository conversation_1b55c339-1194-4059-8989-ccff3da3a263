package api

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
)

// QueryAccountIncExploreService 增量探索配置列表
type QueryAccountIncExploreService struct {
	ctx     context.Context
	cfg     *Configuration
	token   string
	Request *QueryAccountIncExploreReq
}

type QueryAccountIncExploreReq struct {
	AdvertiserId int64 `json:"advertiser_id"` // 广告主id
}

// GwIncExploreDetailView 增量探索配置详情结构体
type GwIncExploreDetailView struct {
	OcpxActionType            int     `json:"ocpx_action_type" dc:"转化目标类型"`
	OcpxActionTypeName        string  `json:"ocpx_action_type_name" dc:"转化目标类型名称"`
	DeepConversionType        int     `json:"deep_conversion_type" dc:"深度转化目标类型"`
	DeepConversionTypeName    string  `json:"deep_conversion_type_name" dc:"深度转化目标类型名称"`
	AutoMode                  int     `json:"auto_mode" dc:"是否开启uax: 1-开启, 2-未开启"`
	ExploreBudget             float64 `json:"explore_budget" dc:"探索预算，单位（元）"`
	Status                    int     `json:"status" dc:"探索状态: 1-探索未开始, 2-探索中, 3-探索结束, 4-探索暂停"`
	IncExploreTimeType        int     `json:"inc_explore_time_type" dc:"增量时间类型: 1-长期生效, 2-当日生效, 3-立即生效6小时"`
	FirstStartTime            int64   `json:"first_start_time" dc:"第一次开始时间 (Unix毫秒时间戳)"`
	CreateTime                int64   `json:"create_time" dc:"创建时间 (Unix毫秒时间戳)"`
	AdDspCost                 float64 `json:"ad_dsp_cost" dc:"消耗"`
	ConversionCost            float64 `json:"conversion_cost" dc:"转化成本"`
	AdShow                    int64   `json:"ad_show" dc:"暴露量"`
	AdItemClick               int64   `json:"ad_item_click" dc:"点击量"`
	ConversionCnt             int64   `json:"conversion_cnt" dc:"转化数"`
	Ltv                       float64 `json:"ltv" dc:"付费金额,单位元"`
	Roi                       float64 `json:"roi" dc:"ROI"`
	MiniGameIaaPurchaseAmount float64 `json:"mini_game_iaa_purchase_amount" dc:"IAA广告变现LTV,单位元"`
	MiniGameIaaRoi            float64 `json:"mini_game_iaa_roi" dc:"IAA广告变现ROI"`
	KeyAction                 int64   `json:"key_action" dc:"关键行为数"`
	KeyActionCost             float64 `json:"key_action_cost" dc:"关键行为成本"`
}

func (r *QueryAccountIncExploreService) SetCfg(cfg *Configuration) *QueryAccountIncExploreService {
	r.cfg = cfg
	return r
}

func (r *QueryAccountIncExploreService) SetReq(req QueryAccountIncExploreReq) *QueryAccountIncExploreService {
	r.Request = &req
	return r
}

func (r *QueryAccountIncExploreService) AccessToken(accessToken string) *QueryAccountIncExploreService {
	r.token = accessToken
	return r
}

func (r *QueryAccountIncExploreService) Do() (data *KsBaseResp[[]GwIncExploreDetailView], err error) {
	localBasePath := r.cfg.BasePath
	localVarPath := localBasePath + "/rest/openapi/gw/dsp/account/incExplore/list"
	response, err := r.cfg.HTTPClient.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("Access-Token", r.token).
		SetBody(r.Request).
		SetResult(&KsBaseResp[[]GwIncExploreDetailView]{}).
		Post(localVarPath)
	if err != nil {
		return nil, err
	}
	resp := new(KsBaseResp[[]GwIncExploreDetailView])
	// 将 JSON 响应解码到结构体中
	err = json.Unmarshal(response.Body(), &resp)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("/rest/openapi/gw/dsp/account/incExplore/list解析响应出错: %v\n", err))
	}
	if resp.Code == 0 && resp.Data != nil {
		return resp, nil
	} else {
		return resp, errors.New(resp.Message)
	}
}
