// ==========================================================================
// GFast自动生成controller操作代码。
// 生成日期：2025-08-22 11:52:52
// 生成路径: internal/app/ad/controller/ks_advertiser_strategy_unit.go
// 生成人：cq
// desc:快手策略组-广告组
// company:云南奇讯科技有限公司
// ==========================================================================

package controller

import (
	"context"

	"github.com/tiger1103/gfast/v3/api/v1/ad"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	systemController "github.com/tiger1103/gfast/v3/internal/app/system/controller"
)

type ksAdvertiserStrategyUnitController struct {
	systemController.BaseController
}

var KsAdvertiserStrategyUnit = new(ksAdvertiserStrategyUnitController)

// List 列表
func (c *ksAdvertiserStrategyUnitController) List(ctx context.Context, req *ad.KsAdvertiserStrategyUnitSearchReq) (res *ad.KsAdvertiserStrategyUnitSearchRes, err error) {
	res = new(ad.KsAdvertiserStrategyUnitSearchRes)
	res.KsAdvertiserStrategyUnitSearchRes, err = service.KsAdvertiserStrategyUnit().List(ctx, &req.KsAdvertiserStrategyUnitSearchReq)
	return
}

// Get 获取快手策略组-广告组
func (c *ksAdvertiserStrategyUnitController) Get(ctx context.Context, req *ad.KsAdvertiserStrategyUnitGetReq) (res *ad.KsAdvertiserStrategyUnitGetRes, err error) {
	res = new(ad.KsAdvertiserStrategyUnitGetRes)
	res.KsAdvertiserStrategyUnitInfoRes, err = service.KsAdvertiserStrategyUnit().GetById(ctx, req.Id)
	return
}

// Add 添加快手策略组-广告组
func (c *ksAdvertiserStrategyUnitController) Add(ctx context.Context, req *ad.KsAdvertiserStrategyUnitAddReq) (res *ad.KsAdvertiserStrategyUnitAddRes, err error) {
	err = service.KsAdvertiserStrategyUnit().Add(ctx, req.KsAdvertiserStrategyUnitAddReq)
	return
}

// Edit 修改快手策略组-广告组
func (c *ksAdvertiserStrategyUnitController) Edit(ctx context.Context, req *ad.KsAdvertiserStrategyUnitEditReq) (res *ad.KsAdvertiserStrategyUnitEditRes, err error) {
	err = service.KsAdvertiserStrategyUnit().Edit(ctx, req.KsAdvertiserStrategyUnitEditReq)
	return
}

// Delete 删除快手策略组-广告组
func (c *ksAdvertiserStrategyUnitController) Delete(ctx context.Context, req *ad.KsAdvertiserStrategyUnitDeleteReq) (res *ad.KsAdvertiserStrategyUnitDeleteRes, err error) {
	err = service.KsAdvertiserStrategyUnit().Delete(ctx, req.Ids)
	return
}
