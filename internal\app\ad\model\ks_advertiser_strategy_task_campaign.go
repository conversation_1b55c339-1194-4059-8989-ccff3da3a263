// ==========================================================================
// GFast自动生成model操作代码。
// 生成日期：2025-08-23 17:40:19
// 生成路径: internal/app/ad/model/ks_advertiser_strategy_task_campaign.go
// 生成人：cyao
// desc:快手广告搭建-任务-广告计划
// company:云南奇讯科技有限公司
// ==========================================================================

package model

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
	comModel "github.com/tiger1103/gfast/v3/internal/app/common/model"
)

// KsAdvertiserStrategyTaskCampaignInfoRes is the golang structure for table ks_advertiser_strategy_task_campaign.
type KsAdvertiserStrategyTaskCampaignInfoRes struct {
	gmeta.Meta     `orm:"table:ks_advertiser_strategy_task_campaign"`
	TaskCampaignId string      `orm:"task_campaign_id,primary" json:"taskCampaignId" dc:"任务计划ID"`                // 任务计划ID
	CampaignId     int64       `orm:"campaign_id" json:"campaignId" dc:"计划ID"`                                   // 计划ID
	CampaignName   string      `orm:"campaign_name" json:"campaignName" dc:"任务计划名称"`                             // 任务计划名称
	TaskId         string      `orm:"task_id" json:"taskId" dc:"任务ID"`                                           // 任务ID
	AdvertiserId   string      `orm:"advertiser_id" json:"advertiserId" dc:"广告主ID"`                              // 广告主ID
	AdvertiserNick string      `orm:"advertiser_nick" json:"advertiserNick" dc:"广告主名称"`                          // 广告主名称
	CampaignData   string      `orm:"campaign_data" json:"campaignData" dc:"创建广告计划数据"`                           // 创建广告计划数据
	Status         string      `orm:"status" json:"status" dc:"状态 等待提交：INIT 已终止：TERMINATED 成功：SUCCESS 失败：ERROR"` // 状态 等待提交：INIT 已终止：TERMINATED 成功：SUCCESS 失败：ERROR
	CreatedAt      *gtime.Time `orm:"created_at" json:"createdAt" dc:"创建时间"`                                     // 创建时间
	UpdatedAt      *gtime.Time `orm:"updated_at" json:"updatedAt" dc:"更新时间"`                                     // 更新时间
	DeletedAt      *gtime.Time `orm:"deleted_at" json:"deletedAt" dc:"删除时间"`                                     // 删除时间
}

type KsAdvertiserStrategyTaskCampaignListRes struct {
	TaskCampaignId string      `json:"taskCampaignId" dc:"任务计划ID"`
	CampaignId     int64       `json:"campaignId" dc:"计划ID"`
	CampaignName   string      `json:"campaignName" dc:"任务计划名称"`
	TaskId         string      `json:"taskId" dc:"任务ID"`
	AdvertiserId   string      `json:"advertiserId" dc:"广告主ID"`
	AdvertiserNick string      `json:"advertiserNick" dc:"广告主名称"`
	CampaignData   string      `json:"campaignData" dc:"创建广告计划数据"`
	Status         string      `json:"status" dc:"状态 等待提交：INIT 已终止：TERMINATED 成功：SUCCESS 失败：ERROR"`
	CreatedAt      *gtime.Time `json:"createdAt" dc:"创建时间"`
}

// KsAdvertiserStrategyTaskCampaignSearchReq 分页请求参数
type KsAdvertiserStrategyTaskCampaignSearchReq struct {
	comModel.PageReq
	TaskCampaignId string `p:"taskCampaignId" dc:"任务计划ID"`                                             //任务计划ID
	CampaignId     string `p:"campaignId" v:"campaignId@integer#计划ID需为整数" dc:"计划ID"`                   //计划ID
	CampaignName   string `p:"campaignName" dc:"任务计划名称"`                                               //任务计划名称
	TaskId         string `p:"taskId" dc:"任务ID"`                                                       //任务ID
	AdvertiserId   string `p:"advertiserId" dc:"广告主ID"`                                                //广告主ID
	AdvertiserNick string `p:"advertiserNick" dc:"广告主名称"`                                              //广告主名称
	CampaignData   string `p:"campaignData" dc:"创建广告计划数据"`                                             //创建广告计划数据
	Status         string `p:"status" dc:"状态 等待提交：INIT 已终止：TERMINATED 成功：SUCCESS 失败：ERROR"`            //状态 等待提交：INIT 已终止：TERMINATED 成功：SUCCESS 失败：ERROR
	CreatedAt      string `p:"createdAt" v:"createdAt@datetime#创建时间需为YYYY-MM-DD hh:mm:ss格式" dc:"创建时间"` //创建时间
}

// KsAdvertiserStrategyTaskCampaignSearchRes 列表返回结果
type KsAdvertiserStrategyTaskCampaignSearchRes struct {
	comModel.ListRes
	List []*KsAdvertiserStrategyTaskCampaignListRes `json:"list"`
}

// KsAdvertiserStrategyTaskCampaignAddReq 添加操作请求参数
type KsAdvertiserStrategyTaskCampaignAddReq struct {
	TaskCampaignId string `p:"taskCampaignId" v:"required#主键ID不能为空" dc:"任务计划ID"`
	CampaignId     int64  `p:"campaignId"  dc:"计划ID"`
	CampaignName   string `p:"campaignName" v:"required#任务计划名称不能为空" dc:"任务计划名称"`
	TaskId         string `p:"taskId"  dc:"任务ID"`
	AdvertiserId   string `p:"advertiserId"  dc:"广告主ID"`
	AdvertiserNick string `p:"advertiserNick"  dc:"广告主名称"`
	CampaignData   string `p:"campaignData"  dc:"创建广告计划数据"`
	Status         string `p:"status" v:"required#状态 等待提交：INIT 已终止：TERMINATED 成功：SUCCESS 失败：ERROR不能为空" dc:"状态 等待提交：INIT 已终止：TERMINATED 成功：SUCCESS 失败：ERROR"`
}

// KsAdvertiserStrategyTaskCampaignEditReq 修改操作请求参数
type KsAdvertiserStrategyTaskCampaignEditReq struct {
	TaskCampaignId string `p:"taskCampaignId" v:"required#主键ID不能为空" dc:"任务计划ID"`
	CampaignId     int64  `p:"campaignId"  dc:"计划ID"`
	CampaignName   string `p:"campaignName" v:"required#任务计划名称不能为空" dc:"任务计划名称"`
	TaskId         string `p:"taskId"  dc:"任务ID"`
	AdvertiserId   string `p:"advertiserId"  dc:"广告主ID"`
	AdvertiserNick string `p:"advertiserNick"  dc:"广告主名称"`
	CampaignData   string `p:"campaignData"  dc:"创建广告计划数据"`
	Status         string `p:"status" v:"required#状态 等待提交：INIT 已终止：TERMINATED 成功：SUCCESS 失败：ERROR不能为空" dc:"状态 等待提交：INIT 已终止：TERMINATED 成功：SUCCESS 失败：ERROR"`
}
