// ==========================================================================
// GFast自动生成model操作代码。
// 生成日期：2025-08-22 11:52:52
// 生成路径: internal/app/ad/model/ks_advertiser_strategy_unit.go
// 生成人：cq
// desc:快手策略组-广告组
// company:云南奇讯科技有限公司
// ==========================================================================

package model

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
	commonConsts "github.com/tiger1103/gfast/v3/internal/app/common/consts"
	comModel "github.com/tiger1103/gfast/v3/internal/app/common/model"
	ksApi "github.com/tiger1103/gfast/v3/library/advertiser/ks/api"
)

// KsAdvertiserStrategyUnitInfoRes is the golang structure for table ks_advertiser_strategy_unit.
type KsAdvertiserStrategyUnitInfoRes struct {
	gmeta.Meta                `orm:"table:ks_advertiser_strategy_unit"`
	Id                        uint64                                   `orm:"id,primary" json:"id" dc:"主键ID"`                                                                                                    // 主键ID
	StrategyId                string                                   `orm:"strategy_id" json:"strategyId" dc:"策略组ID"`                                                                                          // 策略组ID
	TaskId                    string                                   `orm:"task_id" json:"taskId" dc:"任务ID"`                                                                                                   // 任务ID
	SceneCategory             string                                   `orm:"scene_category" json:"sceneCategory" dc:"投放版位 0：快手主站"`                                                                              // 投放版位 0：快手主站
	SceneId                   []int                                    `orm:"scene_id" json:"sceneId" dc:"资源位置数组"`                                                                                               // 资源位置数组
	KsUserId                  int64                                    `orm:"ks_user_id" json:"ksUserId" dc:"快手号"`                                                                                               // 快手号
	ShortPlayAllocationMethod commonConsts.KsShortPlayAllocationMethod `orm:"short_play_allocation_method" json:"shortPlayAllocationMethod" dc:"短剧分配方式 全部相同：SAME 按账户分配：ADVERTISER 按广告计划分配：CAMPAIGN 按广告组分配：UNIT"` // 短剧分配方式 全部相同：SAME 按账户分配：ADVERTISER 按广告计划分配：CAMPAIGN 按广告组分配：UNIT
	ShortPlayData             []*ShortPlayData                         `orm:"short_play_data" json:"shortPlayData" dc:"选择短剧与集数"`                                                                                 // 选择短剧与集数
	ProductAllocationMethod   commonConsts.KsProductAllocationMethod   `orm:"product_allocation_method" json:"productAllocationMethod" dc:"产品分配方式 全部相同：SAME 按账户分配：ADVERTISER 按广告计划分配：CAMPAIGN 按广告组分配：UNIT"`      // 产品分配方式 全部相同：SAME 按账户分配：ADVERTISER 按广告计划分配：CAMPAIGN 按广告组分配：UNIT
	ProductData               []*ProductData                           `orm:"product_data" json:"productData" dc:"关联商品"`                                                                                         // 关联商品
	QuickSearch               int                                      `orm:"quick_search" json:"quickSearch" dc:"搜索快投开关 0：不开启（默认填充）；1：开启搜索快投"`                                                                  // 搜索快投开关 0：不开启（默认填充）；1：开启搜索快投
	TargetExplore             int                                      `orm:"target_explore" json:"targetExplore" dc:"搜索人群探索 0：不开启；1：开启"`                                                                        // 搜索人群探索 0：不开启；1：开启
	NegativeWordParam         NegativeWordParam                        `orm:"negative_word_param" json:"negativeWordParam" dc:"搜索广告否词"`                                                                          // 搜索广告否词
	BeginTime                 string                                   `orm:"begin_time" json:"beginTime" dc:"投放开始时间 格式为 yyyy-MM-dd"`                                                                            // 投放开始时间 格式为 yyyy-MM-dd
	EndTime                   string                                   `orm:"end_time" json:"endTime" dc:"投放结束时间 格式为 yyyy-MM-dd"`                                                                                // 投放结束时间 格式为 yyyy-MM-dd
	ScheduleTime              string                                   `orm:"schedule_time" json:"scheduleTime" dc:"投放时间段 24*7位字符串"`                                                                             // 投放时间段 24*7位字符串
	DayBudget                 float64                                  `orm:"day_budget" json:"dayBudget" dc:"日预算 不限传0"`                                                                                         // 日预算 不限传0
	OcpxActionType            int                                      `orm:"ocpx_action_type" json:"ocpxActionType" dc:"转化目标（优化目标）"`                                                                            // 转化目标（优化目标）
	BidType                   int                                      `orm:"bid_type" json:"bidType" dc:"计费方式（优化目标出价类型） 2：CPC；10：OCPM；12:MCB最大转化"`                                                              // 计费方式（优化目标出价类型） 2：CPC；10：OCPM；12:MCB最大转化
	BidAllocationMethod       commonConsts.KsBidAllocationMethod       `orm:"bid_allocation_method" json:"bidAllocationMethod" dc:"出价分配规则 全部相同：SAME 按账户分配：ADVERTISER"`                                           // 出价分配规则 全部相同：SAME 按账户分配：ADVERTISER
	BidData                   []*BidData                               `orm:"bid_data" json:"bidData" dc:"出价信息"`                                                                                                 // 出价信息
	UnitName                  string                                   `orm:"unit_name" json:"unitName" dc:"广告组名称"`                                                                                              // 广告组名称
	PutStatus                 int                                      `orm:"put_status" json:"putStatus" dc:"广告组默认状态 1：广告组投放中；2：广告组暂停投放"`                                                                       // 广告组默认状态 1：广告组投放中；2：广告组暂停投放
	CreatedAt                 *gtime.Time                              `orm:"created_at" json:"createdAt" dc:"创建时间"`                                                                                             // 创建时间
	UpdatedAt                 *gtime.Time                              `orm:"updated_at" json:"updatedAt" dc:"更新时间"`                                                                                             // 更新时间
	DeletedAt                 *gtime.Time                              `orm:"deleted_at" json:"deletedAt" dc:"删除时间"`                                                                                             // 删除时间
}

type KsAdvertiserStrategyUnitListRes struct {
	Id                        uint64                                   `json:"id" dc:"主键ID"`
	StrategyId                string                                   `json:"strategyId" dc:"策略组ID"`
	TaskId                    string                                   `json:"taskId" dc:"任务ID"`
	SceneCategory             string                                   `json:"sceneCategory" dc:"投放版位 0：快手主站"`
	SceneId                   []int                                    `json:"sceneId" dc:"资源位置数组"`
	KsUserId                  int64                                    `json:"ksUserId" dc:"快手号"`
	ShortPlayAllocationMethod commonConsts.KsShortPlayAllocationMethod `json:"shortPlayAllocationMethod" dc:"短剧分配方式 全部相同：SAME 按账户分配：ADVERTISER 按广告计划分配：CAMPAIGN 按广告组分配：UNIT"`
	ShortPlayData             []*ShortPlayData                         `json:"shortPlayData" dc:"选择短剧与集数"`
	ProductAllocationMethod   commonConsts.KsProductAllocationMethod   `json:"productAllocationMethod" dc:"产品分配方式 全部相同：SAME 按账户分配：ADVERTISER 按广告计划分配：CAMPAIGN 按广告组分配：UNIT"`
	ProductData               []*ProductData                           `json:"productData" dc:"关联商品"`
	QuickSearch               int                                      `json:"quickSearch" dc:"搜索快投开关 0：不开启（默认填充）；1：开启搜索快投"`
	TargetExplore             int                                      `json:"targetExplore" dc:"搜索人群探索 0：不开启；1：开启"`
	NegativeWordParam         NegativeWordParam                        `json:"negativeWordParam" dc:"搜索广告否词"`
	BeginTime                 string                                   `json:"beginTime" dc:"投放开始时间 格式为 yyyy-MM-dd"`
	EndTime                   string                                   `json:"endTime" dc:"投放结束时间 格式为 yyyy-MM-dd"`
	ScheduleTime              string                                   `json:"scheduleTime" dc:"投放时间段 24*7位字符串"`
	DayBudget                 float64                                  `json:"dayBudget" dc:"日预算 不限传0"`
	OcpxActionType            int                                      `json:"ocpxActionType" dc:"转化目标（优化目标）"`
	BidType                   int                                      `json:"bidType" dc:"计费方式（优化目标出价类型） 2：CPC；10：OCPM；12:MCB最大转化"`
	BidAllocationMethod       commonConsts.KsBidAllocationMethod       `json:"bidAllocationMethod" dc:"出价分配规则 全部相同：SAME 按账户分配：ADVERTISER"`
	BidData                   []*BidData                               `json:"bidData" dc:"出价信息"`
	UnitName                  string                                   `json:"unitName" dc:"广告组名称"`
	PutStatus                 int                                      `json:"putStatus" dc:"广告组默认状态 1：广告组投放中；2：广告组暂停投放"`
	CreatedAt                 *gtime.Time                              `json:"createdAt" dc:"创建时间"`
}

// KsAdvertiserStrategyUnitSearchReq 分页请求参数
type KsAdvertiserStrategyUnitSearchReq struct {
	comModel.PageReq
	Id                        string                                   `p:"id" dc:"主键ID"`                                                                                                         //主键ID
	StrategyId                string                                   `p:"strategyId" dc:"策略组ID"`                                                                                                //策略组ID
	TaskId                    string                                   `p:"taskId" dc:"任务ID"`                                                                                                     //任务ID
	SceneCategory             string                                   `p:"sceneCategory" dc:"投放版位 0：快手主站"`                                                                                       //投放版位 0：快手主站
	SceneId                   []int                                    `p:"sceneId" dc:"资源位置数组"`                                                                                                  //资源位置数组
	KsUserId                  string                                   `p:"ksUserId" v:"ksUserId@integer#快手号需为整数" dc:"快手号"`                                                                       //快手号
	ShortPlayAllocationMethod commonConsts.KsShortPlayAllocationMethod `p:"shortPlayAllocationMethod" dc:"短剧分配方式 全部相同：SAME 按账户分配：ADVERTISER 按广告计划分配：CAMPAIGN 按广告组分配：UNIT"`                        //短剧分配方式 全部相同：SAME 按账户分配：ADVERTISER 按广告计划分配：CAMPAIGN 按广告组分配：UNIT
	ShortPlayData             []*ShortPlayData                         `p:"shortPlayData" dc:"选择短剧与集数"`                                                                                           //选择短剧与集数
	ProductAllocationMethod   commonConsts.KsProductAllocationMethod   `p:"productAllocationMethod" dc:"产品分配方式 全部相同：SAME 按账户分配：ADVERTISER 按广告计划分配：CAMPAIGN 按广告组分配：UNIT"`                          //产品分配方式 全部相同：SAME 按账户分配：ADVERTISER 按广告计划分配：CAMPAIGN 按广告组分配：UNIT
	ProductData               []*ProductData                           `p:"productData" dc:"关联商品"`                                                                                                //关联商品
	QuickSearch               string                                   `p:"quickSearch" v:"quickSearch@integer#搜索快投开关 0：不开启（默认填充）；1：开启搜索快投需为整数" dc:"搜索快投开关 0：不开启（默认填充）；1：开启搜索快投"`                 //搜索快投开关 0：不开启（默认填充）；1：开启搜索快投
	TargetExplore             string                                   `p:"targetExplore" v:"targetExplore@integer#搜索人群探索 0：不开启；1：开启需为整数" dc:"搜索人群探索 0：不开启；1：开启"`                                 //搜索人群探索 0：不开启；1：开启
	NegativeWordParam         NegativeWordParam                        `p:"negativeWordParam" dc:"搜索广告否词"`                                                                                        //搜索广告否词
	BeginTime                 string                                   `p:"beginTime" dc:"投放开始时间 格式为 yyyy-MM-dd"`                                                                                 //投放开始时间 格式为 yyyy-MM-dd
	EndTime                   string                                   `p:"endTime" dc:"投放结束时间 格式为 yyyy-MM-dd"`                                                                                   //投放结束时间 格式为 yyyy-MM-dd
	ScheduleTime              string                                   `p:"scheduleTime" dc:"投放时间段 24*7位字符串"`                                                                                     //投放时间段 24*7位字符串
	DayBudget                 string                                   `p:"dayBudget" v:"dayBudget@float#日预算 不限传0需为浮点数" dc:"日预算 不限传0"`                                                            //日预算 不限传0
	OcpxActionType            string                                   `p:"ocpxActionType" v:"ocpxActionType@integer#转化目标（优化目标）需为整数" dc:"转化目标（优化目标）"`                                             //转化目标（优化目标）
	BidType                   string                                   `p:"bidType" v:"bidType@integer#计费方式（优化目标出价类型） 2：CPC；10：OCPM；12:MCB最大转化需为整数" dc:"计费方式（优化目标出价类型） 2：CPC；10：OCPM；12:MCB最大转化"` //计费方式（优化目标出价类型） 2：CPC；10：OCPM；12:MCB最大转化
	BidAllocationMethod       commonConsts.KsBidAllocationMethod       `p:"bidAllocationMethod" dc:"出价分配规则 全部相同：SAME 按账户分配：ADVERTISER"`                                                           //出价分配规则 全部相同：SAME 按账户分配：ADVERTISER
	BidData                   []*BidData                               `p:"bidData" dc:"出价信息"`                                                                                                    //出价信息
	UnitName                  string                                   `p:"unitName" dc:"广告组名称"`                                                                                                  //广告组名称
	PutStatus                 string                                   `p:"putStatus" v:"putStatus@integer#广告组默认状态 1：广告组投放中；2：广告组暂停投放需为整数" dc:"广告组默认状态 1：广告组投放中；2：广告组暂停投放"`                       //广告组默认状态 1：广告组投放中；2：广告组暂停投放
	CreatedAt                 string                                   `p:"createdAt" v:"createdAt@datetime#创建时间需为YYYY-MM-DD hh:mm:ss格式" dc:"创建时间"`                                               //创建时间
}

// KsAdvertiserStrategyUnitSearchRes 列表返回结果
type KsAdvertiserStrategyUnitSearchRes struct {
	comModel.ListRes
	List []*KsAdvertiserStrategyUnitListRes `json:"list"`
}

// KsAdvertiserStrategyUnitAddReq 添加操作请求参数
type KsAdvertiserStrategyUnitAddReq struct {
	StrategyId                string                                   `p:"-" json:"-" dc:"策略组ID"`
	TaskId                    string                                   `p:"-" json:"-" dc:"任务ID"`
	SceneCategory             string                                   `p:"sceneCategory" json:"sceneCategory"  dc:"投放版位 0：快手主站"`
	SceneId                   []int                                    `p:"sceneId" json:"sceneId"  dc:"资源位置数组"`
	KsUserId                  int64                                    `p:"ksUserId" json:"ksUserId"  dc:"快手号"`
	ShortPlayAllocationMethod commonConsts.KsShortPlayAllocationMethod `p:"shortPlayAllocationMethod"  json:"shortPlayAllocationMethod" dc:"短剧分配方式 全部相同：SAME 按账户分配：ADVERTISER 按广告计划分配：CAMPAIGN 按广告组分配：UNIT"`
	ShortPlayData             []*ShortPlayData                         `p:"shortPlayData"  json:"shortPlayData" dc:"选择短剧与集数"`
	ProductAllocationMethod   commonConsts.KsProductAllocationMethod   `p:"productAllocationMethod" json:"productAllocationMethod"  dc:"产品分配方式 全部相同：SAME 按账户分配：ADVERTISER 按广告计划分配：CAMPAIGN 按广告组分配：UNIT"`
	ProductData               []*ProductData                           `p:"productData" json:"productData"  dc:"关联商品"`
	QuickSearch               int                                      `p:"quickSearch" json:"quickSearch" dc:"搜索快投开关 0：不开启（默认填充）；1：开启搜索快投"`
	TargetExplore             int                                      `p:"targetExplore" json:"targetExplore"  dc:"搜索人群探索 0：不开启；1：开启"`
	NegativeWordParam         NegativeWordParam                        `p:"negativeWordParam" json:"negativeWordParam"  dc:"搜索广告否词"`
	BeginTime                 string                                   `p:"beginTime" json:"beginTime"  dc:"投放开始时间 格式为 yyyy-MM-dd"`
	EndTime                   string                                   `p:"endTime" json:"endTime"  dc:"投放结束时间 格式为 yyyy-MM-dd"`
	ScheduleTime              string                                   `p:"scheduleTime" json:"scheduleTime"  dc:"投放时间段 24*7位字符串"`
	DayBudget                 float64                                  `p:"dayBudget" json:"dayBudget" dc:"日预算 不限传0"`
	OcpxActionType            int                                      `p:"ocpxActionType" json:"ocpxActionType"  dc:"转化目标（优化目标）"`
	// DeepConversionType 深度转化目标
	// 通过接口「/rest/openapi/gw/dsp/v1/ocpx/deepTypes」获取可以选择深度转化目标；3:付费，7:次日留存，10:完件, 11:授信；13:添加购物车；14:提交订单；15:购买；44：有效线索；92：付费 roi；181：激活后24H次日留存。
	//DeepConversionType  int                                `json:"deep_conversion_type,omitempty"`
	BidType             int                                `p:"bidType" json:"bidType"  dc:"计费方式（优化目标出价类型） 2：CPC；10：OCPM；12:MCB最大转化"`
	BidAllocationMethod commonConsts.KsBidAllocationMethod `p:"bidAllocationMethod" json:"bidAllocationMethod"  dc:"出价分配规则 全部相同：SAME 按账户分配：ADVERTISER"`
	BidData             []*BidData                         `p:"bidData" json:"bidData"  dc:"出价信息"`
	UnitName            string                             `p:"unitName" json:"unitName" dc:"广告组名称"`
	PutStatus           int                                `p:"putStatus" json:"putStatus" dc:"广告组默认状态 1：广告组投放中；2：广告组暂停投放"`
}

// KsAdvertiserStrategyUnitEditReq 修改操作请求参数
type KsAdvertiserStrategyUnitEditReq struct {
	StrategyId                string                                   `p:"strategyId"  dc:"策略组ID"`
	TaskId                    string                                   `p:"taskId"  dc:"任务ID"`
	SceneCategory             string                                   `p:"sceneCategory"  dc:"投放版位 0：快手主站"`
	SceneId                   []int                                    `p:"sceneId"  dc:"资源位置数组"`
	KsUserId                  int64                                    `p:"ksUserId"  dc:"快手号"`
	ShortPlayAllocationMethod commonConsts.KsShortPlayAllocationMethod `p:"shortPlayAllocationMethod"  dc:"短剧分配方式 全部相同：SAME 按账户分配：ADVERTISER 按广告计划分配：CAMPAIGN 按广告组分配：UNIT"`
	ShortPlayData             []*ShortPlayData                         `p:"shortPlayData"  dc:"选择短剧与集数"`
	ProductAllocationMethod   commonConsts.KsProductAllocationMethod   `p:"productAllocationMethod"  dc:"产品分配方式 全部相同：SAME 按账户分配：ADVERTISER 按广告计划分配：CAMPAIGN 按广告组分配：UNIT"`
	ProductData               []*ProductData                           `p:"productData"  dc:"关联商品"`
	QuickSearch               int                                      `p:"quickSearch"  dc:"搜索快投开关 0：不开启（默认填充）；1：开启搜索快投"`
	TargetExplore             int                                      `p:"targetExplore"  dc:"搜索人群探索 0：不开启；1：开启"`
	NegativeWordParam         NegativeWordParam                        `p:"negativeWordParam"  dc:"搜索广告否词"`
	BeginTime                 string                                   `p:"beginTime"  dc:"投放开始时间 格式为 yyyy-MM-dd"`
	EndTime                   string                                   `p:"endTime"  dc:"投放结束时间 格式为 yyyy-MM-dd"`
	ScheduleTime              string                                   `p:"scheduleTime"  dc:"投放时间段 24*7位字符串"`
	DayBudget                 float64                                  `p:"dayBudget"  dc:"日预算 不限传0"`
	OcpxActionType            int                                      `p:"ocpxActionType"  dc:"转化目标（优化目标）"`
	BidType                   int                                      `p:"bidType"  dc:"计费方式（优化目标出价类型） 2：CPC；10：OCPM；12:MCB最大转化"`
	BidAllocationMethod       commonConsts.KsBidAllocationMethod       `p:"bidAllocationMethod"  dc:"出价分配规则 全部相同：SAME 按账户分配：ADVERTISER"`
	BidData                   []*BidData                               `p:"bidData"  dc:"出价信息"`
	UnitName                  string                                   `p:"unitName" v:"required#广告组名称不能为空" dc:"广告组名称"`
	PutStatus                 int                                      `p:"putStatus" v:"required#广告组默认状态 1：广告组投放中；2：广告组暂停投放不能为空" dc:"广告组默认状态 1：广告组投放中；2：广告组暂停投放"`
}

type ShortPlayData struct {
	AdvertiserId      int64            `p:"advertiser_id" json:"advertiser_id"`
	ShortPlayInfoList []*ShortPlayInfo `p:"short_play_info_list" json:"short_play_info_list"`
}

type ShortPlayInfo struct {
	Id            int64  `json:"id,omitempty"`
	AdvertiserId  int64  `json:"advertiser_id,omitempty"`
	KsUserId      string `json:"ks_user_id,omitempty"`
	SeriesId      int    `json:"series_id,omitempty"`
	CoverImg      string `json:"cover_img,omitempty"`
	Title         string `json:"title,omitempty"`
	Description   string `json:"description,omitempty"`
	EpisodeAmount int    `json:"episode_amount,omitempty"`
	Children      []struct {
		OrderNo      int    `json:"order_no,omitempty"`
		SerialId     int    `json:"serial_id,omitempty"`
		Name         string `json:"name,omitempty"`
		Description  string `json:"description,omitempty"`
		Id           int    `json:"id,omitempty"`
		AdvertiserId int    `json:"advertiser_id,omitempty"`
		SeriesId     int    `json:"series_id,omitempty"`
		EpisodeId    int    `json:"episode_id,omitempty"`
		KsUserId     int64  `json:"ks_user_id,omitempty"`
	} `json:"children,omitempty"`
	SeriesPayMode           int    `json:"series_pay_mode,omitempty"  dc:"付费模式 1-打包，2-虚拟币，3-观看广告解锁"`
	SeriesPayModeName       string `json:"series_pay_mode_name,omitempty"`
	SeriesPayTemplateId     int64  `json:"series_pay_template_id,omitempty"  dc:"付费模板ID"`
	SeriesPayTemplateIdName string `json:"series_pay_template_id_name,omitempty"`
}

type ProductData struct {
	AdvertiserId    int64                `p:"advertiser_id" json:"advertiser_id"`
	ProductInfoList []*ksApi.ProductInfo `p:"product_list" json:"product_list"`
}

type NegativeWordParam struct {
	ExactWords  []string `p:"exact_words" json:"exact_words" dc:"精确否定词"`
	PhraseWords []string `p:"phrase_words" json:"phrase_words" dc:"短语否定词"`
}

type BidData struct {
	AdvertiserId int64   `p:"advertiser_id" json:"advertiser_id"`
	BidInfo      BidInfo `p:"bid_info" json:"bid_info"`
}

type BidInfo struct {
	Bid      int64   `p:"bid" json:"bid" dc:"出价 bid_type 为 CPC 时该字段必填，单位：厘，不得低于 0.2 元，不得高于 100 元，不得高于组预算"`
	RoiRatio float64 `p:"roi_ratio" json:"roi_ratio" dc:"付费ROI系数 优化目标为「首日 ROI、7日ROI出价」时必填：ROI 系数取值范围 ( 0,100 ] 最多支持到三位小数（如：0.066）"`
}
