// ==========================================================================
// GFast自动生成dao操作代码。
// 生成日期：2025-08-13 16:33:59
// 生成路径: internal/app/ad/dao/ks_advertiser_campaign.go
// 生成人：cyao
// desc:快手广告计划
// company:云南奇讯科技有限公司
// ==========================================================================

package dao

import (
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao/internal"
)

// ksAdvertiserCampaignDao is the manager for logic model data accessing and custom defined data operations functions management.
// You can define custom methods on it to extend its functionality as you wish.
type ksAdvertiserCampaignDao struct {
	*internal.KsAdvertiserCampaignDao
}

var (
	// KsAdvertiserCampaign is globally public accessible object for table tools_gen_table operations.
	KsAdvertiserCampaign = ksAdvertiserCampaignDao{
		internal.NewKsAdvertiserCampaignDao(),
	}
)

// Fill with you ideas below.
