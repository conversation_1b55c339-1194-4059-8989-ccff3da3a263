// ==========================================================================
// GFast自动生成dao internal操作代码。
// 生成日期：2025-08-20 11:00:00
// 生成路径: internal/app/ad/dao/internal/ks_advertiser_program_creative_report.go
// 生成人：cyao
// desc:创意数据
// company:云南奇讯科技有限公司
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// KsAdvertiserProgramCreativeReportDao is the manager for logic model data accessing and custom defined data operations functions management.
type KsAdvertiserProgramCreativeReportDao struct {
	table   string                                   // Table is the underlying table name of the DAO.
	group   string                                   // Group is the database configuration group name of current DAO.
	columns KsAdvertiserProgramCreativeReportColumns // Columns is the short type for Columns, which contains all the column names of Table for convenient usage.
}

// KsAdvertiserProgramCreativeReportColumns defines and stores column names for table ks_advertiser_program_creative_report.
type KsAdvertiserProgramCreativeReportColumns struct {
	CreativeId                                       string // 创意 id
	StatDate                                         string // 统计日期
	AdvertiserId                                     string // 广告id
	Charge                                           string // 消耗
	Show                                             string // 封面曝光数
	Aclick                                           string // 素材曝光数
	Bclick                                           string // 行为数
	Share                                            string // 分享数
	Comment                                          string // 评论数
	Like                                             string // 点赞数
	Follow                                           string // 关注数
	Report                                           string // 举报数
	Block                                            string // 拉黑数
	Negative                                         string // 减少此类作品数
	Activation                                       string // 激活数
	Submit                                           string // 表单提交数
	AdPhotoPlayed10S                                 string // 10s播放数
	AdPhotoPlayed2S                                  string // 2s播放数
	AdPhotoPlayed75Percent                           string // 75%进度播放数
	CancelLike                                       string // 取消点赞
	ClickConversionRatio                             string // 点击激活率 (激活数/点击数)
	ConversionCost                                   string // 单次激活成本
	ConversionCostByImpression7D                     string // 7日激活成本
	ConversionNum                                    string // 转化数？
	ConversionNumByImpression7D                      string // 七日转化数？
	ConversionNumCost                                string // 单次转换成本？
	ConversionRatio                                  string // 转化率？
	ConversionRatioByImpression7D                    string // 7日转化率？
	LivePlayed3S                                     string // 3s播放数量
	PlayedEnd                                        string // 完播数量
	PlayedFiveSeconds                                string // 5s数量
	PlayedThreeSeconds                               string // 3s数量
	AdScene                                          string // 广告场景
	PlacementType                                    string // 位置类型
	CancelFollow                                     string // 取消关注
	Play3SRatio                                      string // 3s播放率
	Play5SRatio                                      string // 5s播放率
	PlayEndRatio                                     string // 完播率
	DirectSubmit1DCost                               string // 表单提交成本
	MinigameIaaPurchaseAmountFirstDay                string // 当日广告LTV
	MinigameIaaPurchaseAmountThreeDayByConversion    string // 激活后三日广告LTV
	MinigameIaaPurchaseAmountWeekByConversion        string // 激活后七日广告LTV
	MinigameIaaPurchaseAmountFirstDayRoi             string // 当日广告变现ROI
	MinigameIaaPurchaseAmountThreeDayByConversionRoi string // 激活后三日广告变现ROI
	MinigameIaaPurchaseAmountWeekByConversionRoi     string // 激活后七日广告变现ROI
	MinigameIaaPurchaseAmount                        string // IAA广告变现LTV
	MinigameIaaPurchaseRoi                           string // IAA广告变现ROI
	UnitId                                           string // 广告组id
	EffectiveCustomerAcquisition7DCnt                string // 有效获客数（计费）
	EffectiveCustomerAcquisition7DCost               string // 有效获客成本（计费）
	EffectiveCustomerAcquisition7DRatio              string // 有效获客率（计费）
	MmuEffectiveCustomerAcquisitionCnt               string // MMU识别产生的有效获客数（回传）
	MmuEffectiveCustomerAcquisition7DCnt             string // MMU识别产生的有效获客数（计费）
	PlayedNum                                        string // 播放数
	LeadsSubmitCnt                                   string // 直接私信留资数
	LeadsSubmitCntRatio                              string // 直接私信留资率
	LeadsSubmitCost                                  string // 直接私信留资成本
	PrivateMessageSentCnt                            string // 私信消息数
	PrivateMessageSentRatio                          string // 私信消息转化率
	PrivateMessageSentCost                           string // 私信消息转化成本
	EventFormSubmit                                  string // 表单提交数（回传时间）
	DirectSubmit1DCnt                                string // 表单提交数(计费时间)
	EventFormSubmitRatio                             string // 表单提交率（回传时间）
	EventFormSubmitCost                              string // 表单提交成本（回传时间）
	EventAudition                                    string // 首次试听到课（归因）
	EventAudition30DCnt                              string // 首次试听到课（归因）
	EventAuditionCost                                string // 首次试听到课成本
	AllLessonFinishCnt                               string // 全部试听完课（回传）
	AllLessonFinish30DCnt                            string // 全部试听完课（归因）
	HighPriceClassPayCnt                             string // 成交付费（回传）
	HighPriceClassPay30DCnt                          string // 成交付费（归因）
	CampaignId                                       string // 广告计划Id
	CampaignName                                     string // 计划名称
	UnitName                                         string // 广告组名称
	PhotoId                                          string // 视频 id
	PhotoUrl                                         string // 视频链接
	CoverUrl                                         string // 封面链接
	ImageToken                                       string // 封面 id
	Description                                      string // 作品广告语
	PicId                                            string // 图片库图片ID
	PhotoMd5                                         string // 视频 md5
	UnitType                                         string // 单元组的创意类型，7=程序化2.0创意，10=智能创意；当值为空时，代表不区分创意类型取全部创意； 默认为空
	CreatedAt                                        string // 创建时间
}

var ksAdvertiserProgramCreativeReportColumns = KsAdvertiserProgramCreativeReportColumns{
	CreativeId:                        "creative_id",
	StatDate:                          "stat_date",
	AdvertiserId:                      "advertiser_id",
	Charge:                            "charge",
	Show:                              "show",
	Aclick:                            "aclick",
	Bclick:                            "bclick",
	Share:                             "share",
	Comment:                           "comment",
	Like:                              "like",
	Follow:                            "follow",
	Report:                            "report",
	Block:                             "block",
	Negative:                          "negative",
	Activation:                        "activation",
	Submit:                            "submit",
	AdPhotoPlayed10S:                  "ad_photo_played_10s",
	AdPhotoPlayed2S:                   "ad_photo_played_2s",
	AdPhotoPlayed75Percent:            "ad_photo_played_75percent",
	CancelLike:                        "cancel_like",
	ClickConversionRatio:              "click_conversion_ratio",
	ConversionCost:                    "conversion_cost",
	ConversionCostByImpression7D:      "conversion_cost_by_impression_7d",
	ConversionNum:                     "conversion_num",
	ConversionNumByImpression7D:       "conversion_num_by_impression_7d",
	ConversionNumCost:                 "conversion_num_cost",
	ConversionRatio:                   "conversion_ratio",
	ConversionRatioByImpression7D:     "conversion_ratio_by_impression_7d",
	LivePlayed3S:                      "live_played_3s",
	PlayedEnd:                         "played_end",
	PlayedFiveSeconds:                 "played_five_seconds",
	PlayedThreeSeconds:                "played_three_seconds",
	AdScene:                           "ad_scene",
	PlacementType:                     "placement_type",
	CancelFollow:                      "cancel_follow",
	Play3SRatio:                       "play_3s_ratio",
	Play5SRatio:                       "play_5s_ratio",
	PlayEndRatio:                      "play_end_ratio",
	DirectSubmit1DCost:                "direct_submit_1d_cost",
	MinigameIaaPurchaseAmountFirstDay: "minigame_iaa_purchase_amount_first_day",
	MinigameIaaPurchaseAmountThreeDayByConversion:    "minigame_iaa_purchase_amount_three_day_by_conversion",
	MinigameIaaPurchaseAmountWeekByConversion:        "minigame_iaa_purchase_amount_week_by_conversion",
	MinigameIaaPurchaseAmountFirstDayRoi:             "minigame_iaa_purchase_amount_first_day_roi",
	MinigameIaaPurchaseAmountThreeDayByConversionRoi: "minigame_iaa_purchase_amount_three_day_by_conversion_roi",
	MinigameIaaPurchaseAmountWeekByConversionRoi:     "minigame_iaa_purchase_amount_week_by_conversion_roi",
	MinigameIaaPurchaseAmount:                        "minigame_iaa_purchase_amount",
	MinigameIaaPurchaseRoi:                           "minigame_iaa_purchase_roi",
	UnitId:                                           "unit_id",
	EffectiveCustomerAcquisition7DCnt:                "effective_customer_acquisition_7d_cnt",
	EffectiveCustomerAcquisition7DCost:               "effective_customer_acquisition_7d_cost",
	EffectiveCustomerAcquisition7DRatio:              "effective_customer_acquisition_7d_ratio",
	MmuEffectiveCustomerAcquisitionCnt:               "mmu_effective_customer_acquisition_cnt",
	MmuEffectiveCustomerAcquisition7DCnt:             "mmu_effective_customer_acquisition_7d_cnt",
	PlayedNum:                                        "played_num",
	LeadsSubmitCnt:                                   "leads_submit_cnt",
	LeadsSubmitCntRatio:                              "leads_submit_cnt_ratio",
	LeadsSubmitCost:                                  "leads_submit_cost",
	PrivateMessageSentCnt:                            "private_message_sent_cnt",
	PrivateMessageSentRatio:                          "private_message_sent_ratio",
	PrivateMessageSentCost:                           "private_message_sent_cost",
	EventFormSubmit:                                  "event_form_submit",
	DirectSubmit1DCnt:                                "direct_submit_1d_cnt",
	EventFormSubmitRatio:                             "event_form_submit_ratio",
	EventFormSubmitCost:                              "event_form_submit_cost",
	EventAudition:                                    "event_audition",
	EventAudition30DCnt:                              "event_audition_30d_cnt",
	EventAuditionCost:                                "event_audition_cost",
	AllLessonFinishCnt:                               "all_lesson_finish_cnt",
	AllLessonFinish30DCnt:                            "all_lesson_finish_30d_cnt",
	HighPriceClassPayCnt:                             "high_price_class_pay_cnt",
	HighPriceClassPay30DCnt:                          "high_price_class_pay_30d_cnt",
	CampaignId:                                       "campaign_id",
	CampaignName:                                     "campaign_name",
	UnitName:                                         "unit_name",
	PhotoId:                                          "photo_id",
	PhotoUrl:                                         "photo_url",
	CoverUrl:                                         "cover_url",
	ImageToken:                                       "image_token",
	Description:                                      "description",
	PicId:                                            "pic_id",
	PhotoMd5:                                         "photo_md5",
	UnitType:                                         "unit_type",
	CreatedAt:                                        "created_at",
}

// NewKsAdvertiserProgramCreativeReportDao creates and returns a new DAO object for table data access.
func NewKsAdvertiserProgramCreativeReportDao() *KsAdvertiserProgramCreativeReportDao {
	return &KsAdvertiserProgramCreativeReportDao{
		group:   "default",
		table:   "ks_advertiser_program_creative_report",
		columns: ksAdvertiserProgramCreativeReportColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *KsAdvertiserProgramCreativeReportDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *KsAdvertiserProgramCreativeReportDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *KsAdvertiserProgramCreativeReportDao) Columns() KsAdvertiserProgramCreativeReportColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *KsAdvertiserProgramCreativeReportDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *KsAdvertiserProgramCreativeReportDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *KsAdvertiserProgramCreativeReportDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
