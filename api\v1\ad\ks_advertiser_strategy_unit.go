// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2025-08-22 11:52:52
// 生成路径: api/v1/ad/ks_advertiser_strategy_unit.go
// 生成人：cq
// desc:快手策略组-广告组相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package ad

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
)

// KsAdvertiserStrategyUnitSearchReq 分页请求参数
type KsAdvertiserStrategyUnitSearchReq struct {
	g.Meta `path:"/list" tags:"快手策略组-广告组" method:"get" summary:"快手策略组-广告组列表"`
	commonApi.Author
	model.KsAdvertiserStrategyUnitSearchReq
}

// KsAdvertiserStrategyUnitSearchRes 列表返回结果
type KsAdvertiserStrategyUnitSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.KsAdvertiserStrategyUnitSearchRes
}

// KsAdvertiserStrategyUnitAddReq 添加操作请求参数
type KsAdvertiserStrategyUnitAddReq struct {
	g.Meta `path:"/add" tags:"快手策略组-广告组" method:"post" summary:"快手策略组-广告组添加"`
	commonApi.Author
	*model.KsAdvertiserStrategyUnitAddReq
}

// KsAdvertiserStrategyUnitAddRes 添加操作返回结果
type KsAdvertiserStrategyUnitAddRes struct {
	commonApi.EmptyRes
}

// KsAdvertiserStrategyUnitEditReq 修改操作请求参数
type KsAdvertiserStrategyUnitEditReq struct {
	g.Meta `path:"/edit" tags:"快手策略组-广告组" method:"put" summary:"快手策略组-广告组修改"`
	commonApi.Author
	*model.KsAdvertiserStrategyUnitEditReq
}

// KsAdvertiserStrategyUnitEditRes 修改操作返回结果
type KsAdvertiserStrategyUnitEditRes struct {
	commonApi.EmptyRes
}

// KsAdvertiserStrategyUnitGetReq 获取一条数据请求
type KsAdvertiserStrategyUnitGetReq struct {
	g.Meta `path:"/get" tags:"快手策略组-广告组" method:"get" summary:"获取快手策略组-广告组信息"`
	commonApi.Author
	Id uint64 `p:"id" v:"required#主键必须"` //通过主键获取
}

// KsAdvertiserStrategyUnitGetRes 获取一条数据结果
type KsAdvertiserStrategyUnitGetRes struct {
	g.Meta `mime:"application/json"`
	*model.KsAdvertiserStrategyUnitInfoRes
}

// KsAdvertiserStrategyUnitDeleteReq 删除数据请求
type KsAdvertiserStrategyUnitDeleteReq struct {
	g.Meta `path:"/delete" tags:"快手策略组-广告组" method:"delete" summary:"删除快手策略组-广告组"`
	commonApi.Author
	Ids []uint64 `p:"ids" v:"required#主键必须"` //通过主键删除
}

// KsAdvertiserStrategyUnitDeleteRes 删除数据返回
type KsAdvertiserStrategyUnitDeleteRes struct {
	commonApi.EmptyRes
}
