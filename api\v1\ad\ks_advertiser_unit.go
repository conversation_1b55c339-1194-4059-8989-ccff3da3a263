// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2025-08-13 16:33:56
// 生成路径: api/v1/ad/ks_advertiser_unit.go
// 生成人：cyao
// desc:快手广告组相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package ad

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
)

// KsAdvertiserUnitSearchReq 分页请求参数
type KsAdvertiserUnitSearchReq struct {
	g.Meta `path:"/list" tags:"快手广告组" method:"get" summary:"快手广告组列表"`
	commonApi.Author
	model.KsAdvertiserUnitSearchReq
}

// KsAdvertiserUnitSearchRes 列表返回结果
type KsAdvertiserUnitSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.KsAdvertiserUnitSearchRes
}

// KsAdvertiserUnitAddReq 添加操作请求参数
type KsAdvertiserUnitAddReq struct {
	g.Meta `path:"/add" tags:"快手广告组" method:"post" summary:"快手广告组添加"`
	commonApi.Author
	*model.KsAdvertiserUnitAddReq
}

// KsAdvertiserUnitAddRes 添加操作返回结果
type KsAdvertiserUnitAddRes struct {
	commonApi.EmptyRes
}

// KsAdvertiserUnitEditReq 修改操作请求参数
type KsAdvertiserUnitEditReq struct {
	g.Meta `path:"/edit" tags:"快手广告组" method:"put" summary:"快手广告组修改"`
	commonApi.Author
	*model.KsAdvertiserUnitEditReq
}

// KsAdvertiserUnitEditRes 修改操作返回结果
type KsAdvertiserUnitEditRes struct {
	commonApi.EmptyRes
}

// KsAdvertiserUnitGetReq 获取一条数据请求
type KsAdvertiserUnitGetReq struct {
	g.Meta `path:"/get" tags:"快手广告组" method:"get" summary:"获取快手广告组信息"`
	commonApi.Author
	UnitId int64 `p:"unitId" v:"required#主键必须"` //通过主键获取
}

// KsAdvertiserUnitGetRes 获取一条数据结果
type KsAdvertiserUnitGetRes struct {
	g.Meta `mime:"application/json"`
	*model.KsAdvertiserUnitInfoRes
}

// KsAdvertiserUnitInfoReq 获取一条数据请求
type KsAdvertiserUnitInfoReq struct {
	g.Meta `path:"/getUnitInfo" tags:"快手广告组" method:"get" summary:"获取快手广告组详情"`
	commonApi.Author
	UnitId int64 `p:"unitId" v:"required#主键必须"` //通过主键获取
}

// KsAdvertiserUnitInfoRes 获取一条数据结果
type KsAdvertiserUnitInfoRes struct {
	g.Meta `mime:"application/json"`
	*model.KsAdvertiserUnitInfoRes
}

// KsAdvertiserUnitDeleteReq 删除数据请求
type KsAdvertiserUnitDeleteReq struct {
	g.Meta `path:"/delete" tags:"快手广告组" method:"delete" summary:"删除快手广告组"`
	commonApi.Author
	UnitIds []int64 `p:"unitIds" v:"required#主键必须"` //通过主键删除
}

// KsAdvertiserUnitDeleteRes 删除数据返回
type KsAdvertiserUnitDeleteRes struct {
	commonApi.EmptyRes
}

// KsAdvertiserUnitManualSyncReq 手动同步广告组请求参数
type KsAdvertiserUnitManualSyncReq struct {
	g.Meta `path:"/manualSync" tags:"快手广告组" method:"post" summary:"手动同步广告组"`
	commonApi.Author
	AdvertiserIds []int64 `p:"advertiserIds" v:"required#账户ID必须" dc:"账户ID"`
	StartTime     string  `p:"startTime" v:"required#开始时间必须" dc:"开始时间"`
	EndTime       string  `p:"endTime" v:"required#结束时间必须" dc:"结束时间"`
}

// KsAdvertiserUnitManualSyncRes 手动同步广告组返回结果
type KsAdvertiserUnitManualSyncRes struct {
	commonApi.EmptyRes
}

// KsAdvertiserUnitEditNameReq 修改广告组名称请求参数
type KsAdvertiserUnitEditNameReq struct {
	g.Meta `path:"/editName" tags:"快手广告组" method:"post" summary:"修改广告组名称"`
	commonApi.Author
	UnitId   int64  `p:"unitId" v:"required#广告组ID必须" dc:"广告组ID"`
	UnitName string `p:"unitName" v:"required#广告组名称必须" dc:"广告组名称"`
}

// KsAdvertiserUnitEditNameRes 修改广告组名称返回结果
type KsAdvertiserUnitEditNameRes struct {
	commonApi.EmptyRes
}
