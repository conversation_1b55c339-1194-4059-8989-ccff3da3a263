// ==========================================================================
// GFast自动生成dao操作代码。
// 生成日期：2025-08-20 11:00:00
// 生成路径: internal/app/ad/dao/ks_advertiser_program_creative_report.go
// 生成人：cyao
// desc:创意数据
// company:云南奇讯科技有限公司
// ==========================================================================

package dao

import (
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao/internal"
)

// ksAdvertiserProgramCreativeReportDao is the manager for logic model data accessing and custom defined data operations functions management.
// You can define custom methods on it to extend its functionality as you wish.
type ksAdvertiserProgramCreativeReportDao struct {
	*internal.KsAdvertiserProgramCreativeReportDao
}

var (
	// KsAdvertiserProgramCreativeReport is globally public accessible object for table tools_gen_table operations.
	KsAdvertiserProgramCreativeReport = ksAdvertiserProgramCreativeReportDao{
		internal.NewKsAdvertiserProgramCreativeReportDao(),
	}
)

// Fill with you ideas below.
