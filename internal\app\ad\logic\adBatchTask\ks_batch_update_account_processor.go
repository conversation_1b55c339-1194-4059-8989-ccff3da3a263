// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2025-08-18 17:30:45
// 生成路径: internal/app/ad/logic/adBatchTask/ks_batch_update_account_processor.go
// 生成人：cq
// desc:快手账户批量任务处理器
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	commonConsts "github.com/tiger1103/gfast/v3/internal/app/common/consts"
	"github.com/tiger1103/gfast/v3/internal/app/common/logic/channelStream"
	ksApi "github.com/tiger1103/gfast/v3/library/advertiser/ks/api"
)

var streamConsumer *channelStream.StreamConsumer

func init() {
	go StartKsBatchUpdateAccountTaskConsumer()
}

// StartKsBatchUpdateAccountTaskConsumer 启动快手账户批量任务消费者
func StartKsBatchUpdateAccountTaskConsumer() {
	dev := g.Cfg().MustGet(context.Background(), "redis.channel.stream.env").String()
	config := &channelStream.StreamConsumerConfig{
		StreamName:    fmt.Sprintf(commonConsts.KsBatchUpdateAccountChannelStream, dev),
		GroupName:     fmt.Sprintf(commonConsts.KsBatchUpdateAccountChannelGroup1, dev),
		ConsumerName:  fmt.Sprintf(commonConsts.KsBatchUpdateAccountChannelConsumer1, dev),
		BlockTime:     5 * time.Second,
		BatchSize:     10,
		LockTimeout:   10 * time.Second,
		MessageField:  commonConsts.KsBatchUpdateAccountMessageField,
		CleanupCron:   "0 0 1 * * ?", // 每天凌晨1点执行清理
		MaxStreamSize: 1000,
	}
	processor := NewKsBatchUpdateAccountProcessor()
	streamConsumer = channelStream.NewStreamConsumer(config, processor)
	streamConsumer.Start()
}

// KsBatchUpdateAccountProcessor 快手账户批量任务处理器
type KsBatchUpdateAccountProcessor struct{}

// NewKsBatchUpdateAccountProcessor 创建快手账户批量任务处理器
func NewKsBatchUpdateAccountProcessor() *KsBatchUpdateAccountProcessor {
	return &KsBatchUpdateAccountProcessor{}
}

// ProcessTask 处理任务
func (s *KsBatchUpdateAccountProcessor) ProcessTask(ctx context.Context, taskData []byte) (success bool, err error) {
	var task model.KsBatchUpdateAccountTaskInfo
	if err = json.Unmarshal(taskData, &task); err != nil {
		return false, err
	}

	optResult, err := s.processTask(task)
	if err != nil {
		return false, err
	}

	return optResult == commonConsts.OptResultSuccess, nil
}

// UpdateTaskStatus 更新任务状态
func (s *KsBatchUpdateAccountProcessor) UpdateTaskStatus(ctx context.Context, taskId string, success bool) error {
	taskEditReq := &model.AdBatchTaskEditReq{
		TaskId: taskId,
	}
	if success {
		taskEditReq.SuccessNum = 1
	} else {
		taskEditReq.FailNum = 1
	}
	return service.AdBatchTask().EditOptStatus(ctx, taskEditReq)
}

// GetTaskId 获取任务ID
func (s *KsBatchUpdateAccountProcessor) GetTaskId(taskData []byte) (string, error) {
	var task model.KsBatchUpdateAccountTaskInfo
	if err := json.Unmarshal(taskData, &task); err != nil {
		return "", err
	}
	return task.TaskId, nil
}

// processTask 任务处理逻辑
func (s *KsBatchUpdateAccountProcessor) processTask(task model.KsBatchUpdateAccountTaskInfo) (optResult string, err error) {
	ctx := context.Background()
	err = g.Try(ctx, func(ctx context.Context) {
		var errMsg string
		accessToken, _ := service.KsAdvertiserAccountInfo().GetAccessToken(ctx, task.AdvertiserId)
		if accessToken == "" {
			errMsg = commonConsts.ErrMsgGetAccessToken
			optResult = commonConsts.OptResultFail
		} else {
			err = s.executeOperation(ctx, accessToken, task)
			if err != nil {
				errMsg = err.Error()
				optResult = commonConsts.OptResultFail
			} else {
				optResult = commonConsts.OptResultSuccess
			}
		}
		// 记录任务详情
		detail := &model.AdBatchTaskDetailAddReq{
			TaskId:       task.TaskId,
			SerialNumber: task.SerialNumber,
			AdvertiserId: gconv.String(task.AdvertiserId),
			OptResult:    optResult,
			ErrMsg:       errMsg,
		}
		err = service.AdBatchTaskDetail().Add(ctx, detail)
	})
	return
}

// executeOperation 执行具体的操作
func (s *KsBatchUpdateAccountProcessor) executeOperation(ctx context.Context, accessToken string, task model.KsBatchUpdateAccountTaskInfo) error {
	switch task.OptType {
	case commonConsts.OptTypeEditAdvertiserBudget:
		return s.updateAccountBudget(ctx, accessToken, task)
	case commonConsts.OptTypeEditAdvertiserAutoInfo:
		return s.updateAccountAutoInfo(ctx, accessToken, task)
	case commonConsts.OptTypeEditAdvertiserIncExplore:
		return s.updateAccountIncExplore(ctx, accessToken, task)
	default:
		return errors.New(commonConsts.ErrMsgUnSupportOptType)
	}
}

// updateAccountBudget 更新账户预算
func (s *KsBatchUpdateAccountProcessor) updateAccountBudget(ctx context.Context, accessToken string, task model.KsBatchUpdateAccountTaskInfo) error {
	_, err := ksApi.GetKSApiClient().UpdateAccountBudgetService.AccessToken(accessToken).
		SetReq(ksApi.UpdateAccountBudgetReq{
			AdvertiserId: task.AdvertiserId,
			DayBudget:    task.DayBudget,
		}).Do()
	if err == nil {
		_ = service.KsAdvertiserAccountInfo().UpdateAccountInfo(ctx, &model.KsAdvertiserAccountInfoUpdateReq{
			AccountId: task.AdvertiserId,
			DayBudget: &task.DayBudget,
		})
	}
	return err
}

// updateAccountAutoInfo 更新账户智投
func (s *KsBatchUpdateAccountProcessor) updateAccountAutoInfo(ctx context.Context, accessToken string, task model.KsBatchUpdateAccountTaskInfo) error {
	_, err := ksApi.GetKSApiClient().ModAccountAutoInfoService.AccessToken(accessToken).
		SetReq(ksApi.ModAccountAutoInfoReq{
			AdvertiserId:             task.AdvertiserId,
			AccountAutoManage:        task.AccountAutoManage,
			OcpxActionTypeConstraint: task.OcpxActionTypeConstraint,
		}).Do()
	if err == nil {
		_ = service.KsAdvertiserAccountInfo().UpdateAccountInfo(ctx, &model.KsAdvertiserAccountInfoUpdateReq{
			AccountId:         task.AdvertiserId,
			AccountAutoManage: &task.AccountAutoManage,
		})
	}
	return err
}

// updateAccountIncExplore 更新增量探索
func (s *KsBatchUpdateAccountProcessor) updateAccountIncExplore(ctx context.Context, accessToken string, task model.KsBatchUpdateAccountTaskInfo) error {
	_, err := ksApi.GetKSApiClient().UpdateAccountIncExploreService.AccessToken(accessToken).
		SetReq(ksApi.UpdateAccountIncExploreReq{
			AdvertiserId:   task.AdvertiserId,
			IncExploreInfo: task.IncExploreInfo,
		}).Do()
	return err
}
