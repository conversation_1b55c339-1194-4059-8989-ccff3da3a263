// ==========================================================================
// GFast自动生成dao操作代码。
// 生成日期：2025-08-12 10:45:09
// 生成路径: internal/app/ad/dao/ks_advertiser_unit_report_data.go
// 生成人：cq
// desc:快手广告组报表数据
// company:云南奇讯科技有限公司
// ==========================================================================

package dao

import (
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao/internal"
)

// ksAdvertiserUnitReportDataDao is the manager for logic model data accessing and custom defined data operations functions management.
// You can define custom methods on it to extend its functionality as you wish.
type ksAdvertiserUnitReportDataDao struct {
	*internal.KsAdvertiserUnitReportDataDao
}

var (
	// KsAdvertiserUnitReportData is globally public accessible object for table tools_gen_table operations.
	KsAdvertiserUnitReportData = ksAdvertiserUnitReportDataDao{
		internal.NewKsAdvertiserUnitReportDataDao(),
	}
)

// Fill with you ideas below.
