// ==========================================================================
// GFast自动生成model entity操作代码。
// 生成日期：2025-08-12 10:44:33
// 生成路径: internal/app/ad/model/entity/ks_advertiser_account_report_data.go
// 生成人：cq
// desc:快手账户报表数据
// company:云南奇讯科技有限公司
// ==========================================================================

package do

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
)

// KsAdvertiserAccountReportData is the golang structure for table ks_advertiser_account_report_data.
type KsAdvertiserAccountReportData struct {
	gmeta.Meta                                       `orm:"table:ks_advertiser_account_report_data, do:true"`
	AdvertiserId                                     interface{} `orm:"advertiser_id,primary" json:"advertiserId"`                                                                        // 广告主ID
	StatDate                                         interface{} `orm:"stat_date,primary" json:"statDate"`                                                                                // 数据日期，格式：YYYY-MM-DD
	PrivateMessageSentCost                           interface{} `orm:"private_message_sent_cost" json:"privateMessageSentCost"`                                                          // 私信消息转化成本
	PrivateMessageSentRatio                          interface{} `orm:"private_message_sent_ratio" json:"privateMessageSentRatio"`                                                        // 私信消息转化率
	PrivateMessageSentCnt                            interface{} `orm:"private_message_sent_cnt" json:"privateMessageSentCnt"`                                                            // 私信消息数
	LeadsSubmitCost                                  interface{} `orm:"leads_submit_cost" json:"leadsSubmitCost"`                                                                         // 直接私信留资成本
	LeadsSubmitCntRatio                              interface{} `orm:"leads_submit_cnt_ratio" json:"leadsSubmitCntRatio"`                                                                // 直接私信留资率
	LeadsSubmitCnt                                   interface{} `orm:"leads_submit_cnt" json:"leadsSubmitCnt"`                                                                           // 直接私信留资数
	PlayedNum                                        interface{} `orm:"played_num" json:"playedNum"`                                                                                      // 播放数
	PlayedEnd                                        interface{} `orm:"played_end" json:"playedEnd"`                                                                                      // 播放完成
	PlayedFiveSeconds                                interface{} `orm:"played_five_seconds" json:"playedFiveSeconds"`                                                                     // 播放5s
	PlayedThreeSeconds                               interface{} `orm:"played_three_seconds" json:"playedThreeSeconds"`                                                                   // 有效播放数
	Play3SRatio                                      interface{} `orm:"play_3s_ratio" json:"play3SRatio"`                                                                                 // 3s 播放率
	Play5SRatio                                      interface{} `orm:"play_5s_ratio" json:"play5SRatio"`                                                                                 // 5s播放率
	PlayEndRatio                                     interface{} `orm:"play_end_ratio" json:"playEndRatio"`                                                                               // 完播率
	AdPhotoPlayed10S                                 interface{} `orm:"ad_photo_played_10s" json:"adPhotoPlayed10S"`                                                                      // 10s播放数
	AdPhotoPlayed2S                                  interface{} `orm:"ad_photo_played_2s" json:"adPhotoPlayed2S"`                                                                        // 2s播放数
	AdPhotoPlayed75Percent                           interface{} `orm:"ad_photo_played_75percent" json:"adPhotoPlayed75Percent"`                                                          // 75%进度播放数
	AdPhotoPlayed75PercentRatio                      interface{} `orm:"ad_photo_played_75_percent_ratio" json:"adPhotoPlayed75PercentRatio"`                                              // 75%进度播放率
	AdPhotoPlayed10SRatio                            interface{} `orm:"ad_photo_played_10s_ratio" json:"adPhotoPlayed10SRatio"`                                                           // 10s播放率
	AdPhotoPlayed2SRatio                             interface{} `orm:"ad_photo_played_2s_ratio" json:"adPhotoPlayed2SRatio"`                                                             // 2s播放率
	MinigameIaaPurchaseAmountWeekByConversionRoi     interface{} `orm:"minigame_iaa_purchase_amount_week_by_conversion_roi" json:"minigameIaaPurchaseAmountWeekByConversionRoi"`          // 激活后七日广告变现ROI
	MinigameIaaPurchaseAmountThreeDayByConversionRoi interface{} `orm:"minigame_iaa_purchase_amount_three_day_by_conversion_roi" json:"minigameIaaPurchaseAmountThreeDayByConversionRoi"` // 激活后三日广告变现ROI
	MinigameIaaPurchaseAmountFirstDayRoi             interface{} `orm:"minigame_iaa_purchase_amount_first_day_roi" json:"minigameIaaPurchaseAmountFirstDayRoi"`                           // 当日广告变现ROI
	MinigameIaaPurchaseAmountWeekByConversion        interface{} `orm:"minigame_iaa_purchase_amount_week_by_conversion" json:"minigameIaaPurchaseAmountWeekByConversion"`                 // 激活后七日广告LTV
	MinigameIaaPurchaseAmountThreeDayByConversion    interface{} `orm:"minigame_iaa_purchase_amount_three_day_by_conversion" json:"minigameIaaPurchaseAmountThreeDayByConversion"`        // 激活后三日广告LTV
	MinigameIaaPurchaseAmountFirstDay                interface{} `orm:"minigame_iaa_purchase_amount_first_day" json:"minigameIaaPurchaseAmountFirstDay"`                                  // 当日广告LTV
	MinigameIaaPurchaseRoi                           interface{} `orm:"minigame_iaa_purchase_roi" json:"minigameIaaPurchaseRoi"`                                                          // IAA广告变现ROI
	MinigameIaaPurchaseAmount                        interface{} `orm:"minigame_iaa_purchase_amount" json:"minigameIaaPurchaseAmount"`                                                    // IAA广告变现LTV
	MinigameIaaPurchaseAmount30DayByConversionRoi    interface{} `orm:"minigame_iaa_purchase_amount_30_day_by_conversion_roi" json:"minigameIaaPurchaseAmount30DayByConversionRoi"`       // 激活后30日广告变现ROI
	MinigameIaaPurchaseAmount15DayByConversionRoi    interface{} `orm:"minigame_iaa_purchase_amount_15_day_by_conversion_roi" json:"minigameIaaPurchaseAmount15DayByConversionRoi"`       // 激活后15日广告变现ROI
	MinigameIaaPurchaseAmount30DayByConversion       interface{} `orm:"minigame_iaa_purchase_amount_30_day_by_conversion" json:"minigameIaaPurchaseAmount30DayByConversion"`              // 激活后30日广告LTV
	MinigameIaaPurchaseAmount15DayByConversion       interface{} `orm:"minigame_iaa_purchase_amount_15_day_by_conversion" json:"minigameIaaPurchaseAmount15DayByConversion"`              // 激活后15日广告LTV
	MmuEffectiveCustomerAcquisition7DCnt             interface{} `orm:"mmu_effective_customer_acquisition_7d_cnt" json:"mmuEffectiveCustomerAcquisition7DCnt"`                            // MMU识别产生的有效获客数（计费）
	MmuEffectiveCustomerAcquisitionCnt               interface{} `orm:"mmu_effective_customer_acquisition_cnt" json:"mmuEffectiveCustomerAcquisitionCnt"`                                 // MMU识别产生的有效获客数（回传）
	EffectiveCustomerAcquisition7DRatio              interface{} `orm:"effective_customer_acquisition_7d_ratio" json:"effectiveCustomerAcquisition7DRatio"`                               // 有效获客率（计费）
	EffectiveCustomerAcquisition7DCost               interface{} `orm:"effective_customer_acquisition_7d_cost" json:"effectiveCustomerAcquisition7DCost"`                                 // 有效获客成本（计费）
	EffectiveCustomerAcquisition7DCnt                interface{} `orm:"effective_customer_acquisition_7d_cnt" json:"effectiveCustomerAcquisition7DCnt"`                                   // 有效获客数（计费）
	EventPay30DayOverallRoi                          interface{} `orm:"event_pay_30_day_overall_roi" json:"eventPay30DayOverallRoi"`                                                      // 激活后30日整体ROI
	EventPay15DayOverallRoi                          interface{} `orm:"event_pay_15_day_overall_roi" json:"eventPay15DayOverallRoi"`                                                      // 激活后15日整体ROI
	EventPayPurchaseAmount15DayByConversion          interface{} `orm:"event_pay_purchase_amount_15_day_by_conversion" json:"eventPayPurchaseAmount15DayByConversion"`                    // 激活后15日付费金额
	EventPayPurchaseAmount30DayByConversion          interface{} `orm:"event_pay_purchase_amount_30_day_by_conversion" json:"eventPayPurchaseAmount30DayByConversion"`                    // 激活后30日付费金额
	EventPayFirstDay                                 interface{} `orm:"event_pay_first_day" json:"eventPayFirstDay"`                                                                      // 应用下载数据-首日付费次数
	EventPayPurchaseAmountFirstDay                   interface{} `orm:"event_pay_purchase_amount_first_day" json:"eventPayPurchaseAmountFirstDay"`                                        // 应用下载数据-首日付费金额
	EventPayFirstDayRoi                              interface{} `orm:"event_pay_first_day_roi" json:"eventPayFirstDayRoi"`                                                               // 应用下载数据-首日 ROI
	EventPay                                         interface{} `orm:"event_pay" json:"eventPay"`                                                                                        // 应用下载数据-付费次数
	EventPayPurchaseAmount                           interface{} `orm:"event_pay_purchase_amount" json:"eventPayPurchaseAmount"`                                                          // 应用下载数据-付费金额
	EventPayRoi                                      interface{} `orm:"event_pay_roi" json:"eventPayRoi"`                                                                                 // 应用下载数据-ROI
	EventPayPurchaseAmountOneDay                     interface{} `orm:"event_pay_purchase_amount_one_day" json:"eventPayPurchaseAmountOneDay"`                                            // 激活后24h付费金额(回传时间)
	EventPayPurchaseAmountOneDayByConversion         interface{} `orm:"event_pay_purchase_amount_one_day_by_conversion" json:"eventPayPurchaseAmountOneDayByConversion"`                  // 激活后24h付费金额(激活时间)
	EventPayPurchaseAmountOneDayByConversionRoi      interface{} `orm:"event_pay_purchase_amount_one_day_by_conversion_roi" json:"eventPayPurchaseAmountOneDayByConversionRoi"`           // 激活后24小时付费ROI
	EventPayPurchaseAmountOneDayRoi                  interface{} `orm:"event_pay_purchase_amount_one_day_roi" json:"eventPayPurchaseAmountOneDayRoi"`                                     // 激活后24h-ROI(回传时间)
	EventPayWeightedPurchaseAmount                   interface{} `orm:"event_pay_weighted_purchase_amount" json:"eventPayWeightedPurchaseAmount"`                                         // 加权付费金额
	EventPayWeightedPurchaseAmountFirstDay           interface{} `orm:"event_pay_weighted_purchase_amount_first_day" json:"eventPayWeightedPurchaseAmountFirstDay"`                       // 首日加权付费金额
	Charge                                           interface{} `orm:"charge" json:"charge"`                                                                                             // 花费（元）
	Show                                             interface{} `orm:"show" json:"show"`                                                                                                 // 封面曝光数
	Aclick                                           interface{} `orm:"aclick" json:"aclick"`                                                                                             // 素材曝光数
	Bclick                                           interface{} `orm:"bclick" json:"bclick"`                                                                                             // 行为数
	AdShow                                           interface{} `orm:"ad_show" json:"adShow"`                                                                                            // 广告曝光
	Share                                            interface{} `orm:"share" json:"share"`                                                                                               // 分享数
	Comment                                          interface{} `orm:"comment" json:"comment"`                                                                                           // 评论数
	Like                                             interface{} `orm:"like" json:"like"`                                                                                                 // 点赞数
	Follow                                           interface{} `orm:"follow" json:"follow"`                                                                                             // 新增粉丝数
	CancelLike                                       interface{} `orm:"cancel_like" json:"cancelLike"`                                                                                    // 取消点赞数
	CancelFollow                                     interface{} `orm:"cancel_follow" json:"cancelFollow"`                                                                                // 取消关注数
	Report                                           interface{} `orm:"report" json:"report"`                                                                                             // 举报数
	Block                                            interface{} `orm:"block" json:"block"`                                                                                               // 拉黑数
	Negative                                         interface{} `orm:"negative" json:"negative"`                                                                                         // 减少此类作品数
	Activation                                       interface{} `orm:"activation" json:"activation"`                                                                                     // 应用下载数据-激活数
	DownloadStarted                                  interface{} `orm:"download_started" json:"downloadStarted"`                                                                          // 应用下载数据-安卓下载开始数
	DownloadCompleted                                interface{} `orm:"download_completed" json:"downloadCompleted"`                                                                      // 应用下载数据-安卓下载完成数
	DownloadInstalled                                interface{} `orm:"download_installed" json:"downloadInstalled"`                                                                      // 安卓安装完成数
	ClickConversionRatio                             interface{} `orm:"click_conversion_ratio" json:"clickConversionRatio"`                                                               // 点击激活成本
	ConversionCost                                   interface{} `orm:"conversion_cost" json:"conversionCost"`                                                                            // 激活单价
	DownloadCompletedCost                            interface{} `orm:"download_completed_cost" json:"downloadCompletedCost"`                                                             // 安卓下载完成单价（元）
	DownloadCompletedRatio                           interface{} `orm:"download_completed_ratio" json:"downloadCompletedRatio"`                                                           // 安卓下载完成率
	DownloadConversionRatio                          interface{} `orm:"download_conversion_ratio" json:"downloadConversionRatio"`                                                         // 下载完成激活率
	DownloadStartedCost                              interface{} `orm:"download_started_cost" json:"downloadStartedCost"`                                                                 // 安卓下载开始单价（元）
	DownloadStartedRatio                             interface{} `orm:"download_started_ratio" json:"downloadStartedRatio"`                                                               // 安卓下载开始率
	EventRegister                                    interface{} `orm:"event_register" json:"eventRegister"`                                                                              // 应用下载数据-注册数
	EventRegisterCost                                interface{} `orm:"event_register_cost" json:"eventRegisterCost"`                                                                     // 应用下载数据-注册成本
	EventRegisterRatio                               interface{} `orm:"event_register_ratio" json:"eventRegisterRatio"`                                                                   // 应用下载数据-注册率
	EventJinJianApp                                  interface{} `orm:"event_jin_jian_app" json:"eventJinJianApp"`                                                                        // 应用下载数据-完件数
	EventJinJianAppCost                              interface{} `orm:"event_jin_jian_app_cost" json:"eventJinJianAppCost"`                                                               // 应用下载数据-完件成本
	EventJinJianLandingPage                          interface{} `orm:"event_jin_jian_landing_page" json:"eventJinJianLandingPage"`                                                       // 落地页数据-落地页完件数
	EventJinJianLandingPageCost                      interface{} `orm:"event_jin_jian_landing_page_cost" json:"eventJinJianLandingPageCost"`                                              // 落地页数据-落地页完件成本
	Jinjian0DCnt                                     interface{} `orm:"jinjian_0d_cnt" json:"jinjian0DCnt"`                                                                               // T0完件数
	Jinjian3DCnt                                     interface{} `orm:"jinjian_3d_cnt" json:"jinjian3DCnt"`                                                                               // T3完件数
	Jinjian0DCntCost                                 interface{} `orm:"jinjian_0d_cnt_cost" json:"jinjian0DCntCost"`                                                                      // T0完件成本
	Jinjian3DCntCost                                 interface{} `orm:"jinjian_3d_cnt_cost" json:"jinjian3DCntCost"`                                                                      // T3完件成本
	EventCreditGrantApp                              interface{} `orm:"event_credit_grant_app" json:"eventCreditGrantApp"`                                                                // 应用下载数据-授信数
	EventCreditGrantAppCost                          interface{} `orm:"event_credit_grant_app_cost" json:"eventCreditGrantAppCost"`                                                       // 应用下载数据-授信成本
	EventCreditGrantAppRatio                         interface{} `orm:"event_credit_grant_app_ratio" json:"eventCreditGrantAppRatio"`                                                     // 应用下载数据-授信率
	EventCreditGrantLandingPage                      interface{} `orm:"event_credit_grant_landing_page" json:"eventCreditGrantLandingPage"`                                               // 落地页授信数
	EventCreditGrantLandingPageCost                  interface{} `orm:"event_credit_grant_landing_page_cost" json:"eventCreditGrantLandingPageCost"`                                      // 落地页数据-落地页授信成本
	EventCreditGrantLandingRatio                     interface{} `orm:"event_credit_grant_landing_ratio" json:"eventCreditGrantLandingRatio"`                                             // 落地页数据-落地页授信率
	EventCreditGrantFirstDayApp                      interface{} `orm:"event_credit_grant_first_day_app" json:"eventCreditGrantFirstDayApp"`                                              // app首日授信数
	EventCreditGrantFirstDayAppCost                  interface{} `orm:"event_credit_grant_first_day_app_cost" json:"eventCreditGrantFirstDayAppCost"`                                     // 首日授信成本
	EventCreditGrantFirstDayAppRatio                 interface{} `orm:"event_credit_grant_first_day_app_ratio" json:"eventCreditGrantFirstDayAppRatio"`                                   // 首日授信率
	EventCreditGrantFirstDayLandingPage              interface{} `orm:"event_credit_grant_first_day_landing_page" json:"eventCreditGrantFirstDayLandingPage"`                             // 落地页首日授信数
	EventCreditGrantFirstDayLandingPageCost          interface{} `orm:"event_credit_grant_first_day_landing_page_cost" json:"eventCreditGrantFirstDayLandingPageCost"`                    // 落地页首日授信成本
	EventCreditGrantFirstDayLandingPageRatio         interface{} `orm:"event_credit_grant_first_day_landing_page_ratio" json:"eventCreditGrantFirstDayLandingPageRatio"`                  // 落地页首日授信率
	CreditGrant0DCnt                                 interface{} `orm:"credit_grant_0d_cnt" json:"creditGrant0DCnt"`                                                                      // T0授信数
	CreditGrant3DCnt                                 interface{} `orm:"credit_grant_3d_cnt" json:"creditGrant3DCnt"`                                                                      // T3授信数
	CreditGrant0DCntCost                             interface{} `orm:"credit_grant_0d_cnt_cost" json:"creditGrant0DCntCost"`                                                             // T0授信成本
	CreditGrant3DCntCost                             interface{} `orm:"credit_grant_3d_cnt_cost" json:"creditGrant3DCntCost"`                                                             // T3授信成本
	CreditGrant0DCntRatio                            interface{} `orm:"credit_grant_0d_cnt_ratio" json:"creditGrant0DCntRatio"`                                                           // T0完件授信率
	CreditGrant3DCntRatio                            interface{} `orm:"credit_grant_3d_cnt_ratio" json:"creditGrant3DCntRatio"`                                                           // T3完件授信通过率
	EventOrderSubmit                                 interface{} `orm:"event_order_submit" json:"eventOrderSubmit"`                                                                       // 提交订单数
	EventOrderPaid                                   interface{} `orm:"event_order_paid" json:"eventOrderPaid"`                                                                           // 应用下载数据-付款成功数
	EventOrderPaidPurchaseAmount                     interface{} `orm:"event_order_paid_purchase_amount" json:"eventOrderPaidPurchaseAmount"`                                             // 应用下载数据-付款成功金额
	EventOrderPaidCost                               interface{} `orm:"event_order_paid_cost" json:"eventOrderPaidCost"`                                                                  // 应用下载数据-单次付款成本
	EventOrderPaidRoi                                interface{} `orm:"event_order_paid_roi" json:"eventOrderPaidRoi"`                                                                    // 订单支付率
	OrderSubmitAmt                                   interface{} `orm:"order_submit_amt" json:"orderSubmitAmt"`                                                                           // 订单提交金额
	FormCount                                        interface{} `orm:"form_count" json:"formCount"`                                                                                      // 落地页数据-线索提交个数
	FormCost                                         interface{} `orm:"form_cost" json:"formCost"`                                                                                        // 落地页数据-单个线索成本
	FormActionRatio                                  interface{} `orm:"form_action_ratio" json:"formActionRatio"`                                                                         // 落地页数据-表单提交点击率
	Submit                                           interface{} `orm:"submit" json:"submit"`                                                                                             // 提交按钮点击数（历史字段）
	EventValidClues                                  interface{} `orm:"event_valid_clues" json:"eventValidClues"`                                                                         // 落地页数据-有效线索数
	EventValidCluesCost                              interface{} `orm:"event_valid_clues_cost" json:"eventValidCluesCost"`                                                                // 落地页数据-有效线索成本
	EventConsultationValidRetained                   interface{} `orm:"event_consultation_valid_retained" json:"eventConsultationValidRetained"`                                          // 留咨咨询数
	EventConsultationValidRetainedCost               interface{} `orm:"event_consultation_valid_retained_cost" json:"eventConsultationValidRetainedCost"`                                 // 留咨咨询成本
	EventConsultationValidRetainedRatio              interface{} `orm:"event_consultation_valid_retained_ratio" json:"eventConsultationValidRetainedRatio"`                               // 留咨咨询率
	EventConversionClickCost                         interface{} `orm:"event_conversion_click_cost" json:"eventConversionClickCost"`                                                      // 有效咨询成本
	EventConversionClickRatio                        interface{} `orm:"event_conversion_click_ratio" json:"eventConversionClickRatio"`                                                    // 有效咨询率
	EventPreComponentConsultationValidRetained       interface{} `orm:"event_pre_component_consultation_valid_retained" json:"eventPreComponentConsultationValidRetained"`                // 附加咨询组件留资咨询数
	EventAdWatch10Times                              interface{} `orm:"event_ad_watch_10_times" json:"eventAdWatch10Times"`                                                               // 10次广告广告观看数
	EventAdWatch10TimesCost                          interface{} `orm:"event_ad_watch_10_times_cost" json:"eventAdWatch10TimesCost"`                                                      // 10次广告观看成本
	EventAdWatch10TimesRatio                         interface{} `orm:"event_ad_watch_10_times_ratio" json:"eventAdWatch10TimesRatio"`                                                    // 10次广告观看转化率
	EventAdWatch20Times                              interface{} `orm:"event_ad_watch_20_times" json:"eventAdWatch20Times"`                                                               // 20次广告广告观看数
	EventAdWatch20TimesCost                          interface{} `orm:"event_ad_watch_20_times_cost" json:"eventAdWatch20TimesCost"`                                                      // 20次广告观看成本
	EventAdWatch20TimesRatio                         interface{} `orm:"event_ad_watch_20_times_ratio" json:"eventAdWatch20TimesRatio"`                                                    // 20次广告观看转化率
	EventAdWatch5Times                               interface{} `orm:"event_ad_watch_5_times" json:"eventAdWatch5Times"`                                                                 // 5次广告广告观看数
	EventAdWatch5TimesCost                           interface{} `orm:"event_ad_watch_5_times_cost" json:"eventAdWatch5TimesCost"`                                                        // 5次广告观看成本
	EventAdWatch5TimesRatio                          interface{} `orm:"event_ad_watch_5_times_ratio" json:"eventAdWatch5TimesRatio"`                                                      // 5次广告观看转化率
	EventWatchAppAd                                  interface{} `orm:"event_watch_app_ad" json:"eventWatchAppAd"`                                                                        // 广告观看
	EventAdWatchTimes                                interface{} `orm:"event_ad_watch_times" json:"eventAdWatchTimes"`                                                                    // 广告观看次数
	EventAdWatchTimesRatio                           interface{} `orm:"event_ad_watch_times_ratio" json:"eventAdWatchTimesRatio"`                                                         // 广告观看次数转化率
	EventAdWatchTimesCost                            interface{} `orm:"event_ad_watch_times_cost" json:"eventAdWatchTimesCost"`                                                           // 广告观看次数成本
	EventMakingCalls                                 interface{} `orm:"event_making_calls" json:"eventMakingCalls"`                                                                       // 电话拨打数
	EventMakingCallsCost                             interface{} `orm:"event_making_calls_cost" json:"eventMakingCallsCost"`                                                              // 电话拨打成本
	EventMakingCallsRatio                            interface{} `orm:"event_making_calls_ratio" json:"eventMakingCallsRatio"`                                                            // 电话拨打率
	EventGetThrough                                  interface{} `orm:"event_get_through" json:"eventGetThrough"`                                                                         // 智能电话-确认接通数
	EventGetThroughCost                              interface{} `orm:"event_get_through_cost" json:"eventGetThroughCost"`                                                                // 智能电话-确认接通成本
	EventGetThroughRatio                             interface{} `orm:"event_get_through_ratio" json:"eventGetThroughRatio"`                                                              // 智能电话-确认接通率
	EventPhoneGetThrough                             interface{} `orm:"event_phone_get_through" json:"eventPhoneGetThrough"`                                                              // 电话建联数
	EventOutboundCall                                interface{} `orm:"event_outbound_call" json:"eventOutboundCall"`                                                                     // 电话拨打数
	EventOutboundCallCost                            interface{} `orm:"event_outbound_call_cost" json:"eventOutboundCallCost"`                                                            // 电话拨打成本
	EventOutboundCallRatio                           interface{} `orm:"event_outbound_call_ratio" json:"eventOutboundCallRatio"`                                                          // 电话拨打率
	EventWechatQrCodeLinkClick                       interface{} `orm:"event_wechat_qr_code_link_click" json:"eventWechatQrCodeLinkClick"`                                                // 微信小程序深度加粉数
	EventAddWechat                                   interface{} `orm:"event_add_wechat" json:"eventAddWechat"`                                                                           // 微信复制数
	EventAddWechatCost                               interface{} `orm:"event_add_wechat_cost" json:"eventAddWechatCost"`                                                                  // 微信复制成本
	EventAddWechatRatio                              interface{} `orm:"event_add_wechat_ratio" json:"eventAddWechatRatio"`                                                                // 微信复制率
	EventWechatConnected                             interface{} `orm:"event_wechat_connected" json:"eventWechatConnected"`                                                               // 微信加粉数
	EventAudition                                    interface{} `orm:"event_audition" json:"eventAudition"`                                                                              // 首次试听到课数
	EventButtonClick                                 interface{} `orm:"event_button_click" json:"eventButtonClick"`                                                                       // 按钮点击数
	EventButtonClickCost                             interface{} `orm:"event_button_click_cost" json:"eventButtonClickCost"`                                                              // 按钮点击成本
	EventButtonClickRatio                            interface{} `orm:"event_button_click_ratio" json:"eventButtonClickRatio"`                                                            // 按钮点击率
	EventMultiConversion                             interface{} `orm:"event_multi_conversion" json:"eventMultiConversion"`                                                               // 落地页多转化次数
	EventMultiConversionRatio                        interface{} `orm:"event_multi_conversion_ratio" json:"eventMultiConversionRatio"`                                                    // 落地页多转化率
	EventMultiConversionCost                         interface{} `orm:"event_multi_conversion_cost" json:"eventMultiConversionCost"`                                                      // 落地页多转化成本
	EventAddShoppingCart                             interface{} `orm:"event_add_shopping_cart" json:"eventAddShoppingCart"`                                                              // 添加购物车数
	EventAddShoppingCartCost                         interface{} `orm:"event_add_shopping_cart_cost" json:"eventAddShoppingCartCost"`                                                     // 添加购物车成本
	EventIntentionConfirmed                          interface{} `orm:"event_intention_confirmed" json:"eventIntentionConfirmed"`                                                         // 意向确认数
	EventOrderSuccessed                              interface{} `orm:"event_order_successed" json:"eventOrderSuccessed"`                                                                 // 有效线索成交数
	EventPhoneCardActivate                           interface{} `orm:"event_phone_card_activate" json:"eventPhoneCardActivate"`                                                          // 电话卡激活数
	EventMeasurementHouse                            interface{} `orm:"event_measurement_house" json:"eventMeasurementHouse"`                                                             // 量房数
	EventAppInvoked                                  interface{} `orm:"event_app_invoked" json:"eventAppInvoked"`                                                                         // 唤醒应用数
	EventAppInvokedCost                              interface{} `orm:"event_app_invoked_cost" json:"eventAppInvokedCost"`                                                                // 唤醒应用成本
	EventAppInvokedRatio                             interface{} `orm:"event_app_invoked_ratio" json:"eventAppInvokedRatio"`                                                              // 唤醒应用率
	EventNextDayStayCost                             interface{} `orm:"event_next_day_stay_cost" json:"eventNextDayStayCost"`                                                             // 应用下载数据-次留成本（仅支持分日查询）
	EventNextDayStayRatio                            interface{} `orm:"event_next_day_stay_ratio" json:"eventNextDayStayRatio"`                                                           // 应用下载数据-次留率（仅支持分日查询）
	EventNextDayStay                                 interface{} `orm:"event_next_day_stay" json:"eventNextDayStay"`                                                                      // 应用下载数据-次留数（仅支持分日查询）
	PhotoClick                                       interface{} `orm:"photo_click" json:"photoClick"`                                                                                    // 封面点击数
	PhotoClickRatio                                  interface{} `orm:"photo_click_ratio" json:"photoClickRatio"`                                                                         // 封面点击率
	PhotoClickCost                                   interface{} `orm:"photo_click_cost" json:"photoClickCost"`                                                                           // 平均点击单价（元）
	ActionRatio                                      interface{} `orm:"action_ratio" json:"actionRatio"`                                                                                  // 行为率
	ActionNewRatio                                   interface{} `orm:"action_new_ratio" json:"actionNewRatio"`                                                                           // 行为率 新
	ActionCost                                       interface{} `orm:"action_cost" json:"actionCost"`                                                                                    // 平均行为单价（元）
	Impression1KCost                                 interface{} `orm:"impression_1k_cost" json:"impression1KCost"`                                                                       // 平均千次曝光花费（元）
	Click1KCost                                      interface{} `orm:"click_1k_cost" json:"click1KCost"`                                                                                 // 平均千次素材曝光花费(元)
	ApproxPayCost                                    interface{} `orm:"approx_pay_cost" json:"approxPayCost"`                                                                             // 淘系近似购买成本
	ApproxPayCount                                   interface{} `orm:"approx_pay_count" json:"approxPayCount"`                                                                           // 近似购买数
	ApproxPayRatio                                   interface{} `orm:"approx_pay_ratio" json:"approxPayRatio"`                                                                           // 淘系近似购买率
	LiveEventGoodsView                               interface{} `orm:"live_event_goods_view" json:"liveEventGoodsView"`                                                                  // 直播间商品点击数
	LivePlayed3S                                     interface{} `orm:"live_played_3s" json:"livePlayed3S"`                                                                               // 直播观看数
	AdProductCnt                                     interface{} `orm:"ad_product_cnt" json:"adProductCnt"`                                                                               // 商品成交数
	EventGoodsView                                   interface{} `orm:"event_goods_view" json:"eventGoodsView"`                                                                           // 商品访问数
	EventGoodsViewCost                               interface{} `orm:"event_goods_view_cost" json:"eventGoodsViewCost"`                                                                  // 商品访问成本
	MerchantRecoFans                                 interface{} `orm:"merchant_reco_fans" json:"merchantRecoFans"`                                                                       // 涨粉量
	MerchantRecoFansCost                             interface{} `orm:"merchant_reco_fans_cost" json:"merchantRecoFansCost"`                                                              // 涨粉成本
	EventOrderAmountRoi                              interface{} `orm:"event_order_amount_roi" json:"eventOrderAmountRoi"`                                                                // 小店推广roi
	EventNewUserPay                                  interface{} `orm:"event_new_user_pay" json:"eventNewUserPay"`                                                                        // 新增付费人数
	EventNewUserPayCost                              interface{} `orm:"event_new_user_pay_cost" json:"eventNewUserPayCost"`                                                               // 新增付费人数成本
	EventNewUserPayRatio                             interface{} `orm:"event_new_user_pay_ratio" json:"eventNewUserPayRatio"`                                                             // 新增付费人数率
	EventNewUserJinjianApp                           interface{} `orm:"event_new_user_jinjian_app" json:"eventNewUserJinjianApp"`                                                         // 新增完件人数
	EventNewUserJinjianAppCost                       interface{} `orm:"event_new_user_jinjian_app_cost" json:"eventNewUserJinjianAppCost"`                                                // 新增完件人数成本
	EventNewUserJinjianAppRoi                        interface{} `orm:"event_new_user_jinjian_app_roi" json:"eventNewUserJinjianAppRoi"`                                                  // 新增完件人数率
	EventNewUserCreditGrantApp                       interface{} `orm:"event_new_user_credit_grant_app" json:"eventNewUserCreditGrantApp"`                                                // 新增授信人数
	EventNewUserCreditGrantAppCost                   interface{} `orm:"event_new_user_credit_grant_app_cost" json:"eventNewUserCreditGrantAppCost"`                                       // 新增授信人数成本
	EventNewUserCreditGrantAppRoi                    interface{} `orm:"event_new_user_credit_grant_app_roi" json:"eventNewUserCreditGrantAppRoi"`                                         // 新增授信人数率
	EventNewUserJinjianPage                          interface{} `orm:"event_new_user_jinjian_page" json:"eventNewUserJinjianPage"`                                                       // 字段描述，需要修改
	EventNewUserJinjianPageCost                      interface{} `orm:"event_new_user_jinjian_page_cost" json:"eventNewUserJinjianPageCost"`                                              // 字段描述，需要修改
	EventNewUserJinjianPageRoi                       interface{} `orm:"event_new_user_jinjian_page_roi" json:"eventNewUserJinjianPageRoi"`                                                // 字段描述，需要修改
	EventNewUserCreditGrantPage                      interface{} `orm:"event_new_user_credit_grant_page" json:"eventNewUserCreditGrantPage"`                                              // 字段描述，需要修改
	EventNewUserCreditGrantPageCost                  interface{} `orm:"event_new_user_credit_grant_page_cost" json:"eventNewUserCreditGrantPageCost"`                                     // 字段描述，需要修改
	EventNewUserCreditGrantPageRoi                   interface{} `orm:"event_new_user_credit_grant_page_roi" json:"eventNewUserCreditGrantPageRoi"`                                       // 字段描述，需要修改
	EventAppointForm                                 interface{} `orm:"event_appoint_form" json:"eventAppointForm"`                                                                       // 预约表单数
	EventAppointFormCost                             interface{} `orm:"event_appoint_form_cost" json:"eventAppointFormCost"`                                                              // 预约表单点击成本
	EventAppointFormRatio                            interface{} `orm:"event_appoint_form_ratio" json:"eventAppointFormRatio"`                                                            // 预约表单点击率
	EventAppointJumpClick                            interface{} `orm:"event_appoint_jump_click" json:"eventAppointJumpClick"`                                                            // 预约跳转点击数
	EventAppointJumpClickCost                        interface{} `orm:"event_appoint_jump_click_cost" json:"eventAppointJumpClickCost"`                                                   // 预约跳转点击成本
	EventAppointJumpClickRatio                       interface{} `orm:"event_appoint_jump_click_ratio" json:"eventAppointJumpClickRatio"`                                                 // 预约跳转点击率
	UnionEventPayPurchaseAmount7D                    interface{} `orm:"union_event_pay_purchase_amount_7d" json:"unionEventPayPurchaseAmount7D"`                                          // 联盟广告收入
	UnionEventPayPurchaseAmount7DRoi                 interface{} `orm:"union_event_pay_purchase_amount_7d_roi" json:"unionEventPayPurchaseAmount7DRoi"`                                   // 联盟变现ROI
	EventDspGiftForm                                 interface{} `orm:"event_dsp_gift_form" json:"eventDspGiftForm"`                                                                      // 附加组件表单提交
	EventCreditCardRecheck                           interface{} `orm:"event_credit_card_recheck" json:"eventCreditCardRecheck"`                                                          // 信用卡核卡数
	EventCreditCardRecheckFirstDay                   interface{} `orm:"event_credit_card_recheck_first_day" json:"eventCreditCardRecheckFirstDay"`                                        // 信用卡首日核卡数
	KeyAction                                        interface{} `orm:"key_action" json:"keyAction"`                                                                                      // 关键行为数
	KeyActionCost                                    interface{} `orm:"key_action_cost" json:"keyActionCost"`                                                                             // 关键行为成本
	KeyActionRatio                                   interface{} `orm:"key_action_ratio" json:"keyActionRatio"`                                                                           // 关键行为率
	KeyInappAction0DCnt                              interface{} `orm:"key_inapp_action_0d_cnt" json:"keyInappAction0DCnt"`                                                               // T0全量授信数
	KeyInappAction3DCnt                              interface{} `orm:"key_inapp_action_3d_cnt" json:"keyInappAction3DCnt"`                                                               // T3全量授信数
	KeyInappAction0DCntCost                          interface{} `orm:"key_inapp_action_0d_cnt_cost" json:"keyInappAction0DCntCost"`                                                      // T0全量授信成本
	KeyInappAction3DCntCost                          interface{} `orm:"key_inapp_action_3d_cnt_cost" json:"keyInappAction3DCntCost"`                                                      // T3全量授信成本
	KeyInappAction0DCntRatio                         interface{} `orm:"key_inapp_action_0d_cnt_ratio" json:"keyInappAction0DCntRatio"`                                                    // T0全量授信通过率
	KeyInappAction3DCntRatio                         interface{} `orm:"key_inapp_action_3d_cnt_ratio" json:"keyInappAction3DCntRatio"`                                                    // T3全量授信通过率
	DrawCreditLine0DCnt                              interface{} `orm:"draw_credit_line_0d_cnt" json:"drawCreditLine0DCnt"`                                                               // T0用信数
	DrawCreditLine0DCntCost                          interface{} `orm:"draw_credit_line_0d_cnt_cost" json:"drawCreditLine0DCntCost"`                                                      // T0用信成本
	DrawCreditLine0DCntRatio                         interface{} `orm:"draw_credit_line_0d_cnt_ratio" json:"drawCreditLine0DCntRatio"`                                                    // T0授信用信率
	EventNoIntention                                 interface{} `orm:"event_no_intention" json:"eventNoIntention"`                                                                       // 用户无意向数
	AdScene                                          interface{} `orm:"ad_scene" json:"adScene"`                                                                                          // 广告场景
	AdScene2                                         interface{} `orm:"ad_scene_2" json:"adScene2"`                                                                                       // 广告场景2
	PlacementType                                    interface{} `orm:"placement_type" json:"placementType"`                                                                              // 投放类型
	CreatedAt                                        *gtime.Time `orm:"created_at" json:"createdAt"`                                                                                      // 创建时间
	UpdatedAt                                        *gtime.Time `orm:"updated_at" json:"updatedAt"`                                                                                      // 更新时间
}
