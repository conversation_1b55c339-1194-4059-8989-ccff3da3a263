// ==========================================================================
// GFast自动生成controller操作代码。
// 生成日期：2025-08-23 17:40:19
// 生成路径: internal/app/ad/controller/ks_advertiser_strategy_task_campaign.go
// 生成人：cyao
// desc:快手广告搭建-任务-广告计划
// company:云南奇讯科技有限公司
// ==========================================================================

package controller

import (
	"context"

	"github.com/tiger1103/gfast/v3/api/v1/ad"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	systemController "github.com/tiger1103/gfast/v3/internal/app/system/controller"
)

type ksAdvertiserStrategyTaskCampaignController struct {
	systemController.BaseController
}

var KsAdvertiserStrategyTaskCampaign = new(ksAdvertiserStrategyTaskCampaignController)

// List 列表
func (c *ksAdvertiserStrategyTaskCampaignController) List(ctx context.Context, req *ad.KsAdvertiserStrategyTaskCampaignSearchReq) (res *ad.KsAdvertiserStrategyTaskCampaignSearchRes, err error) {
	res = new(ad.KsAdvertiserStrategyTaskCampaignSearchRes)
	res.KsAdvertiserStrategyTaskCampaignSearchRes, err = service.KsAdvertiserStrategyTaskCampaign().List(ctx, &req.KsAdvertiserStrategyTaskCampaignSearchReq)
	return
}

// Get 获取快手广告搭建-任务-广告计划
func (c *ksAdvertiserStrategyTaskCampaignController) Get(ctx context.Context, req *ad.KsAdvertiserStrategyTaskCampaignGetReq) (res *ad.KsAdvertiserStrategyTaskCampaignGetRes, err error) {
	res = new(ad.KsAdvertiserStrategyTaskCampaignGetRes)
	res.KsAdvertiserStrategyTaskCampaignInfoRes, err = service.KsAdvertiserStrategyTaskCampaign().GetByTaskCampaignId(ctx, req.TaskCampaignId)
	return
}

// Add 添加快手广告搭建-任务-广告计划
func (c *ksAdvertiserStrategyTaskCampaignController) Add(ctx context.Context, req *ad.KsAdvertiserStrategyTaskCampaignAddReq) (res *ad.KsAdvertiserStrategyTaskCampaignAddRes, err error) {
	err = service.KsAdvertiserStrategyTaskCampaign().Add(ctx, req.KsAdvertiserStrategyTaskCampaignAddReq)
	return
}

// Edit 修改快手广告搭建-任务-广告计划
func (c *ksAdvertiserStrategyTaskCampaignController) Edit(ctx context.Context, req *ad.KsAdvertiserStrategyTaskCampaignEditReq) (res *ad.KsAdvertiserStrategyTaskCampaignEditRes, err error) {
	err = service.KsAdvertiserStrategyTaskCampaign().Edit(ctx, req.KsAdvertiserStrategyTaskCampaignEditReq)
	return
}

// Delete 删除快手广告搭建-任务-广告计划
func (c *ksAdvertiserStrategyTaskCampaignController) Delete(ctx context.Context, req *ad.KsAdvertiserStrategyTaskCampaignDeleteReq) (res *ad.KsAdvertiserStrategyTaskCampaignDeleteRes, err error) {
	err = service.KsAdvertiserStrategyTaskCampaign().Delete(ctx, req.TaskCampaignIds)
	return
}
