// ==========================================================================
// GFast自动生成model entity操作代码。
// 生成日期：2025-08-14 14:09:04
// 生成路径: internal/app/ad/model/entity/ks_advertiser_account_info.go
// 生成人：cyao
// desc:快手广告账户表格
// company:云南奇讯科技有限公司
// ==========================================================================

package do

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
)

// KsAdvertiserAccountInfo is the golang structure for table ks_advertiser_account_info.
type KsAdvertiserAccountInfo struct {
	gmeta.Meta            `orm:"table:ks_advertiser_account_info, do:true"`
	AccountId             interface{} `orm:"account_id,primary" json:"accountId"`                   // 广告主ID
	AgentAccountId        interface{} `orm:"agent_account_id" json:"agentAccountId"`                // 代理商账户id
	AuthorizeKsAccount    interface{} `orm:"authorize_ks_account" json:"authorizeKsAccount"`        // 授权快手账号(代理商)
	UserId                interface{} `orm:"user_id" json:"userId"`                                 // 快手账户ID
	AccountName           interface{} `orm:"account_name" json:"accountName"`                       // 快手账户名称
	ResponsiblePerson     interface{} `orm:"responsible_person" json:"responsiblePerson"`           // 销售责任人
	UcType                interface{} `orm:"uc_type" json:"ucType"`                                 // 账户类型
	PaymentType           interface{} `orm:"payment_type" json:"paymentType"`                       // 付款类型
	Balance               interface{} `orm:"balance" json:"balance"`                                // 现金余额
	CreditBalance         interface{} `orm:"credit_balance" json:"creditBalance"`                   // 信用账户余额
	ExtendedBalance       interface{} `orm:"extended_balance" json:"extendedBalance"`               // 预留账户余额
	Rebate                interface{} `orm:"rebate" json:"rebate"`                                  // 后返余额
	PreRebate             interface{} `orm:"pre_rebate" json:"preRebate"`                           // 前返余额
	ContractRebate        interface{} `orm:"contract_rebate" json:"contractRebate"`                 // 框返余额
	TotalBalance          interface{} `orm:"total_balance" json:"totalBalance"`                     // 总余额
	LoLimit               interface{} `orm:"lo_limit" json:"loLimit"`                               // 账户最低余额
	SingleOut             interface{} `orm:"single_out" json:"singleOut"`                           // 单次转账金额
	AutoOut               interface{} `orm:"auto_out" json:"autoOut"`                               // 自动转账状态
	BalanceWarn           interface{} `orm:"balance_warn" json:"balanceWarn"`                       // 余额不足提醒
	ProductName           interface{} `orm:"product_name" json:"productName"`                       // 产品名称
	FirstCostDay          interface{} `orm:"first_cost_day" json:"firstCostDay"`                    // 首日消耗日期
	Industry              interface{} `orm:"industry" json:"industry"`                              // 一级行业
	SecondIndustry        interface{} `orm:"second_industry" json:"secondIndustry"`                 // 二级行业
	Recharged             interface{} `orm:"recharged" json:"recharged"`                            // 是否充值
	CorporationName       interface{} `orm:"corporation_name" json:"corporationName"`               // 企业名称
	ReviewStatus          interface{} `orm:"review_status" json:"reviewStatus"`                     // 审核状态 1-审核中; 2-审核通过; 3-审核拒绝; 0-待提交
	FrozenStatus          interface{} `orm:"frozen_status" json:"frozenStatus"`                     // 冻结状态
	TransferAccountStatus interface{} `orm:"transfer_account_status" json:"transferAccountStatus"`  // 转账状态
	ChildReviewStatusInfo interface{} `orm:"child_review_status_info" json:"childReviewStatusInfo"` // 审核状态信息（嵌套结构体）
	CopyAccount           interface{} `orm:"copy_account" json:"copyAccount"`                       // 是否为复制账户
	ReviewDetail          interface{} `orm:"review_detail" json:"reviewDetail"`                     // 审核详情（数组嵌套结构体）
	DirectRebate          interface{} `orm:"direct_rebate" json:"directRebate"`                     // 激励余额
	OptimizerOwner        interface{} `orm:"optimizer_owner" json:"optimizerOwner"`                 // 优化师责任人
	AccountAutoManage     interface{} `orm:"account_auto_manage" json:"accountAutoManage"`          // 账户智投开关
	DayBudget             interface{} `orm:"day_budget" json:"dayBudget"`                           // 单日预算 单位：厘
	Remark                interface{} `orm:"remark" json:"remark"`                                  // 备注
	AuthSource            interface{} `orm:"auth_source" json:"authSource"`                         // 授权来源（如代理商账户授权 1 广告主授权 2）
	AuthStatus            interface{} `orm:"auth_status" json:"authStatus"`                         // 授权状态（0:待授权, 1:已授权）
	DeliveryStatus        interface{} `orm:"delivery_status" json:"deliveryStatus"`                 // 投放状态（0:停用, 1:启用）
	Owner                 interface{} `orm:"owner" json:"owner"`                                    // 归属人员
	DeliveryType          interface{} `orm:"delivery_type" json:"deliveryType"`                     // 投放方式（0:默认, 1:优先效果）暂时先没用到
	EffectFirst           interface{} `orm:"effect_first" json:"effectFirst"`                       // 优先效果策略（1:开启, 其他:未开启）暂时先没用到
	CreateTime            interface{} `orm:"create_time" json:"createTime"`                         // 创建时间
	CreatedAt             *gtime.Time `orm:"created_at" json:"createdAt"`                           // 创建时间记录产生时间
	UpdatedAt             *gtime.Time `orm:"updated_at" json:"updatedAt"`                           // 更新时间
}
