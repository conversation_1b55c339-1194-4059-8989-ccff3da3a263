// ==========================================================================
// GFast自动生成service操作代码。
// 生成日期：2025-08-13 16:34:00
// 生成路径: internal/app/ad/service/ks_advertiser_campaign.go
// 生成人：cyao
// desc:快手广告计划
// company:云南奇讯科技有限公司
// ==========================================================================

package service

import (
	"context"

	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
)

type IKsAdvertiserCampaign interface {
	List(ctx context.Context, req *model.KsAdvertiserCampaignSearchReq) (res *model.KsAdvertiserCampaignSearchRes, err error)
	GetByCampaignId(ctx context.Context, CampaignId int64) (res *model.KsAdvertiserCampaignInfoRes, err error)
	PullCampaignByAdId(ctx context.Context, accessToken string, adId int64) (err error)
	Add(ctx context.Context, req *model.KsAdvertiserCampaignAddReq) (err error)
	Edit(ctx context.Context, req *model.KsAdvertiserCampaignEditReq) (err error)
	Delete(ctx context.Context, CampaignId []int64) (err error)
	UpdateCampaignInfo(ctx context.Context, req *model.KsAdvertiserCampaignUpdateReq) (err error)
	ManualSyncCampaign(ctx context.Context, advertiserIds []int64, startTime, endTime string) (err error)
	EditCampaignName(ctx context.Context, campaignId int64, campaignName string) (err error)
}

var localKsAdvertiserCampaign IKsAdvertiserCampaign

func KsAdvertiserCampaign() IKsAdvertiserCampaign {
	if localKsAdvertiserCampaign == nil {
		panic("implement not found for interface IKsAdvertiserCampaign, forgot register?")
	}
	return localKsAdvertiserCampaign
}

func RegisterKsAdvertiserCampaign(i IKsAdvertiserCampaign) {
	localKsAdvertiserCampaign = i
}
