// ==========================================================================
// GFast自动生成model entity操作代码。
// 生成日期：2025-08-22 11:51:48
// 生成路径: internal/app/ad/model/entity/ks_advertiser_strategy_config.go
// 生成人：cq
// desc:快手广告搭建-策略配置
// company:云南奇讯科技有限公司
// ==========================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
)

// KsAdvertiserStrategyConfig is the golang structure for table ks_advertiser_strategy_config.
type KsAdvertiserStrategyConfig struct {
	gmeta.Meta       `orm:"table:ks_advertiser_strategy_config"`
	Id               int64       `orm:"id,primary" json:"id"`                       //
	StrategyId       string      `orm:"strategy_id" json:"strategyId"`              // 策略组ID
	StrategyName     string      `orm:"strategy_name" json:"strategyName"`          // 策略组名称
	StrategyDescribe string      `orm:"strategy_describe" json:"strategyDescribe"`  // 描述
	TaskId           string      `orm:"task_id" json:"taskId"`                      // 任务ID
	AdvertiserIds    []string    `orm:"advertiser_ids" json:"advertiserIds"`        // 广告主ID列表
	AccountBatchRule string      `orm:"account_batch_rule" json:"accountBatchRule"` // 多账户分配规则 按需分配：ON_DEMAND
	AdGroupRule      string      `orm:"ad_group_rule" json:"adGroupRule"`           // 广告分组规则 按定向包分组：AUDIENCE_PACKAGE 按创意分组：CREATIVE 按文案分组：TITLE
	AdType           int         `orm:"ad_type" json:"adType"`                      // 广告计划类型 0:信息流（展示广告） 1:搜索（搜索广告）
	UserId           int         `orm:"user_id" json:"userId"`                      // 归属人员
	CreatedAt        *gtime.Time `orm:"created_at" json:"createdAt"`                // 创建时间
	UpdatedAt        *gtime.Time `orm:"updated_at" json:"updatedAt"`                // 更新时间
	DeletedAt        *gtime.Time `orm:"deleted_at" json:"deletedAt"`                // 删除时间
}
