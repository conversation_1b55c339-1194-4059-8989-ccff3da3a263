package api

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
)

// UpdateCampaignStatusService 修改广告计划状态
type UpdateCampaignStatusService struct {
	ctx     context.Context
	cfg     *Configuration
	token   string
	Request *UpdateCampaignStatusReq
}

// UpdateCampaignStatusReq 请求结构体
type UpdateCampaignStatusReq struct {
	CampaignId   int64   `json:"campaign_id"`   // 广告计划ID
	PutStatus    int64   `json:"put_status"`    // 要修改的状态 1-投放、2-暂停、3-删除，传其他数字非法。.put_status 为 3 时，会删除广告计划，和计划下的所有广告组，程序化创意，创意、搜索关键词
	CampaignIds  []int64 `json:"campaign_ids"`  // 传入的计划 id 不得重复，且至少有一个;传入的 campaign_id 总数最多 20 个
	AdvertiserId int64   `json:"advertiser_id"` // 广告主ID
}

// UpdateCampaignStatusData API响应结构体
type UpdateCampaignStatusData struct {
	CampaignId  int64   `json:"campaign_id"`  // 广告计划id
	CampaignIds []int64 `json:"campaign_ids"` // 广告计划id集合
}

func (r *UpdateCampaignStatusService) SetCfg(cfg *Configuration) *UpdateCampaignStatusService {
	r.cfg = cfg
	return r
}

func (r *UpdateCampaignStatusService) SetReq(req UpdateCampaignStatusReq) *UpdateCampaignStatusService {
	r.Request = &req
	return r
}

func (r *UpdateCampaignStatusService) AccessToken(accessToken string) *UpdateCampaignStatusService {
	r.token = accessToken
	return r
}

func (r *UpdateCampaignStatusService) Do() (data *KsBaseResp[UpdateCampaignStatusData], err error) {
	localBasePath := r.cfg.BasePath
	localVarPath := localBasePath + "/rest/openapi/v1/campaign/update/status"
	response, err := r.cfg.HTTPClient.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("Access-Token", r.token).
		SetBody(r.Request).
		SetResult(&KsBaseResp[UpdateCampaignStatusData]{}).
		Post(localVarPath)
	if err != nil {
		return nil, err
	}
	resp := new(KsBaseResp[UpdateCampaignStatusData])
	// 将 JSON 响应解码到结构体中
	err = json.Unmarshal(response.Body(), &resp)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("/rest/openapi/v1/campaign/update/status解析响应出错: %v\n", err))
	}
	if resp.Code == 0 && resp.Data != nil {
		return resp, nil
	} else {
		return resp, errors.New(resp.Message)
	}
}
