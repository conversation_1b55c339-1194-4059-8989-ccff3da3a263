// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2025-08-23 17:40:19
// 生成路径: api/v1/ad/ks_advertiser_strategy_task_campaign.go
// 生成人：cyao
// desc:快手广告搭建-任务-广告计划相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package ad

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
)

// KsAdvertiserStrategyTaskCampaignSearchReq 分页请求参数
type KsAdvertiserStrategyTaskCampaignSearchReq struct {
	g.Meta `path:"/list" tags:"快手广告搭建-任务-广告计划" method:"get" summary:"快手广告搭建-任务-广告计划列表"`
	commonApi.Author
	model.KsAdvertiserStrategyTaskCampaignSearchReq
}

// KsAdvertiserStrategyTaskCampaignSearchRes 列表返回结果
type KsAdvertiserStrategyTaskCampaignSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.KsAdvertiserStrategyTaskCampaignSearchRes
}

// KsAdvertiserStrategyTaskCampaignAddReq 添加操作请求参数
type KsAdvertiserStrategyTaskCampaignAddReq struct {
	g.Meta `path:"/add" tags:"快手广告搭建-任务-广告计划" method:"post" summary:"快手广告搭建-任务-广告计划添加"`
	commonApi.Author
	*model.KsAdvertiserStrategyTaskCampaignAddReq
}

// KsAdvertiserStrategyTaskCampaignAddRes 添加操作返回结果
type KsAdvertiserStrategyTaskCampaignAddRes struct {
	commonApi.EmptyRes
}

// KsAdvertiserStrategyTaskCampaignEditReq 修改操作请求参数
type KsAdvertiserStrategyTaskCampaignEditReq struct {
	g.Meta `path:"/edit" tags:"快手广告搭建-任务-广告计划" method:"put" summary:"快手广告搭建-任务-广告计划修改"`
	commonApi.Author
	*model.KsAdvertiserStrategyTaskCampaignEditReq
}

// KsAdvertiserStrategyTaskCampaignEditRes 修改操作返回结果
type KsAdvertiserStrategyTaskCampaignEditRes struct {
	commonApi.EmptyRes
}

// KsAdvertiserStrategyTaskCampaignGetReq 获取一条数据请求
type KsAdvertiserStrategyTaskCampaignGetReq struct {
	g.Meta `path:"/get" tags:"快手广告搭建-任务-广告计划" method:"get" summary:"获取快手广告搭建-任务-广告计划信息"`
	commonApi.Author
	TaskCampaignId string `p:"taskCampaignId" v:"required#主键必须"` //通过主键获取
}

// KsAdvertiserStrategyTaskCampaignGetRes 获取一条数据结果
type KsAdvertiserStrategyTaskCampaignGetRes struct {
	g.Meta `mime:"application/json"`
	*model.KsAdvertiserStrategyTaskCampaignInfoRes
}

// KsAdvertiserStrategyTaskCampaignDeleteReq 删除数据请求
type KsAdvertiserStrategyTaskCampaignDeleteReq struct {
	g.Meta `path:"/delete" tags:"快手广告搭建-任务-广告计划" method:"delete" summary:"删除快手广告搭建-任务-广告计划"`
	commonApi.Author
	TaskCampaignIds []string `p:"taskCampaignIds" v:"required#主键必须"` //通过主键删除
}

// KsAdvertiserStrategyTaskCampaignDeleteRes 删除数据返回
type KsAdvertiserStrategyTaskCampaignDeleteRes struct {
	commonApi.EmptyRes
}
