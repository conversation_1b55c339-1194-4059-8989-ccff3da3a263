// ==========================================================================
// GFast自动生成service操作代码。
// 生成日期：2025-08-19 10:36:49
// 生成路径: internal/app/ad/service/ks_advertiser_common_asset_category.go
// 生成人：cq
// desc:快手通用资产-标题分类
// company:云南奇讯科技有限公司
// ==========================================================================

package service

import (
	"context"

	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
)

type IKsAdvertiserCommonAssetCategory interface {
	List(ctx context.Context, req *model.KsAdvertiserCommonAssetCategorySearchReq) (res *model.KsAdvertiserCommonAssetCategorySearchRes, err error)
	GetById(ctx context.Context, Id int) (res *model.KsAdvertiserCommonAssetCategoryInfoRes, err error)
	Add(ctx context.Context, req *model.KsAdvertiserCommonAssetCategoryAddReq) (lastInsertId int64, err error)
	Edit(ctx context.Context, req *model.KsAdvertiserCommonAssetCategoryEditReq) (err error)
	Delete(ctx context.Context, Id []int) (err error)
}

var localKsAdvertiserCommonAssetCategory IKsAdvertiserCommonAssetCategory

func KsAdvertiserCommonAssetCategory() IKsAdvertiserCommonAssetCategory {
	if localKsAdvertiserCommonAssetCategory == nil {
		panic("implement not found for interface IKsAdvertiserCommonAssetCategory, forgot register?")
	}
	return localKsAdvertiserCommonAssetCategory
}

func RegisterKsAdvertiserCommonAssetCategory(i IKsAdvertiserCommonAssetCategory) {
	localKsAdvertiserCommonAssetCategory = i
}
