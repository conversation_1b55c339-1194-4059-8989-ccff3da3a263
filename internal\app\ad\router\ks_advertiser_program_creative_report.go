// ==========================================================================
// GFast自动生成router操作代码。
// 生成日期：2025-08-20 11:00:01
// 生成路径: internal/app/ad/router/ks_advertiser_program_creative_report.go
// 生成人：cyao
// desc:创意数据
// company:云南奇讯科技有限公司
// ==========================================================================

package router

import (
	"context"

	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/tiger1103/gfast/v3/internal/app/ad/controller"
)

func (router *Router) BindKsAdvertiserProgramCreativeReportController(ctx context.Context, group *ghttp.RouterGroup) {
	group.Group("/ksAdvertiserProgramCreativeReport", func(group *ghttp.RouterGroup) {
		group.Bind(
			controller.KsAdvertiserProgramCreativeReport,
		)
	})
}
