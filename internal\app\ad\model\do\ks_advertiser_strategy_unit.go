// ==========================================================================
// GFast自动生成model entity操作代码。
// 生成日期：2025-08-22 11:52:52
// 生成路径: internal/app/ad/model/entity/ks_advertiser_strategy_unit.go
// 生成人：cq
// desc:快手策略组-广告组
// company:云南奇讯科技有限公司
// ==========================================================================

package do

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
)

// KsAdvertiserStrategyUnit is the golang structure for table ks_advertiser_strategy_unit.
type KsAdvertiserStrategyUnit struct {
	gmeta.Meta                `orm:"table:ks_advertiser_strategy_unit, do:true"`
	Id                        interface{} `orm:"id,primary" json:"id"`                                          // 主键ID
	StrategyId                interface{} `orm:"strategy_id" json:"strategyId"`                                 // 策略组ID
	TaskId                    interface{} `orm:"task_id" json:"taskId"`                                         // 任务ID
	SceneCategory             interface{} `orm:"scene_category" json:"sceneCategory"`                           // 投放版位 0：快手主站
	SceneId                   interface{} `orm:"scene_id" json:"sceneId"`                                       // 资源位置数组
	KsUserId                  interface{} `orm:"ks_user_id" json:"ksUserId"`                                    // 快手号
	ShortPlayAllocationMethod interface{} `orm:"short_play_allocation_method" json:"shortPlayAllocationMethod"` // 短剧分配方式 全部相同：SAME 按账户分配：ADVERTISER 按广告计划分配：CAMPAIGN 按广告组分配：UNIT
	ShortPlayData             interface{} `orm:"short_play_data" json:"shortPlayData"`                          // 选择短剧与集数
	ProductAllocationMethod   interface{} `orm:"product_allocation_method" json:"productAllocationMethod"`      // 产品分配方式 全部相同：SAME 按账户分配：ADVERTISER 按广告计划分配：CAMPAIGN 按广告组分配：UNIT
	ProductData               interface{} `orm:"product_data" json:"productData"`                               // 关联商品
	QuickSearch               interface{} `orm:"quick_search" json:"quickSearch"`                               // 搜索快投开关 0：不开启（默认填充）；1：开启搜索快投
	TargetExplore             interface{} `orm:"target_explore" json:"targetExplore"`                           // 搜索人群探索 0：不开启；1：开启
	NegativeWordParam         interface{} `orm:"negative_word_param" json:"negativeWordParam"`                  // 搜索广告否词
	BeginTime                 interface{} `orm:"begin_time" json:"beginTime"`                                   // 投放开始时间 格式为 yyyy-MM-dd
	EndTime                   interface{} `orm:"end_time" json:"endTime"`                                       // 投放结束时间 格式为 yyyy-MM-dd
	ScheduleTime              interface{} `orm:"schedule_time" json:"scheduleTime"`                             // 投放时间段 24*7位字符串
	DayBudget                 interface{} `orm:"day_budget" json:"dayBudget"`                                   // 日预算 不限传0
	OcpxActionType            interface{} `orm:"ocpx_action_type" json:"ocpxActionType"`                        // 转化目标（优化目标）
	BidType                   interface{} `orm:"bid_type" json:"bidType"`                                       // 计费方式（优化目标出价类型） 2：CPC；10：OCPM；12:MCB最大转化
	BidAllocationMethod       interface{} `orm:"bid_allocation_method" json:"bidAllocationMethod"`              // 出价分配规则 全部相同：SAME 按账户分配：ADVERTISER
	BidData                   interface{} `orm:"bid_data" json:"bidData"`                                       // 出价信息
	UnitName                  interface{} `orm:"unit_name" json:"unitName"`                                     // 广告组名称
	PutStatus                 interface{} `orm:"put_status" json:"putStatus"`                                   // 广告组默认状态 1：广告组投放中；2：广告组暂停投放
	CreatedAt                 *gtime.Time `orm:"created_at" json:"createdAt"`                                   // 创建时间
	UpdatedAt                 *gtime.Time `orm:"updated_at" json:"updatedAt"`                                   // 更新时间
	DeletedAt                 *gtime.Time `orm:"deleted_at" json:"deletedAt"`                                   // 删除时间
}
