// ==========================================================================
// GFast自动生成model操作代码。
// 生成日期：2025-08-22 11:52:27
// 生成路径: internal/app/ad/model/ks_advertiser_strategy_material.go
// 生成人：cq
// desc:快手策略组-素材
// company:云南奇讯科技有限公司
// ==========================================================================

package model

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
	adxModel "github.com/tiger1103/gfast/v3/internal/app/adx/model"
	commonConsts "github.com/tiger1103/gfast/v3/internal/app/common/consts"
	comModel "github.com/tiger1103/gfast/v3/internal/app/common/model"
)

// KsAdvertiserStrategyMaterialInfoRes is the golang structure for table ks_advertiser_strategy_material.
type KsAdvertiserStrategyMaterialInfoRes struct {
	gmeta.Meta               `orm:"table:ks_advertiser_strategy_material"`
	Id                       uint64                                      `orm:"id,primary" json:"id" dc:"主键ID"`                                                                                  // 主键ID
	StrategyId               string                                      `orm:"strategy_id" json:"strategyId" dc:"策略组ID"`                                                                        // 策略组ID
	TaskId                   string                                      `orm:"task_id" json:"taskId" dc:"任务ID"`                                                                                 // 任务ID
	MaterialAllocationMethod commonConsts.CreateMaterialAllocationMethod `orm:"material_allocation_method" json:"materialAllocationMethod" dc:"素材分配方式 平均分配：AVERAGE 全账户服用：SAME 分账户选择：ADVERTISER"` // 素材分配方式 平均分配：AVERAGE 全账户服用：SAME 分账户选择：ADVERTISER
	VideoCount               int                                         `orm:"video_count" json:"videoCount" dc:"视频数量"`                                                                         // 视频数量
	VideoLocked              int                                         `orm:"video_locked" json:"videoLocked" dc:"视频锁定状态 0：未锁定 1：锁定"`                                                          // 视频锁定状态 0：未锁定 1：锁定
	ImageCount               int                                         `orm:"image_count" json:"imageCount" dc:"图片数量"`                                                                         // 图片数量
	ImageLocked              int                                         `orm:"image_locked" json:"imageLocked" dc:"图片锁定状态 0：未锁定 1：锁定"`                                                          // 图片锁定状态 0：未锁定 1：锁定
	DescriptionMatchMethod   commonConsts.KsDescriptionMatchMethod       `orm:"description_match_method" json:"descriptionMatchMethod" dc:"文案匹配方式 MANUAL MATERIAL CREATIVE"`                     // 文案匹配方式 MANUAL MATERIAL CREATIVE
	CreativeMaterialData     []*CreativeMaterialData                     `orm:"creative_material_data" json:"creativeMaterialData" dc:"创意素材数据，包含广告主ID、素材列表等信息"`                                  // 创意素材数据，包含广告主ID、素材列表等信息
	CreatedAt                *gtime.Time                                 `orm:"created_at" json:"createdAt" dc:"创建时间"`                                                                           // 创建时间
	UpdatedAt                *gtime.Time                                 `orm:"updated_at" json:"updatedAt" dc:"更新时间"`                                                                           // 更新时间
	DeletedAt                *gtime.Time                                 `orm:"deleted_at" json:"deletedAt" dc:"删除时间"`                                                                           // 删除时间
}

type KsAdvertiserStrategyMaterialListRes struct {
	Id                       uint64                                      `json:"id" dc:"主键ID"`
	StrategyId               string                                      `json:"strategyId" dc:"策略组ID"`
	TaskId                   string                                      `json:"taskId" dc:"任务ID"`
	MaterialAllocationMethod commonConsts.CreateMaterialAllocationMethod `json:"materialAllocationMethod" dc:"素材分配方式 平均分配：AVERAGE 全账户服用：SAME 分账户选择：ADVERTISER"`
	VideoCount               int                                         `json:"videoCount" dc:"视频数量"`
	VideoLocked              int                                         `json:"videoLocked" dc:"视频锁定状态 0：未锁定 1：锁定"`
	ImageCount               int                                         `json:"imageCount" dc:"图片数量"`
	ImageLocked              int                                         `json:"imageLocked" dc:"图片锁定状态 0：未锁定 1：锁定"`
	DescriptionMatchMethod   commonConsts.KsDescriptionMatchMethod       `json:"descriptionMatchMethod" dc:"文案匹配方式 MANUAL MATERIAL CREATIVE"`
	CreativeMaterialData     []*CreativeMaterialData                     `json:"creativeMaterialData" dc:"创意素材数据，包含广告主ID、素材列表等信息"`
	CreatedAt                *gtime.Time                                 `json:"createdAt" dc:"创建时间"`
}

// KsAdvertiserStrategyMaterialSearchReq 分页请求参数
type KsAdvertiserStrategyMaterialSearchReq struct {
	comModel.PageReq
	Id                       string                                      `p:"id" dc:"主键ID"`                                                                     //主键ID
	StrategyId               string                                      `p:"strategyId" dc:"策略组ID"`                                                            //策略组ID
	TaskId                   string                                      `p:"taskId" dc:"任务ID"`                                                                 //任务ID
	MaterialAllocationMethod commonConsts.CreateMaterialAllocationMethod `p:"materialAllocationMethod" dc:"素材分配方式 平均分配：AVERAGE 全账户服用：SAME 分账户选择：ADVERTISER"`    //素材分配方式 平均分配：AVERAGE 全账户服用：SAME 分账户选择：ADVERTISER
	VideoCount               string                                      `p:"videoCount" v:"videoCount@integer#视频数量需为整数" dc:"视频数量"`                             //视频数量
	VideoLocked              string                                      `p:"videoLocked" v:"videoLocked@integer#视频锁定状态 0：未锁定 1：锁定需为整数" dc:"视频锁定状态 0：未锁定 1：锁定"` //视频锁定状态 0：未锁定 1：锁定
	ImageCount               string                                      `p:"imageCount" v:"imageCount@integer#图片数量需为整数" dc:"图片数量"`                             //图片数量
	ImageLocked              string                                      `p:"imageLocked" v:"imageLocked@integer#图片锁定状态 0：未锁定 1：锁定需为整数" dc:"图片锁定状态 0：未锁定 1：锁定"` //图片锁定状态 0：未锁定 1：锁定
	DescriptionMatchMethod   commonConsts.KsDescriptionMatchMethod       `p:"descriptionMatchMethod" dc:"文案匹配方式 MANUAL MATERIAL CREATIVE"`                      //文案匹配方式 MANUAL MATERIAL CREATIVE
	CreativeMaterialData     []*CreativeMaterialData                     `p:"creativeMaterialData" dc:"创意素材数据，包含广告主ID、素材列表等信息"`                                 //创意素材数据，包含广告主ID、素材列表等信息
	CreatedAt                string                                      `p:"createdAt" v:"createdAt@datetime#创建时间需为YYYY-MM-DD hh:mm:ss格式" dc:"创建时间"`           //创建时间
}

// KsAdvertiserStrategyMaterialSearchRes 列表返回结果
type KsAdvertiserStrategyMaterialSearchRes struct {
	comModel.ListRes
	List []*KsAdvertiserStrategyMaterialListRes `json:"list"`
}

// KsAdvertiserStrategyMaterialAddReq 添加操作请求参数
type KsAdvertiserStrategyMaterialAddReq struct {
	StrategyId               string                                      `p:"-"  dc:"策略组ID"`
	TaskId                   string                                      `p:"-"  dc:"任务ID"`
	MaterialAllocationMethod commonConsts.CreateMaterialAllocationMethod `p:"materialAllocationMethod"  dc:"素材分配方式 平均分配：AVERAGE 全账户服用：SAME 分账户选择：ADVERTISER"`
	VideoCount               int                                         `p:"videoCount"  dc:"视频数量"`
	VideoLocked              int                                         `p:"videoLocked"  dc:"视频锁定状态 0：未锁定 1：锁定"`
	ImageCount               int                                         `p:"imageCount"  dc:"图片数量"`
	ImageLocked              int                                         `p:"imageLocked"  dc:"图片锁定状态 0：未锁定 1：锁定"`
	DescriptionMatchMethod   commonConsts.KsDescriptionMatchMethod       `p:"descriptionMatchMethod"  dc:"文案匹配方式 MANUAL MATERIAL CREATIVE"`
	CreativeMaterialData     []*CreativeMaterialData                     `p:"creativeMaterialData"  dc:"创意素材数据，包含广告主ID、素材列表等信息"`
}

// KsAdvertiserStrategyMaterialEditReq 修改操作请求参数
type KsAdvertiserStrategyMaterialEditReq struct {
	StrategyId               string                                      `p:"strategyId"  dc:"策略组ID"`
	TaskId                   string                                      `p:"taskId"  dc:"任务ID"`
	MaterialAllocationMethod commonConsts.CreateMaterialAllocationMethod `p:"materialAllocationMethod"  dc:"素材分配方式 平均分配：AVERAGE 全账户服用：SAME 分账户选择：ADVERTISER"`
	VideoCount               int                                         `p:"videoCount"  dc:"视频数量"`
	VideoLocked              int                                         `p:"videoLocked"  dc:"视频锁定状态 0：未锁定 1：锁定"`
	ImageCount               int                                         `p:"imageCount"  dc:"图片数量"`
	ImageLocked              int                                         `p:"imageLocked"  dc:"图片锁定状态 0：未锁定 1：锁定"`
	DescriptionMatchMethod   commonConsts.KsDescriptionMatchMethod       `p:"descriptionMatchMethod"  dc:"文案匹配方式 MANUAL MATERIAL CREATIVE"`
	CreativeMaterialData     []*CreativeMaterialData                     `p:"creativeMaterialData"  dc:"创意素材数据，包含广告主ID、素材列表等信息"`
}

type CreativeMaterialData struct {
	AdvertiserId      int64                `json:"advertiser_id"`
	CreativeMaterials []*CreativeMaterials `json:"creativeMaterials"`
}

type CreativeMaterials struct {
	MaterialTab   string                     `json:"materialTab,omitempty" dc:"类型  video  or  image"`
	Name          string                     `json:"name,omitempty" dc:"素材名"`
	Video         []*AdMaterialInfoRes       `json:"video,omitempty"`
	VideoCount    int                        `json:"videoCount,omitempty"`
	Image         []*AdMaterialInfoRes       `json:"image,omitempty"`
	ImageCount    int                        `json:"imageCount,omitempty"`
	AdxVideo      []*adxModel.AdxMaterialRes `json:"adxVideo,omitempty"`
	AdxVideoCount int                        `json:"adxVideoCount,omitempty"`
	AdxImage      []*adxModel.AdxMaterialRes `json:"adxImage,omitempty"`
	AdxImageCount int                        `json:"adxImageCount,omitempty"`
}
