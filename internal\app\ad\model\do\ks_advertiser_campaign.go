// ==========================================================================
// GFast自动生成model entity操作代码。
// 生成日期：2025-08-13 16:34:00
// 生成路径: internal/app/ad/model/entity/ks_advertiser_campaign.go
// 生成人：cyao
// desc:快手广告计划
// company:云南奇讯科技有限公司
// ==========================================================================

package do

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
)

// KsAdvertiserCampaign is the golang structure for table ks_advertiser_campaign.
type KsAdvertiserCampaign struct {
	gmeta.Meta                     `orm:"table:ks_advertiser_campaign, do:true"`
	CampaignId                     interface{} `orm:"campaign_id,primary" json:"campaignId"`                                    // 广告计划 ID
	AdvertiserId                   interface{} `orm:"advertiser_id" json:"advertiserId"`                                        // 广告id
	PhotoPackageDetails            interface{} `orm:"photo_package_details" json:"photoPackageDetails"`                         // 图片包详情（可能为 null）
	AdType                         interface{} `orm:"ad_type" json:"adType"`                                                    // 广告类型
	CampaignType                   interface{} `orm:"campaign_type" json:"campaignType"`                                        // 广告系列类型
	CampaignDeepConversionType     interface{} `orm:"campaign_deep_conversion_type" json:"campaignDeepConversionType"`          // 深度转化类型
	BidType                        interface{} `orm:"bid_type" json:"bidType"`                                                  // 出价类型
	PutStatus                      interface{} `orm:"put_status" json:"putStatus"`                                              // 投放状态
	CampaignOcpxActionTypeName     interface{} `orm:"campaign_ocpx_action_type_name" json:"campaignOcpxActionTypeName"`         // 智投计划优化目标名称 可能为null
	CapRoiRatio                    interface{} `orm:"cap_roi_ratio" json:"capRoiRatio"`                                         // ROI 上限比例
	CampaignOcpxActionType         interface{} `orm:"campaign_ocpx_action_type" json:"campaignOcpxActionType"`                  // OCPX 动作类型
	CampaignName                   interface{} `orm:"campaign_name" json:"campaignName"`                                        // 广告系列名称
	UpdateTime                     *gtime.Time `orm:"update_time" json:"updateTime"`                                            // 更新时间
	DspVersion                     interface{} `orm:"dsp_version" json:"dspVersion"`                                            // DSP 版本
	PeriodicDeliveryType           interface{} `orm:"periodic_delivery_type" json:"periodicDeliveryType"`                       // 周期投放类型
	CampaignDeepConversionTypeName interface{} `orm:"campaign_deep_conversion_type_name" json:"campaignDeepConversionTypeName"` // 智投计划深度优化目标名称
	CampaignSubType                interface{} `orm:"campaign_sub_type" json:"campaignSubType"`                                 // 广告系列子类型
	ConstraintCpa                  interface{} `orm:"constraint_cpa" json:"constraintCpa"`                                      // CPA 限制
	AutoAdjust                     interface{} `orm:"auto_adjust" json:"autoAdjust"`                                            // 是否自动调节
	ContinuePeriodType             interface{} `orm:"continue_period_type" json:"continuePeriodType"`                           // 连续周期类型
	ConstraintActionType           interface{} `orm:"constraint_action_type" json:"constraintActionType"`                       // 动作类型限制
	DayBudget                      interface{} `orm:"day_budget" json:"dayBudget"`                                              // 每日预算
	AutoManage                     interface{} `orm:"auto_manage" json:"autoManage"`                                            // 是否自动管理
	AutoPhotoScope                 interface{} `orm:"auto_photo_scope" json:"autoPhotoScope"`                                   // 自动图片范围
	CreateTime                     *gtime.Time `orm:"create_time" json:"createTime"`                                            // 创建时间
	AutoBuild                      interface{} `orm:"auto_build" json:"autoBuild"`                                              // 是否自动构建
	PeriodicDays                   interface{} `orm:"periodic_days" json:"periodicDays"`                                        // 周期天数
	CapBid                         interface{} `orm:"cap_bid" json:"capBid"`                                                    // 出价上限
	RangeBudget                    interface{} `orm:"range_budget" json:"rangeBudget"`                                          // 区间预算
	DayBudgetSchedule              interface{} `orm:"day_budget_schedule" json:"dayBudgetSchedule"`                             // 分日预算
	Status                         interface{} `orm:"status" json:"status"`                                                     // 状态
	UnitNameRule                   interface{} `orm:"unit_name_rule" json:"unitNameRule"`                                       // 单元命名规则
	CreativeNameRule               interface{} `orm:"creative_name_rule" json:"creativeNameRule"`                               // 创意命名规则
	CreatedAt                      *gtime.Time `orm:"created_at" json:"createdAt"`                                              // 创建时间
	DeletedAt                      *gtime.Time `orm:"deleted_at" json:"deletedAt"`                                              // 删除时间
}
