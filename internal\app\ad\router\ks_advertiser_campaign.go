// ==========================================================================
// GFast自动生成router操作代码。
// 生成日期：2025-08-13 16:34:00
// 生成路径: internal/app/ad/router/ks_advertiser_campaign.go
// 生成人：cyao
// desc:快手广告计划
// company:云南奇讯科技有限公司
// ==========================================================================

package router

import (
	"context"

	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/tiger1103/gfast/v3/internal/app/ad/controller"
)

func (router *Router) BindKsAdvertiserCampaignController(ctx context.Context, group *ghttp.RouterGroup) {
	group.Group("/ksAdvertiserCampaign", func(group *ghttp.RouterGroup) {
		group.Bind(
			controller.KsAdvertiserCampaign,
		)
	})
}
