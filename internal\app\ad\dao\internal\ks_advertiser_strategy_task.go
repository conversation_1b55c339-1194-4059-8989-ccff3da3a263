// ==========================================================================
// GFast自动生成dao internal操作代码。
// 生成日期：2025-08-23 17:40:11
// 生成路径: internal/app/ad/dao/internal/ks_advertiser_strategy_task.go
// 生成人：cyao
// desc:快手广告搭建-任务
// company:云南奇讯科技有限公司
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// KsAdvertiserStrategyTaskDao is the manager for logic model data accessing and custom defined data operations functions management.
type KsAdvertiserStrategyTaskDao struct {
	table   string                          // Table is the underlying table name of the DAO.
	group   string                          // Group is the database configuration group name of current DAO.
	columns KsAdvertiserStrategyTaskColumns // Columns is the short type for Columns, which contains all the column names of Table for convenient usage.
}

// KsAdvertiserStrategyTaskColumns defines and stores column names for table ks_advertiser_strategy_task.
type KsAdvertiserStrategyTaskColumns struct {
	TaskId        string // 任务ID
	TaskName      string // 任务名称
	TaskStatus    string // 任务状态 等待提交：INIT 素材上传中：MATERIAL_PROCESSING 项目等待提交：PROJECT_INIT 项目提交中：PROJECT_PROCESSING 广告等待提交：PROMOTION_INIT 广告提交中：PROMOTION_PROCESSING 已终止：TERMINATED 提交完成：FINISH
	AdvertiserIds string // 广告主ID列表
	CampaignNum   string // 广告计划数
	UnitNum       string // 广告组数
	RuleType      string // 提交规则类型 1：立即提交 2：定时提交
	UserId        string // 归属人员
	CreatedAt     string // 创建时间
	UpdatedAt     string // 更新时间
	DeletedAt     string // 删除时间
}

var ksAdvertiserStrategyTaskColumns = KsAdvertiserStrategyTaskColumns{
	TaskId:        "task_id",
	TaskName:      "task_name",
	TaskStatus:    "task_status",
	AdvertiserIds: "advertiser_ids",
	CampaignNum:   "campaign_num",
	UnitNum:       "unit_num",
	RuleType:      "rule_type",
	UserId:        "user_id",
	CreatedAt:     "created_at",
	UpdatedAt:     "updated_at",
	DeletedAt:     "deleted_at",
}

// NewKsAdvertiserStrategyTaskDao creates and returns a new DAO object for table data access.
func NewKsAdvertiserStrategyTaskDao() *KsAdvertiserStrategyTaskDao {
	return &KsAdvertiserStrategyTaskDao{
		group:   "default",
		table:   "ks_advertiser_strategy_task",
		columns: ksAdvertiserStrategyTaskColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *KsAdvertiserStrategyTaskDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *KsAdvertiserStrategyTaskDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *KsAdvertiserStrategyTaskDao) Columns() KsAdvertiserStrategyTaskColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *KsAdvertiserStrategyTaskDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *KsAdvertiserStrategyTaskDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *KsAdvertiserStrategyTaskDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
