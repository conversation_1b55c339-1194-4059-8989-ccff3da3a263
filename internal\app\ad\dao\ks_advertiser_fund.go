// ==========================================================================
// GFast自动生成dao操作代码。
// 生成日期：2025-08-12 15:45:08
// 生成路径: internal/app/ad/dao/ks_advertiser_fund.go
// 生成人：cyao
// desc:广告主资金信息
// company:云南奇讯科技有限公司
// ==========================================================================

package dao

import (
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao/internal"
)

// ksAdvertiserFundDao is the manager for logic model data accessing and custom defined data operations functions management.
// You can define custom methods on it to extend its functionality as you wish.
type ksAdvertiserFundDao struct {
	*internal.KsAdvertiserFundDao
}

var (
	// KsAdvertiserFund is globally public accessible object for table tools_gen_table operations.
	KsAdvertiserFund = ksAdvertiserFundDao{
		internal.NewKsAdvertiserFundDao(),
	}
)

// Fill with you ideas below.
