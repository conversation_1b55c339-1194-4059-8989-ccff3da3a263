// ==========================================================================
// GFast自动生成service操作代码。
// 生成日期：2025-08-14 14:09:05
// 生成路径: internal/app/ad/service/ks_advertiser_account_info.go
// 生成人：cyao
// desc:快手广告账户表格
// company:云南奇讯科技有限公司
// ==========================================================================

package service

import (
	"context"

	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
)

type IKsAdvertiserAccountInfo interface {
	List(ctx context.Context, req *model.KsAdvertiserAccountInfoSearchReq) (res *model.KsAdvertiserAccountInfoSearchRes, err error)
	GetImportList(ctx context.Context, req *model.KsAdvertiserGetImportListReq) (listRes *model.KsAdvertiserGetImportListRes, err error)
	BatchSetAccountOwner(ctx context.Context, req *model.KsAdvertiserAccountInfoSetOwnerReq) (err error)
	UpLoadVideo(ctx context.Context, req *model.UpLoadVideoReq) (listRes *model.UpLoadVideoRes, err error)
	GetByAccountId(ctx context.Context, AccountId int64) (res *model.KsAdvertiserAccountInfoInfoRes, err error)
	GetByAccountIds(ctx context.Context, accountIds []int64) (res []*model.KsAdvertiserAccountInfoInfoRes, err error)
	PullByAgentId(ctx context.Context, agentId, ksUserId int64) (err error)
	Import(ctx context.Context, req *model.KsAdvertiserAccountInfoImportReq) (accountIds []int, err error)
	Import2(ctx context.Context, req *model.KsAdvertiserAccountInfoImportReq) (err error)
	Add(ctx context.Context, req *model.KsAdvertiserAccountInfoAddReq) (err error)
	SyncKsAccountUnitCampaign(ctx context.Context) (err error)
	Edit(ctx context.Context, req *model.KsAdvertiserAccountInfoEditReq) (err error)
	Delete(ctx context.Context, AccountId []int64) (err error)
	GetAccessToken(ctx context.Context, advertiserId int64) (accessToken string, err error)
	GetAccountList(ctx context.Context, pageNo int, pageSize int) (res []*model.KsAdvertiserAccountInfoInfoRes, err error)
	GetCorporationList(ctx context.Context, corporationName string, pageNo int, pageSize int) (listRes *model.GetCorporationListRes, err error)
	GetAccountAutoInfo(ctx context.Context, advertiserId int64) (res *model.GetAccountAutoInfoRes, err error)
	GetAccountIncExplore(ctx context.Context, advertiserId int64) (res *model.GetAccountIncExploreRes, err error)
	AddAccountIncExplore(ctx context.Context, req *model.AddAccountIncExploreReq) (res *model.AddAccountIncExploreRes, err error)
	UpdateAccountIncExplore(ctx context.Context, req *model.UpdateAccountIncExploreReq) (res *model.UpdateAccountIncExploreRes, err error)
	DeleteAccountIncExplore(ctx context.Context, req *model.DeleteAccountIncExploreReq) (res *model.DeleteAccountIncExploreRes, err error)
	PauseAccountIncExplore(ctx context.Context, req *model.PauseAccountIncExploreReq) (res *model.PauseAccountIncExploreRes, err error)
	RebootAccountIncExplore(ctx context.Context, req *model.RebootAccountIncExploreReq) (res *model.RebootAccountIncExploreRes, err error)
	GetAccountBalance(ctx context.Context, advertiserIds []int64) (res *model.GetAccountBalanceRes, err error)
	UpdateAccountInfo(ctx context.Context, req *model.KsAdvertiserAccountInfoUpdateReq) (err error)
}

var localKsAdvertiserAccountInfo IKsAdvertiserAccountInfo

func KsAdvertiserAccountInfo() IKsAdvertiserAccountInfo {
	if localKsAdvertiserAccountInfo == nil {
		panic("implement not found for interface IKsAdvertiserAccountInfo, forgot register?")
	}
	return localKsAdvertiserAccountInfo
}

func RegisterKsAdvertiserAccountInfo(i IKsAdvertiserAccountInfo) {
	localKsAdvertiserAccountInfo = i
}
