package api

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
)

// QueryAccountIncExploreOcpxTypesService 获取历史上有消耗的转化目标
type QueryAccountIncExploreOcpxTypesService struct {
	ctx     context.Context
	cfg     *Configuration
	token   string
	Request *QueryAccountIncExploreOcpxTypesReq
}

type QueryAccountIncExploreOcpxTypesReq struct {
	AdvertiserId int64 `json:"advertiser_id"` // 广告主id
}

// GwOcpxTypeDescriptionData 历史上有消耗的转化目标数据结构体
type GwOcpxTypeDescriptionData struct {
	NotUax []GwOcpxTypeDescription `json:"not_uax"` // 历史上没有开启uax有消耗的转化目标
	Uax    []GwOcpxTypeDescription `json:"uax"`     // 历史上开启uax有消耗的转化目标
}

// GwOcpxTypeDescription 转化目标描述结构体
type GwOcpxTypeDescription struct {
	DeepConversionType     int    `json:"deep_conversion_type"`      // 深度转化目标
	DeepConversionTypeName string `json:"deep_conversion_type_name"` // 深度转化目标名称
	OcpxActionType         int    `json:"ocpx_action_type"`          // 转化目标
	OcpxActionTypeName     string `json:"ocpx_action_type_name"`     // 转化目标名称
}

func (r *QueryAccountIncExploreOcpxTypesService) SetCfg(cfg *Configuration) *QueryAccountIncExploreOcpxTypesService {
	r.cfg = cfg
	return r
}

func (r *QueryAccountIncExploreOcpxTypesService) SetReq(req QueryAccountIncExploreOcpxTypesReq) *QueryAccountIncExploreOcpxTypesService {
	r.Request = &req
	return r
}

func (r *QueryAccountIncExploreOcpxTypesService) AccessToken(accessToken string) *QueryAccountIncExploreOcpxTypesService {
	r.token = accessToken
	return r
}

func (r *QueryAccountIncExploreOcpxTypesService) Do() (data *KsBaseResp[GwOcpxTypeDescriptionData], err error) {
	localBasePath := r.cfg.BasePath
	localVarPath := localBasePath + "/rest/openapi/gw/dsp/account/incExplore/ocpxTypes"
	response, err := r.cfg.HTTPClient.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("Access-Token", r.token).
		SetBody(r.Request).
		SetResult(&KsBaseResp[GwOcpxTypeDescriptionData]{}).
		Post(localVarPath)
	if err != nil {
		return nil, err
	}
	resp := new(KsBaseResp[GwOcpxTypeDescriptionData])
	// 将 JSON 响应解码到结构体中
	err = json.Unmarshal(response.Body(), &resp)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("/rest/openapi/gw/dsp/account/incExplore/ocpxTypes解析响应出错: %v\n", err))
	}
	if resp.Code == 0 && resp.Data != nil {
		return resp, nil
	} else {
		return resp, errors.New(resp.Message)
	}
}
