// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2025-08-12 10:45:10
// 生成路径: internal/app/ad/logic/ks_advertiser_unit_report_data.go
// 生成人：cq
// desc:快手广告组报表数据
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"
	"fmt"
	"github.com/go-redsync/redsync/v4"
	"github.com/go-redsync/redsync/v4/redis/goredis/v9"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gconv"
	commonConsts "github.com/tiger1103/gfast/v3/internal/app/common/consts"
	commonService "github.com/tiger1103/gfast/v3/internal/app/common/service"
	systemModel "github.com/tiger1103/gfast/v3/internal/app/system/model"
	sysDo "github.com/tiger1103/gfast/v3/internal/app/system/model/do"
	sysService "github.com/tiger1103/gfast/v3/internal/app/system/service"
	ksApi "github.com/tiger1103/gfast/v3/library/advertiser/ks/api"
	"github.com/tiger1103/gfast/v3/library/libUtils"
	"slices"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	"github.com/tiger1103/gfast/v3/library/liberr"
)

func init() {
	service.RegisterKsAdvertiserUnitReportData(New())
}

func New() service.IKsAdvertiserUnitReportData {
	return &sKsAdvertiserUnitReportData{}
}

type sKsAdvertiserUnitReportData struct{}

func (s *sKsAdvertiserUnitReportData) List(ctx context.Context, req *model.KsAdvertiserUnitReportDataSearchReq) (listRes *model.KsAdvertiserUnitReportDataSearchRes, err error) {
	listRes = new(model.KsAdvertiserUnitReportDataSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.KsAdvertiserUnit.Ctx(ctx).As("d").
			LeftJoin("ks_advertiser_campaign c", "d.campaign_id = c.campaign_id").
			LeftJoin("ks_advertiser_account_info a", "c.advertiser_id = a.account_id").
			LeftJoin("sys_user u", "a.owner = u.id")
		m = m.LeftJoin("ks_advertiser_unit_report_data", "b",
			fmt.Sprintf("d.unit_id = b.unit_id AND b.stat_date >= '%s' AND b.stat_date <= '%s'", req.StartTime, req.EndTime))
		if len(req.UserIds) > 0 {
			m = m.WhereIn("a.owner", req.UserIds)
		} else {
			userInfo := sysService.Context().GetLoginUser(ctx)
			userIds, admin, _ := sysService.SysUser().GetContainUser(ctx, &systemModel.ContextUser{
				LoginUserRes: &systemModel.LoginUserRes{
					Id:     userInfo.Id,
					DeptId: userInfo.DeptId,
				},
			})
			if !admin && len(userIds) > 0 {
				m = m.WhereIn("a.owner", userIds)
			}
		}
		if len(req.CorporationNames) > 0 {
			m = m.WhereIn("a.corporation_name", req.CorporationNames)
		}
		if len(req.AdvertiserIds) > 0 {
			m = m.WhereIn("d.advertiser_id", req.AdvertiserIds)
		}
		if len(req.CampaignIds) > 0 {
			m = m.WhereIn("d.campaign_id", req.CampaignIds)
		}
		if len(req.UnitNames) > 0 {
			m = m.WhereIn("d.unit_name", req.UnitNames)
		}
		if len(req.UnitIds) > 0 {
			m = m.WhereIn("d.unit_id", req.UnitIds)
		}
		if len(req.DeptIds) > 0 {
			m = m.WhereIn("u.dept_id", req.DeptIds)
		}
		if req.Status != -1 && req.Status != 0 {
			m = m.Where("d.status", req.Status)
		}
		if req.OcpxActionType != "" {
			m = m.Where("d.ocpx_action_type", req.OcpxActionType)
		}
		if req.StartCreateTime != "" && req.EndCreateTime != "" {
			startTime, endTime := libUtils.GetDayStartAndEnd(req.StartCreateTime, req.EndCreateTime)
			m = m.Where("d.create_time >= ? AND d.create_time <= ?", startTime, endTime)
		}
		if req.Keyword != "" {
			m = m.Where("d.unit_name like ?", "%"+req.Keyword+"%")
		}
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		group := "d.unit_id"
		order := "charge desc"
		if req.OrderBy != "" {
			order = req.OrderBy + " " + req.OrderType
		}
		listRes.Total, err = m.Group(group).Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")

		fields := make([]string, 0)
		// 基础字段
		fields = append(fields, "ANY_VALUE(d.unit_id) as unitId")
		fields = append(fields, "ANY_VALUE(d.unit_name) as unitName")
		fields = append(fields, "ANY_VALUE(d.advertiser_id) as advertiserId")
		fields = append(fields, "ANY_VALUE(a.account_name) as advertiserName")
		fields = append(fields, "ANY_VALUE(a.corporation_name) as corporationName")
		fields = append(fields, "ANY_VALUE(c.campaign_id) as campaignId")
		fields = append(fields, "ANY_VALUE(c.campaign_name) as campaignName")
		fields = append(fields, "ANY_VALUE(u.id) as userId")
		fields = append(fields, "ANY_VALUE(u.user_name) as userName")
		fields = append(fields, "ANY_VALUE(d.put_status) as putStatus")
		fields = append(fields, "ANY_VALUE(d.status) as status")
		fields = append(fields, "ANY_VALUE(d.day_budget) as dayBudget")
		fields = append(fields, "ANY_VALUE(d.unit_source) as unitSource")
		fields = append(fields, "ANY_VALUE(d.ocpx_action_type) as ocpxActionType")
		fields = append(fields, "ANY_VALUE(d.create_time) as createTime")
		fields = append(fields, "ANY_VALUE(d.roi_ratio) as roiRatio")

		// 私信相关字段
		// 私信消息转化成本 = 花费/私信消息数
		fields = append(fields, "ROUND(CASE WHEN SUM(b.private_message_sent_cnt) > 0 THEN SUM(b.charge)/SUM(b.private_message_sent_cnt) ELSE 0 END,2) as privateMessageSentCost")
		// 私信消息转化率 = 私信消息数/行为数*100%
		fields = append(fields, "ROUND(CASE WHEN SUM(b.bclick) > 0 THEN SUM(b.private_message_sent_cnt)/SUM(b.bclick)*100 ELSE 0 END,2) as privateMessageSentRatio")
		fields = append(fields, "SUM(b.private_message_sent_cnt) as privateMessageSentCnt")
		// 直接私信留资成本 = 花费/直接私信留资数
		fields = append(fields, "ROUND(CASE WHEN SUM(b.leads_submit_cnt) > 0 THEN SUM(b.charge)/SUM(b.leads_submit_cnt) ELSE 0 END,2) as leadsSubmitCost")
		// 直接私信留资率 = 直接私信留资数/行为数*100%
		fields = append(fields, "ROUND(CASE WHEN SUM(b.bclick) > 0 THEN SUM(b.leads_submit_cnt)/SUM(b.bclick)*100 ELSE 0 END,2) as leadsSubmitCntRatio")
		fields = append(fields, "SUM(b.leads_submit_cnt) as leadsSubmitCnt")

		// 播放相关字段
		fields = append(fields, "SUM(b.played_num) as playedNum")
		fields = append(fields, "SUM(b.played_end) as playedEnd")
		fields = append(fields, "SUM(b.played_five_seconds) as playedFiveSeconds")
		fields = append(fields, "SUM(b.played_three_seconds) as playedThreeSeconds")
		// 3s播放率 = 3s播放数/播放数*100%
		fields = append(fields, "ROUND(CASE WHEN SUM(b.played_num) > 0 THEN SUM(b.played_three_seconds)/SUM(b.played_num)*100 ELSE 0 END,2) as play3SRatio")
		// 5s播放率 = 5s播放数/播放数*100%
		fields = append(fields, "ROUND(CASE WHEN SUM(b.played_num) > 0 THEN SUM(b.played_five_seconds)/SUM(b.played_num)*100 ELSE 0 END,2) as play5SRatio")
		// 完播率 = 播放完成/播放数*100%
		fields = append(fields, "ROUND(CASE WHEN SUM(b.played_num) > 0 THEN SUM(b.played_end)/SUM(b.played_num)*100 ELSE 0 END,2) as playEndRatio")
		fields = append(fields, "SUM(b.ad_photo_played_10s) as adPhotoPlayed10S")
		fields = append(fields, "SUM(b.ad_photo_played_2s) as adPhotoPlayed2S")
		fields = append(fields, "SUM(b.ad_photo_played_75percent) as adPhotoPlayed75Percent")
		// 75%播放率 = 75%播放数/播放数*100%
		fields = append(fields, "ROUND(CASE WHEN SUM(b.played_num) > 0 THEN SUM(b.ad_photo_played_75percent)/SUM(b.played_num)*100 ELSE 0 END,2) as adPhotoPlayed75PercentRatio")
		// 10s播放率 = 10s播放数/播放数*100%
		fields = append(fields, "ROUND(CASE WHEN SUM(b.played_num) > 0 THEN SUM(b.ad_photo_played_10s)/SUM(b.played_num)*100 ELSE 0 END,2) as adPhotoPlayed10SRatio")
		// 2s播放率 = 2s播放数/播放数*100%
		fields = append(fields, "ROUND(CASE WHEN SUM(b.played_num) > 0 THEN SUM(b.ad_photo_played_2s)/SUM(b.played_num)*100 ELSE 0 END,2) as adPhotoPlayed2SRatio")

		// 小游戏IAA相关字段
		// 激活后七日广告变现ROI = 激活后七日广告LTV/花费
		fields = append(fields, "ROUND(CASE WHEN SUM(b.charge) > 0 THEN SUM(b.minigame_iaa_purchase_amount_week_by_conversion)/SUM(b.charge) ELSE 0 END,2) as minigameIaaPurchaseAmountWeekByConversionRoi")
		// 激活后三日广告变现ROI = 激活后三日广告LTV/花费
		fields = append(fields, "ROUND(CASE WHEN SUM(b.charge) > 0 THEN SUM(b.minigame_iaa_purchase_amount_three_day_by_conversion)/SUM(b.charge) ELSE 0 END,2) as minigameIaaPurchaseAmountThreeDayByConversionRoi")
		// 当日广告变现ROI = 当日广告LTV/花费
		fields = append(fields, "ROUND(CASE WHEN SUM(b.charge) > 0 THEN SUM(b.minigame_iaa_purchase_amount_first_day)/SUM(b.charge) ELSE 0 END,2) as minigameIaaPurchaseAmountFirstDayRoi")
		fields = append(fields, "ROUND(SUM(b.minigame_iaa_purchase_amount_week_by_conversion),2) as minigameIaaPurchaseAmountWeekByConversion")
		fields = append(fields, "ROUND(SUM(b.minigame_iaa_purchase_amount_three_day_by_conversion),2) as minigameIaaPurchaseAmountThreeDayByConversion")
		fields = append(fields, "ROUND(SUM(b.minigame_iaa_purchase_amount_first_day),2) as minigameIaaPurchaseAmountFirstDay")
		// IAA广告变现ROI = IAA广告变现LTV/花费
		fields = append(fields, "ROUND(CASE WHEN SUM(b.charge) > 0 THEN SUM(b.minigame_iaa_purchase_amount)/SUM(b.charge) ELSE 0 END,2) as minigameIaaPurchaseRoi")
		fields = append(fields, "ROUND(SUM(b.minigame_iaa_purchase_amount),2) as minigameIaaPurchaseAmount")
		// 激活后30日广告变现ROI = 激活后30日广告LTV/花费
		fields = append(fields, "ROUND(CASE WHEN SUM(b.charge) > 0 THEN SUM(b.minigame_iaa_purchase_amount_30_day_by_conversion)/SUM(b.charge) ELSE 0 END,2) as minigameIaaPurchaseAmount30DayByConversionRoi")
		// 激活后15日广告变现ROI = 激活后15日广告LTV/花费
		fields = append(fields, "ROUND(CASE WHEN SUM(b.charge) > 0 THEN SUM(b.minigame_iaa_purchase_amount_15_day_by_conversion)/SUM(b.charge) ELSE 0 END,2) as minigameIaaPurchaseAmount15DayByConversionRoi")
		fields = append(fields, "ROUND(SUM(b.minigame_iaa_purchase_amount_30_day_by_conversion),2) as minigameIaaPurchaseAmount30DayByConversion")
		fields = append(fields, "ROUND(SUM(b.minigame_iaa_purchase_amount_15_day_by_conversion),2) as minigameIaaPurchaseAmount15DayByConversion")

		// MMU有效获客相关字段
		fields = append(fields, "SUM(b.mmu_effective_customer_acquisition_7d_cnt) as mmuEffectiveCustomerAcquisition7DCnt")
		fields = append(fields, "SUM(b.mmu_effective_customer_acquisition_cnt) as mmuEffectiveCustomerAcquisitionCnt")
		fields = append(fields, "ROUND(SUM(b.effective_customer_acquisition_7d_ratio),2) as effectiveCustomerAcquisition7DRatio")
		fields = append(fields, "ROUND(SUM(b.effective_customer_acquisition_7d_cost),2) as effectiveCustomerAcquisition7DCost")
		fields = append(fields, "SUM(b.effective_customer_acquisition_7d_cnt) as effectiveCustomerAcquisition7DCnt")

		// 事件付费相关字段
		// 激活后30日整体ROI = 激活后30日付费金额/花费
		fields = append(fields, "ROUND(CASE WHEN SUM(b.charge) > 0 THEN SUM(b.event_pay_purchase_amount_30_day_by_conversion)/SUM(b.charge) ELSE 0 END,2) as eventPay30DayOverallRoi")
		// 激活后15日整体ROI = 激活后15日付费金额/花费
		fields = append(fields, "ROUND(CASE WHEN SUM(b.charge) > 0 THEN SUM(b.event_pay_purchase_amount_15_day_by_conversion)/SUM(b.charge) ELSE 0 END,2) as eventPay15DayOverallRoi")
		fields = append(fields, "ROUND(SUM(b.event_pay_purchase_amount_15_day_by_conversion),2) as eventPayPurchaseAmount15DayByConversion")
		fields = append(fields, "ROUND(SUM(b.event_pay_purchase_amount_30_day_by_conversion),2) as eventPayPurchaseAmount30DayByConversion")
		fields = append(fields, "SUM(b.event_pay_first_day) as eventPayFirstDay")
		fields = append(fields, "ROUND(SUM(b.event_pay_purchase_amount_first_day),2) as eventPayPurchaseAmountFirstDay")
		// 应用下载数据-首日ROI = 首日付费金额/花费
		fields = append(fields, "ROUND(CASE WHEN SUM(b.charge) > 0 THEN SUM(b.event_pay_purchase_amount_first_day)/SUM(b.charge) ELSE 0 END,2) as eventPayFirstDayRoi")
		fields = append(fields, "SUM(b.event_pay) as eventPay")
		fields = append(fields, "ROUND(SUM(b.event_pay_purchase_amount),2) as eventPayPurchaseAmount")
		// 应用下载数据-ROI = 付费金额/花费
		fields = append(fields, "ROUND(CASE WHEN SUM(b.charge) > 0 THEN SUM(b.event_pay_purchase_amount)/SUM(b.charge) ELSE 0 END,2) as eventPayRoi")
		fields = append(fields, "ROUND(SUM(b.event_pay_purchase_amount_one_day),2) as eventPayPurchaseAmountOneDay")
		fields = append(fields, "ROUND(SUM(b.event_pay_purchase_amount_one_day_by_conversion),2) as eventPayPurchaseAmountOneDayByConversion")
		// 激活后24小时付费ROI = 激活后24h付费金额(激活时间)/花费
		fields = append(fields, "ROUND(CASE WHEN SUM(b.charge) > 0 THEN SUM(b.event_pay_purchase_amount_one_day_by_conversion)/SUM(b.charge) ELSE 0 END,2) as eventPayPurchaseAmountOneDayByConversionRoi")
		// 激活后24h-ROI(回传时间) = 激活后24h付费金额(回传时间)/花费
		fields = append(fields, "ROUND(CASE WHEN SUM(b.charge) > 0 THEN SUM(b.event_pay_purchase_amount_one_day)/SUM(b.charge) ELSE 0 END,2) as eventPayPurchaseAmountOneDayRoi")
		fields = append(fields, "ROUND(SUM(b.event_pay_weighted_purchase_amount),2) as eventPayWeightedPurchaseAmount")
		fields = append(fields, "ROUND(SUM(b.event_pay_weighted_purchase_amount_first_day),2) as eventPayWeightedPurchaseAmountFirstDay")

		// 基础广告数据字段
		fields = append(fields, "ROUND(SUM(b.charge),2) as charge")
		fields = append(fields, "SUM(b.`show`) as `show`")
		fields = append(fields, "SUM(b.aclick) as aclick")
		// 素材点击率 = 行为数/素材曝光数*100%
		fields = append(fields, "ROUND(CASE WHEN SUM(b.aclick) > 0 THEN SUM(b.bclick)/SUM(b.aclick)*100 ELSE 0 END,2) as aclickRatio")
		fields = append(fields, "SUM(b.bclick) as bclick")
		fields = append(fields, "ROUND(SUM(b.ad_show),2) as adShow")
		fields = append(fields, "SUM(b.share) as share")
		fields = append(fields, "SUM(b.`comment`) as `comment`")
		fields = append(fields, "SUM(b.`like`) as `like`")
		fields = append(fields, "SUM(b.`follow`) as `follow`")
		fields = append(fields, "SUM(b.cancel_like) as cancelLike")
		fields = append(fields, "SUM(b.cancel_follow) as cancelFollow")
		fields = append(fields, "SUM(b.report) as report")
		fields = append(fields, "SUM(b.block) as block")
		fields = append(fields, "SUM(b.negative) as negative")

		// 应用下载相关字段
		fields = append(fields, "SUM(b.activation) as activation")
		fields = append(fields, "SUM(b.download_started) as downloadStarted")
		fields = append(fields, "SUM(b.download_completed) as downloadCompleted")
		fields = append(fields, "SUM(b.download_installed) as downloadInstalled")
		// 点击激活率 = 激活数/行为数*100%
		fields = append(fields, "ROUND(CASE WHEN SUM(b.bclick) > 0 THEN SUM(b.activation)/SUM(b.bclick)*100 ELSE 0 END,2) as clickConversionRatio")
		// 激活单价 = 花费/激活数
		fields = append(fields, "ROUND(CASE WHEN SUM(b.activation) > 0 THEN SUM(b.charge)/SUM(b.activation) ELSE 0 END,2) as conversionCost")
		// 安卓下载完成单价 = 花费/安卓下载完成数
		fields = append(fields, "ROUND(CASE WHEN SUM(b.download_completed) > 0 THEN SUM(b.charge)/SUM(b.download_completed) ELSE 0 END,2) as downloadCompletedCost")
		// 安卓下载完成率 = 安卓下载完成数/安卓下载开始数*100%
		fields = append(fields, "ROUND(CASE WHEN SUM(b.download_started) > 0 THEN SUM(b.download_completed)/SUM(b.download_started)*100 ELSE 0 END,2) as downloadCompletedRatio")
		// 下载完成激活率 = 激活数/安卓下载完成数*100%
		fields = append(fields, "ROUND(CASE WHEN SUM(b.download_completed) > 0 THEN SUM(b.activation)/SUM(b.download_completed)*100 ELSE 0 END,2) as downloadConversionRatio")
		// 安卓下载开始单价 = 花费/安卓下载开始数
		fields = append(fields, "ROUND(CASE WHEN SUM(b.download_started) > 0 THEN SUM(b.charge)/SUM(b.download_started) ELSE 0 END,2) as downloadStartedCost")
		// 安卓下载开始率 = 安卓下载开始数/行为数*100%
		fields = append(fields, "ROUND(CASE WHEN SUM(b.bclick) > 0 THEN SUM(b.download_started)/SUM(b.bclick)*100 ELSE 0 END,2) as downloadStartedRatio")

		// 注册相关字段
		fields = append(fields, "SUM(b.event_register) as eventRegister")
		// 注册成本 = 花费/注册数
		fields = append(fields, "ROUND(CASE WHEN SUM(b.event_register) > 0 THEN SUM(b.charge)/SUM(b.event_register) ELSE 0 END,2) as eventRegisterCost")
		// 注册率 = 注册数/激活数*100%
		fields = append(fields, "ROUND(CASE WHEN SUM(b.activation) > 0 THEN SUM(b.event_register)/SUM(b.activation)*100 ELSE 0 END,2) as eventRegisterRatio")

		// 完件相关字段
		fields = append(fields, "SUM(b.event_jin_jian_app) as eventJinJianApp")
		// 完件成本 = 花费/完件数
		fields = append(fields, "ROUND(CASE WHEN SUM(b.event_jin_jian_app) > 0 THEN SUM(b.charge)/SUM(b.event_jin_jian_app) ELSE 0 END,2) as eventJinJianAppCost")
		fields = append(fields, "SUM(b.event_jin_jian_landing_page) as eventJinJianLandingPage")
		// 落地页完件成本 = 花费/落地页完件数
		fields = append(fields, "ROUND(CASE WHEN SUM(b.event_jin_jian_landing_page) > 0 THEN SUM(b.charge)/SUM(b.event_jin_jian_landing_page) ELSE 0 END,2) as eventJinJianLandingPageCost")
		fields = append(fields, "SUM(b.jinjian_0d_cnt) as jinjian0DCnt")
		fields = append(fields, "SUM(b.jinjian_3d_cnt) as jinjian3DCnt")
		// T0完件成本 = 花费/T0完件数
		fields = append(fields, "ROUND(CASE WHEN SUM(b.jinjian_0d_cnt) > 0 THEN SUM(b.charge)/SUM(b.jinjian_0d_cnt) ELSE 0 END,2) as jinjian0DCntCost")
		// T3完件成本 = 花费/T3完件数
		fields = append(fields, "ROUND(CASE WHEN SUM(b.jinjian_3d_cnt) > 0 THEN SUM(b.charge)/SUM(b.jinjian_3d_cnt) ELSE 0 END,2) as jinjian3DCntCost")

		// 授信相关字段
		fields = append(fields, "SUM(b.event_credit_grant_app) as eventCreditGrantApp")
		// 授信成本 = 花费/授信数
		fields = append(fields, "ROUND(CASE WHEN SUM(b.event_credit_grant_app) > 0 THEN SUM(b.charge)/SUM(b.event_credit_grant_app) ELSE 0 END,2) as eventCreditGrantAppCost")
		// 授信率 = 授信数/完件数*100%
		fields = append(fields, "ROUND(CASE WHEN SUM(b.event_jin_jian_app) > 0 THEN SUM(b.event_credit_grant_app)/SUM(b.event_jin_jian_app)*100 ELSE 0 END,2) as eventCreditGrantAppRatio")
		fields = append(fields, "SUM(b.event_credit_grant_landing_page) as eventCreditGrantLandingPage")
		// 落地页授信成本 = 花费/落地页授信数
		fields = append(fields, "ROUND(CASE WHEN SUM(b.event_credit_grant_landing_page) > 0 THEN SUM(b.charge)/SUM(b.event_credit_grant_landing_page) ELSE 0 END,2) as eventCreditGrantLandingPageCost")
		// 落地页授信率 = 落地页授信数/落地页完件数*100%
		fields = append(fields, "ROUND(CASE WHEN SUM(b.event_jin_jian_landing_page) > 0 THEN SUM(b.event_credit_grant_landing_page)/SUM(b.event_jin_jian_landing_page)*100 ELSE 0 END,2) as eventCreditGrantLandingRatio")
		fields = append(fields, "SUM(b.event_credit_grant_first_day_app) as eventCreditGrantFirstDayApp")
		// 首日授信成本 = 花费/首日授信数
		fields = append(fields, "ROUND(CASE WHEN SUM(b.event_credit_grant_first_day_app) > 0 THEN SUM(b.charge)/SUM(b.event_credit_grant_first_day_app) ELSE 0 END,2) as eventCreditGrantFirstDayAppCost")
		// 首日授信率 = 首日授信数/完件数*100%
		fields = append(fields, "ROUND(CASE WHEN SUM(b.event_jin_jian_app) > 0 THEN SUM(b.event_credit_grant_first_day_app)/SUM(b.event_jin_jian_app)*100 ELSE 0 END,2) as eventCreditGrantFirstDayAppRatio")
		fields = append(fields, "SUM(b.event_credit_grant_first_day_landing_page) as eventCreditGrantFirstDayLandingPage")
		// 落地页首日授信成本 = 花费/落地页首日授信数
		fields = append(fields, "ROUND(CASE WHEN SUM(b.event_credit_grant_first_day_landing_page) > 0 THEN SUM(b.charge)/SUM(b.event_credit_grant_first_day_landing_page) ELSE 0 END,2) as eventCreditGrantFirstDayLandingPageCost")
		// 落地页首日授信率 = 落地页首日授信数/落地页完件数*100%
		fields = append(fields, "ROUND(CASE WHEN SUM(b.event_jin_jian_landing_page) > 0 THEN SUM(b.event_credit_grant_first_day_landing_page)/SUM(b.event_jin_jian_landing_page)*100 ELSE 0 END,2) as eventCreditGrantFirstDayLandingPageRatio")
		fields = append(fields, "SUM(b.credit_grant_0d_cnt) as creditGrant0DCnt")
		fields = append(fields, "SUM(b.credit_grant_3d_cnt) as creditGrant3DCnt")
		// T0授信成本 = 花费/T0授信数
		fields = append(fields, "ROUND(CASE WHEN SUM(b.credit_grant_0d_cnt) > 0 THEN SUM(b.charge)/SUM(b.credit_grant_0d_cnt) ELSE 0 END,2) as creditGrant0DCntCost")
		// T3授信成本 = 花费/T3授信数
		fields = append(fields, "ROUND(CASE WHEN SUM(b.credit_grant_3d_cnt) > 0 THEN SUM(b.charge)/SUM(b.credit_grant_3d_cnt) ELSE 0 END,2) as creditGrant3DCntCost")
		// T0完件授信率 = T0授信数/T0完件数*100%
		fields = append(fields, "ROUND(CASE WHEN SUM(b.jinjian_0d_cnt) > 0 THEN SUM(b.credit_grant_0d_cnt)/SUM(b.jinjian_0d_cnt)*100 ELSE 0 END,2) as creditGrant0DCntRatio")
		// T3完件授信通过率 = T3授信数/T3完件数*100%
		fields = append(fields, "ROUND(CASE WHEN SUM(b.jinjian_3d_cnt) > 0 THEN SUM(b.credit_grant_3d_cnt)/SUM(b.jinjian_3d_cnt)*100 ELSE 0 END,2) as creditGrant3DCntRatio")

		// 表单相关字段
		fields = append(fields, "SUM(b.form_count) as formCount")
		// 单个线索成本 = 花费/线索提交个数
		fields = append(fields, "ROUND(CASE WHEN SUM(b.form_count) > 0 THEN SUM(b.charge)/SUM(b.form_count) ELSE 0 END,2) as formCost")
		// 表单提交点击率 = 线索提交个数/行为数*100%
		fields = append(fields, "ROUND(CASE WHEN SUM(b.bclick) > 0 THEN SUM(b.form_count)/SUM(b.bclick)*100 ELSE 0 END,2) as formActionRatio")

		// 点击相关字段
		fields = append(fields, "SUM(b.photo_click) as photoClick")
		// 封面点击率 = 封面点击数/封面曝光数*100%
		fields = append(fields, "ROUND(CASE WHEN SUM(b.show) > 0 THEN SUM(b.photo_click)/SUM(b.show)*100 ELSE 0 END,2) as photoClickRatio")
		// 平均点击单价 = 花费/封面点击数
		fields = append(fields, "ROUND(CASE WHEN SUM(b.photo_click) > 0 THEN SUM(b.charge)/SUM(b.photo_click) ELSE 0 END,2) as photoClickCost")
		// 行为率 = 行为数/素材曝光数*100%
		fields = append(fields, "ROUND(CASE WHEN SUM(b.aclick) > 0 THEN SUM(b.bclick)/SUM(b.aclick)*100 ELSE 0 END,2) as actionRatio")
		// 行为率 新 = 行为数/广告曝光数*100%
		fields = append(fields, "ROUND(CASE WHEN SUM(b.ad_show) > 0 THEN SUM(b.bclick)/SUM(b.ad_show)*100 ELSE 0 END,2) as actionNewRatio")
		// 平均行为单价 = 花费/行为数
		fields = append(fields, "ROUND(CASE WHEN SUM(b.bclick) > 0 THEN SUM(b.charge)/SUM(b.bclick) ELSE 0 END,2) as actionCost")
		// 平均千次曝光花费 = 花费/广告曝光数*1000
		fields = append(fields, "ROUND(CASE WHEN SUM(b.ad_show) > 0 THEN SUM(b.charge)/SUM(b.ad_show)*1000 ELSE 0 END,2) as impression1KCost")
		// 平均千次素材曝光花费 = 花费/素材曝光数*1000
		fields = append(fields, "ROUND(CASE WHEN SUM(b.aclick) > 0 THEN SUM(b.charge)/SUM(b.aclick)*1000 ELSE 0 END,2) as click1KCost")

		// 订单相关字段
		fields = append(fields, "SUM(b.event_order_submit) as eventOrderSubmit")
		fields = append(fields, "SUM(b.event_order_paid) as eventOrderPaid")
		fields = append(fields, "ROUND(SUM(b.event_order_paid_purchase_amount),2) as eventOrderPaidPurchaseAmount")
		// 单次付款成本 = 花费/付款成功数
		fields = append(fields, "ROUND(CASE WHEN SUM(b.event_order_paid) > 0 THEN SUM(b.charge)/SUM(b.event_order_paid) ELSE 0 END,2) as eventOrderPaidCost")
		// 订单支付率 = 商品成交数/商品访问数*100%
		fields = append(fields, "ROUND(CASE WHEN SUM(b.event_goods_view) > 0 THEN SUM(b.ad_product_cnt)/SUM(b.event_goods_view)*100 ELSE 0 END,2) as eventOrderPaidRoi")

		// 其他重要字段
		fields = append(fields, "SUM(b.submit) as submit")
		fields = append(fields, "SUM(b.event_valid_clues) as eventValidClues")
		// 有效线索成本 = 花费/有效线索数
		fields = append(fields, "ROUND(CASE WHEN SUM(b.event_valid_clues) > 0 THEN SUM(b.charge)/SUM(b.event_valid_clues) ELSE 0 END,2) as eventValidCluesCost")
		fields = append(fields, "SUM(b.event_audition) as eventAudition")
		fields = append(fields, "SUM(b.event_consultation_valid_retained) as eventConsultationValidRetained")
		// 留咨咨询成本 = 花费/留咨咨询数
		fields = append(fields, "ROUND(CASE WHEN SUM(b.event_consultation_valid_retained) > 0 THEN SUM(b.charge)/SUM(b.event_consultation_valid_retained) ELSE 0 END,2) as eventConsultationValidRetainedCost")
		// 留咨咨询率 = 留咨咨询数/行为数*100%
		fields = append(fields, "ROUND(CASE WHEN SUM(b.bclick) > 0 THEN SUM(b.event_consultation_valid_retained)/SUM(b.bclick)*100 ELSE 0 END,2) as eventConsultationValidRetainedRatio")
		// 有效咨询成本 = 花费/有效咨询数
		fields = append(fields, "ROUND(CASE WHEN SUM(b.event_consultation_valid_retained) > 0 THEN SUM(b.charge)/SUM(b.event_consultation_valid_retained) ELSE 0 END,2) as eventConversionClickCost")
		// 有效咨询率 = 有效咨询数/行为数*100%
		fields = append(fields, "ROUND(CASE WHEN SUM(b.bclick) > 0 THEN SUM(b.event_consultation_valid_retained)/SUM(b.bclick)*100 ELSE 0 END,2) as eventConversionClickRatio")
		fields = append(fields, "SUM(b.event_pre_component_consultation_valid_retained) as eventPreComponentConsultationValidRetained")
		// 次留成本 = 花费/次留数
		fields = append(fields, "ROUND(CASE WHEN SUM(b.event_next_day_stay) > 0 THEN SUM(b.charge)/SUM(b.event_next_day_stay) ELSE 0 END,2) as eventNextDayStayCost")
		// 次留率 = 次留数/激活数*100%
		fields = append(fields, "ROUND(CASE WHEN SUM(b.activation) > 0 THEN SUM(b.event_next_day_stay)/SUM(b.activation)*100 ELSE 0 END,2) as eventNextDayStayRatio")
		fields = append(fields, "SUM(b.event_next_day_stay) as eventNextDayStay")

		// 广告观看相关字段
		fields = append(fields, "SUM(b.event_ad_watch_10_times) as eventAdWatch10Times")
		// 10次广告观看成本 = 花费/10次广告观看数
		fields = append(fields, "ROUND(CASE WHEN SUM(b.event_ad_watch_10_times) > 0 THEN SUM(b.charge)/SUM(b.event_ad_watch_10_times) ELSE 0 END,2) as eventAdWatch10TimesCost")
		// 10次广告观看转化率 = 10次广告观看数/行为数*100%
		fields = append(fields, "ROUND(CASE WHEN SUM(b.bclick) > 0 THEN SUM(b.event_ad_watch_10_times)/SUM(b.bclick)*100 ELSE 0 END,2) as eventAdWatch10TimesRatio")
		fields = append(fields, "SUM(b.event_ad_watch_20_times) as eventAdWatch20Times")
		// 20次广告观看成本 = 花费/20次广告观看数
		fields = append(fields, "ROUND(CASE WHEN SUM(b.event_ad_watch_20_times) > 0 THEN SUM(b.charge)/SUM(b.event_ad_watch_20_times) ELSE 0 END,2) as eventAdWatch20TimesCost")
		// 20次广告观看转化率 = 20次广告观看数/行为数*100%
		fields = append(fields, "ROUND(CASE WHEN SUM(b.bclick) > 0 THEN SUM(b.event_ad_watch_20_times)/SUM(b.bclick)*100 ELSE 0 END,2) as eventAdWatch20TimesRatio")
		fields = append(fields, "SUM(b.event_ad_watch_5_times) as eventAdWatch5Times")
		// 5次广告观看成本 = 花费/5次广告观看数
		fields = append(fields, "ROUND(CASE WHEN SUM(b.event_ad_watch_5_times) > 0 THEN SUM(b.charge)/SUM(b.event_ad_watch_5_times) ELSE 0 END,2) as eventAdWatch5TimesCost")
		// 5次广告观看转化率 = 5次广告观看数/行为数*100%
		fields = append(fields, "ROUND(CASE WHEN SUM(b.bclick) > 0 THEN SUM(b.event_ad_watch_5_times)/SUM(b.bclick)*100 ELSE 0 END,2) as eventAdWatch5TimesRatio")
		fields = append(fields, "SUM(b.event_watch_app_ad) as eventWatchAppAd")
		fields = append(fields, "SUM(b.event_ad_watch_times) as eventAdWatchTimes")
		// 广告观看次数转化率 = 广告观看次数/行为数*100%
		fields = append(fields, "ROUND(CASE WHEN SUM(b.bclick) > 0 THEN SUM(b.event_ad_watch_times)/SUM(b.bclick)*100 ELSE 0 END,2) as eventAdWatchTimesRatio")
		// 广告观看次数成本 = 花费/广告观看次数
		fields = append(fields, "ROUND(CASE WHEN SUM(b.event_ad_watch_times) > 0 THEN SUM(b.charge)/SUM(b.event_ad_watch_times) ELSE 0 END,2) as eventAdWatchTimesCost")

		// 电话相关字段
		fields = append(fields, "SUM(b.event_making_calls) as eventMakingCalls")
		// 电话拨打成本 = 花费/电话拨打数
		fields = append(fields, "ROUND(CASE WHEN SUM(b.event_making_calls) > 0 THEN SUM(b.charge)/SUM(b.event_making_calls) ELSE 0 END,2) as eventMakingCallsCost")
		// 电话拨打率 = 电话拨打数/行为数*100%
		fields = append(fields, "ROUND(CASE WHEN SUM(b.bclick) > 0 THEN SUM(b.event_making_calls)/SUM(b.bclick)*100 ELSE 0 END,2) as eventMakingCallsRatio")
		fields = append(fields, "SUM(b.event_get_through) as eventGetThrough")
		// 确认接通成本 = 花费/确认接通数
		fields = append(fields, "ROUND(CASE WHEN SUM(b.event_get_through) > 0 THEN SUM(b.charge)/SUM(b.event_get_through) ELSE 0 END,2) as eventGetThroughCost")
		// 确认接通率 = 确认接通数/电话拨打数*100%
		fields = append(fields, "ROUND(CASE WHEN SUM(b.event_making_calls) > 0 THEN SUM(b.event_get_through)/SUM(b.event_making_calls)*100 ELSE 0 END,2) as eventGetThroughRatio")
		fields = append(fields, "SUM(b.event_phone_get_through) as eventPhoneGetThrough")
		fields = append(fields, "SUM(b.event_outbound_call) as eventOutboundCall")
		// 电话拨打成本 = 花费/电话拨打数
		fields = append(fields, "ROUND(CASE WHEN SUM(b.event_outbound_call) > 0 THEN SUM(b.charge)/SUM(b.event_outbound_call) ELSE 0 END,2) as eventOutboundCallCost")
		// 电话拨打率 = 电话拨打数/行为数*100%
		fields = append(fields, "ROUND(CASE WHEN SUM(b.bclick) > 0 THEN SUM(b.event_outbound_call)/SUM(b.bclick)*100 ELSE 0 END,2) as eventOutboundCallRatio")

		// 微信相关字段
		fields = append(fields, "SUM(b.event_wechat_qr_code_link_click) as eventWechatQrCodeLinkClick")
		fields = append(fields, "SUM(b.event_add_wechat) as eventAddWechat")
		// 微信复制成本 = 花费/微信复制数
		fields = append(fields, "ROUND(CASE WHEN SUM(b.event_add_wechat) > 0 THEN SUM(b.charge)/SUM(b.event_add_wechat) ELSE 0 END,2) as eventAddWechatCost")
		// 微信复制率 = 微信复制数/行为数*100%
		fields = append(fields, "ROUND(CASE WHEN SUM(b.bclick) > 0 THEN SUM(b.event_add_wechat)/SUM(b.bclick)*100 ELSE 0 END,2) as eventAddWechatRatio")
		fields = append(fields, "SUM(b.event_wechat_connected) as eventWechatConnected")

		// 淘系相关字段
		// 淘系近似购买成本 = 花费/近似购买数
		fields = append(fields, "ROUND(CASE WHEN SUM(b.approx_pay_count) > 0 THEN SUM(b.charge)/SUM(b.approx_pay_count) ELSE 0 END,2) as approxPayCost")
		fields = append(fields, "SUM(b.approx_pay_count) as approxPayCount")
		// 淘系近似购买率 = 近似购买数/行为数*100%
		fields = append(fields, "ROUND(CASE WHEN SUM(b.bclick) > 0 THEN SUM(b.approx_pay_count)/SUM(b.bclick)*100 ELSE 0 END,2) as approxPayRatio")

		// 直播相关字段
		fields = append(fields, "SUM(b.live_event_goods_view) as liveEventGoodsView")
		fields = append(fields, "SUM(b.live_played_3s) as livePlayed3S")

		// 商品相关字段
		fields = append(fields, "SUM(b.ad_product_cnt) as adProductCnt")
		fields = append(fields, "SUM(b.event_goods_view) as eventGoodsView")
		// 商品访问成本 = 花费/商品访问数
		fields = append(fields, "ROUND(CASE WHEN SUM(b.event_goods_view) > 0 THEN SUM(b.charge)/SUM(b.event_goods_view) ELSE 0 END,2) as eventGoodsViewCost")
		fields = append(fields, "SUM(b.merchant_reco_fans) as merchantRecoFans")
		// 涨粉成本 = 花费/涨粉量
		fields = append(fields, "ROUND(CASE WHEN SUM(b.merchant_reco_fans) > 0 THEN SUM(b.charge)/SUM(b.merchant_reco_fans) ELSE 0 END,2) as merchantRecoFansCost")
		// 小店推广ROI = 订单金额/花费
		fields = append(fields, "ROUND(CASE WHEN SUM(b.charge) > 0 THEN SUM(b.event_order_paid_purchase_amount)/SUM(b.charge) ELSE 0 END,2) as eventOrderAmountRoi")

		// 新用户相关字段
		fields = append(fields, "SUM(b.event_new_user_pay) as eventNewUserPay")
		// 新增付费人数成本 = 花费/新增付费人数
		fields = append(fields, "ROUND(CASE WHEN SUM(b.event_new_user_pay) > 0 THEN SUM(b.charge)/SUM(b.event_new_user_pay) ELSE 0 END,2) as eventNewUserPayCost")
		// 新增付费人数率 = 新增付费人数/(激活数+表单数)*100%
		fields = append(fields, "ROUND(CASE WHEN (SUM(b.activation) + SUM(b.form_count)) > 0 THEN SUM(b.event_new_user_pay)/(SUM(b.activation) + SUM(b.form_count))*100 ELSE 0 END,2) as eventNewUserPayRatio")
		fields = append(fields, "SUM(b.event_new_user_jinjian_app) as eventNewUserJinjianApp")
		// 新增完件人数成本 = 花费/新增完件人数
		fields = append(fields, "ROUND(CASE WHEN SUM(b.event_new_user_jinjian_app) > 0 THEN SUM(b.charge)/SUM(b.event_new_user_jinjian_app) ELSE 0 END,2) as eventNewUserJinjianAppCost")

		// 联盟相关字段
		fields = append(fields, "ROUND(SUM(b.union_event_pay_purchase_amount_7d),2) as unionEventPayPurchaseAmount7D")
		// 联盟变现ROI = 联盟广告收入/花费
		fields = append(fields, "ROUND(CASE WHEN SUM(b.charge) > 0 THEN SUM(b.union_event_pay_purchase_amount_7d)/SUM(b.charge) ELSE 0 END,2) as unionEventPayPurchaseAmount7DRoi")

		// 执行查询
		err = m.Fields(fields).Group(group).Page(req.PageNum, req.PageSize).Order(order).Scan(&listRes.List)
		liberr.ErrIsNil(ctx, err, "获取数据失败")

		// 计算汇总
		err = m.Fields(fields).Scan(&listRes.Summary)
		liberr.ErrIsNil(ctx, err, "获取汇总数据失败")
	})
	return
}

func (s *sKsAdvertiserUnitReportData) Add(ctx context.Context, req *model.KsAdvertiserUnitReportDataAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.KsAdvertiserUnitReportData.Ctx(ctx).Insert(do.KsAdvertiserUnitReportData{
			UnitId:                      req.UnitId,
			StatDate:                    req.StatDate,
			AdvertiserId:                req.AdvertiserId,
			CampaignId:                  req.CampaignId,
			Status:                      req.Status,
			UnitSource:                  req.UnitSource,
			PrivateMessageSentCost:      req.PrivateMessageSentCost,
			PrivateMessageSentRatio:     req.PrivateMessageSentRatio,
			PrivateMessageSentCnt:       req.PrivateMessageSentCnt,
			LeadsSubmitCost:             req.LeadsSubmitCost,
			LeadsSubmitCntRatio:         req.LeadsSubmitCntRatio,
			LeadsSubmitCnt:              req.LeadsSubmitCnt,
			PlayedNum:                   req.PlayedNum,
			PlayedEnd:                   req.PlayedEnd,
			PlayedFiveSeconds:           req.PlayedFiveSeconds,
			PlayedThreeSeconds:          req.PlayedThreeSeconds,
			Play3SRatio:                 req.Play3SRatio,
			Play5SRatio:                 req.Play5SRatio,
			PlayEndRatio:                req.PlayEndRatio,
			AdPhotoPlayed10S:            req.AdPhotoPlayed10S,
			AdPhotoPlayed2S:             req.AdPhotoPlayed2S,
			AdPhotoPlayed75Percent:      req.AdPhotoPlayed75Percent,
			AdPhotoPlayed75PercentRatio: req.AdPhotoPlayed75PercentRatio,
			AdPhotoPlayed10SRatio:       req.AdPhotoPlayed10SRatio,
			AdPhotoPlayed2SRatio:        req.AdPhotoPlayed2SRatio,
			MinigameIaaPurchaseAmountWeekByConversionRoi:     req.MinigameIaaPurchaseAmountWeekByConversionRoi,
			MinigameIaaPurchaseAmountThreeDayByConversionRoi: req.MinigameIaaPurchaseAmountThreeDayByConversionRoi,
			MinigameIaaPurchaseAmountFirstDayRoi:             req.MinigameIaaPurchaseAmountFirstDayRoi,
			MinigameIaaPurchaseAmountWeekByConversion:        req.MinigameIaaPurchaseAmountWeekByConversion,
			MinigameIaaPurchaseAmountThreeDayByConversion:    req.MinigameIaaPurchaseAmountThreeDayByConversion,
			MinigameIaaPurchaseAmountFirstDay:                req.MinigameIaaPurchaseAmountFirstDay,
			MinigameIaaPurchaseRoi:                           req.MinigameIaaPurchaseRoi,
			MinigameIaaPurchaseAmount:                        req.MinigameIaaPurchaseAmount,
			MinigameIaaPurchaseAmount30DayByConversionRoi:    req.MinigameIaaPurchaseAmount30DayByConversionRoi,
			MinigameIaaPurchaseAmount15DayByConversionRoi:    req.MinigameIaaPurchaseAmount15DayByConversionRoi,
			MinigameIaaPurchaseAmount30DayByConversion:       req.MinigameIaaPurchaseAmount30DayByConversion,
			MinigameIaaPurchaseAmount15DayByConversion:       req.MinigameIaaPurchaseAmount15DayByConversion,
			MmuEffectiveCustomerAcquisition7DCnt:             req.MmuEffectiveCustomerAcquisition7DCnt,
			MmuEffectiveCustomerAcquisitionCnt:               req.MmuEffectiveCustomerAcquisitionCnt,
			EffectiveCustomerAcquisition7DRatio:              req.EffectiveCustomerAcquisition7DRatio,
			EffectiveCustomerAcquisition7DCost:               req.EffectiveCustomerAcquisition7DCost,
			EffectiveCustomerAcquisition7DCnt:                req.EffectiveCustomerAcquisition7DCnt,
			EventPay30DayOverallRoi:                          req.EventPay30DayOverallRoi,
			EventPay15DayOverallRoi:                          req.EventPay15DayOverallRoi,
			EventPayPurchaseAmount15DayByConversion:          req.EventPayPurchaseAmount15DayByConversion,
			EventPayPurchaseAmount30DayByConversion:          req.EventPayPurchaseAmount30DayByConversion,
			EventPayFirstDay:                                 req.EventPayFirstDay,
			EventPayPurchaseAmountFirstDay:                   req.EventPayPurchaseAmountFirstDay,
			EventPayFirstDayRoi:                              req.EventPayFirstDayRoi,
			EventPay:                                         req.EventPay,
			EventPayPurchaseAmount:                           req.EventPayPurchaseAmount,
			EventPayRoi:                                      req.EventPayRoi,
			EventPayPurchaseAmountOneDay:                     req.EventPayPurchaseAmountOneDay,
			EventPayPurchaseAmountOneDayByConversion:         req.EventPayPurchaseAmountOneDayByConversion,
			EventPayPurchaseAmountOneDayByConversionRoi:      req.EventPayPurchaseAmountOneDayByConversionRoi,
			EventPayPurchaseAmountOneDayRoi:                  req.EventPayPurchaseAmountOneDayRoi,
			EventPayWeightedPurchaseAmount:                   req.EventPayWeightedPurchaseAmount,
			EventPayWeightedPurchaseAmountFirstDay:           req.EventPayWeightedPurchaseAmountFirstDay,
			Charge:                                           req.Charge,
			Show:                                             req.Show,
			Aclick:                                           req.Aclick,
			Bclick:                                           req.Bclick,
			AdShow:                                           req.AdShow,
			Share:                                            req.Share,
			Comment:                                          req.Comment,
			Like:                                             req.Like,
			Follow:                                           req.Follow,
			CancelLike:                                       req.CancelLike,
			CancelFollow:                                     req.CancelFollow,
			Report:                                           req.Report,
			Block:                                            req.Block,
			Negative:                                         req.Negative,
			Activation:                                       req.Activation,
			DownloadStarted:                                  req.DownloadStarted,
			DownloadCompleted:                                req.DownloadCompleted,
			DownloadInstalled:                                req.DownloadInstalled,
			ClickConversionRatio:                             req.ClickConversionRatio,
			ConversionCost:                                   req.ConversionCost,
			DownloadCompletedCost:                            req.DownloadCompletedCost,
			DownloadCompletedRatio:                           req.DownloadCompletedRatio,
			DownloadConversionRatio:                          req.DownloadConversionRatio,
			DownloadStartedCost:                              req.DownloadStartedCost,
			DownloadStartedRatio:                             req.DownloadStartedRatio,
			EventRegister:                                    req.EventRegister,
			EventRegisterCost:                                req.EventRegisterCost,
			EventRegisterRatio:                               req.EventRegisterRatio,
			EventJinJianApp:                                  req.EventJinJianApp,
			EventJinJianAppCost:                              req.EventJinJianAppCost,
			EventJinJianLandingPage:                          req.EventJinJianLandingPage,
			EventJinJianLandingPageCost:                      req.EventJinJianLandingPageCost,
			Jinjian0DCnt:                                     req.Jinjian0DCnt,
			Jinjian3DCnt:                                     req.Jinjian3DCnt,
			Jinjian0DCntCost:                                 req.Jinjian0DCntCost,
			Jinjian3DCntCost:                                 req.Jinjian3DCntCost,
			EventCreditGrantApp:                              req.EventCreditGrantApp,
			EventCreditGrantAppCost:                          req.EventCreditGrantAppCost,
			EventCreditGrantAppRatio:                         req.EventCreditGrantAppRatio,
			EventCreditGrantLandingPage:                      req.EventCreditGrantLandingPage,
			EventCreditGrantLandingPageCost:                  req.EventCreditGrantLandingPageCost,
			EventCreditGrantLandingRatio:                     req.EventCreditGrantLandingRatio,
			EventCreditGrantFirstDayApp:                      req.EventCreditGrantFirstDayApp,
			EventCreditGrantFirstDayAppCost:                  req.EventCreditGrantFirstDayAppCost,
			EventCreditGrantFirstDayAppRatio:                 req.EventCreditGrantFirstDayAppRatio,
			EventCreditGrantFirstDayLandingPage:              req.EventCreditGrantFirstDayLandingPage,
			EventCreditGrantFirstDayLandingPageCost:          req.EventCreditGrantFirstDayLandingPageCost,
			EventCreditGrantFirstDayLandingPageRatio:         req.EventCreditGrantFirstDayLandingPageRatio,
			CreditGrant0DCnt:                                 req.CreditGrant0DCnt,
			CreditGrant3DCnt:                                 req.CreditGrant3DCnt,
			CreditGrant0DCntCost:                             req.CreditGrant0DCntCost,
			CreditGrant3DCntCost:                             req.CreditGrant3DCntCost,
			CreditGrant0DCntRatio:                            req.CreditGrant0DCntRatio,
			CreditGrant3DCntRatio:                            req.CreditGrant3DCntRatio,
			EventOrderSubmit:                                 req.EventOrderSubmit,
			EventOrderPaid:                                   req.EventOrderPaid,
			EventOrderPaidPurchaseAmount:                     req.EventOrderPaidPurchaseAmount,
			EventOrderPaidCost:                               req.EventOrderPaidCost,
			EventOrderPaidRoi:                                req.EventOrderPaidRoi,
			Submit:                                           req.Submit,
			FormCount:                                        req.FormCount,
			FormCost:                                         req.FormCost,
			FormActionRatio:                                  req.FormActionRatio,
			EventValidClues:                                  req.EventValidClues,
			EventValidCluesCost:                              req.EventValidCluesCost,
			EventAudition:                                    req.EventAudition,
			EventConsultationValidRetained:                   req.EventConsultationValidRetained,
			EventConsultationValidRetainedCost:               req.EventConsultationValidRetainedCost,
			EventConsultationValidRetainedRatio:              req.EventConsultationValidRetainedRatio,
			EventConversionClickCost:                         req.EventConversionClickCost,
			EventConversionClickRatio:                        req.EventConversionClickRatio,
			EventPreComponentConsultationValidRetained:       req.EventPreComponentConsultationValidRetained,
			EventNextDayStayCost:                             req.EventNextDayStayCost,
			EventNextDayStayRatio:                            req.EventNextDayStayRatio,
			EventNextDayStay:                                 req.EventNextDayStay,
			EventAdWatch10Times:                              req.EventAdWatch10Times,
			EventAdWatch10TimesCost:                          req.EventAdWatch10TimesCost,
			EventAdWatch10TimesRatio:                         req.EventAdWatch10TimesRatio,
			EventAdWatch20Times:                              req.EventAdWatch20Times,
			EventAdWatch20TimesCost:                          req.EventAdWatch20TimesCost,
			EventAdWatch20TimesRatio:                         req.EventAdWatch20TimesRatio,
			EventAdWatch5Times:                               req.EventAdWatch5Times,
			EventAdWatch5TimesCost:                           req.EventAdWatch5TimesCost,
			EventAdWatch5TimesRatio:                          req.EventAdWatch5TimesRatio,
			EventWatchAppAd:                                  req.EventWatchAppAd,
			EventAdWatchTimes:                                req.EventAdWatchTimes,
			EventAdWatchTimesRatio:                           req.EventAdWatchTimesRatio,
			EventAdWatchTimesCost:                            req.EventAdWatchTimesCost,
			EventMakingCalls:                                 req.EventMakingCalls,
			EventMakingCallsCost:                             req.EventMakingCallsCost,
			EventMakingCallsRatio:                            req.EventMakingCallsRatio,
			EventGetThrough:                                  req.EventGetThrough,
			EventGetThroughCost:                              req.EventGetThroughCost,
			EventGetThroughRatio:                             req.EventGetThroughRatio,
			EventPhoneGetThrough:                             req.EventPhoneGetThrough,
			EventOutboundCall:                                req.EventOutboundCall,
			EventOutboundCallCost:                            req.EventOutboundCallCost,
			EventOutboundCallRatio:                           req.EventOutboundCallRatio,
			EventWechatQrCodeLinkClick:                       req.EventWechatQrCodeLinkClick,
			EventAddWechat:                                   req.EventAddWechat,
			EventAddWechatCost:                               req.EventAddWechatCost,
			EventAddWechatRatio:                              req.EventAddWechatRatio,
			EventWechatConnected:                             req.EventWechatConnected,
			PhotoClick:                                       req.PhotoClick,
			PhotoClickRatio:                                  req.PhotoClickRatio,
			PhotoClickCost:                                   req.PhotoClickCost,
			ActionRatio:                                      req.ActionRatio,
			ActionNewRatio:                                   req.ActionNewRatio,
			ActionCost:                                       req.ActionCost,
			Impression1KCost:                                 req.Impression1KCost,
			Click1KCost:                                      req.Click1KCost,
			ApproxPayCost:                                    req.ApproxPayCost,
			ApproxPayCount:                                   req.ApproxPayCount,
			ApproxPayRatio:                                   req.ApproxPayRatio,
			LiveEventGoodsView:                               req.LiveEventGoodsView,
			LivePlayed3S:                                     req.LivePlayed3S,
			AdProductCnt:                                     req.AdProductCnt,
			EventGoodsView:                                   req.EventGoodsView,
			EventGoodsViewCost:                               req.EventGoodsViewCost,
			MerchantRecoFans:                                 req.MerchantRecoFans,
			MerchantRecoFansCost:                             req.MerchantRecoFansCost,
			EventOrderAmountRoi:                              req.EventOrderAmountRoi,
			EventNewUserPay:                                  req.EventNewUserPay,
			EventNewUserPayCost:                              req.EventNewUserPayCost,
			EventNewUserPayRatio:                             req.EventNewUserPayRatio,
			EventNewUserJinjianApp:                           req.EventNewUserJinjianApp,
			EventNewUserJinjianAppCost:                       req.EventNewUserJinjianAppCost,
			EventNewUserJinjianAppRoi:                        req.EventNewUserJinjianAppRoi,
			EventNewUserCreditGrantApp:                       req.EventNewUserCreditGrantApp,
			EventNewUserCreditGrantAppCost:                   req.EventNewUserCreditGrantAppCost,
			EventNewUserCreditGrantAppRoi:                    req.EventNewUserCreditGrantAppRoi,
			EventNewUserJinjianPage:                          req.EventNewUserJinjianPage,
			EventNewUserJinjianPageCost:                      req.EventNewUserJinjianPageCost,
			EventNewUserJinjianPageRoi:                       req.EventNewUserJinjianPageRoi,
			EventNewUserCreditGrantPage:                      req.EventNewUserCreditGrantPage,
			EventNewUserCreditGrantPageCost:                  req.EventNewUserCreditGrantPageCost,
			EventNewUserCreditGrantPageRoi:                   req.EventNewUserCreditGrantPageRoi,
			EventAppointForm:                                 req.EventAppointForm,
			EventAppointFormCost:                             req.EventAppointFormCost,
			EventAppointFormRatio:                            req.EventAppointFormRatio,
			EventAppointJumpClick:                            req.EventAppointJumpClick,
			EventAppointJumpClickCost:                        req.EventAppointJumpClickCost,
			EventAppointJumpClickRatio:                       req.EventAppointJumpClickRatio,
			UnionEventPayPurchaseAmount7D:                    req.UnionEventPayPurchaseAmount7D,
			UnionEventPayPurchaseAmount7DRoi:                 req.UnionEventPayPurchaseAmount7DRoi,
			EventDspGiftForm:                                 req.EventDspGiftForm,
			EventAppInvoked:                                  req.EventAppInvoked,
			EventAppInvokedCost:                              req.EventAppInvokedCost,
			EventAppInvokedRatio:                             req.EventAppInvokedRatio,
			EventButtonClick:                                 req.EventButtonClick,
			EventButtonClickCost:                             req.EventButtonClickCost,
			EventButtonClickRatio:                            req.EventButtonClickRatio,
			EventMultiConversion:                             req.EventMultiConversion,
			EventMultiConversionRatio:                        req.EventMultiConversionRatio,
			EventMultiConversionCost:                         req.EventMultiConversionCost,
			EventAddShoppingCart:                             req.EventAddShoppingCart,
			EventAddShoppingCartCost:                         req.EventAddShoppingCartCost,
			EventIntentionConfirmed:                          req.EventIntentionConfirmed,
			EventOrderSuccessed:                              req.EventOrderSuccessed,
			EventPhoneCardActivate:                           req.EventPhoneCardActivate,
			EventMeasurementHouse:                            req.EventMeasurementHouse,
			KeyAction:                                        req.KeyAction,
			KeyActionCost:                                    req.KeyActionCost,
			KeyActionRatio:                                   req.KeyActionRatio,
			KeyInappAction0DCnt:                              req.KeyInappAction0DCnt,
			KeyInappAction3DCnt:                              req.KeyInappAction3DCnt,
			KeyInappAction0DCntCost:                          req.KeyInappAction0DCntCost,
			KeyInappAction3DCntCost:                          req.KeyInappAction3DCntCost,
			KeyInappAction0DCntRatio:                         req.KeyInappAction0DCntRatio,
			KeyInappAction3DCntRatio:                         req.KeyInappAction3DCntRatio,
			DrawCreditLine0DCnt:                              req.DrawCreditLine0DCnt,
			DrawCreditLine0DCntCost:                          req.DrawCreditLine0DCntCost,
			DrawCreditLine0DCntRatio:                         req.DrawCreditLine0DCntRatio,
			EventCreditCardRecheck:                           req.EventCreditCardRecheck,
			EventCreditCardRecheckFirstDay:                   req.EventCreditCardRecheckFirstDay,
			EventNoIntention:                                 req.EventNoIntention,
			AdScene:                                          req.AdScene,
			AdSceneField:                                     req.AdSceneField,
			PlacementType:                                    req.PlacementType,
		})
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sKsAdvertiserUnitReportData) BatchAdd(ctx context.Context, batchReq []*model.KsAdvertiserUnitReportDataAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		data := make([]*do.KsAdvertiserUnitReportData, len(batchReq))
		for k, v := range batchReq {
			data[k] = &do.KsAdvertiserUnitReportData{
				UnitId:                      v.UnitId,
				StatDate:                    v.StatDate,
				AdvertiserId:                v.AdvertiserId,
				CampaignId:                  v.CampaignId,
				Status:                      v.Status,
				UnitSource:                  v.UnitSource,
				PrivateMessageSentCost:      v.PrivateMessageSentCost,
				PrivateMessageSentRatio:     v.PrivateMessageSentRatio,
				PrivateMessageSentCnt:       v.PrivateMessageSentCnt,
				LeadsSubmitCost:             v.LeadsSubmitCost,
				LeadsSubmitCntRatio:         v.LeadsSubmitCntRatio,
				LeadsSubmitCnt:              v.LeadsSubmitCnt,
				PlayedNum:                   v.PlayedNum,
				PlayedEnd:                   v.PlayedEnd,
				PlayedFiveSeconds:           v.PlayedFiveSeconds,
				PlayedThreeSeconds:          v.PlayedThreeSeconds,
				Play3SRatio:                 v.Play3SRatio,
				Play5SRatio:                 v.Play5SRatio,
				PlayEndRatio:                v.PlayEndRatio,
				AdPhotoPlayed10S:            v.AdPhotoPlayed10S,
				AdPhotoPlayed2S:             v.AdPhotoPlayed2S,
				AdPhotoPlayed75Percent:      v.AdPhotoPlayed75Percent,
				AdPhotoPlayed75PercentRatio: v.AdPhotoPlayed75PercentRatio,
				AdPhotoPlayed10SRatio:       v.AdPhotoPlayed10SRatio,
				AdPhotoPlayed2SRatio:        v.AdPhotoPlayed2SRatio,
				MinigameIaaPurchaseAmountWeekByConversionRoi:     v.MinigameIaaPurchaseAmountWeekByConversionRoi,
				MinigameIaaPurchaseAmountThreeDayByConversionRoi: v.MinigameIaaPurchaseAmountThreeDayByConversionRoi,
				MinigameIaaPurchaseAmountFirstDayRoi:             v.MinigameIaaPurchaseAmountFirstDayRoi,
				MinigameIaaPurchaseAmountWeekByConversion:        v.MinigameIaaPurchaseAmountWeekByConversion,
				MinigameIaaPurchaseAmountThreeDayByConversion:    v.MinigameIaaPurchaseAmountThreeDayByConversion,
				MinigameIaaPurchaseAmountFirstDay:                v.MinigameIaaPurchaseAmountFirstDay,
				MinigameIaaPurchaseRoi:                           v.MinigameIaaPurchaseRoi,
				MinigameIaaPurchaseAmount:                        v.MinigameIaaPurchaseAmount,
				MinigameIaaPurchaseAmount30DayByConversionRoi:    v.MinigameIaaPurchaseAmount30DayByConversionRoi,
				MinigameIaaPurchaseAmount15DayByConversionRoi:    v.MinigameIaaPurchaseAmount15DayByConversionRoi,
				MinigameIaaPurchaseAmount30DayByConversion:       v.MinigameIaaPurchaseAmount30DayByConversion,
				MinigameIaaPurchaseAmount15DayByConversion:       v.MinigameIaaPurchaseAmount15DayByConversion,
				MmuEffectiveCustomerAcquisition7DCnt:             v.MmuEffectiveCustomerAcquisition7DCnt,
				MmuEffectiveCustomerAcquisitionCnt:               v.MmuEffectiveCustomerAcquisitionCnt,
				EffectiveCustomerAcquisition7DRatio:              v.EffectiveCustomerAcquisition7DRatio,
				EffectiveCustomerAcquisition7DCost:               v.EffectiveCustomerAcquisition7DCost,
				EffectiveCustomerAcquisition7DCnt:                v.EffectiveCustomerAcquisition7DCnt,
				EventPay30DayOverallRoi:                          v.EventPay30DayOverallRoi,
				EventPay15DayOverallRoi:                          v.EventPay15DayOverallRoi,
				EventPayPurchaseAmount15DayByConversion:          v.EventPayPurchaseAmount15DayByConversion,
				EventPayPurchaseAmount30DayByConversion:          v.EventPayPurchaseAmount30DayByConversion,
				EventPayFirstDay:                                 v.EventPayFirstDay,
				EventPayPurchaseAmountFirstDay:                   v.EventPayPurchaseAmountFirstDay,
				EventPayFirstDayRoi:                              v.EventPayFirstDayRoi,
				EventPay:                                         v.EventPay,
				EventPayPurchaseAmount:                           v.EventPayPurchaseAmount,
				EventPayRoi:                                      v.EventPayRoi,
				EventPayPurchaseAmountOneDay:                     v.EventPayPurchaseAmountOneDay,
				EventPayPurchaseAmountOneDayByConversion:         v.EventPayPurchaseAmountOneDayByConversion,
				EventPayPurchaseAmountOneDayByConversionRoi:      v.EventPayPurchaseAmountOneDayByConversionRoi,
				EventPayPurchaseAmountOneDayRoi:                  v.EventPayPurchaseAmountOneDayRoi,
				EventPayWeightedPurchaseAmount:                   v.EventPayWeightedPurchaseAmount,
				EventPayWeightedPurchaseAmountFirstDay:           v.EventPayWeightedPurchaseAmountFirstDay,
				Charge:                                           v.Charge,
				Show:                                             v.Show,
				Aclick:                                           v.Aclick,
				Bclick:                                           v.Bclick,
				AdShow:                                           v.AdShow,
				Share:                                            v.Share,
				Comment:                                          v.Comment,
				Like:                                             v.Like,
				Follow:                                           v.Follow,
				CancelLike:                                       v.CancelLike,
				CancelFollow:                                     v.CancelFollow,
				Report:                                           v.Report,
				Block:                                            v.Block,
				Negative:                                         v.Negative,
				Activation:                                       v.Activation,
				DownloadStarted:                                  v.DownloadStarted,
				DownloadCompleted:                                v.DownloadCompleted,
				DownloadInstalled:                                v.DownloadInstalled,
				ClickConversionRatio:                             v.ClickConversionRatio,
				ConversionCost:                                   v.ConversionCost,
				DownloadCompletedCost:                            v.DownloadCompletedCost,
				DownloadCompletedRatio:                           v.DownloadCompletedRatio,
				DownloadConversionRatio:                          v.DownloadConversionRatio,
				DownloadStartedCost:                              v.DownloadStartedCost,
				DownloadStartedRatio:                             v.DownloadStartedRatio,
				EventRegister:                                    v.EventRegister,
				EventRegisterCost:                                v.EventRegisterCost,
				EventRegisterRatio:                               v.EventRegisterRatio,
				EventJinJianApp:                                  v.EventJinJianApp,
				EventJinJianAppCost:                              v.EventJinJianAppCost,
				EventJinJianLandingPage:                          v.EventJinJianLandingPage,
				EventJinJianLandingPageCost:                      v.EventJinJianLandingPageCost,
				Jinjian0DCnt:                                     v.Jinjian0DCnt,
				Jinjian3DCnt:                                     v.Jinjian3DCnt,
				Jinjian0DCntCost:                                 v.Jinjian0DCntCost,
				Jinjian3DCntCost:                                 v.Jinjian3DCntCost,
				EventCreditGrantApp:                              v.EventCreditGrantApp,
				EventCreditGrantAppCost:                          v.EventCreditGrantAppCost,
				EventCreditGrantAppRatio:                         v.EventCreditGrantAppRatio,
				EventCreditGrantLandingPage:                      v.EventCreditGrantLandingPage,
				EventCreditGrantLandingPageCost:                  v.EventCreditGrantLandingPageCost,
				EventCreditGrantLandingRatio:                     v.EventCreditGrantLandingRatio,
				EventCreditGrantFirstDayApp:                      v.EventCreditGrantFirstDayApp,
				EventCreditGrantFirstDayAppCost:                  v.EventCreditGrantFirstDayAppCost,
				EventCreditGrantFirstDayAppRatio:                 v.EventCreditGrantFirstDayAppRatio,
				EventCreditGrantFirstDayLandingPage:              v.EventCreditGrantFirstDayLandingPage,
				EventCreditGrantFirstDayLandingPageCost:          v.EventCreditGrantFirstDayLandingPageCost,
				EventCreditGrantFirstDayLandingPageRatio:         v.EventCreditGrantFirstDayLandingPageRatio,
				CreditGrant0DCnt:                                 v.CreditGrant0DCnt,
				CreditGrant3DCnt:                                 v.CreditGrant3DCnt,
				CreditGrant0DCntCost:                             v.CreditGrant0DCntCost,
				CreditGrant3DCntCost:                             v.CreditGrant3DCntCost,
				CreditGrant0DCntRatio:                            v.CreditGrant0DCntRatio,
				CreditGrant3DCntRatio:                            v.CreditGrant3DCntRatio,
				EventOrderSubmit:                                 v.EventOrderSubmit,
				EventOrderPaid:                                   v.EventOrderPaid,
				EventOrderPaidPurchaseAmount:                     v.EventOrderPaidPurchaseAmount,
				EventOrderPaidCost:                               v.EventOrderPaidCost,
				EventOrderPaidRoi:                                v.EventOrderPaidRoi,
				Submit:                                           v.Submit,
				FormCount:                                        v.FormCount,
				FormCost:                                         v.FormCost,
				FormActionRatio:                                  v.FormActionRatio,
				EventValidClues:                                  v.EventValidClues,
				EventValidCluesCost:                              v.EventValidCluesCost,
				EventAudition:                                    v.EventAudition,
				EventConsultationValidRetained:                   v.EventConsultationValidRetained,
				EventConsultationValidRetainedCost:               v.EventConsultationValidRetainedCost,
				EventConsultationValidRetainedRatio:              v.EventConsultationValidRetainedRatio,
				EventConversionClickCost:                         v.EventConversionClickCost,
				EventConversionClickRatio:                        v.EventConversionClickRatio,
				EventPreComponentConsultationValidRetained:       v.EventPreComponentConsultationValidRetained,
				EventNextDayStayCost:                             v.EventNextDayStayCost,
				EventNextDayStayRatio:                            v.EventNextDayStayRatio,
				EventNextDayStay:                                 v.EventNextDayStay,
				EventAdWatch10Times:                              v.EventAdWatch10Times,
				EventAdWatch10TimesCost:                          v.EventAdWatch10TimesCost,
				EventAdWatch10TimesRatio:                         v.EventAdWatch10TimesRatio,
				EventAdWatch20Times:                              v.EventAdWatch20Times,
				EventAdWatch20TimesCost:                          v.EventAdWatch20TimesCost,
				EventAdWatch20TimesRatio:                         v.EventAdWatch20TimesRatio,
				EventAdWatch5Times:                               v.EventAdWatch5Times,
				EventAdWatch5TimesCost:                           v.EventAdWatch5TimesCost,
				EventAdWatch5TimesRatio:                          v.EventAdWatch5TimesRatio,
				EventWatchAppAd:                                  v.EventWatchAppAd,
				EventAdWatchTimes:                                v.EventAdWatchTimes,
				EventAdWatchTimesRatio:                           v.EventAdWatchTimesRatio,
				EventAdWatchTimesCost:                            v.EventAdWatchTimesCost,
				EventMakingCalls:                                 v.EventMakingCalls,
				EventMakingCallsCost:                             v.EventMakingCallsCost,
				EventMakingCallsRatio:                            v.EventMakingCallsRatio,
				EventGetThrough:                                  v.EventGetThrough,
				EventGetThroughCost:                              v.EventGetThroughCost,
				EventGetThroughRatio:                             v.EventGetThroughRatio,
				EventPhoneGetThrough:                             v.EventPhoneGetThrough,
				EventOutboundCall:                                v.EventOutboundCall,
				EventOutboundCallCost:                            v.EventOutboundCallCost,
				EventOutboundCallRatio:                           v.EventOutboundCallRatio,
				EventWechatQrCodeLinkClick:                       v.EventWechatQrCodeLinkClick,
				EventAddWechat:                                   v.EventAddWechat,
				EventAddWechatCost:                               v.EventAddWechatCost,
				EventAddWechatRatio:                              v.EventAddWechatRatio,
				EventWechatConnected:                             v.EventWechatConnected,
				PhotoClick:                                       v.PhotoClick,
				PhotoClickRatio:                                  v.PhotoClickRatio,
				PhotoClickCost:                                   v.PhotoClickCost,
				ActionRatio:                                      v.ActionRatio,
				ActionNewRatio:                                   v.ActionNewRatio,
				ActionCost:                                       v.ActionCost,
				Impression1KCost:                                 v.Impression1KCost,
				Click1KCost:                                      v.Click1KCost,
				ApproxPayCost:                                    v.ApproxPayCost,
				ApproxPayCount:                                   v.ApproxPayCount,
				ApproxPayRatio:                                   v.ApproxPayRatio,
				LiveEventGoodsView:                               v.LiveEventGoodsView,
				LivePlayed3S:                                     v.LivePlayed3S,
				AdProductCnt:                                     v.AdProductCnt,
				EventGoodsView:                                   v.EventGoodsView,
				EventGoodsViewCost:                               v.EventGoodsViewCost,
				MerchantRecoFans:                                 v.MerchantRecoFans,
				MerchantRecoFansCost:                             v.MerchantRecoFansCost,
				EventOrderAmountRoi:                              v.EventOrderAmountRoi,
				EventNewUserPay:                                  v.EventNewUserPay,
				EventNewUserPayCost:                              v.EventNewUserPayCost,
				EventNewUserPayRatio:                             v.EventNewUserPayRatio,
				EventNewUserJinjianApp:                           v.EventNewUserJinjianApp,
				EventNewUserJinjianAppCost:                       v.EventNewUserJinjianAppCost,
				EventNewUserJinjianAppRoi:                        v.EventNewUserJinjianAppRoi,
				EventNewUserCreditGrantApp:                       v.EventNewUserCreditGrantApp,
				EventNewUserCreditGrantAppCost:                   v.EventNewUserCreditGrantAppCost,
				EventNewUserCreditGrantAppRoi:                    v.EventNewUserCreditGrantAppRoi,
				EventNewUserJinjianPage:                          v.EventNewUserJinjianPage,
				EventNewUserJinjianPageCost:                      v.EventNewUserJinjianPageCost,
				EventNewUserJinjianPageRoi:                       v.EventNewUserJinjianPageRoi,
				EventNewUserCreditGrantPage:                      v.EventNewUserCreditGrantPage,
				EventNewUserCreditGrantPageCost:                  v.EventNewUserCreditGrantPageCost,
				EventNewUserCreditGrantPageRoi:                   v.EventNewUserCreditGrantPageRoi,
				EventAppointForm:                                 v.EventAppointForm,
				EventAppointFormCost:                             v.EventAppointFormCost,
				EventAppointFormRatio:                            v.EventAppointFormRatio,
				EventAppointJumpClick:                            v.EventAppointJumpClick,
				EventAppointJumpClickCost:                        v.EventAppointJumpClickCost,
				EventAppointJumpClickRatio:                       v.EventAppointJumpClickRatio,
				UnionEventPayPurchaseAmount7D:                    v.UnionEventPayPurchaseAmount7D,
				UnionEventPayPurchaseAmount7DRoi:                 v.UnionEventPayPurchaseAmount7DRoi,
				EventDspGiftForm:                                 v.EventDspGiftForm,
				EventAppInvoked:                                  v.EventAppInvoked,
				EventAppInvokedCost:                              v.EventAppInvokedCost,
				EventAppInvokedRatio:                             v.EventAppInvokedRatio,
				EventButtonClick:                                 v.EventButtonClick,
				EventButtonClickCost:                             v.EventButtonClickCost,
				EventButtonClickRatio:                            v.EventButtonClickRatio,
				EventMultiConversion:                             v.EventMultiConversion,
				EventMultiConversionRatio:                        v.EventMultiConversionRatio,
				EventMultiConversionCost:                         v.EventMultiConversionCost,
				EventAddShoppingCart:                             v.EventAddShoppingCart,
				EventAddShoppingCartCost:                         v.EventAddShoppingCartCost,
				EventIntentionConfirmed:                          v.EventIntentionConfirmed,
				EventOrderSuccessed:                              v.EventOrderSuccessed,
				EventPhoneCardActivate:                           v.EventPhoneCardActivate,
				EventMeasurementHouse:                            v.EventMeasurementHouse,
				KeyAction:                                        v.KeyAction,
				KeyActionCost:                                    v.KeyActionCost,
				KeyActionRatio:                                   v.KeyActionRatio,
				KeyInappAction0DCnt:                              v.KeyInappAction0DCnt,
				KeyInappAction3DCnt:                              v.KeyInappAction3DCnt,
				KeyInappAction0DCntCost:                          v.KeyInappAction0DCntCost,
				KeyInappAction3DCntCost:                          v.KeyInappAction3DCntCost,
				KeyInappAction0DCntRatio:                         v.KeyInappAction0DCntRatio,
				KeyInappAction3DCntRatio:                         v.KeyInappAction3DCntRatio,
				DrawCreditLine0DCnt:                              v.DrawCreditLine0DCnt,
				DrawCreditLine0DCntCost:                          v.DrawCreditLine0DCntCost,
				DrawCreditLine0DCntRatio:                         v.DrawCreditLine0DCntRatio,
				EventCreditCardRecheck:                           v.EventCreditCardRecheck,
				EventCreditCardRecheckFirstDay:                   v.EventCreditCardRecheckFirstDay,
				EventNoIntention:                                 v.EventNoIntention,
				AdScene:                                          v.AdScene,
				AdSceneField:                                     v.AdSceneField,
				PlacementType:                                    v.PlacementType,
			}
		}
		_, err = dao.KsAdvertiserUnitReportData.Ctx(ctx).Save(data)
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sKsAdvertiserUnitReportData) RunSyncKsUnitReportData(ctx context.Context, req *model.KsAdvertiserUnitReportDataSearchReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		startTime := req.StartTime
		endTime := req.EndTime
		for {
			if startTime > endTime {
				break
			}
			errors := s.SyncKsUnitReportData(ctx, startTime)
			if errors != nil {
				g.Log().Error(ctx, errors)
			}
			startTime = libUtils.PlusDays(startTime, 1)
		}
	})
	return
}

func (s *sKsAdvertiserUnitReportData) SyncKsUnitReportDataTask(ctx context.Context) {
	err := g.Try(ctx, func(ctx context.Context) {
		pool := goredis.NewPool(commonService.GetGoRedis())
		rs := redsync.New(pool)
		mutex := rs.NewMutex(commonConsts.PlatSyncKsUnitReportDataLock, redsync.WithRetryDelay(50*time.Millisecond))
		// TryLockContext只尝试锁定一次，无论成功或失败立即返回，无需重试
		err := mutex.TryLockContext(ctx)
		liberr.ErrIsNil(ctx, err, fmt.Sprintf("Redisson没有获取到分布式锁：%s", commonConsts.PlatSyncKsUnitReportDataLock))
		// 释放锁
		defer mutex.UnlockContext(ctx)
		today := gtime.Now().Format("Y-m-d")
		err = s.SyncKsUnitReportData(ctx, today)
		liberr.ErrIsNil(ctx, err, "同步快手广告组报表数据失败")
		sysService.SysJobLog().Add(ctx, &sysDo.SysJobLog{
			TargetName: "SyncKsUnitReportDataTask",
			CreatedAt:  gtime.Now(),
			Result:     "同步快手广告组报表数据，执行成功",
		})
	})
	if err != nil {
		g.Log().Error(ctx, err)
	}
}

// SyncKsUnitReportData 同步快手广告组报表数据
func (s *sKsAdvertiserUnitReportData) SyncKsUnitReportData(ctx context.Context, statDate string) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		var pageNo = 1
		var pageSize = 500
		for {
			// 获取账户列表
			advertiserList, _ := service.KsAdvertiserAccountInfo().GetAccountList(ctx, pageNo, pageSize)
			if len(advertiserList) == 0 {
				break
			}
			var statList = make([]*model.KsAdvertiserUnitReportDataAddReq, 0)
			for _, item := range advertiserList {
				agentInfo, _ := service.KsAdvertiserAgentInfo().GetAPPConfig(ctx, item.AgentAccountId, item.AuthorizeKsAccount)
				var accessToken = ksApi.GetAccessTokenByAgentCache(item.AgentAccountId, item.AuthorizeKsAccount, gconv.Int64(agentInfo), agentInfo.Secret)
				//var accessToken = ksApi.GetAccessTokenByAgentCache(item.AgentAccountId, item.AuthorizeKsAccount)
				if accessToken == "" {
					g.Log().Infof(ctx, "同步快手广告组报表数据失败, 广告主%v accessToken不存在", item.AccountId)
					continue
				}
				var unitPageNo = 1
				var unitPageSize = 500
				for {
					unitReportRes, err1 := ksApi.GetKSApiClient().UnitReportService.
						AccessToken(accessToken).
						SetReq(ksApi.UnitReportReq{
							AdvertiserId:        item.AccountId,
							StartDate:           statDate,
							EndDate:             statDate,
							Page:                unitPageNo,
							PageSize:            unitPageSize,
							TemporalGranularity: "DAILY",
						}).Do()
					if err1 != nil {
						g.Log().Errorf(ctx, "同步快手广告组报表数据失败, advertiserId: %v, err: %v", item.AccountId, err1)
						break
					}
					if unitReportRes.Data == nil || len(unitReportRes.Data.Details) == 0 {
						break
					}
					for _, unitDetail := range unitReportRes.Data.Details {
						var unitReport *model.KsAdvertiserUnitReportDataAddReq
						_ = gconv.Struct(unitDetail, &unitReport)
						unitReport.AdvertiserId = item.AccountId
						statList = append(statList, unitReport)
					}
					if len(statList) >= 100 {
						_ = s.BatchAdd(ctx, statList)
						statList = slices.Delete(statList, 0, len(statList))
					}
					if gconv.Int64(unitPageNo*unitPageSize) >= unitReportRes.Data.TotalCount {
						break
					}
					unitPageNo++
				}
				if len(statList) > 0 {
					err = s.BatchAdd(ctx, statList)
				}
			}
			pageNo++
		}
	})
	return
}
