// ==========================================================================
// GFast自动生成service操作代码。
// 生成日期：2025-08-22 11:52:52
// 生成路径: internal/app/ad/service/ks_advertiser_strategy_unit.go
// 生成人：cq
// desc:快手策略组-广告组
// company:云南奇讯科技有限公司
// ==========================================================================

package service

import (
	"context"

	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
)

type IKsAdvertiserStrategyUnit interface {
	List(ctx context.Context, req *model.KsAdvertiserStrategyUnitSearchReq) (res *model.KsAdvertiserStrategyUnitSearchRes, err error)
	GetById(ctx context.Context, Id uint64) (res *model.KsAdvertiserStrategyUnitInfoRes, err error)
	GetInfoById(ctx context.Context, strategyId string, taskId string) (res *model.KsAdvertiserStrategyUnitInfoRes, err error)
	Add(ctx context.Context, req *model.KsAdvertiserStrategyUnitAddReq) (err error)
	Edit(ctx context.Context, req *model.KsAdvertiserStrategyUnitEditReq) (err error)
	Delete(ctx context.Context, Id []uint64) (err error)
}

var localKsAdvertiserStrategyUnit IKsAdvertiserStrategyUnit

func KsAdvertiserStrategyUnit() IKsAdvertiserStrategyUnit {
	if localKsAdvertiserStrategyUnit == nil {
		panic("implement not found for interface IKsAdvertiserStrategyUnit, forgot register?")
	}
	return localKsAdvertiserStrategyUnit
}

func RegisterKsAdvertiserStrategyUnit(i IKsAdvertiserStrategyUnit) {
	localKsAdvertiserStrategyUnit = i
}
