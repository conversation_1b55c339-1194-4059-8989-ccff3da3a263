// ==========================================================================
// GFast自动生成controller操作代码。
// 生成日期：2025-08-14 14:09:04
// 生成路径: internal/app/ad/controller/ks_advertiser_account_info.go
// 生成人：cyao
// desc:快手广告账户表格
// company:云南奇讯科技有限公司
// ==========================================================================

package controller

import (
	"context"

	"github.com/tiger1103/gfast/v3/api/v1/ad"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	systemController "github.com/tiger1103/gfast/v3/internal/app/system/controller"
)

type ksAdvertiserAccountInfoController struct {
	systemController.BaseController
}

var KsAdvertiserAccountInfo = new(ksAdvertiserAccountInfoController)

// List 列表
func (c *ksAdvertiserAccountInfoController) List(ctx context.Context, req *ad.KsAdvertiserAccountInfoSearchReq) (res *ad.KsAdvertiserAccountInfoSearchRes, err error) {
	res = new(ad.KsAdvertiserAccountInfoSearchRes)
	res.KsAdvertiserAccountInfoSearchRes, err = service.KsAdvertiserAccountInfo().List(ctx, &req.KsAdvertiserAccountInfoSearchReq)
	return
}

// UpLoadVideo
func (c *ksAdvertiserAccountInfoController) UpLoadVideo(ctx context.Context, req *ad.UpLoadVideoReq) (res *ad.UpLoadVideoRes, err error) {
	res = new(ad.UpLoadVideoRes)
	res.UpLoadVideoRes, err = service.KsAdvertiserAccountInfo().UpLoadVideo(ctx, req.UpLoadVideoReq)
	return
}

// 批量设置归属人员
func (c *ksAdvertiserAccountInfoController) BatchSetAccountOwner(ctx context.Context, req *ad.KsAdvertiserAccountInfoSetOwnerReq) (res *ad.KsAdvertiserAccountInfoSetOwnerRes, err error) {
	err = service.KsAdvertiserAccountInfo().BatchSetAccountOwner(ctx, req.KsAdvertiserAccountInfoSetOwnerReq)
	return
}

// KsAdvertiserGetImportList
func (c *ksAdvertiserAccountInfoController) KsAdvertiserGetImportList(ctx context.Context, req *ad.KsAdvertiserGetImportListReq) (res *ad.KsAdvertiserGetImportListRes, err error) {
	res = new(ad.KsAdvertiserGetImportListRes)
	res.KsAdvertiserGetImportListRes, err = service.KsAdvertiserAccountInfo().GetImportList(ctx, &req.KsAdvertiserGetImportListReq)
	return
}

// Get 获取快手广告账户表格
func (c *ksAdvertiserAccountInfoController) Get(ctx context.Context, req *ad.KsAdvertiserAccountInfoGetReq) (res *ad.KsAdvertiserAccountInfoGetRes, err error) {
	res = new(ad.KsAdvertiserAccountInfoGetRes)
	res.KsAdvertiserAccountInfoInfoRes, err = service.KsAdvertiserAccountInfo().GetByAccountId(ctx, req.AccountId)
	return
}

// Add 添加快手广告账户表格
func (c *ksAdvertiserAccountInfoController) Add(ctx context.Context, req *ad.KsAdvertiserAccountInfoAddReq) (res *ad.KsAdvertiserAccountInfoAddRes, err error) {
	err = service.KsAdvertiserAccountInfo().Add(ctx, req.KsAdvertiserAccountInfoAddReq)
	return
}

// Import 通过代理商账号导入广告主账户
func (c *ksAdvertiserAccountInfoController) Import(ctx context.Context, req *ad.KsAdvertiserAccountInfoImportReq) (res *ad.KsAdvertiserAccountInfoImportRes, err error) {
	res = new(ad.KsAdvertiserAccountInfoImportRes)
	res.AccountIds, err = service.KsAdvertiserAccountInfo().Import(ctx, &req.KsAdvertiserAccountInfoImportReq)
	return
}

// Import2
func (c *ksAdvertiserAccountInfoController) Import2(ctx context.Context, req *ad.KsAdvertiserAccountInfoImport2Req) (res *ad.KsAdvertiserAccountInfoImport2Res, err error) {
	res = new(ad.KsAdvertiserAccountInfoImport2Res)
	err = service.KsAdvertiserAccountInfo().Import2(ctx, &req.KsAdvertiserAccountInfoImportReq)
	return
}

// Edit 修改快手广告账户表格
func (c *ksAdvertiserAccountInfoController) Edit(ctx context.Context, req *ad.KsAdvertiserAccountInfoEditReq) (res *ad.KsAdvertiserAccountInfoEditRes, err error) {
	err = service.KsAdvertiserAccountInfo().Edit(ctx, req.KsAdvertiserAccountInfoEditReq)
	return
}

// Delete 删除快手广告账户表格
func (c *ksAdvertiserAccountInfoController) Delete(ctx context.Context, req *ad.KsAdvertiserAccountInfoDeleteReq) (res *ad.KsAdvertiserAccountInfoDeleteRes, err error) {
	err = service.KsAdvertiserAccountInfo().Delete(ctx, req.AccountIds)
	return
}

// GetCorporationList 获取账户主体
func (c *ksAdvertiserAccountInfoController) GetCorporationList(ctx context.Context, req *ad.GetKsAdCorporationListReq) (res *ad.GetKsAdCorporationListRes, err error) {
	res = new(ad.GetKsAdCorporationListRes)
	res.GetCorporationListRes, err = service.KsAdvertiserAccountInfo().GetCorporationList(ctx, req.CorporationName, req.PageNum, req.PageSize)
	return
}

// GetAccountAutoInfo 查询账户智投配置信息
func (c *ksAdvertiserAccountInfoController) GetAccountAutoInfo(ctx context.Context, req *ad.GetKsAdAccountAutoInfoReq) (res *ad.GetKsAdAccountAutoInfoRes, err error) {
	res = new(ad.GetKsAdAccountAutoInfoRes)
	res.GetAccountAutoInfoRes, err = service.KsAdvertiserAccountInfo().GetAccountAutoInfo(ctx, req.AdvertiserId)
	return
}

// GetAccountIncExplore 查询账户增量探索配置信息
func (c *ksAdvertiserAccountInfoController) GetAccountIncExplore(ctx context.Context, req *ad.GetKsAdAccountIncExploreReq) (res *ad.GetKsAdAccountIncExploreRes, err error) {
	res = new(ad.GetKsAdAccountIncExploreRes)
	res.GetAccountIncExploreRes, err = service.KsAdvertiserAccountInfo().GetAccountIncExplore(ctx, req.AdvertiserId)
	return
}

// AddAccountIncExplore 新增账户增量探索配置
func (c *ksAdvertiserAccountInfoController) AddAccountIncExplore(ctx context.Context, req *ad.AddKsAdAccountIncExploreReq) (res *ad.AddKsAdAccountIncExploreRes, err error) {
	res = new(ad.AddKsAdAccountIncExploreRes)
	res.AddAccountIncExploreRes, err = service.KsAdvertiserAccountInfo().AddAccountIncExplore(ctx, &req.AddAccountIncExploreReq)
	return
}

// UpdateAccountIncExplore 编辑账户增量探索配置
func (c *ksAdvertiserAccountInfoController) UpdateAccountIncExplore(ctx context.Context, req *ad.UpdateKsAdAccountIncExploreReq) (res *ad.UpdateKsAdAccountIncExploreRes, err error) {
	res = new(ad.UpdateKsAdAccountIncExploreRes)
	res.UpdateAccountIncExploreRes, err = service.KsAdvertiserAccountInfo().UpdateAccountIncExplore(ctx, &req.UpdateAccountIncExploreReq)
	return
}

// DeleteAccountIncExplore 删除账户增量探索配置
func (c *ksAdvertiserAccountInfoController) DeleteAccountIncExplore(ctx context.Context, req *ad.DeleteKsAdAccountIncExploreReq) (res *ad.DeleteKsAdAccountIncExploreRes, err error) {
	res = new(ad.DeleteKsAdAccountIncExploreRes)
	res.DeleteAccountIncExploreRes, err = service.KsAdvertiserAccountInfo().DeleteAccountIncExplore(ctx, &req.DeleteAccountIncExploreReq)
	return
}

// PauseAccountIncExplore 暂停账户增量探索配置
func (c *ksAdvertiserAccountInfoController) PauseAccountIncExplore(ctx context.Context, req *ad.PauseKsAdAccountIncExploreReq) (res *ad.PauseKsAdAccountIncExploreRes, err error) {
	res = new(ad.PauseKsAdAccountIncExploreRes)
	res.PauseAccountIncExploreRes, err = service.KsAdvertiserAccountInfo().PauseAccountIncExplore(ctx, &req.PauseAccountIncExploreReq)
	return
}

// RebootAccountIncExplore 重启账户增量探索配置
func (c *ksAdvertiserAccountInfoController) RebootAccountIncExplore(ctx context.Context, req *ad.RebootKsAdAccountIncExploreReq) (res *ad.RebootKsAdAccountIncExploreRes, err error) {
	res = new(ad.RebootKsAdAccountIncExploreRes)
	res.RebootAccountIncExploreRes, err = service.KsAdvertiserAccountInfo().RebootAccountIncExplore(ctx, &req.RebootAccountIncExploreReq)
	return
}

// GetAccountBalance 实时查询账户余额
func (c *ksAdvertiserAccountInfoController) GetAccountBalance(ctx context.Context, req *ad.GetAccountBalanceReq) (res *ad.GetAccountBalanceRes, err error) {
	res = new(ad.GetAccountBalanceRes)
	res.GetAccountBalanceRes, err = service.KsAdvertiserAccountInfo().GetAccountBalance(ctx, req.AdvertiserIds)
	return
}
