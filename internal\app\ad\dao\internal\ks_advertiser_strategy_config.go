// ==========================================================================
// GFast自动生成dao internal操作代码。
// 生成日期：2025-08-22 11:51:48
// 生成路径: internal/app/ad/dao/internal/ks_advertiser_strategy_config.go
// 生成人：cq
// desc:快手广告搭建-策略配置
// company:云南奇讯科技有限公司
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// KsAdvertiserStrategyConfigDao is the manager for logic model data accessing and custom defined data operations functions management.
type KsAdvertiserStrategyConfigDao struct {
	table   string                            // Table is the underlying table name of the DAO.
	group   string                            // Group is the database configuration group name of current DAO.
	columns KsAdvertiserStrategyConfigColumns // Columns is the short type for Columns, which contains all the column names of Table for convenient usage.
}

// KsAdvertiserStrategyConfigColumns defines and stores column names for table ks_advertiser_strategy_config.
type KsAdvertiserStrategyConfigColumns struct {
	Id               string //
	StrategyId       string // 策略组ID
	StrategyName     string // 策略组名称
	StrategyDescribe string // 描述
	TaskId           string // 任务ID
	AdvertiserIds    string // 广告主ID列表
	AccountBatchRule string // 多账户分配规则 按需分配：ON_DEMAND
	AdGroupRule      string // 广告分组规则 按定向包分组：AUDIENCE_PACKAGE 按创意分组：CREATIVE 按文案分组：TITLE
	AdType           string // 广告计划类型 0:信息流（展示广告） 1:搜索（搜索广告）
	UserId           string // 归属人员
	CreatedAt        string // 创建时间
	UpdatedAt        string // 更新时间
	DeletedAt        string // 删除时间
}

var ksAdvertiserStrategyConfigColumns = KsAdvertiserStrategyConfigColumns{
	Id:               "id",
	StrategyId:       "strategy_id",
	StrategyName:     "strategy_name",
	StrategyDescribe: "strategy_describe",
	TaskId:           "task_id",
	AdvertiserIds:    "advertiser_ids",
	AccountBatchRule: "account_batch_rule",
	AdGroupRule:      "ad_group_rule",
	AdType:           "ad_type",
	UserId:           "user_id",
	CreatedAt:        "created_at",
	UpdatedAt:        "updated_at",
	DeletedAt:        "deleted_at",
}

// NewKsAdvertiserStrategyConfigDao creates and returns a new DAO object for table data access.
func NewKsAdvertiserStrategyConfigDao() *KsAdvertiserStrategyConfigDao {
	return &KsAdvertiserStrategyConfigDao{
		group:   "default",
		table:   "ks_advertiser_strategy_config",
		columns: ksAdvertiserStrategyConfigColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *KsAdvertiserStrategyConfigDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *KsAdvertiserStrategyConfigDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *KsAdvertiserStrategyConfigDao) Columns() KsAdvertiserStrategyConfigColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *KsAdvertiserStrategyConfigDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *KsAdvertiserStrategyConfigDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *KsAdvertiserStrategyConfigDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
