package api

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
)

// PauseAccountIncExploreService 暂停增量探索
type PauseAccountIncExploreService struct {
	ctx     context.Context
	cfg     *Configuration
	token   string
	Request *PauseAccountIncExploreReq
}

// PauseAccountIncExploreReq 请求结构体
type PauseAccountIncExploreReq struct {
	PauseInfo    []GwIncExploreDetailPauseDto `json:"pause_info"`    // 暂停详情
	AdvertiserId int64                        `json:"advertiser_id"` // 账号id
}

type GwIncExploreDetailPauseDto struct {
	OcpxActionType     int64 `json:"ocpx_action_type" dc:"转化目标类型"`
	DeepConversionType int64 `json:"deep_conversion_type" dc:"深度转化目标类型"`
}

func (r *PauseAccountIncExploreService) SetCfg(cfg *Configuration) *PauseAccountIncExploreService {
	r.cfg = cfg
	return r
}

func (r *PauseAccountIncExploreService) SetReq(req PauseAccountIncExploreReq) *PauseAccountIncExploreService {
	r.Request = &req
	return r
}

func (r *PauseAccountIncExploreService) AccessToken(accessToken string) *PauseAccountIncExploreService {
	r.token = accessToken
	return r
}

func (r *PauseAccountIncExploreService) Do() (data *KsBaseResp[[]GwIncExploreDetailView], err error) {
	localBasePath := r.cfg.BasePath
	localVarPath := localBasePath + "/rest/openapi/gw/dsp/account/incExplore/pause"
	response, err := r.cfg.HTTPClient.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("Access-Token", r.token).
		SetBody(r.Request).
		SetResult(&KsBaseResp[[]GwIncExploreDetailView]{}).
		Post(localVarPath)
	if err != nil {
		return nil, err
	}
	resp := new(KsBaseResp[[]GwIncExploreDetailView])
	// 将 JSON 响应解码到结构体中
	err = json.Unmarshal(response.Body(), &resp)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("/rest/openapi/gw/dsp/account/incExplore/pause解析响应出错: %v\n", err))
	}
	if resp.Code == 0 && resp.Data != nil {
		return resp, nil
	} else {
		return resp, errors.New(resp.Message)
	}
}
