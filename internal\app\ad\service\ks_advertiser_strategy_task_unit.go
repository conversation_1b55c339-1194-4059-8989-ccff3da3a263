// ==========================================================================
// GFast自动生成service操作代码。
// 生成日期：2025-08-23 17:40:24
// 生成路径: internal/app/ad/service/ks_advertiser_strategy_task_unit.go
// 生成人：cyao
// desc:快手广告搭建-任务-广告组
// company:云南奇讯科技有限公司
// ==========================================================================

package service

import (
	"context"

	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
)

type IKsAdvertiserStrategyTaskUnit interface {
	List(ctx context.Context, req *model.KsAdvertiserStrategyTaskUnitSearchReq) (res *model.KsAdvertiserStrategyTaskUnitSearchRes, err error)
	GetByTaskUnitId(ctx context.Context, TaskUnitId string) (res *model.KsAdvertiserStrategyTaskUnitInfoRes, err error)
	Add(ctx context.Context, req *model.KsAdvertiserStrategyTaskUnitAddReq) (err error)
	Edit(ctx context.Context, req *model.KsAdvertiserStrategyTaskUnitEditReq) (err error)
	Delete(ctx context.Context, TaskUnitId []string) (err error)
}

var localKsAdvertiserStrategyTaskUnit IKsAdvertiserStrategyTaskUnit

func KsAdvertiserStrategyTaskUnit() IKsAdvertiserStrategyTaskUnit {
	if localKsAdvertiserStrategyTaskUnit == nil {
		panic("implement not found for interface IKsAdvertiserStrategyTaskUnit, forgot register?")
	}
	return localKsAdvertiserStrategyTaskUnit
}

func RegisterKsAdvertiserStrategyTaskUnit(i IKsAdvertiserStrategyTaskUnit) {
	localKsAdvertiserStrategyTaskUnit = i
}
