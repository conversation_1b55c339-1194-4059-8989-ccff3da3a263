// ==========================================================================
// GFast自动生成service操作代码。
// 生成日期：2025-08-12 15:45:06
// 生成路径: internal/app/ad/service/ks_advertiser_agent_info.go
// 生成人：cyao
// desc:快手代理商信息表
// company:云南奇讯科技有限公司
// ==========================================================================

package service

import (
	"context"

	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
)

type IKsAdvertiserAgentInfo interface {
	List(ctx context.Context, req *model.KsAdvertiserAgentInfoSearchReq) (res *model.KsAdvertiserAgentInfoSearchRes, err error)
	GetById(ctx context.Context, Id uint64) (res *model.KsAdvertiserAgentInfoInfoRes, err error)
	GetByAgentIdAndKsUserId(ctx context.Context, agentId, userId int64) (res *model.KsAdvertiserAgentInfoInfoRes, err error)
	Add(ctx context.Context, req *model.KsAdvertiserAgentInfoAddReq) (err error)
	Share(ctx context.Context, req *model.KsAdvertiserAgentInfoShareReq) (res *model.KsAdvertiserAgentInfoShareRes, err error)
	GetAPPConfig(ctx context.Context, agentId, userId int64) (res *model.AdAppConfigInfoRes, err error)
	Edit(ctx context.Context, req *model.KsAdvertiserAgentInfoEditReq) (err error)
	Delete(ctx context.Context, Id []uint64) (err error)
}

var localKsAdvertiserAgentInfo IKsAdvertiserAgentInfo

func KsAdvertiserAgentInfo() IKsAdvertiserAgentInfo {
	if localKsAdvertiserAgentInfo == nil {
		panic("implement not found for interface IKsAdvertiserAgentInfo, forgot register?")
	}
	return localKsAdvertiserAgentInfo
}

func RegisterKsAdvertiserAgentInfo(i IKsAdvertiserAgentInfo) {
	localKsAdvertiserAgentInfo = i
}
