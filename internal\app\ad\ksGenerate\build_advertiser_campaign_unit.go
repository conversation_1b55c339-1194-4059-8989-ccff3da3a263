package generate

import (
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/gogf/gf/v2/util/gutil"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	ksApi "github.com/tiger1103/gfast/v3/library/advertiser/ks/api"
	"github.com/tiger1103/gfast/v3/library/libUtils"
	"time"
)

func BuildAdvertiserList(
	req *model.KsAdvertiserStrategyGenerateReq,
	estimateInfoMap map[int64]*model.EstimateInfo,
	previewBaseInfoList []*model.PreviewBaseInfo) (advertiserList []*model.AdvertiserInfo, err error) {
	for _, baseInfo := range previewBaseInfoList {
		var advertiserId = baseInfo.AdvertiserId
		estimateInfo := estimateInfoMap[advertiserId]
		advertiserInfo := &model.AdvertiserInfo{
			AdvertiserId:     gconv.String(estimateInfo.AdvertiserId),
			AdvertiserName:   estimateInfo.AdvertiserName,
			AdvertiserRemark: estimateInfo.AdvertiserRemark,
			CampaignNum:      estimateInfo.CampaignNum,
			UnitNum:          estimateInfo.UnitNum,
		}
		// 构建广告计划列表
		advertiserInfo.CampaignList, err = BuildCampaignList(req, baseInfo)
		if err != nil {
			return nil, err
		}
		advertiserList = append(advertiserList, advertiserInfo)
	}
	return
}

func BuildCampaignList(req *model.KsAdvertiserStrategyGenerateReq, baseInfo *model.PreviewBaseInfo) (campaignList []*model.CampaignInfo, err error) {
	campaignList = make([]*model.CampaignInfo, 0)
	for _, campaignBaseInfo := range baseInfo.CampaignList {
		campaignInfo := &model.CampaignInfo{
			CampaignId:                         campaignBaseInfo.CampaignId,
			KsAdvertiserStrategyCampaignAddReq: gutil.Copy(req.KsAdvertiserStrategyCampaignConfig).(*model.KsAdvertiserStrategyCampaignAddReq),
		}
		campaignInfo.UnitList, err = BuildUnitList(req, campaignBaseInfo, baseInfo)
		campaignList = append(campaignList, campaignInfo)
	}
	return
}

func BuildUnitList(req *model.KsAdvertiserStrategyGenerateReq,
	campaignBaseInfo *model.PreviewCampaignBaseInfo,
	baseInfo *model.PreviewBaseInfo) (unitList []*model.UnitInfo, err error) {
	var advertiserId = baseInfo.AdvertiserId
	shortPlayList, productList, bidInfo := FilterUnitDataByAdvertiserId(req.KsAdvertiserStrategyUnitConfig, advertiserId)
	var shortPlayIndex, productIndex = 0, 0
	unitList = make([]*model.UnitInfo, 0)
	for _, unitBaseInfo := range campaignBaseInfo.UnitList {
		unitInfo := &model.UnitInfo{
			KsAdvertiserStrategyUnitAddReq: gutil.Copy(req.KsAdvertiserStrategyUnitConfig).(*model.KsAdvertiserStrategyUnitAddReq),
		}
		// 选择短剧与集数
		shortPlayInfo := libUtils.LoopAndAssign(shortPlayList, &shortPlayIndex)
		unitInfo.ShortPlayData = []*model.ShortPlayData{
			{
				AdvertiserId:      advertiserId,
				ShortPlayInfoList: []*model.ShortPlayInfo{shortPlayInfo},
			},
		}
		unitInfo.ShortPlayName = shortPlayInfo.Title
		unitInfo.SeriesPayMode = shortPlayInfo.SeriesPayMode
		unitInfo.SeriesPayModeName = shortPlayInfo.SeriesPayModeName
		unitInfo.SeriesPayTemplateId = shortPlayInfo.SeriesPayTemplateId
		unitInfo.SeriesPayTemplateIdName = shortPlayInfo.SeriesPayTemplateIdName
		// 关联商品
		productInfo := libUtils.LoopAndAssign(productList, &productIndex)
		unitInfo.ProductData = []*model.ProductData{
			{
				AdvertiserId:    advertiserId,
				ProductInfoList: []*ksApi.ProductInfo{productInfo},
			},
		}
		// 出价信息
		unitInfo.BidData = []*model.BidData{
			{
				AdvertiserId: advertiserId,
				BidInfo:      bidInfo,
			},
		}
		unitInfo.Bid = bidInfo.Bid
		unitInfo.RoiRatio = bidInfo.RoiRatio
		// 创意素材
		creativeList := make([]*model.CreativeInfo, 0)
		creativeList = append(creativeList, &model.CreativeInfo{
			CreativeConfig:    req.KsAdvertiserStrategyCreativeConfig,
			CreativeMaterials: unitBaseInfo.CreativeMaterials,
			Title:             unitBaseInfo.Title,
			ProductId:         productInfo.ProductID,
			ProductName:       productInfo.Name,
		})
		unitInfo.CreativeList = creativeList
		unitList = append(unitList, unitInfo)
	}
	return
}

// BuildCampaignAndUnitName 生成广告计划名称和广告组名称
func BuildCampaignAndUnitName(advertiserList []*model.AdvertiserInfo) {
	for _, advertiserInfo := range advertiserList {
		var advertiserId = advertiserInfo.AdvertiserId
		var advertiserName = advertiserInfo.AdvertiserName
		var advertiserRemark = advertiserInfo.AdvertiserRemark
		campaignDailyNum := GetCampaignDailyNum(advertiserId)
		for _, campaignInfo := range advertiserInfo.CampaignList {
			var unitDailyNum = GetUnitDailyNum(advertiserId)
			for _, unitInfo := range campaignInfo.UnitList {
				var originUnitName = unitInfo.UnitName
				var now = time.Now()
				unitDailyNum++
				wildcardsData := map[string]string{
					AdvertiserName:   advertiserName,
					AdvertiserRemark: advertiserRemark,
					Date:             now.Format(consts.DateOnly),
					Time:             now.Format(consts.TimeOnly),
					DailyNum:         gconv.String(unitDailyNum),
				}
				promotionName := ReplaceWildcards(originUnitName, wildcardsData)
				unitInfo.UnitName = promotionName
			}
			var originCampaignName = campaignInfo.CampaignName
			var now = time.Now()
			campaignDailyNum++
			wildcardsData := map[string]string{
				AdvertiserName:   advertiserName,
				AdvertiserRemark: advertiserRemark,
				Date:             now.Format(consts.DateOnly),
				Time:             now.Format(consts.TimeOnly),
				DailyNum:         gconv.String(campaignDailyNum),
			}
			var campaignName = ReplaceWildcards(originCampaignName, wildcardsData)
			campaignInfo.CampaignName = campaignName
		}
	}
	return
}

// FilterUnitDataByAdvertiserId 获取目标广告主的广告组配置
func FilterUnitDataByAdvertiserId(unitConfig *model.KsAdvertiserStrategyUnitAddReq, advertiserId int64) (
	shortPlayList []*model.ShortPlayInfo,
	productList []*ksApi.ProductInfo,
	bidInfo model.BidInfo) {
	for _, shortPlay := range unitConfig.ShortPlayData {
		if shortPlay.AdvertiserId == advertiserId {
			shortPlayList = shortPlay.ShortPlayInfoList
			break
		}
	}
	for _, product := range unitConfig.ProductData {
		if product.AdvertiserId == advertiserId {
			productList = product.ProductInfoList
			break
		}
	}
	for _, bid := range unitConfig.BidData {
		if bid.AdvertiserId == advertiserId {
			bidInfo = bid.BidInfo
			break
		}
	}
	return
}
