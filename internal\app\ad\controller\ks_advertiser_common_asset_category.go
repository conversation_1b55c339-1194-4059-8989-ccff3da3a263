// ==========================================================================
// GFast自动生成controller操作代码。
// 生成日期：2025-08-19 10:36:49
// 生成路径: internal/app/ad/controller/ks_advertiser_common_asset_category.go
// 生成人：cq
// desc:快手通用资产-标题分类
// company:云南奇讯科技有限公司
// ==========================================================================

package controller

import (
	"context"

	"github.com/tiger1103/gfast/v3/api/v1/ad"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	systemController "github.com/tiger1103/gfast/v3/internal/app/system/controller"
)

type ksAdvertiserCommonAssetCategoryController struct {
	systemController.BaseController
}

var KsAdvertiserCommonAssetCategory = new(ksAdvertiserCommonAssetCategoryController)

// List 列表
func (c *ksAdvertiserCommonAssetCategoryController) List(ctx context.Context, req *ad.KsAdvertiserCommonAssetCategorySearchReq) (res *ad.KsAdvertiserCommonAssetCategorySearchRes, err error) {
	res = new(ad.KsAdvertiserCommonAssetCategorySearchRes)
	res.KsAdvertiserCommonAssetCategorySearchRes, err = service.KsAdvertiserCommonAssetCategory().List(ctx, &req.KsAdvertiserCommonAssetCategorySearchReq)
	return
}

// Get 获取快手通用资产-标题分类
func (c *ksAdvertiserCommonAssetCategoryController) Get(ctx context.Context, req *ad.KsAdvertiserCommonAssetCategoryGetReq) (res *ad.KsAdvertiserCommonAssetCategoryGetRes, err error) {
	res = new(ad.KsAdvertiserCommonAssetCategoryGetRes)
	res.KsAdvertiserCommonAssetCategoryInfoRes, err = service.KsAdvertiserCommonAssetCategory().GetById(ctx, req.Id)
	return
}

// Add 添加快手通用资产-标题分类
func (c *ksAdvertiserCommonAssetCategoryController) Add(ctx context.Context, req *ad.KsAdvertiserCommonAssetCategoryAddReq) (res *ad.KsAdvertiserCommonAssetCategoryAddRes, err error) {
	_, err = service.KsAdvertiserCommonAssetCategory().Add(ctx, req.KsAdvertiserCommonAssetCategoryAddReq)
	return
}

// Edit 修改快手通用资产-标题分类
func (c *ksAdvertiserCommonAssetCategoryController) Edit(ctx context.Context, req *ad.KsAdvertiserCommonAssetCategoryEditReq) (res *ad.KsAdvertiserCommonAssetCategoryEditRes, err error) {
	err = service.KsAdvertiserCommonAssetCategory().Edit(ctx, req.KsAdvertiserCommonAssetCategoryEditReq)
	return
}

// Delete 删除快手通用资产-标题分类
func (c *ksAdvertiserCommonAssetCategoryController) Delete(ctx context.Context, req *ad.KsAdvertiserCommonAssetCategoryDeleteReq) (res *ad.KsAdvertiserCommonAssetCategoryDeleteRes, err error) {
	err = service.KsAdvertiserCommonAssetCategory().Delete(ctx, req.Ids)
	return
}
