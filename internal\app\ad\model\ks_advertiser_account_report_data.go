// ==========================================================================
// GFast自动生成model操作代码。
// 生成日期：2025-08-12 10:44:32
// 生成路径: internal/app/ad/model/ks_advertiser_account_report_data.go
// 生成人：cq
// desc:快手账户报表数据
// company:云南奇讯科技有限公司
// ==========================================================================

package model

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
	comModel "github.com/tiger1103/gfast/v3/internal/app/common/model"
)

// KsAdvertiserAccountReportDataInfoRes is the golang structure for table ks_advertiser_account_report_data.
type KsAdvertiserAccountReportDataInfoRes struct {
	gmeta.Meta                                       `orm:"table:ks_advertiser_account_report_data"`
	AdvertiserId                                     int64       `orm:"advertiser_id,primary" json:"advertiserId" dc:"广告主ID"`                                                                               // 广告主ID
	StatDate                                         string      `orm:"stat_date,primary" json:"statDate" dc:"数据日期，格式：YYYY-MM-DD"`                                                                          // 数据日期，格式：YYYY-MM-DD
	PrivateMessageSentCost                           float64     `orm:"private_message_sent_cost" json:"privateMessageSentCost" dc:"私信消息转化成本"`                                                              // 私信消息转化成本
	PrivateMessageSentRatio                          float64     `orm:"private_message_sent_ratio" json:"privateMessageSentRatio" dc:"私信消息转化率"`                                                             // 私信消息转化率
	PrivateMessageSentCnt                            int64       `orm:"private_message_sent_cnt" json:"privateMessageSentCnt" dc:"私信消息数"`                                                                   // 私信消息数
	LeadsSubmitCost                                  float64     `orm:"leads_submit_cost" json:"leadsSubmitCost" dc:"直接私信留资成本"`                                                                             // 直接私信留资成本
	LeadsSubmitCntRatio                              float64     `orm:"leads_submit_cnt_ratio" json:"leadsSubmitCntRatio" dc:"直接私信留资率"`                                                                     // 直接私信留资率
	LeadsSubmitCnt                                   int64       `orm:"leads_submit_cnt" json:"leadsSubmitCnt" dc:"直接私信留资数"`                                                                                // 直接私信留资数
	PlayedNum                                        int64       `orm:"played_num" json:"playedNum" dc:"播放数"`                                                                                               // 播放数
	PlayedEnd                                        int64       `orm:"played_end" json:"playedEnd" dc:"播放完成"`                                                                                              // 播放完成
	PlayedFiveSeconds                                int64       `orm:"played_five_seconds" json:"playedFiveSeconds" dc:"播放5s"`                                                                             // 播放5s
	PlayedThreeSeconds                               int64       `orm:"played_three_seconds" json:"playedThreeSeconds" dc:"有效播放数"`                                                                          // 有效播放数
	Play3SRatio                                      float64     `orm:"play_3s_ratio" json:"play3SRatio" dc:"3s 播放率"`                                                                                       // 3s 播放率
	Play5SRatio                                      float64     `orm:"play_5s_ratio" json:"play5SRatio" dc:"5s播放率"`                                                                                        // 5s播放率
	PlayEndRatio                                     float64     `orm:"play_end_ratio" json:"playEndRatio" dc:"完播率"`                                                                                        // 完播率
	AdPhotoPlayed10S                                 int64       `orm:"ad_photo_played_10s" json:"adPhotoPlayed10S" dc:"10s播放数"`                                                                            // 10s播放数
	AdPhotoPlayed2S                                  int64       `orm:"ad_photo_played_2s" json:"adPhotoPlayed2S" dc:"2s播放数"`                                                                               // 2s播放数
	AdPhotoPlayed75Percent                           int64       `orm:"ad_photo_played_75percent" json:"adPhotoPlayed75Percent" dc:"75%进度播放数"`                                                              // 75%进度播放数
	AdPhotoPlayed75PercentRatio                      float64     `orm:"ad_photo_played_75_percent_ratio" json:"adPhotoPlayed75PercentRatio" dc:"75%进度播放率"`                                                  // 75%进度播放率
	AdPhotoPlayed10SRatio                            float64     `orm:"ad_photo_played_10s_ratio" json:"adPhotoPlayed10SRatio" dc:"10s播放率"`                                                                 // 10s播放率
	AdPhotoPlayed2SRatio                             float64     `orm:"ad_photo_played_2s_ratio" json:"adPhotoPlayed2SRatio" dc:"2s播放率"`                                                                    // 2s播放率
	MinigameIaaPurchaseAmountWeekByConversionRoi     float64     `orm:"minigame_iaa_purchase_amount_week_by_conversion_roi" json:"minigameIaaPurchaseAmountWeekByConversionRoi" dc:"激活后七日广告变现ROI"`          // 激活后七日广告变现ROI
	MinigameIaaPurchaseAmountThreeDayByConversionRoi float64     `orm:"minigame_iaa_purchase_amount_three_day_by_conversion_roi" json:"minigameIaaPurchaseAmountThreeDayByConversionRoi" dc:"激活后三日广告变现ROI"` // 激活后三日广告变现ROI
	MinigameIaaPurchaseAmountFirstDayRoi             float64     `orm:"minigame_iaa_purchase_amount_first_day_roi" json:"minigameIaaPurchaseAmountFirstDayRoi" dc:"当日广告变现ROI"`                              // 当日广告变现ROI
	MinigameIaaPurchaseAmountWeekByConversion        float64     `orm:"minigame_iaa_purchase_amount_week_by_conversion" json:"minigameIaaPurchaseAmountWeekByConversion" dc:"激活后七日广告LTV"`                   // 激活后七日广告LTV
	MinigameIaaPurchaseAmountThreeDayByConversion    float64     `orm:"minigame_iaa_purchase_amount_three_day_by_conversion" json:"minigameIaaPurchaseAmountThreeDayByConversion" dc:"激活后三日广告LTV"`          // 激活后三日广告LTV
	MinigameIaaPurchaseAmountFirstDay                float64     `orm:"minigame_iaa_purchase_amount_first_day" json:"minigameIaaPurchaseAmountFirstDay" dc:"当日广告LTV"`                                       // 当日广告LTV
	MinigameIaaPurchaseRoi                           float64     `orm:"minigame_iaa_purchase_roi" json:"minigameIaaPurchaseRoi" dc:"IAA广告变现ROI"`                                                            // IAA广告变现ROI
	MinigameIaaPurchaseAmount                        float64     `orm:"minigame_iaa_purchase_amount" json:"minigameIaaPurchaseAmount" dc:"IAA广告变现LTV"`                                                      // IAA广告变现LTV
	MinigameIaaPurchaseAmount30DayByConversionRoi    float64     `orm:"minigame_iaa_purchase_amount_30_day_by_conversion_roi" json:"minigameIaaPurchaseAmount30DayByConversionRoi" dc:"激活后30日广告变现ROI"`      // 激活后30日广告变现ROI
	MinigameIaaPurchaseAmount15DayByConversionRoi    float64     `orm:"minigame_iaa_purchase_amount_15_day_by_conversion_roi" json:"minigameIaaPurchaseAmount15DayByConversionRoi" dc:"激活后15日广告变现ROI"`      // 激活后15日广告变现ROI
	MinigameIaaPurchaseAmount30DayByConversion       float64     `orm:"minigame_iaa_purchase_amount_30_day_by_conversion" json:"minigameIaaPurchaseAmount30DayByConversion" dc:"激活后30日广告LTV"`               // 激活后30日广告LTV
	MinigameIaaPurchaseAmount15DayByConversion       float64     `orm:"minigame_iaa_purchase_amount_15_day_by_conversion" json:"minigameIaaPurchaseAmount15DayByConversion" dc:"激活后15日广告LTV"`               // 激活后15日广告LTV
	MmuEffectiveCustomerAcquisition7DCnt             int64       `orm:"mmu_effective_customer_acquisition_7d_cnt" json:"mmuEffectiveCustomerAcquisition7DCnt" dc:"MMU识别产生的有效获客数（计费）"`                       // MMU识别产生的有效获客数（计费）
	MmuEffectiveCustomerAcquisitionCnt               int64       `orm:"mmu_effective_customer_acquisition_cnt" json:"mmuEffectiveCustomerAcquisitionCnt" dc:"MMU识别产生的有效获客数（回传）"`                            // MMU识别产生的有效获客数（回传）
	EffectiveCustomerAcquisition7DRatio              float64     `orm:"effective_customer_acquisition_7d_ratio" json:"effectiveCustomerAcquisition7DRatio" dc:"有效获客率（计费）"`                                  // 有效获客率（计费）
	EffectiveCustomerAcquisition7DCost               float64     `orm:"effective_customer_acquisition_7d_cost" json:"effectiveCustomerAcquisition7DCost" dc:"有效获客成本（计费）"`                                   // 有效获客成本（计费）
	EffectiveCustomerAcquisition7DCnt                int64       `orm:"effective_customer_acquisition_7d_cnt" json:"effectiveCustomerAcquisition7DCnt" dc:"有效获客数（计费）"`                                      // 有效获客数（计费）
	EventPay30DayOverallRoi                          float64     `orm:"event_pay_30_day_overall_roi" json:"eventPay30DayOverallRoi" dc:"激活后30日整体ROI"`                                                       // 激活后30日整体ROI
	EventPay15DayOverallRoi                          float64     `orm:"event_pay_15_day_overall_roi" json:"eventPay15DayOverallRoi" dc:"激活后15日整体ROI"`                                                       // 激活后15日整体ROI
	EventPayPurchaseAmount15DayByConversion          float64     `orm:"event_pay_purchase_amount_15_day_by_conversion" json:"eventPayPurchaseAmount15DayByConversion" dc:"激活后15日付费金额"`                      // 激活后15日付费金额
	EventPayPurchaseAmount30DayByConversion          float64     `orm:"event_pay_purchase_amount_30_day_by_conversion" json:"eventPayPurchaseAmount30DayByConversion" dc:"激活后30日付费金额"`                      // 激活后30日付费金额
	EventPayFirstDay                                 int64       `orm:"event_pay_first_day" json:"eventPayFirstDay" dc:"应用下载数据-首日付费次数"`                                                                     // 应用下载数据-首日付费次数
	EventPayPurchaseAmountFirstDay                   float64     `orm:"event_pay_purchase_amount_first_day" json:"eventPayPurchaseAmountFirstDay" dc:"应用下载数据-首日付费金额"`                                       // 应用下载数据-首日付费金额
	EventPayFirstDayRoi                              float64     `orm:"event_pay_first_day_roi" json:"eventPayFirstDayRoi" dc:"应用下载数据-首日 ROI"`                                                              // 应用下载数据-首日 ROI
	EventPay                                         int64       `orm:"event_pay" json:"eventPay" dc:"应用下载数据-付费次数"`                                                                                         // 应用下载数据-付费次数
	EventPayPurchaseAmount                           float64     `orm:"event_pay_purchase_amount" json:"eventPayPurchaseAmount" dc:"应用下载数据-付费金额"`                                                           // 应用下载数据-付费金额
	EventPayRoi                                      float64     `orm:"event_pay_roi" json:"eventPayRoi" dc:"应用下载数据-ROI"`                                                                                   // 应用下载数据-ROI
	EventPayPurchaseAmountOneDay                     float64     `orm:"event_pay_purchase_amount_one_day" json:"eventPayPurchaseAmountOneDay" dc:"激活后24h付费金额(回传时间)"`                                        // 激活后24h付费金额(回传时间)
	EventPayPurchaseAmountOneDayByConversion         float64     `orm:"event_pay_purchase_amount_one_day_by_conversion" json:"eventPayPurchaseAmountOneDayByConversion" dc:"激活后24h付费金额(激活时间)"`              // 激活后24h付费金额(激活时间)
	EventPayPurchaseAmountOneDayByConversionRoi      float64     `orm:"event_pay_purchase_amount_one_day_by_conversion_roi" json:"eventPayPurchaseAmountOneDayByConversionRoi" dc:"激活后24小时付费ROI"`           // 激活后24小时付费ROI
	EventPayPurchaseAmountOneDayRoi                  float64     `orm:"event_pay_purchase_amount_one_day_roi" json:"eventPayPurchaseAmountOneDayRoi" dc:"激活后24h-ROI(回传时间)"`                                 // 激活后24h-ROI(回传时间)
	EventPayWeightedPurchaseAmount                   float64     `orm:"event_pay_weighted_purchase_amount" json:"eventPayWeightedPurchaseAmount" dc:"加权付费金额"`                                               // 加权付费金额
	EventPayWeightedPurchaseAmountFirstDay           float64     `orm:"event_pay_weighted_purchase_amount_first_day" json:"eventPayWeightedPurchaseAmountFirstDay" dc:"首日加权付费金额"`                           // 首日加权付费金额
	Charge                                           float64     `orm:"charge" json:"charge" dc:"花费（元）"`                                                                                                    // 花费（元）
	Show                                             int64       `orm:"show" json:"show" dc:"封面曝光数"`                                                                                                        // 封面曝光数
	Aclick                                           int64       `orm:"aclick" json:"aclick" dc:"素材曝光数"`                                                                                                    // 素材曝光数
	Bclick                                           int64       `orm:"bclick" json:"bclick" dc:"行为数"`                                                                                                      // 行为数
	AdShow                                           float64     `orm:"ad_show" json:"adShow" dc:"广告曝光"`                                                                                                    // 广告曝光
	Share                                            int64       `orm:"share" json:"share" dc:"分享数"`                                                                                                        // 分享数
	Comment                                          int64       `orm:"comment" json:"comment" dc:"评论数"`                                                                                                    // 评论数
	Like                                             int64       `orm:"like" json:"like" dc:"点赞数"`                                                                                                          // 点赞数
	Follow                                           int64       `orm:"follow" json:"follow" dc:"新增粉丝数"`                                                                                                    // 新增粉丝数
	CancelLike                                       int64       `orm:"cancel_like" json:"cancelLike" dc:"取消点赞数"`                                                                                           // 取消点赞数
	CancelFollow                                     int64       `orm:"cancel_follow" json:"cancelFollow" dc:"取消关注数"`                                                                                       // 取消关注数
	Report                                           int64       `orm:"report" json:"report" dc:"举报数"`                                                                                                      // 举报数
	Block                                            int64       `orm:"block" json:"block" dc:"拉黑数"`                                                                                                        // 拉黑数
	Negative                                         int64       `orm:"negative" json:"negative" dc:"减少此类作品数"`                                                                                              // 减少此类作品数
	Activation                                       int64       `orm:"activation" json:"activation" dc:"应用下载数据-激活数"`                                                                                       // 应用下载数据-激活数
	DownloadStarted                                  int64       `orm:"download_started" json:"downloadStarted" dc:"应用下载数据-安卓下载开始数"`                                                                        // 应用下载数据-安卓下载开始数
	DownloadCompleted                                int64       `orm:"download_completed" json:"downloadCompleted" dc:"应用下载数据-安卓下载完成数"`                                                                    // 应用下载数据-安卓下载完成数
	DownloadInstalled                                int64       `orm:"download_installed" json:"downloadInstalled" dc:"安卓安装完成数"`                                                                           // 安卓安装完成数
	ClickConversionRatio                             float64     `orm:"click_conversion_ratio" json:"clickConversionRatio" dc:"点击激活成本"`                                                                     // 点击激活成本
	ConversionCost                                   float64     `orm:"conversion_cost" json:"conversionCost" dc:"激活单价"`                                                                                    // 激活单价
	DownloadCompletedCost                            float64     `orm:"download_completed_cost" json:"downloadCompletedCost" dc:"安卓下载完成单价（元）"`                                                              // 安卓下载完成单价（元）
	DownloadCompletedRatio                           float64     `orm:"download_completed_ratio" json:"downloadCompletedRatio" dc:"安卓下载完成率"`                                                                // 安卓下载完成率
	DownloadConversionRatio                          float64     `orm:"download_conversion_ratio" json:"downloadConversionRatio" dc:"下载完成激活率"`                                                              // 下载完成激活率
	DownloadStartedCost                              float64     `orm:"download_started_cost" json:"downloadStartedCost" dc:"安卓下载开始单价（元）"`                                                                  // 安卓下载开始单价（元）
	DownloadStartedRatio                             float64     `orm:"download_started_ratio" json:"downloadStartedRatio" dc:"安卓下载开始率"`                                                                    // 安卓下载开始率
	EventRegister                                    int64       `orm:"event_register" json:"eventRegister" dc:"应用下载数据-注册数"`                                                                                // 应用下载数据-注册数
	EventRegisterCost                                float64     `orm:"event_register_cost" json:"eventRegisterCost" dc:"应用下载数据-注册成本"`                                                                      // 应用下载数据-注册成本
	EventRegisterRatio                               float64     `orm:"event_register_ratio" json:"eventRegisterRatio" dc:"应用下载数据-注册率"`                                                                     // 应用下载数据-注册率
	EventJinJianApp                                  int64       `orm:"event_jin_jian_app" json:"eventJinJianApp" dc:"应用下载数据-完件数"`                                                                          // 应用下载数据-完件数
	EventJinJianAppCost                              float64     `orm:"event_jin_jian_app_cost" json:"eventJinJianAppCost" dc:"应用下载数据-完件成本"`                                                                // 应用下载数据-完件成本
	EventJinJianLandingPage                          int64       `orm:"event_jin_jian_landing_page" json:"eventJinJianLandingPage" dc:"落地页数据-落地页完件数"`                                                       // 落地页数据-落地页完件数
	EventJinJianLandingPageCost                      float64     `orm:"event_jin_jian_landing_page_cost" json:"eventJinJianLandingPageCost" dc:"落地页数据-落地页完件成本"`                                             // 落地页数据-落地页完件成本
	Jinjian0DCnt                                     int64       `orm:"jinjian_0d_cnt" json:"jinjian0DCnt" dc:"T0完件数"`                                                                                      // T0完件数
	Jinjian3DCnt                                     int64       `orm:"jinjian_3d_cnt" json:"jinjian3DCnt" dc:"T3完件数"`                                                                                      // T3完件数
	Jinjian0DCntCost                                 float64     `orm:"jinjian_0d_cnt_cost" json:"jinjian0DCntCost" dc:"T0完件成本"`                                                                            // T0完件成本
	Jinjian3DCntCost                                 float64     `orm:"jinjian_3d_cnt_cost" json:"jinjian3DCntCost" dc:"T3完件成本"`                                                                            // T3完件成本
	EventCreditGrantApp                              int64       `orm:"event_credit_grant_app" json:"eventCreditGrantApp" dc:"应用下载数据-授信数"`                                                                  // 应用下载数据-授信数
	EventCreditGrantAppCost                          float64     `orm:"event_credit_grant_app_cost" json:"eventCreditGrantAppCost" dc:"应用下载数据-授信成本"`                                                        // 应用下载数据-授信成本
	EventCreditGrantAppRatio                         float64     `orm:"event_credit_grant_app_ratio" json:"eventCreditGrantAppRatio" dc:"应用下载数据-授信率"`                                                       // 应用下载数据-授信率
	EventCreditGrantLandingPage                      int64       `orm:"event_credit_grant_landing_page" json:"eventCreditGrantLandingPage" dc:"落地页授信数"`                                                     // 落地页授信数
	EventCreditGrantLandingPageCost                  float64     `orm:"event_credit_grant_landing_page_cost" json:"eventCreditGrantLandingPageCost" dc:"落地页数据-落地页授信成本"`                                     // 落地页数据-落地页授信成本
	EventCreditGrantLandingRatio                     float64     `orm:"event_credit_grant_landing_ratio" json:"eventCreditGrantLandingRatio" dc:"落地页数据-落地页授信率"`                                             // 落地页数据-落地页授信率
	EventCreditGrantFirstDayApp                      int64       `orm:"event_credit_grant_first_day_app" json:"eventCreditGrantFirstDayApp" dc:"app首日授信数"`                                                  // app首日授信数
	EventCreditGrantFirstDayAppCost                  float64     `orm:"event_credit_grant_first_day_app_cost" json:"eventCreditGrantFirstDayAppCost" dc:"首日授信成本"`                                           // 首日授信成本
	EventCreditGrantFirstDayAppRatio                 float64     `orm:"event_credit_grant_first_day_app_ratio" json:"eventCreditGrantFirstDayAppRatio" dc:"首日授信率"`                                          // 首日授信率
	EventCreditGrantFirstDayLandingPage              int64       `orm:"event_credit_grant_first_day_landing_page" json:"eventCreditGrantFirstDayLandingPage" dc:"落地页首日授信数"`                                 // 落地页首日授信数
	EventCreditGrantFirstDayLandingPageCost          float64     `orm:"event_credit_grant_first_day_landing_page_cost" json:"eventCreditGrantFirstDayLandingPageCost" dc:"落地页首日授信成本"`                       // 落地页首日授信成本
	EventCreditGrantFirstDayLandingPageRatio         float64     `orm:"event_credit_grant_first_day_landing_page_ratio" json:"eventCreditGrantFirstDayLandingPageRatio" dc:"落地页首日授信率"`                      // 落地页首日授信率
	CreditGrant0DCnt                                 int64       `orm:"credit_grant_0d_cnt" json:"creditGrant0DCnt" dc:"T0授信数"`                                                                             // T0授信数
	CreditGrant3DCnt                                 int64       `orm:"credit_grant_3d_cnt" json:"creditGrant3DCnt" dc:"T3授信数"`                                                                             // T3授信数
	CreditGrant0DCntCost                             float64     `orm:"credit_grant_0d_cnt_cost" json:"creditGrant0DCntCost" dc:"T0授信成本"`                                                                   // T0授信成本
	CreditGrant3DCntCost                             float64     `orm:"credit_grant_3d_cnt_cost" json:"creditGrant3DCntCost" dc:"T3授信成本"`                                                                   // T3授信成本
	CreditGrant0DCntRatio                            float64     `orm:"credit_grant_0d_cnt_ratio" json:"creditGrant0DCntRatio" dc:"T0完件授信率"`                                                                // T0完件授信率
	CreditGrant3DCntRatio                            float64     `orm:"credit_grant_3d_cnt_ratio" json:"creditGrant3DCntRatio" dc:"T3完件授信通过率"`                                                              // T3完件授信通过率
	EventOrderSubmit                                 int64       `orm:"event_order_submit" json:"eventOrderSubmit" dc:"提交订单数"`                                                                              // 提交订单数
	EventOrderPaid                                   int64       `orm:"event_order_paid" json:"eventOrderPaid" dc:"应用下载数据-付款成功数"`                                                                           // 应用下载数据-付款成功数
	EventOrderPaidPurchaseAmount                     float64     `orm:"event_order_paid_purchase_amount" json:"eventOrderPaidPurchaseAmount" dc:"应用下载数据-付款成功金额"`                                            // 应用下载数据-付款成功金额
	EventOrderPaidCost                               float64     `orm:"event_order_paid_cost" json:"eventOrderPaidCost" dc:"应用下载数据-单次付款成本"`                                                                 // 应用下载数据-单次付款成本
	EventOrderPaidRoi                                float64     `orm:"event_order_paid_roi" json:"eventOrderPaidRoi" dc:"订单支付率"`                                                                           // 订单支付率
	OrderSubmitAmt                                   float64     `orm:"order_submit_amt" json:"orderSubmitAmt" dc:"订单提交金额"`                                                                                 // 订单提交金额
	FormCount                                        int64       `orm:"form_count" json:"formCount" dc:"落地页数据-线索提交个数"`                                                                                      // 落地页数据-线索提交个数
	FormCost                                         float64     `orm:"form_cost" json:"formCost" dc:"落地页数据-单个线索成本"`                                                                                        // 落地页数据-单个线索成本
	FormActionRatio                                  float64     `orm:"form_action_ratio" json:"formActionRatio" dc:"落地页数据-表单提交点击率"`                                                                        // 落地页数据-表单提交点击率
	Submit                                           int64       `orm:"submit" json:"submit" dc:"提交按钮点击数（历史字段）"`                                                                                            // 提交按钮点击数（历史字段）
	EventValidClues                                  int64       `orm:"event_valid_clues" json:"eventValidClues" dc:"落地页数据-有效线索数"`                                                                          // 落地页数据-有效线索数
	EventValidCluesCost                              float64     `orm:"event_valid_clues_cost" json:"eventValidCluesCost" dc:"落地页数据-有效线索成本"`                                                                // 落地页数据-有效线索成本
	EventConsultationValidRetained                   int64       `orm:"event_consultation_valid_retained" json:"eventConsultationValidRetained" dc:"留咨咨询数"`                                                 // 留咨咨询数
	EventConsultationValidRetainedCost               float64     `orm:"event_consultation_valid_retained_cost" json:"eventConsultationValidRetainedCost" dc:"留咨咨询成本"`                                       // 留咨咨询成本
	EventConsultationValidRetainedRatio              float64     `orm:"event_consultation_valid_retained_ratio" json:"eventConsultationValidRetainedRatio" dc:"留咨咨询率"`                                      // 留咨咨询率
	EventConversionClickCost                         float64     `orm:"event_conversion_click_cost" json:"eventConversionClickCost" dc:"有效咨询成本"`                                                            // 有效咨询成本
	EventConversionClickRatio                        float64     `orm:"event_conversion_click_ratio" json:"eventConversionClickRatio" dc:"有效咨询率"`                                                           // 有效咨询率
	EventPreComponentConsultationValidRetained       int64       `orm:"event_pre_component_consultation_valid_retained" json:"eventPreComponentConsultationValidRetained" dc:"附加咨询组件留资咨询数"`                 // 附加咨询组件留资咨询数
	EventAdWatch10Times                              int64       `orm:"event_ad_watch_10_times" json:"eventAdWatch10Times" dc:"10次广告广告观看数"`                                                                 // 10次广告广告观看数
	EventAdWatch10TimesCost                          float64     `orm:"event_ad_watch_10_times_cost" json:"eventAdWatch10TimesCost" dc:"10次广告观看成本"`                                                         // 10次广告观看成本
	EventAdWatch10TimesRatio                         float64     `orm:"event_ad_watch_10_times_ratio" json:"eventAdWatch10TimesRatio" dc:"10次广告观看转化率"`                                                      // 10次广告观看转化率
	EventAdWatch20Times                              int64       `orm:"event_ad_watch_20_times" json:"eventAdWatch20Times" dc:"20次广告广告观看数"`                                                                 // 20次广告广告观看数
	EventAdWatch20TimesCost                          float64     `orm:"event_ad_watch_20_times_cost" json:"eventAdWatch20TimesCost" dc:"20次广告观看成本"`                                                         // 20次广告观看成本
	EventAdWatch20TimesRatio                         float64     `orm:"event_ad_watch_20_times_ratio" json:"eventAdWatch20TimesRatio" dc:"20次广告观看转化率"`                                                      // 20次广告观看转化率
	EventAdWatch5Times                               int64       `orm:"event_ad_watch_5_times" json:"eventAdWatch5Times" dc:"5次广告广告观看数"`                                                                    // 5次广告广告观看数
	EventAdWatch5TimesCost                           float64     `orm:"event_ad_watch_5_times_cost" json:"eventAdWatch5TimesCost" dc:"5次广告观看成本"`                                                            // 5次广告观看成本
	EventAdWatch5TimesRatio                          float64     `orm:"event_ad_watch_5_times_ratio" json:"eventAdWatch5TimesRatio" dc:"5次广告观看转化率"`                                                         // 5次广告观看转化率
	EventWatchAppAd                                  int64       `orm:"event_watch_app_ad" json:"eventWatchAppAd" dc:"广告观看"`                                                                                // 广告观看
	EventAdWatchTimes                                int64       `orm:"event_ad_watch_times" json:"eventAdWatchTimes" dc:"广告观看次数"`                                                                          // 广告观看次数
	EventAdWatchTimesRatio                           float64     `orm:"event_ad_watch_times_ratio" json:"eventAdWatchTimesRatio" dc:"广告观看次数转化率"`                                                            // 广告观看次数转化率
	EventAdWatchTimesCost                            float64     `orm:"event_ad_watch_times_cost" json:"eventAdWatchTimesCost" dc:"广告观看次数成本"`                                                               // 广告观看次数成本
	EventMakingCalls                                 int64       `orm:"event_making_calls" json:"eventMakingCalls" dc:"电话拨打数"`                                                                              // 电话拨打数
	EventMakingCallsCost                             float64     `orm:"event_making_calls_cost" json:"eventMakingCallsCost" dc:"电话拨打成本"`                                                                    // 电话拨打成本
	EventMakingCallsRatio                            float64     `orm:"event_making_calls_ratio" json:"eventMakingCallsRatio" dc:"电话拨打率"`                                                                   // 电话拨打率
	EventGetThrough                                  int64       `orm:"event_get_through" json:"eventGetThrough" dc:"智能电话-确认接通数"`                                                                           // 智能电话-确认接通数
	EventGetThroughCost                              float64     `orm:"event_get_through_cost" json:"eventGetThroughCost" dc:"智能电话-确认接通成本"`                                                                 // 智能电话-确认接通成本
	EventGetThroughRatio                             float64     `orm:"event_get_through_ratio" json:"eventGetThroughRatio" dc:"智能电话-确认接通率"`                                                                // 智能电话-确认接通率
	EventPhoneGetThrough                             int64       `orm:"event_phone_get_through" json:"eventPhoneGetThrough" dc:"电话建联数"`                                                                     // 电话建联数
	EventOutboundCall                                int64       `orm:"event_outbound_call" json:"eventOutboundCall" dc:"电话拨打数"`                                                                            // 电话拨打数
	EventOutboundCallCost                            float64     `orm:"event_outbound_call_cost" json:"eventOutboundCallCost" dc:"电话拨打成本"`                                                                  // 电话拨打成本
	EventOutboundCallRatio                           float64     `orm:"event_outbound_call_ratio" json:"eventOutboundCallRatio" dc:"电话拨打率"`                                                                 // 电话拨打率
	EventWechatQrCodeLinkClick                       int64       `orm:"event_wechat_qr_code_link_click" json:"eventWechatQrCodeLinkClick" dc:"微信小程序深度加粉数"`                                                  // 微信小程序深度加粉数
	EventAddWechat                                   int64       `orm:"event_add_wechat" json:"eventAddWechat" dc:"微信复制数"`                                                                                  // 微信复制数
	EventAddWechatCost                               float64     `orm:"event_add_wechat_cost" json:"eventAddWechatCost" dc:"微信复制成本"`                                                                        // 微信复制成本
	EventAddWechatRatio                              float64     `orm:"event_add_wechat_ratio" json:"eventAddWechatRatio" dc:"微信复制率"`                                                                       // 微信复制率
	EventWechatConnected                             int64       `orm:"event_wechat_connected" json:"eventWechatConnected" dc:"微信加粉数"`                                                                      // 微信加粉数
	EventAudition                                    int64       `orm:"event_audition" json:"eventAudition" dc:"首次试听到课数"`                                                                                   // 首次试听到课数
	EventButtonClick                                 int64       `orm:"event_button_click" json:"eventButtonClick" dc:"按钮点击数"`                                                                              // 按钮点击数
	EventButtonClickCost                             float64     `orm:"event_button_click_cost" json:"eventButtonClickCost" dc:"按钮点击成本"`                                                                    // 按钮点击成本
	EventButtonClickRatio                            float64     `orm:"event_button_click_ratio" json:"eventButtonClickRatio" dc:"按钮点击率"`                                                                   // 按钮点击率
	EventMultiConversion                             int64       `orm:"event_multi_conversion" json:"eventMultiConversion" dc:"落地页多转化次数"`                                                                   // 落地页多转化次数
	EventMultiConversionRatio                        float64     `orm:"event_multi_conversion_ratio" json:"eventMultiConversionRatio" dc:"落地页多转化率"`                                                         // 落地页多转化率
	EventMultiConversionCost                         float64     `orm:"event_multi_conversion_cost" json:"eventMultiConversionCost" dc:"落地页多转化成本"`                                                          // 落地页多转化成本
	EventAddShoppingCart                             int64       `orm:"event_add_shopping_cart" json:"eventAddShoppingCart" dc:"添加购物车数"`                                                                    // 添加购物车数
	EventAddShoppingCartCost                         float64     `orm:"event_add_shopping_cart_cost" json:"eventAddShoppingCartCost" dc:"添加购物车成本"`                                                          // 添加购物车成本
	EventIntentionConfirmed                          int64       `orm:"event_intention_confirmed" json:"eventIntentionConfirmed" dc:"意向确认数"`                                                                // 意向确认数
	EventOrderSuccessed                              int64       `orm:"event_order_successed" json:"eventOrderSuccessed" dc:"有效线索成交数"`                                                                      // 有效线索成交数
	EventPhoneCardActivate                           int64       `orm:"event_phone_card_activate" json:"eventPhoneCardActivate" dc:"电话卡激活数"`                                                                // 电话卡激活数
	EventMeasurementHouse                            int64       `orm:"event_measurement_house" json:"eventMeasurementHouse" dc:"量房数"`                                                                      // 量房数
	EventAppInvoked                                  int64       `orm:"event_app_invoked" json:"eventAppInvoked" dc:"唤醒应用数"`                                                                                // 唤醒应用数
	EventAppInvokedCost                              float64     `orm:"event_app_invoked_cost" json:"eventAppInvokedCost" dc:"唤醒应用成本"`                                                                      // 唤醒应用成本
	EventAppInvokedRatio                             float64     `orm:"event_app_invoked_ratio" json:"eventAppInvokedRatio" dc:"唤醒应用率"`                                                                     // 唤醒应用率
	EventNextDayStayCost                             float64     `orm:"event_next_day_stay_cost" json:"eventNextDayStayCost" dc:"应用下载数据-次留成本（仅支持分日查询）"`                                                     // 应用下载数据-次留成本（仅支持分日查询）
	EventNextDayStayRatio                            float64     `orm:"event_next_day_stay_ratio" json:"eventNextDayStayRatio" dc:"应用下载数据-次留率（仅支持分日查询）"`                                                    // 应用下载数据-次留率（仅支持分日查询）
	EventNextDayStay                                 int64       `orm:"event_next_day_stay" json:"eventNextDayStay" dc:"应用下载数据-次留数（仅支持分日查询）"`                                                               // 应用下载数据-次留数（仅支持分日查询）
	PhotoClick                                       int64       `orm:"photo_click" json:"photoClick" dc:"封面点击数"`                                                                                           // 封面点击数
	PhotoClickRatio                                  float64     `orm:"photo_click_ratio" json:"photoClickRatio" dc:"封面点击率"`                                                                                // 封面点击率
	PhotoClickCost                                   float64     `orm:"photo_click_cost" json:"photoClickCost" dc:"平均点击单价（元）"`                                                                              // 平均点击单价（元）
	ActionRatio                                      float64     `orm:"action_ratio" json:"actionRatio" dc:"行为率"`                                                                                           // 行为率
	ActionNewRatio                                   float64     `orm:"action_new_ratio" json:"actionNewRatio" dc:"行为率 新"`                                                                                  // 行为率 新
	ActionCost                                       float64     `orm:"action_cost" json:"actionCost" dc:"平均行为单价（元）"`                                                                                       // 平均行为单价（元）
	Impression1KCost                                 float64     `orm:"impression_1k_cost" json:"impression1KCost" dc:"平均千次曝光花费（元）"`                                                                        // 平均千次曝光花费（元）
	Click1KCost                                      float64     `orm:"click_1k_cost" json:"click1KCost" dc:"平均千次素材曝光花费(元)"`                                                                                // 平均千次素材曝光花费(元)
	ApproxPayCost                                    float64     `orm:"approx_pay_cost" json:"approxPayCost" dc:"淘系近似购买成本"`                                                                                 // 淘系近似购买成本
	ApproxPayCount                                   int64       `orm:"approx_pay_count" json:"approxPayCount" dc:"近似购买数"`                                                                                  // 近似购买数
	ApproxPayRatio                                   float64     `orm:"approx_pay_ratio" json:"approxPayRatio" dc:"淘系近似购买率"`                                                                                // 淘系近似购买率
	LiveEventGoodsView                               int64       `orm:"live_event_goods_view" json:"liveEventGoodsView" dc:"直播间商品点击数"`                                                                      // 直播间商品点击数
	LivePlayed3S                                     int64       `orm:"live_played_3s" json:"livePlayed3S" dc:"直播观看数"`                                                                                      // 直播观看数
	AdProductCnt                                     int64       `orm:"ad_product_cnt" json:"adProductCnt" dc:"商品成交数"`                                                                                      // 商品成交数
	EventGoodsView                                   int64       `orm:"event_goods_view" json:"eventGoodsView" dc:"商品访问数"`                                                                                  // 商品访问数
	EventGoodsViewCost                               float64     `orm:"event_goods_view_cost" json:"eventGoodsViewCost" dc:"商品访问成本"`                                                                        // 商品访问成本
	MerchantRecoFans                                 int64       `orm:"merchant_reco_fans" json:"merchantRecoFans" dc:"涨粉量"`                                                                                // 涨粉量
	MerchantRecoFansCost                             float64     `orm:"merchant_reco_fans_cost" json:"merchantRecoFansCost" dc:"涨粉成本"`                                                                      // 涨粉成本
	EventOrderAmountRoi                              float64     `orm:"event_order_amount_roi" json:"eventOrderAmountRoi" dc:"小店推广roi"`                                                                     // 小店推广roi
	EventNewUserPay                                  int64       `orm:"event_new_user_pay" json:"eventNewUserPay" dc:"新增付费人数"`                                                                              // 新增付费人数
	EventNewUserPayCost                              float64     `orm:"event_new_user_pay_cost" json:"eventNewUserPayCost" dc:"新增付费人数成本"`                                                                   // 新增付费人数成本
	EventNewUserPayRatio                             float64     `orm:"event_new_user_pay_ratio" json:"eventNewUserPayRatio" dc:"新增付费人数率"`                                                                  // 新增付费人数率
	EventNewUserJinjianApp                           int64       `orm:"event_new_user_jinjian_app" json:"eventNewUserJinjianApp" dc:"新增完件人数"`                                                               // 新增完件人数
	EventNewUserJinjianAppCost                       float64     `orm:"event_new_user_jinjian_app_cost" json:"eventNewUserJinjianAppCost" dc:"新增完件人数成本"`                                                    // 新增完件人数成本
	EventNewUserJinjianAppRoi                        float64     `orm:"event_new_user_jinjian_app_roi" json:"eventNewUserJinjianAppRoi" dc:"新增完件人数率"`                                                       // 新增完件人数率
	EventNewUserCreditGrantApp                       int64       `orm:"event_new_user_credit_grant_app" json:"eventNewUserCreditGrantApp" dc:"新增授信人数"`                                                      // 新增授信人数
	EventNewUserCreditGrantAppCost                   float64     `orm:"event_new_user_credit_grant_app_cost" json:"eventNewUserCreditGrantAppCost" dc:"新增授信人数成本"`                                           // 新增授信人数成本
	EventNewUserCreditGrantAppRoi                    float64     `orm:"event_new_user_credit_grant_app_roi" json:"eventNewUserCreditGrantAppRoi" dc:"新增授信人数率"`                                              // 新增授信人数率
	EventNewUserJinjianPage                          int64       `orm:"event_new_user_jinjian_page" json:"eventNewUserJinjianPage" dc:"字段描述，需要修改"`                                                          // 字段描述，需要修改
	EventNewUserJinjianPageCost                      float64     `orm:"event_new_user_jinjian_page_cost" json:"eventNewUserJinjianPageCost" dc:"字段描述，需要修改"`                                                 // 字段描述，需要修改
	EventNewUserJinjianPageRoi                       float64     `orm:"event_new_user_jinjian_page_roi" json:"eventNewUserJinjianPageRoi" dc:"字段描述，需要修改"`                                                   // 字段描述，需要修改
	EventNewUserCreditGrantPage                      int64       `orm:"event_new_user_credit_grant_page" json:"eventNewUserCreditGrantPage" dc:"字段描述，需要修改"`                                                 // 字段描述，需要修改
	EventNewUserCreditGrantPageCost                  float64     `orm:"event_new_user_credit_grant_page_cost" json:"eventNewUserCreditGrantPageCost" dc:"字段描述，需要修改"`                                        // 字段描述，需要修改
	EventNewUserCreditGrantPageRoi                   float64     `orm:"event_new_user_credit_grant_page_roi" json:"eventNewUserCreditGrantPageRoi" dc:"字段描述，需要修改"`                                          // 字段描述，需要修改
	EventAppointForm                                 int64       `orm:"event_appoint_form" json:"eventAppointForm" dc:"预约表单数"`                                                                              // 预约表单数
	EventAppointFormCost                             float64     `orm:"event_appoint_form_cost" json:"eventAppointFormCost" dc:"预约表单点击成本"`                                                                  // 预约表单点击成本
	EventAppointFormRatio                            float64     `orm:"event_appoint_form_ratio" json:"eventAppointFormRatio" dc:"预约表单点击率"`                                                                 // 预约表单点击率
	EventAppointJumpClick                            int64       `orm:"event_appoint_jump_click" json:"eventAppointJumpClick" dc:"预约跳转点击数"`                                                                 // 预约跳转点击数
	EventAppointJumpClickCost                        float64     `orm:"event_appoint_jump_click_cost" json:"eventAppointJumpClickCost" dc:"预约跳转点击成本"`                                                       // 预约跳转点击成本
	EventAppointJumpClickRatio                       float64     `orm:"event_appoint_jump_click_ratio" json:"eventAppointJumpClickRatio" dc:"预约跳转点击率"`                                                      // 预约跳转点击率
	UnionEventPayPurchaseAmount7D                    float64     `orm:"union_event_pay_purchase_amount_7d" json:"unionEventPayPurchaseAmount7D" dc:"联盟广告收入"`                                                // 联盟广告收入
	UnionEventPayPurchaseAmount7DRoi                 float64     `orm:"union_event_pay_purchase_amount_7d_roi" json:"unionEventPayPurchaseAmount7DRoi" dc:"联盟变现ROI"`                                        // 联盟变现ROI
	EventDspGiftForm                                 int64       `orm:"event_dsp_gift_form" json:"eventDspGiftForm" dc:"附加组件表单提交"`                                                                          // 附加组件表单提交
	EventCreditCardRecheck                           int64       `orm:"event_credit_card_recheck" json:"eventCreditCardRecheck" dc:"信用卡核卡数"`                                                                // 信用卡核卡数
	EventCreditCardRecheckFirstDay                   int64       `orm:"event_credit_card_recheck_first_day" json:"eventCreditCardRecheckFirstDay" dc:"信用卡首日核卡数"`                                            // 信用卡首日核卡数
	KeyAction                                        int64       `orm:"key_action" json:"keyAction" dc:"关键行为数"`                                                                                             // 关键行为数
	KeyActionCost                                    float64     `orm:"key_action_cost" json:"keyActionCost" dc:"关键行为成本"`                                                                                   // 关键行为成本
	KeyActionRatio                                   float64     `orm:"key_action_ratio" json:"keyActionRatio" dc:"关键行为率"`                                                                                  // 关键行为率
	KeyInappAction0DCnt                              int64       `orm:"key_inapp_action_0d_cnt" json:"keyInappAction0DCnt" dc:"T0全量授信数"`                                                                    // T0全量授信数
	KeyInappAction3DCnt                              int64       `orm:"key_inapp_action_3d_cnt" json:"keyInappAction3DCnt" dc:"T3全量授信数"`                                                                    // T3全量授信数
	KeyInappAction0DCntCost                          float64     `orm:"key_inapp_action_0d_cnt_cost" json:"keyInappAction0DCntCost" dc:"T0全量授信成本"`                                                          // T0全量授信成本
	KeyInappAction3DCntCost                          float64     `orm:"key_inapp_action_3d_cnt_cost" json:"keyInappAction3DCntCost" dc:"T3全量授信成本"`                                                          // T3全量授信成本
	KeyInappAction0DCntRatio                         float64     `orm:"key_inapp_action_0d_cnt_ratio" json:"keyInappAction0DCntRatio" dc:"T0全量授信通过率"`                                                       // T0全量授信通过率
	KeyInappAction3DCntRatio                         float64     `orm:"key_inapp_action_3d_cnt_ratio" json:"keyInappAction3DCntRatio" dc:"T3全量授信通过率"`                                                       // T3全量授信通过率
	DrawCreditLine0DCnt                              int64       `orm:"draw_credit_line_0d_cnt" json:"drawCreditLine0DCnt" dc:"T0用信数"`                                                                      // T0用信数
	DrawCreditLine0DCntCost                          float64     `orm:"draw_credit_line_0d_cnt_cost" json:"drawCreditLine0DCntCost" dc:"T0用信成本"`                                                            // T0用信成本
	DrawCreditLine0DCntRatio                         float64     `orm:"draw_credit_line_0d_cnt_ratio" json:"drawCreditLine0DCntRatio" dc:"T0授信用信率"`                                                         // T0授信用信率
	EventNoIntention                                 int64       `orm:"event_no_intention" json:"eventNoIntention" dc:"用户无意向数"`                                                                             // 用户无意向数
	AdScene                                          string      `orm:"ad_scene" json:"adScene" dc:"广告场景"`                                                                                                  // 广告场景
	AdScene2                                         string      `orm:"ad_scene_2" json:"adScene2" dc:"广告场景2"`                                                                                              // 广告场景2
	PlacementType                                    string      `orm:"placement_type" json:"placementType" dc:"投放类型"`                                                                                      // 投放类型
	CreatedAt                                        *gtime.Time `orm:"created_at" json:"createdAt" dc:"创建时间"`                                                                                              // 创建时间
	UpdatedAt                                        *gtime.Time `orm:"updated_at" json:"updatedAt" dc:"更新时间"`                                                                                              // 更新时间
}

type KsAdvertiserAccountReportDataListRes struct {
	AdvertiserId      int64   `json:"advertiserId" dc:"广告主ID"`
	AdvertiserName    string  `json:"advertiserName" dc:"广告主名称"`
	StatDate          string  `json:"statDate" dc:"数据日期，格式：YYYY-MM-DD"`
	Remark            string  `json:"remark" dc:"备注"`
	CorporationName   string  `json:"corporationName" dc:"公司主体"`
	UserId            int64   `json:"userId" dc:"优化师ID"`
	UserName          string  `json:"userName" dc:"优化师名称"`
	AccountAutoManage int     `json:"accountAutoManage" dc:"账户智投开关 1：开启 0：关闭"`
	DayBudget         float64 `json:"dayBudget" dc:"单日预算"`
	*KsAdvertiserAccountReportDataSummaryRes
}

type KsAdvertiserAccountReportDataSummaryRes struct {
	AccountBalance                                   float64 `json:"accountBalance" dc:"账户余额"`
	PrivateMessageSentCost                           float64 `json:"privateMessageSentCost" dc:"私信消息转化成本"`
	PrivateMessageSentRatio                          float64 `json:"privateMessageSentRatio" dc:"私信消息转化率"`
	PrivateMessageSentCnt                            int64   `json:"privateMessageSentCnt" dc:"私信消息数"`
	LeadsSubmitCost                                  float64 `json:"leadsSubmitCost" dc:"直接私信留资成本"`
	LeadsSubmitCntRatio                              float64 `json:"leadsSubmitCntRatio" dc:"直接私信留资率"`
	LeadsSubmitCnt                                   int64   `json:"leadsSubmitCnt" dc:"直接私信留资数"`
	PlayedNum                                        int64   `json:"playedNum" dc:"播放数"`
	PlayedEnd                                        int64   `json:"playedEnd" dc:"播放完成"`
	PlayedFiveSeconds                                int64   `json:"playedFiveSeconds" dc:"播放5s"`
	PlayedThreeSeconds                               int64   `json:"playedThreeSeconds" dc:"有效播放数"`
	Play3SRatio                                      float64 `json:"play3SRatio"  dc:"3s播放率 = 3s播放数/播放数*100%"`
	Play5SRatio                                      float64 `json:"play5SRatio"  dc:"5s播放率 = 5s播放数/播放数*100%"`
	PlayEndRatio                                     float64 `json:"playEndRatio"  dc:"完播率 = 播放完成/播放数*100%"`
	AdPhotoPlayed10S                                 int64   `json:"adPhotoPlayed10S" dc:"10s播放数"`
	AdPhotoPlayed2S                                  int64   `json:"adPhotoPlayed2S" dc:"2s播放数"`
	AdPhotoPlayed75Percent                           int64   `json:"adPhotoPlayed75Percent" dc:"75%进度播放数"`
	AdPhotoPlayed75PercentRatio                      float64 `json:"adPhotoPlayed75PercentRatio" dc:"75%进度播放率 = 75%进度播放数/播放数*100%"`
	AdPhotoPlayed10SRatio                            float64 `json:"adPhotoPlayed10SRatio" dc:"10s播放率 = 10s播放数/播放数*100%"`
	AdPhotoPlayed2SRatio                             float64 `json:"adPhotoPlayed2SRatio" dc:"2s播放率 = 2s播放数/播放数*100%"`
	MinigameIaaPurchaseAmountWeekByConversionRoi     float64 `json:"minigameIaaPurchaseAmountWeekByConversionRoi" dc:"激活后七日广告变现ROI"`
	MinigameIaaPurchaseAmountThreeDayByConversionRoi float64 `json:"minigameIaaPurchaseAmountThreeDayByConversionRoi" dc:"激活后三日广告变现ROI"`
	MinigameIaaPurchaseAmountFirstDayRoi             float64 `json:"minigameIaaPurchaseAmountFirstDayRoi" dc:"当日广告变现ROI"`
	MinigameIaaPurchaseAmountWeekByConversion        float64 `json:"minigameIaaPurchaseAmountWeekByConversion" dc:"激活后七日广告LTV"`
	MinigameIaaPurchaseAmountThreeDayByConversion    float64 `json:"minigameIaaPurchaseAmountThreeDayByConversion" dc:"激活后三日广告LTV"`
	MinigameIaaPurchaseAmountFirstDay                float64 `json:"minigameIaaPurchaseAmountFirstDay" dc:"当日广告LTV"`
	MinigameIaaPurchaseRoi                           float64 `json:"minigameIaaPurchaseRoi" dc:"IAA广告变现ROI"`
	MinigameIaaPurchaseAmount                        float64 `json:"minigameIaaPurchaseAmount" dc:"IAA广告变现LTV"`
	MinigameIaaPurchaseAmount30DayByConversionRoi    float64 `json:"minigameIaaPurchaseAmount30DayByConversionRoi" dc:"激活后30日广告变现ROI"`
	MinigameIaaPurchaseAmount15DayByConversionRoi    float64 `json:"minigameIaaPurchaseAmount15DayByConversionRoi" dc:"激活后15日广告变现ROI"`
	MinigameIaaPurchaseAmount30DayByConversion       float64 `json:"minigameIaaPurchaseAmount30DayByConversion" dc:"激活后30日广告LTV"`
	MinigameIaaPurchaseAmount15DayByConversion       float64 `json:"minigameIaaPurchaseAmount15DayByConversion" dc:"激活后15日广告LTV"`
	MmuEffectiveCustomerAcquisition7DCnt             int64   `json:"mmuEffectiveCustomerAcquisition7DCnt" dc:"MMU识别产生的有效获客数（计费）"`
	MmuEffectiveCustomerAcquisitionCnt               int64   `json:"mmuEffectiveCustomerAcquisitionCnt" dc:"MMU识别产生的有效获客数（回传）"`
	EffectiveCustomerAcquisition7DRatio              float64 `json:"effectiveCustomerAcquisition7DRatio" dc:"有效获客率（计费）"`
	EffectiveCustomerAcquisition7DCost               float64 `json:"effectiveCustomerAcquisition7DCost" dc:"有效获客成本（计费）"`
	EffectiveCustomerAcquisition7DCnt                int64   `json:"effectiveCustomerAcquisition7DCnt" dc:"有效获客数（计费）"`
	EventPay30DayOverallRoi                          float64 `json:"eventPay30DayOverallRoi" dc:"激活后30日整体ROI"`
	EventPay15DayOverallRoi                          float64 `json:"eventPay15DayOverallRoi" dc:"激活后15日整体ROI"`
	EventPayPurchaseAmount15DayByConversion          float64 `json:"eventPayPurchaseAmount15DayByConversion" dc:"激活后15日付费金额"`
	EventPayPurchaseAmount30DayByConversion          float64 `json:"eventPayPurchaseAmount30DayByConversion" dc:"激活后30日付费金额"`
	EventPayFirstDay                                 int64   `json:"eventPayFirstDay" dc:"应用下载数据-首日付费次数"`
	EventPayPurchaseAmountFirstDay                   float64 `json:"eventPayPurchaseAmountFirstDay" dc:"应用下载数据-首日付费金额"`
	EventPayFirstDayRoi                              float64 `json:"eventPayFirstDayRoi" dc:"应用下载数据-首日 ROI"`
	EventPay                                         int64   `json:"eventPay" dc:"应用下载数据-付费次数"`
	EventPayPurchaseAmount                           float64 `json:"eventPayPurchaseAmount" dc:"应用下载数据-付费金额"`
	EventPayRoi                                      float64 `json:"eventPayRoi" dc:"应用下载数据-ROI"`
	EventPayPurchaseAmountOneDay                     float64 `json:"eventPayPurchaseAmountOneDay" dc:"激活后24h付费金额(回传时间)"`
	EventPayPurchaseAmountOneDayByConversion         float64 `json:"eventPayPurchaseAmountOneDayByConversion" dc:"激活后24h付费金额(激活时间)"`
	EventPayPurchaseAmountOneDayByConversionRoi      float64 `json:"eventPayPurchaseAmountOneDayByConversionRoi" dc:"激活后24小时付费ROI"`
	EventPayPurchaseAmountOneDayRoi                  float64 `json:"eventPayPurchaseAmountOneDayRoi" dc:"激活后24h-ROI(回传时间)"`
	EventPayWeightedPurchaseAmount                   float64 `json:"eventPayWeightedPurchaseAmount" dc:"加权付费金额"`
	EventPayWeightedPurchaseAmountFirstDay           float64 `json:"eventPayWeightedPurchaseAmountFirstDay" dc:"首日加权付费金额"`
	Charge                                           float64 `json:"charge" dc:"花费（元）"`
	Show                                             int64   `json:"show" dc:"封面曝光数"`
	Aclick                                           int64   `json:"aclick" dc:"素材曝光数"`
	AclickRatio                                      float64 `json:"aclickRatio" dc:"素材点击率 = 行为数/素材曝光数*100%"`
	Bclick                                           int64   `json:"bclick" dc:"行为数"`
	AdShow                                           float64 `json:"adShow" dc:"广告曝光"`
	Share                                            int64   `json:"share" dc:"分享数"`
	Comment                                          int64   `json:"comment" dc:"评论数"`
	Like                                             int64   `json:"like" dc:"点赞数"`
	Follow                                           int64   `json:"follow" dc:"新增粉丝数"`
	CancelLike                                       int64   `json:"cancelLike" dc:"取消点赞数"`
	CancelFollow                                     int64   `json:"cancelFollow" dc:"取消关注数"`
	Report                                           int64   `json:"report" dc:"举报数"`
	Block                                            int64   `json:"block" dc:"拉黑数"`
	Negative                                         int64   `json:"negative" dc:"减少此类作品数"`
	Activation                                       int64   `json:"activation" dc:"应用下载数据-激活数"`
	DownloadStarted                                  int64   `json:"downloadStarted" dc:"应用下载数据-安卓下载开始数"`
	DownloadCompleted                                int64   `json:"downloadCompleted" dc:"应用下载数据-安卓下载完成数"`
	DownloadInstalled                                int64   `json:"downloadInstalled" dc:"安卓安装完成数"`
	ClickConversionRatio                             float64 `json:"clickConversionRatio" dc:"点击激活成本"`
	ConversionCost                                   float64 `json:"conversionCost" dc:"激活单价"`
	DownloadCompletedCost                            float64 `json:"downloadCompletedCost" dc:"安卓下载完成单价（元）"`
	DownloadCompletedRatio                           float64 `json:"downloadCompletedRatio" dc:"安卓下载完成率 = 安卓下载完成数/安卓下载开始数*100%"`
	DownloadConversionRatio                          float64 `json:"downloadConversionRatio" dc:"下载完成激活率 = 激活数/安卓下载完成数*100%"`
	DownloadStartedCost                              float64 `json:"downloadStartedCost" dc:"安卓下载开始单价（元）"`
	DownloadStartedRatio                             float64 `json:"downloadStartedRatio" dc:"安卓下载开始率 = 安卓下载开始数/行为数*100%"`
	EventRegister                                    int64   `json:"eventRegister" dc:"应用下载数据-注册数"`
	EventRegisterCost                                float64 `json:"eventRegisterCost" dc:"应用下载数据-注册成本"`
	EventRegisterRatio                               float64 `json:"eventRegisterRatio" dc:"应用下载数据-注册率 = 注册数/激活数*100%"`
	EventJinJianApp                                  int64   `json:"eventJinJianApp" dc:"应用下载数据-完件数"`
	EventJinJianAppCost                              float64 `json:"eventJinJianAppCost" dc:"应用下载数据-完件成本"`
	EventJinJianLandingPage                          int64   `json:"eventJinJianLandingPage" dc:"落地页数据-落地页完件数"`
	EventJinJianLandingPageCost                      float64 `json:"eventJinJianLandingPageCost" dc:"落地页数据-落地页完件成本"`
	Jinjian0DCnt                                     int64   `json:"jinjian0DCnt" dc:"T0完件数"`
	Jinjian3DCnt                                     int64   `json:"jinjian3DCnt" dc:"T3完件数"`
	Jinjian0DCntCost                                 float64 `json:"jinjian0DCntCost" dc:"T0完件成本"`
	Jinjian3DCntCost                                 float64 `json:"jinjian3DCntCost" dc:"T3完件成本"`
	EventCreditGrantApp                              int64   `json:"eventCreditGrantApp" dc:"应用下载数据-授信数"`
	EventCreditGrantAppCost                          float64 `json:"eventCreditGrantAppCost" dc:"应用下载数据-授信成本"`
	EventCreditGrantAppRatio                         float64 `json:"eventCreditGrantAppRatio" dc:"应用下载数据-授信率 = 授信数/完件数*100%"`
	EventCreditGrantLandingPage                      int64   `json:"eventCreditGrantLandingPage" dc:"落地页授信数"`
	EventCreditGrantLandingPageCost                  float64 `json:"eventCreditGrantLandingPageCost" dc:"落地页数据-落地页授信成本"`
	EventCreditGrantLandingRatio                     float64 `json:"eventCreditGrantLandingRatio" dc:"落地页数据-落地页授信率"`
	EventCreditGrantFirstDayApp                      int64   `json:"eventCreditGrantFirstDayApp" dc:"app首日授信数"`
	EventCreditGrantFirstDayAppCost                  float64 `json:"eventCreditGrantFirstDayAppCost" dc:"首日授信成本"`
	EventCreditGrantFirstDayAppRatio                 float64 `json:"eventCreditGrantFirstDayAppRatio" dc:"首日授信率 = 首日授信数/完件数*100%"`
	EventCreditGrantFirstDayLandingPage              int64   `json:"eventCreditGrantFirstDayLandingPage" dc:"落地页首日授信数"`
	EventCreditGrantFirstDayLandingPageCost          float64 `json:"eventCreditGrantFirstDayLandingPageCost" dc:"落地页首日授信成本"`
	EventCreditGrantFirstDayLandingPageRatio         float64 `json:"eventCreditGrantFirstDayLandingPageRatio" dc:"落地页首日授信率"`
	CreditGrant0DCnt                                 int64   `json:"creditGrant0DCnt" dc:"T0授信数"`
	CreditGrant3DCnt                                 int64   `json:"creditGrant3DCnt" dc:"T3授信数"`
	CreditGrant0DCntCost                             float64 `json:"creditGrant0DCntCost" dc:"T0授信成本"`
	CreditGrant3DCntCost                             float64 `json:"creditGrant3DCntCost" dc:"T3授信成本"`
	CreditGrant0DCntRatio                            float64 `json:"creditGrant0DCntRatio" dc:"T0完件授信率"`
	CreditGrant3DCntRatio                            float64 `json:"creditGrant3DCntRatio" dc:"T3完件授信通过率"`
	EventOrderSubmit                                 int64   `json:"eventOrderSubmit" dc:"提交订单数"`
	EventOrderPaid                                   int64   `json:"eventOrderPaid" dc:"应用下载数据-付款成功数"`
	EventOrderPaidPurchaseAmount                     float64 `json:"eventOrderPaidPurchaseAmount" dc:"应用下载数据-付款成功金额"`
	EventOrderPaidCost                               float64 `json:"eventOrderPaidCost" dc:"应用下载数据-单次付款成本"`
	EventOrderPaidRoi                                float64 `json:"eventOrderPaidRoi" dc:"订单支付率 = 商品成交数/商品访问数*100%"`
	OrderSubmitAmt                                   float64 `json:"orderSubmitAmt" dc:"订单提交金额"`
	FormCount                                        int64   `json:"formCount" dc:"落地页数据-线索提交个数"`
	FormCost                                         float64 `json:"formCost" dc:"落地页数据-单个线索成本"`
	FormActionRatio                                  float64 `json:"formActionRatio" dc:"落地页数据-表单提交点击率"`
	Submit                                           int64   `json:"submit" dc:"提交按钮点击数（历史字段）"`
	EventValidClues                                  int64   `json:"eventValidClues" dc:"落地页数据-有效线索数"`
	EventValidCluesCost                              float64 `json:"eventValidCluesCost" dc:"落地页数据-有效线索成本"`
	EventConsultationValidRetained                   int64   `json:"eventConsultationValidRetained" dc:"留咨咨询数"`
	EventConsultationValidRetainedCost               float64 `json:"eventConsultationValidRetainedCost" dc:"留咨咨询成本"`
	EventConsultationValidRetainedRatio              float64 `json:"eventConsultationValidRetainedRatio" dc:"留咨咨询率"`
	EventConversionClickCost                         float64 `json:"eventConversionClickCost" dc:"有效咨询成本"`
	EventConversionClickRatio                        float64 `json:"eventConversionClickRatio" dc:"有效咨询率"`
	EventPreComponentConsultationValidRetained       int64   `json:"eventPreComponentConsultationValidRetained" dc:"附加咨询组件留资咨询数"`
	EventAdWatch10Times                              int64   `json:"eventAdWatch10Times" dc:"10次广告广告观看数"`
	EventAdWatch10TimesCost                          float64 `json:"eventAdWatch10TimesCost" dc:"10次广告观看成本"`
	EventAdWatch10TimesRatio                         float64 `json:"eventAdWatch10TimesRatio" dc:"10次广告观看转化率 = 10次广告广告观看数/行为数*100%"`
	EventAdWatch20Times                              int64   `json:"eventAdWatch20Times" dc:"20次广告广告观看数"`
	EventAdWatch20TimesCost                          float64 `json:"eventAdWatch20TimesCost" dc:"20次广告观看成本"`
	EventAdWatch20TimesRatio                         float64 `json:"eventAdWatch20TimesRatio" dc:"20次广告观看转化率 = 20次广告广告观看数/行为数*100%"`
	EventAdWatch5Times                               int64   `json:"eventAdWatch5Times" dc:"5次广告广告观看数"`
	EventAdWatch5TimesCost                           float64 `json:"eventAdWatch5TimesCost" dc:"5次广告观看成本"`
	EventAdWatch5TimesRatio                          float64 `json:"eventAdWatch5TimesRatio" dc:"5次广告观看转化率 = 5次广告广告观看数/行为数*100%"`
	EventWatchAppAd                                  int64   `json:"eventWatchAppAd" dc:"广告观看"`
	EventAdWatchTimes                                int64   `json:"eventAdWatchTimes" dc:"广告观看次数"`
	EventAdWatchTimesRatio                           float64 `json:"eventAdWatchTimesRatio" dc:"广告观看次数转化率 = 广告观看次数/行为数*100%"`
	EventAdWatchTimesCost                            float64 `json:"eventAdWatchTimesCost" dc:"广告观看次数成本"`
	EventMakingCalls                                 int64   `json:"eventMakingCalls" dc:"电话拨打数"`
	EventMakingCallsCost                             float64 `json:"eventMakingCallsCost" dc:"电话拨打成本"`
	EventMakingCallsRatio                            float64 `json:"eventMakingCallsRatio" dc:"电话拨打率"`
	EventGetThrough                                  int64   `json:"eventGetThrough" dc:"智能电话-确认接通数"`
	EventGetThroughCost                              float64 `json:"eventGetThroughCost" dc:"智能电话-确认接通成本"`
	EventGetThroughRatio                             float64 `json:"eventGetThroughRatio" dc:"智能电话-确认接通率"`
	EventPhoneGetThrough                             int64   `json:"eventPhoneGetThrough" dc:"电话建联数"`
	EventOutboundCall                                int64   `json:"eventOutboundCall" dc:"电话拨打数"`
	EventOutboundCallCost                            float64 `json:"eventOutboundCallCost" dc:"电话拨打成本"`
	EventOutboundCallRatio                           float64 `json:"eventOutboundCallRatio" dc:"电话拨打率"`
	EventWechatQrCodeLinkClick                       int64   `json:"eventWechatQrCodeLinkClick" dc:"微信小程序深度加粉数"`
	EventAddWechat                                   int64   `json:"eventAddWechat" dc:"微信复制数"`
	EventAddWechatCost                               float64 `json:"eventAddWechatCost" dc:"微信复制成本"`
	EventAddWechatRatio                              float64 `json:"eventAddWechatRatio" dc:"微信复制率"`
	EventWechatConnected                             int64   `json:"eventWechatConnected" dc:"微信加粉数"`
	EventAudition                                    int64   `json:"eventAudition" dc:"首次试听到课数"`
	EventButtonClick                                 int64   `json:"eventButtonClick" dc:"按钮点击数"`
	EventButtonClickCost                             float64 `json:"eventButtonClickCost" dc:"按钮点击成本"`
	EventButtonClickRatio                            float64 `json:"eventButtonClickRatio" dc:"按钮点击率"`
	EventMultiConversion                             int64   `json:"eventMultiConversion" dc:"落地页多转化次数"`
	EventMultiConversionRatio                        float64 `json:"eventMultiConversionRatio" dc:"落地页多转化率"`
	EventMultiConversionCost                         float64 `json:"eventMultiConversionCost" dc:"落地页多转化成本"`
	EventAddShoppingCart                             int64   `json:"eventAddShoppingCart" dc:"添加购物车数"`
	EventAddShoppingCartCost                         float64 `json:"eventAddShoppingCartCost" dc:"添加购物车成本"`
	EventIntentionConfirmed                          int64   `json:"eventIntentionConfirmed" dc:"意向确认数"`
	EventOrderSuccessed                              int64   `json:"eventOrderSuccessed" dc:"有效线索成交数"`
	EventPhoneCardActivate                           int64   `json:"eventPhoneCardActivate" dc:"电话卡激活数"`
	EventMeasurementHouse                            int64   `json:"eventMeasurementHouse" dc:"量房数"`
	EventAppInvoked                                  int64   `json:"eventAppInvoked" dc:"唤醒应用数"`
	EventAppInvokedCost                              float64 `json:"eventAppInvokedCost" dc:"唤醒应用成本"`
	EventAppInvokedRatio                             float64 `json:"eventAppInvokedRatio" dc:"唤醒应用率 = 唤醒应用数/行为数*100%"`
	EventNextDayStayCost                             float64 `json:"eventNextDayStayCost" dc:"应用下载数据-次留成本（仅支持分日查询）"`
	EventNextDayStayRatio                            float64 `json:"eventNextDayStayRatio" dc:"应用下载数据-次留率（仅支持分日查询）= 次留数/激活数*100%"`
	EventNextDayStay                                 int64   `json:"eventNextDayStay" dc:"应用下载数据-次留数（仅支持分日查询）"`
	PhotoClick                                       int64   `json:"photoClick" dc:"封面点击数"`
	PhotoClickRatio                                  float64 `json:"photoClickRatio" dc:"封面点击率 = 封面点击数/封面曝光数*100%"`
	PhotoClickCost                                   float64 `json:"photoClickCost" dc:"平均点击单价（元）"`
	ActionRatio                                      float64 `json:"actionRatio" dc:"行为率 = 行为数/素材曝光数*100%"`
	ActionNewRatio                                   float64 `json:"actionNewRatio" dc:"行为率 新"`
	ActionCost                                       float64 `json:"actionCost" dc:"平均行为单价（元）"`
	Impression1KCost                                 float64 `json:"impression1KCost" dc:"平均千次曝光花费（元）"`
	Click1KCost                                      float64 `json:"click1KCost" dc:"平均千次素材曝光花费(元)"`
	ApproxPayCost                                    float64 `json:"approxPayCost" dc:"淘系近似购买成本"`
	ApproxPayCount                                   int64   `json:"approxPayCount" dc:"近似购买数"`
	ApproxPayRatio                                   float64 `json:"approxPayRatio" dc:"淘系近似购买率"`
	LiveEventGoodsView                               int64   `json:"liveEventGoodsView" dc:"直播间商品点击数"`
	LivePlayed3S                                     int64   `json:"livePlayed3S" dc:"直播观看数"`
	AdProductCnt                                     int64   `json:"adProductCnt" dc:"商品成交数"`
	EventGoodsView                                   int64   `json:"eventGoodsView" dc:"商品访问数"`
	EventGoodsViewCost                               float64 `json:"eventGoodsViewCost" dc:"商品访问成本"`
	MerchantRecoFans                                 int64   `json:"merchantRecoFans" dc:"涨粉量"`
	MerchantRecoFansCost                             float64 `json:"merchantRecoFansCost" dc:"涨粉成本"`
	EventOrderAmountRoi                              float64 `json:"eventOrderAmountRoi" dc:"小店推广roi"`
	EventNewUserPay                                  int64   `json:"eventNewUserPay" dc:"新增付费人数"`
	EventNewUserPayCost                              float64 `json:"eventNewUserPayCost" dc:"新增付费人数成本"`
	EventNewUserPayRatio                             float64 `json:"eventNewUserPayRatio" dc:"新增付费人数率 = 新增付费人数/（激活数+表单数）*100%"`
	EventNewUserJinjianApp                           int64   `json:"eventNewUserJinjianApp" dc:"新增完件人数"`
	EventNewUserJinjianAppCost                       float64 `json:"eventNewUserJinjianAppCost" dc:"新增完件人数成本"`
	EventNewUserJinjianAppRoi                        float64 `json:"eventNewUserJinjianAppRoi" dc:"新增完件人数率 = 新增完件人数/（激活数+表单数）*100%"`
	EventNewUserCreditGrantApp                       int64   `json:"eventNewUserCreditGrantApp" dc:"新增授信人数"`
	EventNewUserCreditGrantAppCost                   float64 `json:"eventNewUserCreditGrantAppCost" dc:"新增授信人数成本"`
	EventNewUserCreditGrantAppRoi                    float64 `json:"eventNewUserCreditGrantAppRoi" dc:"新增授信人数率 = 新增授信人数/（激活数+表单数）*100%"`
	EventNewUserJinjianPage                          int64   `json:"eventNewUserJinjianPage" dc:"字段描述，需要修改"`
	EventNewUserJinjianPageCost                      float64 `json:"eventNewUserJinjianPageCost" dc:"字段描述，需要修改"`
	EventNewUserJinjianPageRoi                       float64 `json:"eventNewUserJinjianPageRoi" dc:"字段描述，需要修改"`
	EventNewUserCreditGrantPage                      int64   `json:"eventNewUserCreditGrantPage" dc:"字段描述，需要修改"`
	EventNewUserCreditGrantPageCost                  float64 `json:"eventNewUserCreditGrantPageCost" dc:"字段描述，需要修改"`
	EventNewUserCreditGrantPageRoi                   float64 `json:"eventNewUserCreditGrantPageRoi" dc:"字段描述，需要修改"`
	EventAppointForm                                 int64   `json:"eventAppointForm" dc:"预约表单数"`
	EventAppointFormCost                             float64 `json:"eventAppointFormCost" dc:"预约表单点击成本"`
	EventAppointFormRatio                            float64 `json:"eventAppointFormRatio" dc:"预约表单点击率"`
	EventAppointJumpClick                            int64   `json:"eventAppointJumpClick" dc:"预约跳转点击数"`
	EventAppointJumpClickCost                        float64 `json:"eventAppointJumpClickCost" dc:"预约跳转点击成本"`
	EventAppointJumpClickRatio                       float64 `json:"eventAppointJumpClickRatio" dc:"预约跳转点击率"`
	UnionEventPayPurchaseAmount7D                    float64 `json:"unionEventPayPurchaseAmount7D" dc:"联盟广告收入"`
	UnionEventPayPurchaseAmount7DRoi                 float64 `json:"unionEventPayPurchaseAmount7DRoi" dc:"联盟变现ROI"`
	EventDspGiftForm                                 int64   `json:"eventDspGiftForm" dc:"附加组件表单提交"`
	EventCreditCardRecheck                           int64   `json:"eventCreditCardRecheck" dc:"信用卡核卡数"`
	EventCreditCardRecheckFirstDay                   int64   `json:"eventCreditCardRecheckFirstDay" dc:"信用卡首日核卡数"`
	KeyAction                                        int64   `json:"keyAction" dc:"关键行为数"`
	KeyActionCost                                    float64 `json:"keyActionCost" dc:"关键行为成本"`
	KeyActionRatio                                   float64 `json:"keyActionRatio" dc:"关键行为率 = 关键行为数/激活数*100%""`
	KeyInappAction0DCnt                              int64   `json:"keyInappAction0DCnt" dc:"T0全量授信数"`
	KeyInappAction3DCnt                              int64   `json:"keyInappAction3DCnt" dc:"T3全量授信数"`
	KeyInappAction0DCntCost                          float64 `json:"keyInappAction0DCntCost" dc:"T0全量授信成本"`
	KeyInappAction3DCntCost                          float64 `json:"keyInappAction3DCntCost" dc:"T3全量授信成本"`
	KeyInappAction0DCntRatio                         float64 `json:"keyInappAction0DCntRatio" dc:"T0全量授信通过率"`
	KeyInappAction3DCntRatio                         float64 `json:"keyInappAction3DCntRatio" dc:"T3全量授信通过率"`
	DrawCreditLine0DCnt                              int64   `json:"drawCreditLine0DCnt" dc:"T0用信数"`
	DrawCreditLine0DCntCost                          float64 `json:"drawCreditLine0DCntCost" dc:"T0用信成本"`
	DrawCreditLine0DCntRatio                         float64 `json:"drawCreditLine0DCntRatio" dc:"T0授信用信率"`
	EventNoIntention                                 int64   `json:"eventNoIntention" dc:"用户无意向数"`
	AdScene                                          string  `json:"adScene" dc:"广告场景"`
	AdScene2                                         string  `json:"adScene2" dc:"广告场景2"`
	PlacementType                                    string  `json:"placementType" dc:"投放类型"`
}

// KsAdvertiserAccountReportDataSearchReq 分页请求参数
type KsAdvertiserAccountReportDataSearchReq struct {
	comModel.PageReq
	StartTime         string   `p:"startTime" dc:"开始时间"`
	EndTime           string   `p:"endTime" dc:"结束时间"`
	CorporationNames  []string `p:"corporationNames" dc:"账户主体"`
	AdvertiserNames   []string `p:"advertiserNames" dc:"账户名称"`
	AdvertiserIds     []int64  `p:"advertiserIds" dc:"账户ID"`
	UserIds           []int    `p:"userIds" dc:"优化师ID"`
	DeptIds           []int    `p:"deptIds" dc:"部门ID"`
	KeyWord           string   `p:"keyword" dc:"搜索关键字"`
	AccountAutoManage *int     `p:"accountAutoManage" dc:"账户智投开关 1：开启 0：关闭"`
}

// KsAdvertiserAccountReportDataSearchRes 列表返回结果
type KsAdvertiserAccountReportDataSearchRes struct {
	comModel.ListRes
	List    []*KsAdvertiserAccountReportDataListRes  `json:"list"`
	Summary *KsAdvertiserAccountReportDataSummaryRes `json:"summary"`
}

// KsAdvertiserAccountReportDataAddReq 添加操作请求参数
type KsAdvertiserAccountReportDataAddReq struct {
	AdvertiserId                                     int64   `p:"advertiserId" v:"required#主键ID不能为空" dc:"广告主ID"`
	StatDate                                         string  `p:"statDate" dc:"数据日期，格式：YYYY-MM-DD"`
	PrivateMessageSentCost                           float64 `p:"privateMessageSentCost"  dc:"私信消息转化成本"`
	PrivateMessageSentRatio                          float64 `p:"privateMessageSentRatio"  dc:"私信消息转化率"`
	PrivateMessageSentCnt                            int64   `p:"privateMessageSentCnt"  dc:"私信消息数"`
	LeadsSubmitCost                                  float64 `p:"leadsSubmitCost"  dc:"直接私信留资成本"`
	LeadsSubmitCntRatio                              float64 `p:"leadsSubmitCntRatio"  dc:"直接私信留资率"`
	LeadsSubmitCnt                                   int64   `p:"leadsSubmitCnt"  dc:"直接私信留资数"`
	PlayedNum                                        int64   `p:"playedNum"  dc:"播放数"`
	PlayedEnd                                        int64   `p:"playedEnd"  dc:"播放完成"`
	PlayedFiveSeconds                                int64   `p:"playedFiveSeconds"  dc:"播放5s"`
	PlayedThreeSeconds                               int64   `p:"playedThreeSeconds"  dc:"有效播放数"`
	Play3SRatio                                      float64 `p:"play3SRatio"  dc:"3s 播放率"`
	Play5SRatio                                      float64 `p:"play5SRatio"  dc:"5s播放率"`
	PlayEndRatio                                     float64 `p:"playEndRatio"  dc:"完播率"`
	AdPhotoPlayed10S                                 int64   `p:"adPhotoPlayed10S"  dc:"10s播放数"`
	AdPhotoPlayed2S                                  int64   `p:"adPhotoPlayed2S"  dc:"2s播放数"`
	AdPhotoPlayed75Percent                           int64   `p:"adPhotoPlayed75Percent"  dc:"75%进度播放数"`
	AdPhotoPlayed75PercentRatio                      float64 `p:"adPhotoPlayed75PercentRatio"  dc:"75%进度播放率"`
	AdPhotoPlayed10SRatio                            float64 `p:"adPhotoPlayed10SRatio"  dc:"10s播放率"`
	AdPhotoPlayed2SRatio                             float64 `p:"adPhotoPlayed2SRatio"  dc:"2s播放率"`
	MinigameIaaPurchaseAmountWeekByConversionRoi     float64 `p:"minigameIaaPurchaseAmountWeekByConversionRoi"  dc:"激活后七日广告变现ROI"`
	MinigameIaaPurchaseAmountThreeDayByConversionRoi float64 `p:"minigameIaaPurchaseAmountThreeDayByConversionRoi"  dc:"激活后三日广告变现ROI"`
	MinigameIaaPurchaseAmountFirstDayRoi             float64 `p:"minigameIaaPurchaseAmountFirstDayRoi"  dc:"当日广告变现ROI"`
	MinigameIaaPurchaseAmountWeekByConversion        float64 `p:"minigameIaaPurchaseAmountWeekByConversion"  dc:"激活后七日广告LTV"`
	MinigameIaaPurchaseAmountThreeDayByConversion    float64 `p:"minigameIaaPurchaseAmountThreeDayByConversion"  dc:"激活后三日广告LTV"`
	MinigameIaaPurchaseAmountFirstDay                float64 `p:"minigameIaaPurchaseAmountFirstDay"  dc:"当日广告LTV"`
	MinigameIaaPurchaseRoi                           float64 `p:"minigameIaaPurchaseRoi"  dc:"IAA广告变现ROI"`
	MinigameIaaPurchaseAmount                        float64 `p:"minigameIaaPurchaseAmount"  dc:"IAA广告变现LTV"`
	MinigameIaaPurchaseAmount30DayByConversionRoi    float64 `p:"minigameIaaPurchaseAmount30DayByConversionRoi"  dc:"激活后30日广告变现ROI"`
	MinigameIaaPurchaseAmount15DayByConversionRoi    float64 `p:"minigameIaaPurchaseAmount15DayByConversionRoi"  dc:"激活后15日广告变现ROI"`
	MinigameIaaPurchaseAmount30DayByConversion       float64 `p:"minigameIaaPurchaseAmount30DayByConversion"  dc:"激活后30日广告LTV"`
	MinigameIaaPurchaseAmount15DayByConversion       float64 `p:"minigameIaaPurchaseAmount15DayByConversion"  dc:"激活后15日广告LTV"`
	MmuEffectiveCustomerAcquisition7DCnt             int64   `p:"mmuEffectiveCustomerAcquisition7DCnt"  dc:"MMU识别产生的有效获客数（计费）"`
	MmuEffectiveCustomerAcquisitionCnt               int64   `p:"mmuEffectiveCustomerAcquisitionCnt"  dc:"MMU识别产生的有效获客数（回传）"`
	EffectiveCustomerAcquisition7DRatio              float64 `p:"effectiveCustomerAcquisition7DRatio"  dc:"有效获客率（计费）"`
	EffectiveCustomerAcquisition7DCost               float64 `p:"effectiveCustomerAcquisition7DCost"  dc:"有效获客成本（计费）"`
	EffectiveCustomerAcquisition7DCnt                int64   `p:"effectiveCustomerAcquisition7DCnt"  dc:"有效获客数（计费）"`
	EventPay30DayOverallRoi                          float64 `p:"eventPay30DayOverallRoi"  dc:"激活后30日整体ROI"`
	EventPay15DayOverallRoi                          float64 `p:"eventPay15DayOverallRoi"  dc:"激活后15日整体ROI"`
	EventPayPurchaseAmount15DayByConversion          float64 `p:"eventPayPurchaseAmount15DayByConversion"  dc:"激活后15日付费金额"`
	EventPayPurchaseAmount30DayByConversion          float64 `p:"eventPayPurchaseAmount30DayByConversion"  dc:"激活后30日付费金额"`
	EventPayFirstDay                                 int64   `p:"eventPayFirstDay"  dc:"应用下载数据-首日付费次数"`
	EventPayPurchaseAmountFirstDay                   float64 `p:"eventPayPurchaseAmountFirstDay"  dc:"应用下载数据-首日付费金额"`
	EventPayFirstDayRoi                              float64 `p:"eventPayFirstDayRoi"  dc:"应用下载数据-首日 ROI"`
	EventPay                                         int64   `p:"eventPay"  dc:"应用下载数据-付费次数"`
	EventPayPurchaseAmount                           float64 `p:"eventPayPurchaseAmount"  dc:"应用下载数据-付费金额"`
	EventPayRoi                                      float64 `p:"eventPayRoi"  dc:"应用下载数据-ROI"`
	EventPayPurchaseAmountOneDay                     float64 `p:"eventPayPurchaseAmountOneDay"  dc:"激活后24h付费金额(回传时间)"`
	EventPayPurchaseAmountOneDayByConversion         float64 `p:"eventPayPurchaseAmountOneDayByConversion"  dc:"激活后24h付费金额(激活时间)"`
	EventPayPurchaseAmountOneDayByConversionRoi      float64 `p:"eventPayPurchaseAmountOneDayByConversionRoi"  dc:"激活后24小时付费ROI"`
	EventPayPurchaseAmountOneDayRoi                  float64 `p:"eventPayPurchaseAmountOneDayRoi"  dc:"激活后24h-ROI(回传时间)"`
	EventPayWeightedPurchaseAmount                   float64 `p:"eventPayWeightedPurchaseAmount"  dc:"加权付费金额"`
	EventPayWeightedPurchaseAmountFirstDay           float64 `p:"eventPayWeightedPurchaseAmountFirstDay"  dc:"首日加权付费金额"`
	Charge                                           float64 `p:"charge"  dc:"消耗(花费（元)）"`
	Show                                             int64   `p:"show"  dc:"封面曝光数"`
	Aclick                                           int64   `p:"aclick"  dc:"素材曝光数"`
	Bclick                                           int64   `p:"bclick"  dc:"行为数"`
	AdShow                                           float64 `p:"adShow"  dc:"广告曝光"`
	Share                                            int64   `p:"share"  dc:"分享数"`
	Comment                                          int64   `p:"comment"  dc:"评论数"`
	Like                                             int64   `p:"like"  dc:"点赞数"`
	Follow                                           int64   `p:"follow"  dc:"新增粉丝数"`
	CancelLike                                       int64   `p:"cancelLike"  dc:"取消点赞数"`
	CancelFollow                                     int64   `p:"cancelFollow"  dc:"取消关注数"`
	Report                                           int64   `p:"report"  dc:"举报数"`
	Block                                            int64   `p:"block"  dc:"拉黑数"`
	Negative                                         int64   `p:"negative"  dc:"减少此类作品数"`
	Activation                                       int64   `p:"activation"  dc:"应用下载数据-激活数"`
	DownloadStarted                                  int64   `p:"downloadStarted"  dc:"应用下载数据-安卓下载开始数"`
	DownloadCompleted                                int64   `p:"downloadCompleted"  dc:"应用下载数据-安卓下载完成数"`
	DownloadInstalled                                int64   `p:"downloadInstalled"  dc:"安卓安装完成数"`
	ClickConversionRatio                             float64 `p:"clickConversionRatio"  dc:"点击激活成本"`
	ConversionCost                                   float64 `p:"conversionCost"  dc:"激活单价"`
	DownloadCompletedCost                            float64 `p:"downloadCompletedCost"  dc:"安卓下载完成单价（元）"`
	DownloadCompletedRatio                           float64 `p:"downloadCompletedRatio"  dc:"安卓下载完成率"`
	DownloadConversionRatio                          float64 `p:"downloadConversionRatio"  dc:"下载完成激活率"`
	DownloadStartedCost                              float64 `p:"downloadStartedCost"  dc:"安卓下载开始单价（元）"`
	DownloadStartedRatio                             float64 `p:"downloadStartedRatio"  dc:"安卓下载开始率"`
	EventRegister                                    int64   `p:"eventRegister"  dc:"应用下载数据-注册数"`
	EventRegisterCost                                float64 `p:"eventRegisterCost"  dc:"应用下载数据-注册成本"`
	EventRegisterRatio                               float64 `p:"eventRegisterRatio"  dc:"应用下载数据-注册率"`
	EventJinJianApp                                  int64   `p:"eventJinJianApp"  dc:"应用下载数据-完件数"`
	EventJinJianAppCost                              float64 `p:"eventJinJianAppCost"  dc:"应用下载数据-完件成本"`
	EventJinJianLandingPage                          int64   `p:"eventJinJianLandingPage"  dc:"落地页数据-落地页完件数"`
	EventJinJianLandingPageCost                      float64 `p:"eventJinJianLandingPageCost"  dc:"落地页数据-落地页完件成本"`
	Jinjian0DCnt                                     int64   `p:"jinjian0DCnt"  dc:"T0完件数"`
	Jinjian3DCnt                                     int64   `p:"jinjian3DCnt"  dc:"T3完件数"`
	Jinjian0DCntCost                                 float64 `p:"jinjian0DCntCost"  dc:"T0完件成本"`
	Jinjian3DCntCost                                 float64 `p:"jinjian3DCntCost"  dc:"T3完件成本"`
	EventCreditGrantApp                              int64   `p:"eventCreditGrantApp"  dc:"应用下载数据-授信数"`
	EventCreditGrantAppCost                          float64 `p:"eventCreditGrantAppCost"  dc:"应用下载数据-授信成本"`
	EventCreditGrantAppRatio                         float64 `p:"eventCreditGrantAppRatio"  dc:"应用下载数据-授信率"`
	EventCreditGrantLandingPage                      int64   `p:"eventCreditGrantLandingPage"  dc:"落地页授信数"`
	EventCreditGrantLandingPageCost                  float64 `p:"eventCreditGrantLandingPageCost"  dc:"落地页数据-落地页授信成本"`
	EventCreditGrantLandingRatio                     float64 `p:"eventCreditGrantLandingRatio"  dc:"落地页数据-落地页授信率"`
	EventCreditGrantFirstDayApp                      int64   `p:"eventCreditGrantFirstDayApp"  dc:"app首日授信数"`
	EventCreditGrantFirstDayAppCost                  float64 `p:"eventCreditGrantFirstDayAppCost"  dc:"首日授信成本"`
	EventCreditGrantFirstDayAppRatio                 float64 `p:"eventCreditGrantFirstDayAppRatio"  dc:"首日授信率"`
	EventCreditGrantFirstDayLandingPage              int64   `p:"eventCreditGrantFirstDayLandingPage"  dc:"落地页首日授信数"`
	EventCreditGrantFirstDayLandingPageCost          float64 `p:"eventCreditGrantFirstDayLandingPageCost"  dc:"落地页首日授信成本"`
	EventCreditGrantFirstDayLandingPageRatio         float64 `p:"eventCreditGrantFirstDayLandingPageRatio"  dc:"落地页首日授信率"`
	CreditGrant0DCnt                                 int64   `p:"creditGrant0DCnt"  dc:"T0授信数"`
	CreditGrant3DCnt                                 int64   `p:"creditGrant3DCnt"  dc:"T3授信数"`
	CreditGrant0DCntCost                             float64 `p:"creditGrant0DCntCost"  dc:"T0授信成本"`
	CreditGrant3DCntCost                             float64 `p:"creditGrant3DCntCost"  dc:"T3授信成本"`
	CreditGrant0DCntRatio                            float64 `p:"creditGrant0DCntRatio"  dc:"T0完件授信率"`
	CreditGrant3DCntRatio                            float64 `p:"creditGrant3DCntRatio"  dc:"T3完件授信通过率"`
	EventOrderSubmit                                 int64   `p:"eventOrderSubmit"  dc:"提交订单数"`
	EventOrderPaid                                   int64   `p:"eventOrderPaid"  dc:"应用下载数据-付款成功数"`
	EventOrderPaidPurchaseAmount                     float64 `p:"eventOrderPaidPurchaseAmount"  dc:"应用下载数据-付款成功金额"`
	EventOrderPaidCost                               float64 `p:"eventOrderPaidCost"  dc:"应用下载数据-单次付款成本"`
	EventOrderPaidRoi                                float64 `p:"eventOrderPaidRoi"  dc:"订单支付率"`
	OrderSubmitAmt                                   float64 `p:"orderSubmitAmt"  dc:"订单提交金额"`
	FormCount                                        int64   `p:"formCount"  dc:"落地页数据-线索提交个数"`
	FormCost                                         float64 `p:"formCost"  dc:"落地页数据-单个线索成本"`
	FormActionRatio                                  float64 `p:"formActionRatio"  dc:"落地页数据-表单提交点击率"`
	Submit                                           int64   `p:"submit"  dc:"提交按钮点击数（历史字段）"`
	EventValidClues                                  int64   `p:"eventValidClues"  dc:"落地页数据-有效线索数"`
	EventValidCluesCost                              float64 `p:"eventValidCluesCost"  dc:"落地页数据-有效线索成本"`
	EventConsultationValidRetained                   int64   `p:"eventConsultationValidRetained"  dc:"留咨咨询数"`
	EventConsultationValidRetainedCost               float64 `p:"eventConsultationValidRetainedCost"  dc:"留咨咨询成本"`
	EventConsultationValidRetainedRatio              float64 `p:"eventConsultationValidRetainedRatio"  dc:"留咨咨询率"`
	EventConversionClickCost                         float64 `p:"eventConversionClickCost"  dc:"有效咨询成本"`
	EventConversionClickRatio                        float64 `p:"eventConversionClickRatio"  dc:"有效咨询率"`
	EventPreComponentConsultationValidRetained       int64   `p:"eventPreComponentConsultationValidRetained"  dc:"附加咨询组件留资咨询数"`
	EventAdWatch10Times                              int64   `p:"eventAdWatch10Times"  dc:"10次广告广告观看数"`
	EventAdWatch10TimesCost                          float64 `p:"eventAdWatch10TimesCost"  dc:"10次广告观看成本"`
	EventAdWatch10TimesRatio                         float64 `p:"eventAdWatch10TimesRatio"  dc:"10次广告观看转化率"`
	EventAdWatch20Times                              int64   `p:"eventAdWatch20Times"  dc:"20次广告广告观看数"`
	EventAdWatch20TimesCost                          float64 `p:"eventAdWatch20TimesCost"  dc:"20次广告观看成本"`
	EventAdWatch20TimesRatio                         float64 `p:"eventAdWatch20TimesRatio"  dc:"20次广告观看转化率"`
	EventAdWatch5Times                               int64   `p:"eventAdWatch5Times"  dc:"5次广告广告观看数"`
	EventAdWatch5TimesCost                           float64 `p:"eventAdWatch5TimesCost"  dc:"5次广告观看成本"`
	EventAdWatch5TimesRatio                          float64 `p:"eventAdWatch5TimesRatio"  dc:"5次广告观看转化率"`
	EventWatchAppAd                                  int64   `p:"eventWatchAppAd"  dc:"广告观看"`
	EventAdWatchTimes                                int64   `p:"eventAdWatchTimes"  dc:"广告观看次数"`
	EventAdWatchTimesRatio                           float64 `p:"eventAdWatchTimesRatio"  dc:"广告观看次数转化率"`
	EventAdWatchTimesCost                            float64 `p:"eventAdWatchTimesCost"  dc:"广告观看次数成本"`
	EventMakingCalls                                 int64   `p:"eventMakingCalls"  dc:"电话拨打数"`
	EventMakingCallsCost                             float64 `p:"eventMakingCallsCost"  dc:"电话拨打成本"`
	EventMakingCallsRatio                            float64 `p:"eventMakingCallsRatio"  dc:"电话拨打率"`
	EventGetThrough                                  int64   `p:"eventGetThrough"  dc:"智能电话-确认接通数"`
	EventGetThroughCost                              float64 `p:"eventGetThroughCost"  dc:"智能电话-确认接通成本"`
	EventGetThroughRatio                             float64 `p:"eventGetThroughRatio"  dc:"智能电话-确认接通率"`
	EventPhoneGetThrough                             int64   `p:"eventPhoneGetThrough"  dc:"电话建联数"`
	EventOutboundCall                                int64   `p:"eventOutboundCall"  dc:"电话拨打数"`
	EventOutboundCallCost                            float64 `p:"eventOutboundCallCost"  dc:"电话拨打成本"`
	EventOutboundCallRatio                           float64 `p:"eventOutboundCallRatio"  dc:"电话拨打率"`
	EventWechatQrCodeLinkClick                       int64   `p:"eventWechatQrCodeLinkClick"  dc:"微信小程序深度加粉数"`
	EventAddWechat                                   int64   `p:"eventAddWechat"  dc:"微信复制数"`
	EventAddWechatCost                               float64 `p:"eventAddWechatCost"  dc:"微信复制成本"`
	EventAddWechatRatio                              float64 `p:"eventAddWechatRatio"  dc:"微信复制率"`
	EventWechatConnected                             int64   `p:"eventWechatConnected"  dc:"微信加粉数"`
	EventAudition                                    int64   `p:"eventAudition"  dc:"首次试听到课数"`
	EventButtonClick                                 int64   `p:"eventButtonClick"  dc:"按钮点击数"`
	EventButtonClickCost                             float64 `p:"eventButtonClickCost"  dc:"按钮点击成本"`
	EventButtonClickRatio                            float64 `p:"eventButtonClickRatio"  dc:"按钮点击率"`
	EventMultiConversion                             int64   `p:"eventMultiConversion"  dc:"落地页多转化次数"`
	EventMultiConversionRatio                        float64 `p:"eventMultiConversionRatio"  dc:"落地页多转化率"`
	EventMultiConversionCost                         float64 `p:"eventMultiConversionCost"  dc:"落地页多转化成本"`
	EventAddShoppingCart                             int64   `p:"eventAddShoppingCart"  dc:"添加购物车数"`
	EventAddShoppingCartCost                         float64 `p:"eventAddShoppingCartCost"  dc:"添加购物车成本"`
	EventIntentionConfirmed                          int64   `p:"eventIntentionConfirmed"  dc:"意向确认数"`
	EventOrderSuccessed                              int64   `p:"eventOrderSuccessed"  dc:"有效线索成交数"`
	EventPhoneCardActivate                           int64   `p:"eventPhoneCardActivate"  dc:"电话卡激活数"`
	EventMeasurementHouse                            int64   `p:"eventMeasurementHouse"  dc:"量房数"`
	EventAppInvoked                                  int64   `p:"eventAppInvoked"  dc:"唤醒应用数"`
	EventAppInvokedCost                              float64 `p:"eventAppInvokedCost"  dc:"唤醒应用成本"`
	EventAppInvokedRatio                             float64 `p:"eventAppInvokedRatio"  dc:"唤醒应用率"`
	EventNextDayStayCost                             float64 `p:"eventNextDayStayCost"  dc:"应用下载数据-次留成本（仅支持分日查询）"`
	EventNextDayStayRatio                            float64 `p:"eventNextDayStayRatio"  dc:"应用下载数据-次留率（仅支持分日查询）"`
	EventNextDayStay                                 int64   `p:"eventNextDayStay"  dc:"应用下载数据-次留数（仅支持分日查询）"`
	PhotoClick                                       int64   `p:"photoClick"  dc:"封面点击数"`
	PhotoClickRatio                                  float64 `p:"photoClickRatio"  dc:"封面点击率"`
	PhotoClickCost                                   float64 `p:"photoClickCost"  dc:"平均点击单价（元）"`
	ActionRatio                                      float64 `p:"actionRatio"  dc:"行为率"`
	ActionNewRatio                                   float64 `p:"actionNewRatio"  dc:"行为率 新"`
	ActionCost                                       float64 `p:"actionCost"  dc:"平均行为单价（元）"`
	Impression1KCost                                 float64 `p:"impression1KCost"  dc:"平均千次曝光花费（元）"`
	Click1KCost                                      float64 `p:"click1KCost"  dc:"平均千次素材曝光花费(元)"`
	ApproxPayCost                                    float64 `p:"approxPayCost"  dc:"淘系近似购买成本"`
	ApproxPayCount                                   int64   `p:"approxPayCount"  dc:"近似购买数"`
	ApproxPayRatio                                   float64 `p:"approxPayRatio"  dc:"淘系近似购买率"`
	LiveEventGoodsView                               int64   `p:"liveEventGoodsView"  dc:"直播间商品点击数"`
	LivePlayed3S                                     int64   `p:"livePlayed3S"  dc:"直播观看数"`
	AdProductCnt                                     int64   `p:"adProductCnt"  dc:"商品成交数"`
	EventGoodsView                                   int64   `p:"eventGoodsView"  dc:"商品访问数"`
	EventGoodsViewCost                               float64 `p:"eventGoodsViewCost"  dc:"商品访问成本"`
	MerchantRecoFans                                 int64   `p:"merchantRecoFans"  dc:"涨粉量"`
	MerchantRecoFansCost                             float64 `p:"merchantRecoFansCost"  dc:"涨粉成本"`
	EventOrderAmountRoi                              float64 `p:"eventOrderAmountRoi"  dc:"小店推广roi"`
	EventNewUserPay                                  int64   `p:"eventNewUserPay"  dc:"新增付费人数"`
	EventNewUserPayCost                              float64 `p:"eventNewUserPayCost"  dc:"新增付费人数成本"`
	EventNewUserPayRatio                             float64 `p:"eventNewUserPayRatio"  dc:"新增付费人数率"`
	EventNewUserJinjianApp                           int64   `p:"eventNewUserJinjianApp"  dc:"新增完件人数"`
	EventNewUserJinjianAppCost                       float64 `p:"eventNewUserJinjianAppCost"  dc:"新增完件人数成本"`
	EventNewUserJinjianAppRoi                        float64 `p:"eventNewUserJinjianAppRoi"  dc:"新增完件人数率"`
	EventNewUserCreditGrantApp                       int64   `p:"eventNewUserCreditGrantApp"  dc:"新增授信人数"`
	EventNewUserCreditGrantAppCost                   float64 `p:"eventNewUserCreditGrantAppCost"  dc:"新增授信人数成本"`
	EventNewUserCreditGrantAppRoi                    float64 `p:"eventNewUserCreditGrantAppRoi"  dc:"新增授信人数率"`
	EventNewUserJinjianPage                          int64   `p:"eventNewUserJinjianPage"  dc:"字段描述，需要修改"`
	EventNewUserJinjianPageCost                      float64 `p:"eventNewUserJinjianPageCost"  dc:"字段描述，需要修改"`
	EventNewUserJinjianPageRoi                       float64 `p:"eventNewUserJinjianPageRoi"  dc:"字段描述，需要修改"`
	EventNewUserCreditGrantPage                      int64   `p:"eventNewUserCreditGrantPage"  dc:"字段描述，需要修改"`
	EventNewUserCreditGrantPageCost                  float64 `p:"eventNewUserCreditGrantPageCost"  dc:"字段描述，需要修改"`
	EventNewUserCreditGrantPageRoi                   float64 `p:"eventNewUserCreditGrantPageRoi"  dc:"字段描述，需要修改"`
	EventAppointForm                                 int64   `p:"eventAppointForm"  dc:"预约表单数"`
	EventAppointFormCost                             float64 `p:"eventAppointFormCost"  dc:"预约表单点击成本"`
	EventAppointFormRatio                            float64 `p:"eventAppointFormRatio"  dc:"预约表单点击率"`
	EventAppointJumpClick                            int64   `p:"eventAppointJumpClick"  dc:"预约跳转点击数"`
	EventAppointJumpClickCost                        float64 `p:"eventAppointJumpClickCost"  dc:"预约跳转点击成本"`
	EventAppointJumpClickRatio                       float64 `p:"eventAppointJumpClickRatio"  dc:"预约跳转点击率"`
	UnionEventPayPurchaseAmount7D                    float64 `p:"unionEventPayPurchaseAmount7D"  dc:"联盟广告收入"`
	UnionEventPayPurchaseAmount7DRoi                 float64 `p:"unionEventPayPurchaseAmount7DRoi"  dc:"联盟变现ROI"`
	EventDspGiftForm                                 int64   `p:"eventDspGiftForm"  dc:"附加组件表单提交"`
	EventCreditCardRecheck                           int64   `p:"eventCreditCardRecheck"  dc:"信用卡核卡数"`
	EventCreditCardRecheckFirstDay                   int64   `p:"eventCreditCardRecheckFirstDay"  dc:"信用卡首日核卡数"`
	KeyAction                                        int64   `p:"keyAction"  dc:"关键行为数"`
	KeyActionCost                                    float64 `p:"keyActionCost"  dc:"关键行为成本"`
	KeyActionRatio                                   float64 `p:"keyActionRatio"  dc:"关键行为率"`
	KeyInappAction0DCnt                              int64   `p:"keyInappAction0DCnt"  dc:"T0全量授信数"`
	KeyInappAction3DCnt                              int64   `p:"keyInappAction3DCnt"  dc:"T3全量授信数"`
	KeyInappAction0DCntCost                          float64 `p:"keyInappAction0DCntCost"  dc:"T0全量授信成本"`
	KeyInappAction3DCntCost                          float64 `p:"keyInappAction3DCntCost"  dc:"T3全量授信成本"`
	KeyInappAction0DCntRatio                         float64 `p:"keyInappAction0DCntRatio"  dc:"T0全量授信通过率"`
	KeyInappAction3DCntRatio                         float64 `p:"keyInappAction3DCntRatio"  dc:"T3全量授信通过率"`
	DrawCreditLine0DCnt                              int64   `p:"drawCreditLine0DCnt"  dc:"T0用信数"`
	DrawCreditLine0DCntCost                          float64 `p:"drawCreditLine0DCntCost"  dc:"T0用信成本"`
	DrawCreditLine0DCntRatio                         float64 `p:"drawCreditLine0DCntRatio"  dc:"T0授信用信率"`
	EventNoIntention                                 int64   `p:"eventNoIntention"  dc:"用户无意向数"`
	AdScene                                          string  `p:"adScene"  dc:"广告场景"`
	AdScene2                                         string  `p:"adScene2"  dc:"广告场景2"`
	PlacementType                                    string  `p:"placementType"  dc:"投放类型"`
}
