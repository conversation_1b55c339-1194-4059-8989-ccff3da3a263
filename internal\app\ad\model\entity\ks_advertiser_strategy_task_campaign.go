// ==========================================================================
// GFast自动生成model entity操作代码。
// 生成日期：2025-08-23 17:40:19
// 生成路径: internal/app/ad/model/entity/ks_advertiser_strategy_task_campaign.go
// 生成人：cyao
// desc:快手广告搭建-任务-广告计划
// company:云南奇讯科技有限公司
// ==========================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
)

// KsAdvertiserStrategyTaskCampaign is the golang structure for table ks_advertiser_strategy_task_campaign.
type KsAdvertiserStrategyTaskCampaign struct {
	gmeta.Meta     `orm:"table:ks_advertiser_strategy_task_campaign"`
	TaskCampaignId string      `orm:"task_campaign_id,primary" json:"taskCampaignId"` // 任务计划ID
	CampaignId     int64       `orm:"campaign_id" json:"campaignId"`                  // 计划ID
	CampaignName   string      `orm:"campaign_name" json:"campaignName"`              // 任务计划名称
	TaskId         string      `orm:"task_id" json:"taskId"`                          // 任务ID
	AdvertiserId   string      `orm:"advertiser_id" json:"advertiserId"`              // 广告主ID
	AdvertiserNick string      `orm:"advertiser_nick" json:"advertiserNick"`          // 广告主名称
	CampaignData   string      `orm:"campaign_data" json:"campaignData"`              // 创建广告计划数据
	Status         string      `orm:"status" json:"status"`                           // 状态 等待提交：INIT 已终止：TERMINATED 成功：SUCCESS 失败：ERROR
	CreatedAt      *gtime.Time `orm:"created_at" json:"createdAt"`                    // 创建时间
	UpdatedAt      *gtime.Time `orm:"updated_at" json:"updatedAt"`                    // 更新时间
	DeletedAt      *gtime.Time `orm:"deleted_at" json:"deletedAt"`                    // 删除时间
}
