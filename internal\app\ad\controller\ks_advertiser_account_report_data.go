// ==========================================================================
// GFast自动生成controller操作代码。
// 生成日期：2025-08-12 10:44:33
// 生成路径: internal/app/ad/controller/ks_advertiser_account_report_data.go
// 生成人：cq
// desc:快手账户报表数据
// company:云南奇讯科技有限公司
// ==========================================================================

package controller

import (
	"context"

	"github.com/tiger1103/gfast/v3/api/v1/ad"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	systemController "github.com/tiger1103/gfast/v3/internal/app/system/controller"
)

type ksAdvertiserAccountReportDataController struct {
	systemController.BaseController
}

var KsAdvertiserAccountReportData = new(ksAdvertiserAccountReportDataController)

// List 列表
func (c *ksAdvertiserAccountReportDataController) List(ctx context.Context, req *ad.KsAdvertiserAccountReportDataSearchReq) (res *ad.KsAdvertiserAccountReportDataSearchRes, err error) {
	res = new(ad.KsAdvertiserAccountReportDataSearchRes)
	res.KsAdvertiserAccountReportDataSearchRes, err = service.KsAdvertiserAccountReportData().List(ctx, &req.KsAdvertiserAccountReportDataSearchReq)
	return
}

// Add 添加快手账户报表数据
func (c *ksAdvertiserAccountReportDataController) Add(ctx context.Context, req *ad.KsAdvertiserAccountReportDataAddReq) (res *ad.KsAdvertiserAccountReportDataAddRes, err error) {
	err = service.KsAdvertiserAccountReportData().Add(ctx, req.KsAdvertiserAccountReportDataAddReq)
	return
}

// RunSyncKsAccountReportData 快手账户报表数据任务
func (c *ksAdvertiserAccountReportDataController) RunSyncKsAccountReportData(ctx context.Context, req *ad.KsAdvertiserAccountReportDataTaskReq) (res *ad.KsAdvertiserAccountReportDataTaskRes, err error) {
	err = service.KsAdvertiserAccountReportData().RunSyncKsAccountReportData(ctx, &req.KsAdvertiserAccountReportDataSearchReq)
	return
}
