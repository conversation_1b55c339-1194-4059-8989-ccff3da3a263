// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2025-08-23 17:40:24
// 生成路径: internal/app/ad/logic/ks_advertiser_strategy_task_unit.go
// 生成人：cyao
// desc:快手广告搭建-任务-广告组
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	"github.com/tiger1103/gfast/v3/library/liberr"
)

func init() {
	service.RegisterKsAdvertiserStrategyTaskUnit(New())
}

func New() service.IKsAdvertiserStrategyTaskUnit {
	return &sKsAdvertiserStrategyTaskUnit{}
}

type sKsAdvertiserStrategyTaskUnit struct{}

func (s *sKsAdvertiserStrategyTaskUnit) List(ctx context.Context, req *model.KsAdvertiserStrategyTaskUnitSearchReq) (listRes *model.KsAdvertiserStrategyTaskUnitSearchRes, err error) {
	listRes = new(model.KsAdvertiserStrategyTaskUnitSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.KsAdvertiserStrategyTaskUnit.Ctx(ctx).WithAll()
		if req.TaskUnitId != "" {
			m = m.Where(dao.KsAdvertiserStrategyTaskUnit.Columns().TaskUnitId+" = ?", req.TaskUnitId)
		}
		if req.UnitId != "" {
			m = m.Where(dao.KsAdvertiserStrategyTaskUnit.Columns().UnitId+" = ?", gconv.Int64(req.UnitId))
		}
		if req.UnitName != "" {
			m = m.Where(dao.KsAdvertiserStrategyTaskUnit.Columns().UnitName+" like ?", "%"+req.UnitName+"%")
		}
		if req.TaskId != "" {
			m = m.Where(dao.KsAdvertiserStrategyTaskUnit.Columns().TaskId+" = ?", req.TaskId)
		}
		if req.TaskCampaignId != "" {
			m = m.Where(dao.KsAdvertiserStrategyTaskUnit.Columns().TaskCampaignId+" = ?", req.TaskCampaignId)
		}
		if req.AdvertiserId != "" {
			m = m.Where(dao.KsAdvertiserStrategyTaskUnit.Columns().AdvertiserId+" = ?", req.AdvertiserId)
		}
		if req.AdvertiserNick != "" {
			m = m.Where(dao.KsAdvertiserStrategyTaskUnit.Columns().AdvertiserNick+" = ?", req.AdvertiserNick)
		}
		if req.ExternalAction != "" {
			m = m.Where(dao.KsAdvertiserStrategyTaskUnit.Columns().ExternalAction+" = ?", req.ExternalAction)
		}
		if req.ErrMsg != "" {
			m = m.Where(dao.KsAdvertiserStrategyTaskUnit.Columns().ErrMsg+" = ?", req.ErrMsg)
		}
		if req.Status != "" {
			m = m.Where(dao.KsAdvertiserStrategyTaskUnit.Columns().Status+" = ?", req.Status)
		}
		if len(req.DateRange) != 0 {
			m = m.Where(dao.KsAdvertiserStrategyTaskUnit.Columns().CreatedAt+" >=? AND "+dao.KsAdvertiserStrategyTaskUnit.Columns().CreatedAt+" <=?", req.DateRange[0], req.DateRange[1])
		}
		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "task_unit_id asc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		var res []*model.KsAdvertiserStrategyTaskUnitListRes
		err = m.Page(req.PageNum, req.PageSize).Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		listRes.List = make([]*model.KsAdvertiserStrategyTaskUnitListRes, len(res))
		for k, v := range res {
			listRes.List[k] = &model.KsAdvertiserStrategyTaskUnitListRes{
				TaskUnitId:     v.TaskUnitId,
				UnitId:         v.UnitId,
				UnitName:       v.UnitName,
				TaskId:         v.TaskId,
				TaskCampaignId: v.TaskCampaignId,
				AdvertiserId:   v.AdvertiserId,
				AdvertiserNick: v.AdvertiserNick,
				ExternalAction: v.ExternalAction,
				ErrMsg:         v.ErrMsg,
				Status:         v.Status,
				UnitData:       v.UnitData,
				CreatedAt:      v.CreatedAt,
			}
		}
	})
	return
}

func (s *sKsAdvertiserStrategyTaskUnit) GetByTaskUnitId(ctx context.Context, taskUnitId string) (res *model.KsAdvertiserStrategyTaskUnitInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.KsAdvertiserStrategyTaskUnit.Ctx(ctx).WithAll().Where(dao.KsAdvertiserStrategyTaskUnit.Columns().TaskUnitId, taskUnitId).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sKsAdvertiserStrategyTaskUnit) Add(ctx context.Context, req *model.KsAdvertiserStrategyTaskUnitAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.KsAdvertiserStrategyTaskUnit.Ctx(ctx).Insert(do.KsAdvertiserStrategyTaskUnit{
			TaskUnitId:     req.TaskUnitId,
			UnitId:         req.UnitId,
			UnitName:       req.UnitName,
			TaskId:         req.TaskId,
			TaskCampaignId: req.TaskCampaignId,
			AdvertiserId:   req.AdvertiserId,
			AdvertiserNick: req.AdvertiserNick,
			ExternalAction: req.ExternalAction,
			ErrMsg:         req.ErrMsg,
			Status:         req.Status,
			UnitData:       req.UnitData,
		})
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sKsAdvertiserStrategyTaskUnit) Edit(ctx context.Context, req *model.KsAdvertiserStrategyTaskUnitEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.KsAdvertiserStrategyTaskUnit.Ctx(ctx).WherePri(req.TaskUnitId).Update(do.KsAdvertiserStrategyTaskUnit{
			UnitId:         req.UnitId,
			UnitName:       req.UnitName,
			TaskId:         req.TaskId,
			TaskCampaignId: req.TaskCampaignId,
			AdvertiserId:   req.AdvertiserId,
			AdvertiserNick: req.AdvertiserNick,
			ExternalAction: req.ExternalAction,
			ErrMsg:         req.ErrMsg,
			Status:         req.Status,
			UnitData:       req.UnitData,
		})
		liberr.ErrIsNil(ctx, err, "修改失败")
	})
	return
}

func (s *sKsAdvertiserStrategyTaskUnit) Delete(ctx context.Context, taskUnitIds []string) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.KsAdvertiserStrategyTaskUnit.Ctx(ctx).Delete(dao.KsAdvertiserStrategyTaskUnit.Columns().TaskUnitId+" in (?)", taskUnitIds)
		liberr.ErrIsNil(ctx, err, "删除失败")
	})
	return
}
