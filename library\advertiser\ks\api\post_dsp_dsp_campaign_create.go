package api

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"time"
)

// CreateCampaignService  创建广告计划
type CreateCampaignService struct {
	ctx     context.Context
	cfg     *Configuration
	token   string
	Request *CreateCampaignRequest
}

// CreateCampaignRequest 创建广告计划 Req
type CreateCampaignRequest struct {
	// AdvertiserID 账号ID，在获取 access_token 的时候返回
	AdvertiserID uint64 `json:"advertiser_id,omitempty"`
	// CampaignName 广告计划名称
	// 长度为 1-100 个字符，同一个账号下面计划名称不能重复
	CampaignName string `json:"campaign_name,omitempty"`
	// Type 营销目标类型
	// 2：提升应用安装；3：获取电商下单；4：推广品牌活动；5：收集销售线索；7：提高应用活跃；9：商品库推广（此营销目标下创建的默认为DPA广告）；16：粉丝/直播推广；19：小程序推广 30：快手号-短剧推广
	Type int `json:"type,omitempty"`
	// DayBudget 单日预算金额
	// 单位：厘，指定 0 表示预算不限，默认为 0；不小于 500 元，不超过 100000000 元，仅支持输入数字；修改预算不得低于该计划当日花费的 120% 和修改前预算的较小者，与 day_budget_schedule 不能同时传，不能低于该计划下任一广告组出价。当 bid_type = 1时，day_budget 或者 budget_schedule 二选一必填
	DayBudget int64 `json:"day_budget,omitempty"`
	// DayBudgetSchedule 分日预算
	// 单位：厘，指定 0 表示预算不限，默认为 0；每天不小于 500 元，不超过 100000000 元，仅支持输入数字；修改预算不得低于该计划当日花费的 120% 和修改前预算的较小者，与 day_budget 不能同时传，均不能低于该计划下任一广告组出价。事例：时间周期为周一到周日，样例："day_budget_schedule":[11110000,22220000,0,0,0,0,0]，优先级高于day_budget。当 bid_type = 1时，day_budget 或者 budget_schedule 二选一必填
	DayBudgetSchedule []int64 `json:"day_budget_schedule,omitempty"`
	// AdType 0:信息流，1:搜索；不填默认信息流
	AdType int `json:"ad_type,omitempty"`
	// BidType 出价类型; 0:默认1:最大转化（添加后不可修改）
	BidType int `json:"bid_type,omitempty"`
	// AutoAdjust 自动调控开关
	// 0：关闭，1：开启
	AutoAdjust int `json:"auto_adjust,omitempty"`
	// AutoBuild 自动基建开关
	// 0：关闭，1：开启
	AutoBuild int `json:"auto_build,omitempty"`
	// AutoBuildNameRule 自动基建命名规则
	// 仅在auto_build为1时，此字段生效，开启自动基建时必填命名规则，组、创意命名规则均必须同时包含[日期]和[序号]宏变量【注：白名单功能】
	AutoBuildNameRule *AutoBuildNameRule `json:"auto_build_name_rule,omitempty"`
	// CapRoiRatio cost cap的roi约束(广告成本约束-ROI约束)
	// 默认0表示“不限”，新建计划时仅支持“不限”，当计划下存在广告组选择ROI出价，编辑时支持修为该约束
	CapRoiRatio float64 `json:"cap_roi_ratio,omitempty"`
	//  CapBid cost cap的成本约束(广告成本约束-非ROI单/双约束)
	// 默认0表示“不限”，新建计划时仅支持“不限”，当计划下存在广告组选择非ROI出价，编辑时支持修为该约束
	CapBid int64 `json:"cap_bid,omitempty"`
	// ConstraintCpa 浅层成本约束(广告成本约束-非ROI双约束)
	// 默认0表示“不限”，新建计划时仅支持“不限”，当计划下存在广告组选择自然日次日留存，编辑时支持修为该约束
	ConstraintCpa int64 `json:"constraint_cpa,omitempty"`
	// AutoManage 全自动投放开关 (UAX，支持UAA和UAL)
	// 0：关闭，1：开启 【注：此字段设置为开启时， 需要开启auto_adjust和auto_build 字段】 根据行业区分UAA&UAL具体详情可联系对应运营加白
	AutoManage int `json:"auto_manage,omitempty"`
	//auto_photo_scope 0：系统优选，系统根据历史数据挑选效果最优视频，1-素材包，系统将挑选您配置的素材包内的视频
	AutoPhotoScope int `json:"auto_photo_scope,omitempty"`
	//素材包ID，当auto_photo_scope=1时必填
	PhotoPackageInfo []int64 `json:"photo_package_info,omitempty"`
}

type CreateCampaignResp struct {
	Code    int                 `json:"code"`    // 状态码
	Message string              `json:"message"` // 返回信息
	Data    *CreateCampaignData `json:"data"`    // 详情
}

type CreateCampaignData struct {
	// CampaignID 广告计划 ID
	CampaignID uint64 `json:"campaign_id"`
}

func (r *CreateCampaignService) SetCfg(cfg *Configuration) *CreateCampaignService {
	r.cfg = cfg
	return r
}

func (r *CreateCampaignService) SetReq(req CreateCampaignRequest) *CreateCampaignService {
	r.Request = &req
	return r
}

func (r *CreateCampaignService) AccessToken(accessToken string) *CreateCampaignService {
	r.token = accessToken
	return r
}

func (r *CreateCampaignService) Do() (data *CreateCampaignResp, err error) {
	localBasePath := r.cfg.BasePath
	localVarPath := localBasePath + "/rest/openapi/gw/dsp/campaign/create"
	response, err := r.cfg.HTTPClient.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("Access-Token", r.token).
		SetBody(r.Request).
		SetResult(&CreateCampaignResp{}).
		Post(localVarPath)
	if err != nil {
		return nil, err
	}
	resp := new(CreateCampaignResp)
	// 将 JSON 响应解码到结构体中
	err = json.Unmarshal(response.Body(), &resp)
	if resp.Code != 0 {
		return resp, errors.New(fmt.Sprintf("/rest/openapi/gw/dsp/campaign/create响应出错: %v\n", resp.Message))
	}
	if err != nil {
		if strings.Contains(err.Error(), "文件操作过于频繁") {
			time.Sleep(200 * time.Millisecond)
			return r.Do()
		}
		return nil, errors.New(fmt.Sprintf("/rest/openapi/gw/dsp/campaign/create解析响应出错: %v\n", err))
	}
	if resp.Code == 0 && resp.Data != nil {
		return resp, nil
	} else {
		return resp, errors.New(resp.Message)
	}
}
