package api

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
)

// QueryAccountBudgetService 账户日预算查询
type QueryAccountBudgetService struct {
	ctx     context.Context
	cfg     *Configuration
	token   string
	Request *QueryAccountBudgetReq
}

// QueryAccountBudgetReq 请求结构体
type QueryAccountBudgetReq struct {
	AdvertiserId int64 `json:"advertiser_id"` // 账号id
}

// QueryAccountBudgetResp API响应结构体
type QueryAccountBudgetResp struct {
	DayBudget         int64   `json:"day_budget"`
	DayBudgetSchedule []int64 `json:"day_budget_schedule"`
}

func (r *QueryAccountBudgetService) SetCfg(cfg *Configuration) *QueryAccountBudgetService {
	r.cfg = cfg
	return r
}

func (r *QueryAccountBudgetService) SetReq(req QueryAccountBudgetReq) *QueryAccountBudgetService {
	r.Request = &req
	return r
}

func (r *QueryAccountBudgetService) AccessToken(accessToken string) *QueryAccountBudgetService {
	r.token = accessToken
	return r
}

func (r *QueryAccountBudgetService) Do() (data *KsBaseResp[QueryAccountBudgetResp], err error) {
	localBasePath := r.cfg.BasePath
	localVarPath := localBasePath + "/rest/openapi/v1/advertiser/budget/get"
	response, err := r.cfg.HTTPClient.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("Access-Token", r.token).
		SetBody(r.Request).
		SetResult(&KsBaseResp[QueryAccountBudgetResp]{}).
		Post(localVarPath)
	if err != nil {
		return nil, err
	}
	resp := new(KsBaseResp[QueryAccountBudgetResp])
	// 将 JSON 响应解码到结构体中
	err = json.Unmarshal(response.Body(), &resp)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("/rest/openapi/v1/advertiser/budget/get解析响应出错: %v\n", err))
	}
	if resp.Code == 0 && resp.Data != nil {
		return resp, nil
	} else {
		return resp, errors.New(resp.Message)
	}
}
