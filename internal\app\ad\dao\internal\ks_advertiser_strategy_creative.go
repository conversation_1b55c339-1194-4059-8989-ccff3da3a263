// ==========================================================================
// GFast自动生成dao internal操作代码。
// 生成日期：2025-08-22 11:52:17
// 生成路径: internal/app/ad/dao/internal/ks_advertiser_strategy_creative.go
// 生成人：cq
// desc:快手策略组-广告创意
// company:云南奇讯科技有限公司
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// KsAdvertiserStrategyCreativeDao is the manager for logic model data accessing and custom defined data operations functions management.
type KsAdvertiserStrategyCreativeDao struct {
	table   string                              // Table is the underlying table name of the DAO.
	group   string                              // Group is the database configuration group name of current DAO.
	columns KsAdvertiserStrategyCreativeColumns // Columns is the short type for Columns, which contains all the column names of Table for convenient usage.
}

// KsAdvertiserStrategyCreativeColumns defines and stores column names for table ks_advertiser_strategy_creative.
type KsAdvertiserStrategyCreativeColumns struct {
	Id               string // 主键ID
	StrategyId       string // 策略组ID
	TaskId           string // 任务ID
	CreateMode       string // 创意制作方式 1：自动化创意 2：程序化3.0
	ActionBarText    string // 行动号召
	NewExposeTag     string // 推荐理由（选填）
	OuterLoopNative  string // 是否开启原生 1开启、0关闭｜不填则默认为0
	KolUserId        string // 达人id 开启原生场景下必传，计划 campaignType=30 短剧推广时，值为短剧作者ID
	KolUserType      string // 达人类型 1普通快手号原生，2服务号原生，3聚星达人（需加白使用）
	CreativeCategory string // 创意分类
	CreativeTag      string // 创意标签
	CreativeName     string // 创意名称
	TrackUrlSwitch   string // 监测链接开关 1:开启，0:关闭
	CreatedAt        string // 创建时间
	UpdatedAt        string // 更新时间
	DeletedAt        string // 删除时间
}

var ksAdvertiserStrategyCreativeColumns = KsAdvertiserStrategyCreativeColumns{
	Id:               "id",
	StrategyId:       "strategy_id",
	TaskId:           "task_id",
	CreateMode:       "create_mode",
	ActionBarText:    "action_bar_text",
	NewExposeTag:     "new_expose_tag",
	OuterLoopNative:  "outer_loop_native",
	KolUserId:        "kol_user_id",
	KolUserType:      "kol_user_type",
	CreativeCategory: "creative_category",
	CreativeTag:      "creative_tag",
	CreativeName:     "creative_name",
	TrackUrlSwitch:   "track_url_switch",
	CreatedAt:        "created_at",
	UpdatedAt:        "updated_at",
	DeletedAt:        "deleted_at",
}

// NewKsAdvertiserStrategyCreativeDao creates and returns a new DAO object for table data access.
func NewKsAdvertiserStrategyCreativeDao() *KsAdvertiserStrategyCreativeDao {
	return &KsAdvertiserStrategyCreativeDao{
		group:   "default",
		table:   "ks_advertiser_strategy_creative",
		columns: ksAdvertiserStrategyCreativeColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *KsAdvertiserStrategyCreativeDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *KsAdvertiserStrategyCreativeDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *KsAdvertiserStrategyCreativeDao) Columns() KsAdvertiserStrategyCreativeColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *KsAdvertiserStrategyCreativeDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *KsAdvertiserStrategyCreativeDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *KsAdvertiserStrategyCreativeDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
