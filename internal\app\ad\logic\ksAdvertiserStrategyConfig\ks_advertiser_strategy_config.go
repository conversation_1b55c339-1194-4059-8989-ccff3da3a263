// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2025-08-22 11:51:48
// 生成路径: internal/app/ad/logic/ks_advertiser_strategy_config.go
// 生成人：cq
// desc:快手广告搭建-策略配置
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"
	systemModel "github.com/tiger1103/gfast/v3/internal/app/system/model"
	sysService "github.com/tiger1103/gfast/v3/internal/app/system/service"
	"github.com/tiger1103/gfast/v3/library/libUtils"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	"github.com/tiger1103/gfast/v3/library/liberr"
)

func init() {
	service.RegisterKsAdvertiserStrategyConfig(New())
}

func New() service.IKsAdvertiserStrategyConfig {
	return &sKsAdvertiserStrategyConfig{}
}

type sKsAdvertiserStrategyConfig struct{}

func (s *sKsAdvertiserStrategyConfig) List(ctx context.Context, req *model.KsAdvertiserStrategyConfigSearchReq) (listRes *model.KsAdvertiserStrategyConfigSearchRes, err error) {
	listRes = new(model.KsAdvertiserStrategyConfigSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		userInfo := sysService.Context().GetLoginUser(ctx)
		userIds, admin, _ := sysService.SysUser().GetContainUser(ctx, &systemModel.ContextUser{
			LoginUserRes: &systemModel.LoginUserRes{
				Id:     userInfo.Id,
				DeptId: userInfo.DeptId,
			},
		})
		m := dao.KsAdvertiserStrategyConfig.Ctx(ctx).WithAll().
			Where(dao.KsAdvertiserStrategyConfig.Columns().TaskId, "")
		if !admin && len(userIds) > 0 {
			m = m.WhereIn(dao.KsAdvertiserStrategyConfig.Columns().UserId, userIds)
		}
		if req.StrategyName != "" {
			m = m.Where(dao.KsAdvertiserStrategyConfig.Columns().StrategyName+" like ?", "%"+req.StrategyName+"%")
		}
		if req.AdType != "" {
			m = m.Where(dao.KsAdvertiserStrategyConfig.Columns().AdType+" = ?", gconv.Int(req.AdType))
		}
		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "id desc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		var res []*model.KsAdvertiserStrategyConfigListRes
		err = m.Page(req.PageNum, req.PageSize).Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		listRes.List = make([]*model.KsAdvertiserStrategyConfigListRes, len(res))
		for k, v := range res {
			listRes.List[k] = &model.KsAdvertiserStrategyConfigListRes{
				Id:               v.Id,
				StrategyId:       v.StrategyId,
				StrategyName:     v.StrategyName,
				StrategyDescribe: v.StrategyDescribe,
				TaskId:           v.TaskId,
				AdvertiserIds:    v.AdvertiserIds,
				AccountBatchRule: v.AccountBatchRule,
				AdGroupRule:      v.AdGroupRule,
				AdType:           v.AdType,
				UserId:           v.UserId,
				CreatedAt:        v.CreatedAt,
			}
		}
	})
	return
}

func (s *sKsAdvertiserStrategyConfig) GetById(ctx context.Context, id int64) (res *model.KsAdvertiserStrategyConfigInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.KsAdvertiserStrategyConfig.Ctx(ctx).WithAll().Where(dao.KsAdvertiserStrategyConfig.Columns().Id, id).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sKsAdvertiserStrategyConfig) GetInfoById(ctx context.Context, strategyId string, taskId string) (res *model.KsAdvertiserStrategyRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		if strategyId == "" && taskId == "" {
			liberr.ErrIsNil(ctx, err, "策略组ID或任务ID不能为空")
		}
		var ruleConfig *model.KsAdvertiserStrategyConfigInfoRes
		m := dao.KsAdvertiserStrategyConfig.Ctx(ctx).WithAll()
		if strategyId != "" {
			m = m.Where(dao.KsAdvertiserStrategyConfig.Columns().StrategyId, strategyId)
		}
		if taskId != "" {
			m = m.Where(dao.KsAdvertiserStrategyConfig.Columns().TaskId, taskId)
		}
		err = m.Scan(&ruleConfig)
		liberr.ErrIsNil(ctx, err, "获取规则配置信息失败")
		res = &model.KsAdvertiserStrategyRes{
			RuleConfig: ruleConfig,
		}
		res.CampaignConfig, err = service.KsAdvertiserStrategyCampaign().GetInfoById(ctx, strategyId, taskId)
		liberr.ErrIsNil(ctx, err, "获取广告计划信息失败")
		res.UnitConfig, err = service.KsAdvertiserStrategyUnit().GetInfoById(ctx, strategyId, taskId)
		liberr.ErrIsNil(ctx, err, "获取广告组信息失败")
		res.CreativeConfig, err = service.KsAdvertiserStrategyCreative().GetInfoById(ctx, strategyId, taskId)
		liberr.ErrIsNil(ctx, err, "获取创意信息失败")
		res.MaterialConfig, err = service.KsAdvertiserStrategyMaterial().GetInfoById(ctx, strategyId, taskId)
		liberr.ErrIsNil(ctx, err, "获取素材信息失败")
		res.TitleConfig, err = service.KsAdvertiserStrategyTitle().GetInfoById(ctx, strategyId, taskId)
		liberr.ErrIsNil(ctx, err, "获取文案信息失败")
	})
	return
}

func (s *sKsAdvertiserStrategyConfig) Add(ctx context.Context, req *model.KsAdvertiserStrategyAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		var taskId string
		var strategyId string
		if req.RuleConfig.TaskId != "" {
			taskId = req.RuleConfig.TaskId
		} else {
			strategyId = libUtils.GenerateID()
		}
		userId := sysService.Context().GetUserId(ctx)
		// 1. 保存策略配置
		if req.RuleConfig != nil {
			_, err = dao.KsAdvertiserStrategyConfig.Ctx(ctx).Insert(do.KsAdvertiserStrategyConfig{
				StrategyId:       strategyId,
				StrategyName:     req.RuleConfig.StrategyName,
				StrategyDescribe: req.RuleConfig.StrategyDescribe,
				TaskId:           req.RuleConfig.TaskId,
				AdvertiserIds:    req.RuleConfig.AdvertiserIds,
				AccountBatchRule: req.RuleConfig.AccountBatchRule,
				AdGroupRule:      req.RuleConfig.AdGroupRule,
				AdType:           req.RuleConfig.AdType,
				UserId:           int(userId),
			})
			liberr.ErrIsNil(ctx, err, "保存策略配置失败")
		}
		// 2. 保存广告计划配置
		if req.CampaignConfig != nil {
			req.CampaignConfig.StrategyId = strategyId
			req.CampaignConfig.TaskId = taskId
			err = service.KsAdvertiserStrategyCampaign().Add(ctx, req.CampaignConfig)
			liberr.ErrIsNil(ctx, err, "保存广告计划配置失败")
		}
		// 3. 保存广告组配置
		if req.UnitConfig != nil {
			req.UnitConfig.StrategyId = strategyId
			req.UnitConfig.TaskId = taskId
			err = service.KsAdvertiserStrategyUnit().Add(ctx, req.UnitConfig)
			liberr.ErrIsNil(ctx, err, "保存广告组配置失败")
		}
		// 4. 保存创意配置
		if req.CreativeConfig != nil {
			req.CreativeConfig.StrategyId = strategyId
			req.CreativeConfig.TaskId = taskId
			err = service.KsAdvertiserStrategyCreative().Add(ctx, req.CreativeConfig)
			liberr.ErrIsNil(ctx, err, "保存创意配置失败")
		}
		// 5. 保存素材配置
		if req.MaterialConfig != nil {
			req.MaterialConfig.StrategyId = strategyId
			req.MaterialConfig.TaskId = taskId
			err = service.KsAdvertiserStrategyMaterial().Add(ctx, req.MaterialConfig)
			liberr.ErrIsNil(ctx, err, "保存素材配置失败")
		}
		// 6. 保存文案配置
		if req.TitleConfig != nil {
			req.TitleConfig.StrategyId = strategyId
			req.TitleConfig.TaskId = taskId
			err = service.KsAdvertiserStrategyTitle().Add(ctx, req.TitleConfig)
			liberr.ErrIsNil(ctx, err, "保存文案配置失败")
		}
	})
	return
}

func (s *sKsAdvertiserStrategyConfig) Edit(ctx context.Context, req *model.KsAdvertiserStrategyEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		strategyId := req.RuleConfig.StrategyId
		_, err = dao.KsAdvertiserStrategyConfig.Ctx(ctx).
			Where(dao.KsAdvertiserStrategyConfig.Columns().StrategyId, strategyId).
			Update(do.KsAdvertiserStrategyConfig{
				StrategyName:     req.RuleConfig.StrategyName,
				StrategyDescribe: req.RuleConfig.StrategyDescribe,
				TaskId:           req.RuleConfig.TaskId,
				AdvertiserIds:    req.RuleConfig.AdvertiserIds,
				AccountBatchRule: req.RuleConfig.AccountBatchRule,
				AdGroupRule:      req.RuleConfig.AdGroupRule,
				AdType:           req.RuleConfig.AdType,
			})
		liberr.ErrIsNil(ctx, err, "修改失败")
		// 修改广告计划配置
		req.CampaignConfig.StrategyId = strategyId
		err = service.KsAdvertiserStrategyCampaign().Edit(ctx, req.CampaignConfig)
		liberr.ErrIsNil(ctx, err, "修改广告计划配置失败")
		// 修改广告组配置
		req.UnitConfig.StrategyId = strategyId
		err = service.KsAdvertiserStrategyUnit().Edit(ctx, req.UnitConfig)
		liberr.ErrIsNil(ctx, err, "修改广告组配置失败")
		// 修改创意配置
		req.CreativeConfig.StrategyId = strategyId
		err = service.KsAdvertiserStrategyCreative().Edit(ctx, req.CreativeConfig)
		liberr.ErrIsNil(ctx, err, "修改创意配置失败")
		// 修改素材配置
		req.MaterialConfig.StrategyId = strategyId
		err = service.KsAdvertiserStrategyMaterial().Edit(ctx, req.MaterialConfig)
		liberr.ErrIsNil(ctx, err, "修改素材配置失败")
		// 修改文案配置
		req.TitleConfig.StrategyId = strategyId
		err = service.KsAdvertiserStrategyTitle().Edit(ctx, req.TitleConfig)
		liberr.ErrIsNil(ctx, err, "修改文案配置失败")
	})
	return
}

func (s *sKsAdvertiserStrategyConfig) Delete(ctx context.Context, strategyIds []string) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.KsAdvertiserStrategyConfig.Ctx(ctx).Delete(dao.KsAdvertiserStrategyConfig.Columns().StrategyId+" in (?)", strategyIds)
		liberr.ErrIsNil(ctx, err, "删除失败")
	})
	return
}

func (s *sKsAdvertiserStrategyConfig) Copy(ctx context.Context, strategyId string) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		adStrategyConfig, err1 := s.GetInfoById(ctx, strategyId, "")
		liberr.ErrIsNil(ctx, err1, "获取策略组信息失败")
		var addReq *model.KsAdvertiserStrategyAddReq
		err2 := gconv.Struct(adStrategyConfig, &addReq)
		liberr.ErrIsNil(ctx, err2)
		addReq.RuleConfig.StrategyName = adStrategyConfig.RuleConfig.StrategyName + "-复制"
		err3 := s.Add(ctx, addReq)
		liberr.ErrIsNil(ctx, err3, "复制失败")
	})
	return
}
