// ==========================================================================
// GFast自动生成model操作代码。
// 生成日期：2025-08-22 11:52:42
// 生成路径: internal/app/ad/model/ks_advertiser_strategy_title.go
// 生成人：cq
// desc:快手策略组-文案
// company:云南奇讯科技有限公司
// ==========================================================================

package model

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
	commonConst "github.com/tiger1103/gfast/v3/internal/app/common/consts"
	comModel "github.com/tiger1103/gfast/v3/internal/app/common/model"
)

// KsAdvertiserStrategyTitleInfoRes is the golang structure for table ks_advertiser_strategy_title.
type KsAdvertiserStrategyTitleInfoRes struct {
	gmeta.Meta            `orm:"table:ks_advertiser_strategy_title"`
	Id                    uint64                                 `orm:"id,primary" json:"id" dc:"主键ID"`                                             // 主键ID
	StrategyId            string                                 `orm:"strategy_id" json:"strategyId" dc:"策略组ID"`                                   // 策略组ID
	TaskId                string                                 `orm:"task_id" json:"taskId" dc:"任务ID"`                                            // 任务ID
	TitleAllocationMethod commonConst.KsTitleAllocationMethod    `orm:"title_allocation_method" json:"titleAllocationMethod" dc:"标题分配方式 AUTO TEST"` // 标题分配方式 AUTO TEST
	TitleData             []*KsAdvertiserCommonAssetTitleInfoRes `orm:"title_data" json:"titleData" dc:"标题数据，包含标题详细信息、分类、性能数据等"`                    // 标题数据，包含标题详细信息、分类、性能数据等
	CreatedAt             *gtime.Time                            `orm:"created_at" json:"createdAt" dc:"创建时间"`                                      // 创建时间
	UpdatedAt             *gtime.Time                            `orm:"updated_at" json:"updatedAt" dc:"更新时间"`                                      // 更新时间
	DeletedAt             *gtime.Time                            `orm:"deleted_at" json:"deletedAt" dc:"删除时间"`                                      // 删除时间
}

type KsAdvertiserStrategyTitleListRes struct {
	Id                    uint64                                 `json:"id" dc:"主键ID"`
	StrategyId            string                                 `json:"strategyId" dc:"策略组ID"`
	TaskId                string                                 `json:"taskId" dc:"任务ID"`
	TitleAllocationMethod commonConst.KsTitleAllocationMethod    `json:"titleAllocationMethod" dc:"标题分配方式 AUTO TEST"`
	TitleData             []*KsAdvertiserCommonAssetTitleInfoRes `json:"titleData" dc:"标题数据，包含标题详细信息、分类、性能数据等"`
	CreatedAt             *gtime.Time                            `json:"createdAt" dc:"创建时间"`
}

// KsAdvertiserStrategyTitleSearchReq 分页请求参数
type KsAdvertiserStrategyTitleSearchReq struct {
	comModel.PageReq
	Id                    string                                 `p:"id" dc:"主键ID"`                                                           //主键ID
	StrategyId            string                                 `p:"strategyId" dc:"策略组ID"`                                                  //策略组ID
	TaskId                string                                 `p:"taskId" dc:"任务ID"`                                                       //任务ID
	TitleAllocationMethod commonConst.KsTitleAllocationMethod    `p:"titleAllocationMethod" dc:"标题分配方式 AUTO TEST"`                            //标题分配方式 AUTO TEST
	TitleData             []*KsAdvertiserCommonAssetTitleInfoRes `p:"titleData" dc:"标题数据，包含标题详细信息、分类、性能数据等"`                                  //标题数据，包含标题详细信息、分类、性能数据等
	CreatedAt             string                                 `p:"createdAt" v:"createdAt@datetime#创建时间需为YYYY-MM-DD hh:mm:ss格式" dc:"创建时间"` //创建时间
}

// KsAdvertiserStrategyTitleSearchRes 列表返回结果
type KsAdvertiserStrategyTitleSearchRes struct {
	comModel.ListRes
	List []*KsAdvertiserStrategyTitleListRes `json:"list"`
}

// KsAdvertiserStrategyTitleAddReq 添加操作请求参数
type KsAdvertiserStrategyTitleAddReq struct {
	StrategyId            string                                 `p:"-"  dc:"策略组ID"`
	TaskId                string                                 `p:"-"  dc:"任务ID"`
	TitleAllocationMethod commonConst.KsTitleAllocationMethod    `p:"titleAllocationMethod"  dc:"标题分配方式 AUTO TEST"`
	TitleData             []*KsAdvertiserCommonAssetTitleInfoRes `p:"titleData"  dc:"标题数据，包含标题详细信息、分类、性能数据等"`
}

// KsAdvertiserStrategyTitleEditReq 修改操作请求参数
type KsAdvertiserStrategyTitleEditReq struct {
	StrategyId            string                                 `p:"strategyId"  dc:"策略组ID"`
	TaskId                string                                 `p:"taskId"  dc:"任务ID"`
	TitleAllocationMethod commonConst.KsTitleAllocationMethod    `p:"titleAllocationMethod"  dc:"标题分配方式 AUTO TEST"`
	TitleData             []*KsAdvertiserCommonAssetTitleInfoRes `p:"titleData"  dc:"标题数据，包含标题详细信息、分类、性能数据等"`
}
