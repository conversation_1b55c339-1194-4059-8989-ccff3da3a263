// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2025-08-19 10:36:49
// 生成路径: api/v1/ad/ks_advertiser_common_asset_category.go
// 生成人：cq
// desc:快手通用资产-标题分类相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package ad

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
)

// KsAdvertiserCommonAssetCategorySearchReq 分页请求参数
type KsAdvertiserCommonAssetCategorySearchReq struct {
	g.Meta `path:"/list" tags:"快手通用资产-标题分类" method:"post" summary:"快手通用资产-标题分类列表"`
	commonApi.Author
	model.KsAdvertiserCommonAssetCategorySearchReq
}

// KsAdvertiserCommonAssetCategorySearchRes 列表返回结果
type KsAdvertiserCommonAssetCategorySearchRes struct {
	g.Meta `mime:"application/json"`
	*model.KsAdvertiserCommonAssetCategorySearchRes
}

// KsAdvertiserCommonAssetCategoryAddReq 添加操作请求参数
type KsAdvertiserCommonAssetCategoryAddReq struct {
	g.Meta `path:"/add" tags:"快手通用资产-标题分类" method:"post" summary:"快手通用资产-标题分类添加"`
	commonApi.Author
	*model.KsAdvertiserCommonAssetCategoryAddReq
}

// KsAdvertiserCommonAssetCategoryAddRes 添加操作返回结果
type KsAdvertiserCommonAssetCategoryAddRes struct {
	commonApi.EmptyRes
}

// KsAdvertiserCommonAssetCategoryEditReq 修改操作请求参数
type KsAdvertiserCommonAssetCategoryEditReq struct {
	g.Meta `path:"/edit" tags:"快手通用资产-标题分类" method:"put" summary:"快手通用资产-标题分类修改"`
	commonApi.Author
	*model.KsAdvertiserCommonAssetCategoryEditReq
}

// KsAdvertiserCommonAssetCategoryEditRes 修改操作返回结果
type KsAdvertiserCommonAssetCategoryEditRes struct {
	commonApi.EmptyRes
}

// KsAdvertiserCommonAssetCategoryGetReq 获取一条数据请求
type KsAdvertiserCommonAssetCategoryGetReq struct {
	g.Meta `path:"/get" tags:"快手通用资产-标题分类" method:"get" summary:"获取快手通用资产-标题分类信息"`
	commonApi.Author
	Id int `p:"id" v:"required#主键必须"` //通过主键获取
}

// KsAdvertiserCommonAssetCategoryGetRes 获取一条数据结果
type KsAdvertiserCommonAssetCategoryGetRes struct {
	g.Meta `mime:"application/json"`
	*model.KsAdvertiserCommonAssetCategoryInfoRes
}

// KsAdvertiserCommonAssetCategoryDeleteReq 删除数据请求
type KsAdvertiserCommonAssetCategoryDeleteReq struct {
	g.Meta `path:"/delete" tags:"快手通用资产-标题分类" method:"post" summary:"删除快手通用资产-标题分类"`
	commonApi.Author
	Ids []int `p:"ids" v:"required#主键必须"` //通过主键删除
}

// KsAdvertiserCommonAssetCategoryDeleteRes 删除数据返回
type KsAdvertiserCommonAssetCategoryDeleteRes struct {
	commonApi.EmptyRes
}
