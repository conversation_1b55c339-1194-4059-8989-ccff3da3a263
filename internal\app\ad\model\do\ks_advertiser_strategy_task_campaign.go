// ==========================================================================
// GFast自动生成model entity操作代码。
// 生成日期：2025-08-23 17:40:19
// 生成路径: internal/app/ad/model/entity/ks_advertiser_strategy_task_campaign.go
// 生成人：cyao
// desc:快手广告搭建-任务-广告计划
// company:云南奇讯科技有限公司
// ==========================================================================

package do

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
)

// KsAdvertiserStrategyTaskCampaign is the golang structure for table ks_advertiser_strategy_task_campaign.
type KsAdvertiserStrategyTaskCampaign struct {
	gmeta.Meta     `orm:"table:ks_advertiser_strategy_task_campaign, do:true"`
	TaskCampaignId interface{} `orm:"task_campaign_id,primary" json:"taskCampaignId"` // 任务计划ID
	CampaignId     interface{} `orm:"campaign_id" json:"campaignId"`                  // 计划ID
	CampaignName   interface{} `orm:"campaign_name" json:"campaignName"`              // 任务计划名称
	TaskId         interface{} `orm:"task_id" json:"taskId"`                          // 任务ID
	AdvertiserId   interface{} `orm:"advertiser_id" json:"advertiserId"`              // 广告主ID
	AdvertiserNick interface{} `orm:"advertiser_nick" json:"advertiserNick"`          // 广告主名称
	CampaignData   interface{} `orm:"campaign_data" json:"campaignData"`              // 创建广告计划数据
	Status         interface{} `orm:"status" json:"status"`                           // 状态 等待提交：INIT 已终止：TERMINATED 成功：SUCCESS 失败：ERROR
	CreatedAt      *gtime.Time `orm:"created_at" json:"createdAt"`                    // 创建时间
	UpdatedAt      *gtime.Time `orm:"updated_at" json:"updatedAt"`                    // 更新时间
	DeletedAt      *gtime.Time `orm:"deleted_at" json:"deletedAt"`                    // 删除时间
}
