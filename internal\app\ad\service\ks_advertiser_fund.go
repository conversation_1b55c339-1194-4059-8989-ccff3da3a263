// ==========================================================================
// GFast自动生成service操作代码。
// 生成日期：2025-08-12 15:45:08
// 生成路径: internal/app/ad/service/ks_advertiser_fund.go
// 生成人：cyao
// desc:广告主资金信息
// company:云南奇讯科技有限公司
// ==========================================================================

package service

import (
	"context"

	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
)

type IKsAdvertiserFund interface {
	List(ctx context.Context, req *model.KsAdvertiserFundSearchReq) (res *model.KsAdvertiserFundSearchRes, err error)
	GetByAdvertiserId(ctx context.Context, AdvertiserId int64) (res *model.KsAdvertiserFundInfoRes, err error)
	Add(ctx context.Context, req *model.KsAdvertiserFundAddReq) (err error)
	Edit(ctx context.Context, req *model.KsAdvertiserFundEditReq) (err error)
	Delete(ctx context.Context, AdvertiserId []int64) (err error)
}

var localKsAdvertiserFund IKsAdvertiserFund

func KsAdvertiserFund() IKsAdvertiserFund {
	if localKsAdvertiserFund == nil {
		panic("implement not found for interface IKsAdvertiserFund, forgot register?")
	}
	return localKsAdvertiserFund
}

func RegisterKsAdvertiserFund(i IKsAdvertiserFund) {
	localKsAdvertiserFund = i
}
