// ==========================================================================
// GFast自动生成service操作代码。
// 生成日期：2025-08-23 17:40:11
// 生成路径: internal/app/ad/service/ks_advertiser_strategy_task.go
// 生成人：cyao
// desc:快手广告搭建-任务
// company:云南奇讯科技有限公司
// ==========================================================================

package service

import (
	"context"

	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
)

type IKsAdvertiserStrategyTask interface {
	List(ctx context.Context, req *model.KsAdvertiserStrategyTaskSearchReq) (res *model.KsAdvertiserStrategyTaskSearchRes, err error)
	GetByTaskId(ctx context.Context, TaskId string) (res *model.KsAdvertiserStrategyTaskInfoRes, err error)
	Add(ctx context.Context, req *model.KsAdvertiserStrategyTaskAddReq) (err error)
	AddTask(ctx context.Context, req *model.AdExecuteTaskReq) (err error)
	Edit(ctx context.Context, req *model.KsAdvertiserStrategyTaskEditReq) (err error)
	Delete(ctx context.Context, TaskId []string) (err error)
	UpdateStatus(ctx context.Context, taskId string, status string) (err error)
}

var localKsAdvertiserStrategyTask IKsAdvertiserStrategyTask

func KsAdvertiserStrategyTask() IKsAdvertiserStrategyTask {
	if localKsAdvertiserStrategyTask == nil {
		panic("implement not found for interface IKsAdvertiserStrategyTask, forgot register?")
	}
	return localKsAdvertiserStrategyTask
}

func RegisterKsAdvertiserStrategyTask(i IKsAdvertiserStrategyTask) {
	localKsAdvertiserStrategyTask = i
}
