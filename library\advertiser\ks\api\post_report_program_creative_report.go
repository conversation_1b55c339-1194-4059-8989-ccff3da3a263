package api

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
)

// ProgramCreativeReportService 程序化创意数据(包含程序化2.0 + 智能创意)
type ProgramCreativeReportService struct {
	ctx     context.Context
	cfg     *Configuration
	token   string
	Request *ProgramCreativeReportReq
}

//	{
//	   "advertiser_id": 75464050,
//	   "start_date": "2025-08-17",
//	   "end_date": "2025-08-18",
//	   "page":33 ,
//	   "page_size":20
//	}

type ProgramCreativeReportReq struct {
	AdvertiserId int64  `json:"advertiser_id"`
	StartDate    string `json:"start_date"`
	EndDate      string `json:"end_date"`
	Page         int    `json:"page"`
	PageSize     int    `json:"page_size"`
}

type ProgramCreativeReportResp struct {
	Code    int                        `json:"code"`
	Message string                     `json:"message"`
	Data    *ProgramCreativeReportData `json:"data"`
}

type ProgramCreativeReportData struct {
	Empty      bool                     `json:"empty"`
	TotalCount int                      `json:"total_count"`
	Details    []*ProgramCreativeReport `json:"details"`
}

type ProgramCreativeReport struct {
	Charge                                           float64 `json:"charge"`
	Show                                             int     `json:"show"`
	Aclick                                           int     `json:"aclick"`
	Bclick                                           int     `json:"bclick"`
	Share                                            int     `json:"share"`
	Comment                                          int     `json:"comment"`
	Like                                             int     `json:"like"`
	Follow                                           int     `json:"follow"`
	Report                                           int     `json:"report"`
	Block                                            int     `json:"block"`
	Negative                                         int     `json:"negative"`
	Activation                                       int     `json:"activation"`
	Submit                                           int     `json:"submit"`
	AdPhotoPlayed10S                                 int     `json:"ad_photo_played_10s"`
	AdPhotoPlayed2S                                  int     `json:"ad_photo_played_2s"`
	AdPhotoPlayed75Percent                           int     `json:"ad_photo_played_75percent"`
	CancelLike                                       int     `json:"cancel_like"`
	ClickConversionRatio                             float64 `json:"click_conversion_ratio"`
	ConversionCost                                   float64 `json:"conversion_cost"`
	ConversionCostByImpression7D                     float64 `json:"conversion_cost_by_impression_7d"`
	ConversionNum                                    int     `json:"conversion_num"`
	ConversionNumByImpression7D                      int     `json:"conversion_num_by_impression_7d"`
	ConversionNumCost                                float64 `json:"conversion_num_cost"`
	ConversionRatio                                  float64 `json:"conversion_ratio"`
	ConversionRatioByImpression7D                    float64 `json:"conversion_ratio_by_impression_7d"`
	DeepConversionCost                               float64 `json:"deep_conversion_cost"`
	DeepConversionCostByImpression7D                 float64 `json:"deep_conversion_cost_by_impression_7d"`
	DeepConversionNum                                int     `json:"deep_conversion_num"`
	DeepConversionNumByImpression7D                  int     `json:"deep_conversion_num_by_impression_7d"`
	DeepConversionRatio                              float64 `json:"deep_conversion_ratio"`
	DeepConversionRatioByImpression7D                float64 `json:"deep_conversion_ratio_by_impression_7d"`
	Event24HStay                                     int     `json:"event_24h_stay"`
	Event24HStayByConversion                         int     `json:"event_24h_stay_by_conversion"`
	Event24HStayByConversionCost                     float64 `json:"event_24h_stay_by_conversion_cost"`
	Event24HStayByConversionRatio                    float64 `json:"event_24h_stay_by_conversion_ratio"`
	Event24HStayCost                                 float64 `json:"event_24h_stay_cost"`
	Event24HStayRatio                                float64 `json:"event_24h_stay_ratio"`
	EventAdWatch10Times                              int     `json:"event_ad_watch_10_times"`
	EventAdWatch10TimesCost                          float64 `json:"event_ad_watch_10_times_cost"`
	EventAdWatch10TimesRatio                         float64 `json:"event_ad_watch_10_times_ratio"`
	EventAdWatch20Times                              int     `json:"event_ad_watch_20_times"`
	EventAdWatch20TimesCost                          float64 `json:"event_ad_watch_20_times_cost"`
	EventAdWatch20TimesRatio                         float64 `json:"event_ad_watch_20_times_ratio"`
	EventAdWatch5Times                               int     `json:"event_ad_watch_5_times"`
	EventAdWatch5TimesCost                           float64 `json:"event_ad_watch_5_times_cost"`
	EventAdWatch5TimesRatio                          float64 `json:"event_ad_watch_5_times_ratio"`
	EventConsultationValidRetained                   int     `json:"event_consultation_valid_retained"`
	EventConsultationValidRetainedCost               float64 `json:"event_consultation_valid_retained_cost"`
	EventConsultationValidRetainedRatio              float64 `json:"event_consultation_valid_retained_ratio"`
	EventConversionClickCost                         float64 `json:"event_conversion_click_cost"`
	EventConversionClickRatio                        float64 `json:"event_conversion_click_ratio"`
	EventCreditGrantFirstDayApp                      int     `json:"event_credit_grant_first_day_app"`
	EventCreditGrantFirstDayAppCost                  float64 `json:"event_credit_grant_first_day_app_cost"`
	EventCreditGrantFirstDayAppRatio                 float64 `json:"event_credit_grant_first_day_app_ratio"`
	EventCreditGrantFirstDayLandingPage              int     `json:"event_credit_grant_first_day_landing_page"`
	EventCreditGrantFirstDayLandingPageCost          float64 `json:"event_credit_grant_first_day_landing_page_cost"`
	EventCreditGrantFirstDayLandingPageRatio         float64 `json:"event_credit_grant_first_day_landing_page_ratio"`
	EventFiveDayStayByConversion                     int     `json:"event_five_day_stay_by_conversion"`
	EventFiveDayStayByConversionCost                 float64 `json:"event_five_day_stay_by_conversion_cost"`
	EventFiveDayStayByConversionRatio                float64 `json:"event_five_day_stay_by_conversion_ratio"`
	EventFourDayStayByConversion                     int     `json:"event_four_day_stay_by_conversion"`
	EventFourDayStayByConversionCost                 float64 `json:"event_four_day_stay_by_conversion_cost"`
	EventFourDayStayByConversionRatio                float64 `json:"event_four_day_stay_by_conversion_ratio"`
	EventPayPurchaseAmountOneDay                     float64 `json:"event_pay_purchase_amount_one_day"`
	EventPayPurchaseAmountOneDayByConversion         float64 `json:"event_pay_purchase_amount_one_day_by_conversion"`
	EventPayPurchaseAmountOneDayByConversionRoi      float64 `json:"event_pay_purchase_amount_one_day_by_conversion_roi"`
	EventPayPurchaseAmountOneDayRoi                  float64 `json:"event_pay_purchase_amount_one_day_roi"`
	EventPayPurchaseAmountThreeDayByConversion       float64 `json:"event_pay_purchase_amount_three_day_by_conversion"`
	EventPayPurchaseAmountThreeDayByConversionRoi    float64 `json:"event_pay_purchase_amount_three_day_by_conversion_roi"`
	EventPayPurchaseAmountWeekByConversion           float64 `json:"event_pay_purchase_amount_week_by_conversion"`
	EventPayPurchaseAmountWeekByConversionRoi        float64 `json:"event_pay_purchase_amount_week_by_conversion_roi"`
	EventPayWeekByConversion                         int     `json:"event_pay_week_by_conversion"`
	EventPayWeekByConversionCost                     float64 `json:"event_pay_week_by_conversion_cost"`
	EventPayWeightedPurchaseAmount                   float64 `json:"event_pay_weighted_purchase_amount"`
	EventPayWeightedPurchaseAmountFirstDay           float64 `json:"event_pay_weighted_purchase_amount_first_day"`
	EventPreComponentConsultationValidRetained       int     `json:"event_pre_component_consultation_valid_retained"`
	EventSixDayStayByConversion                      int     `json:"event_six_day_stay_by_conversion"`
	EventSixDayStayByConversionCost                  float64 `json:"event_six_day_stay_by_conversion_cost"`
	EventSixDayStayByConversionRatio                 float64 `json:"event_six_day_stay_by_conversion_ratio"`
	EventThreeDayStayByConversion                    int     `json:"event_three_day_stay_by_conversion"`
	EventThreeDayStayByConversionCost                float64 `json:"event_three_day_stay_by_conversion_cost"`
	EventThreeDayStayByConversionRatio               float64 `json:"event_three_day_stay_by_conversion_ratio"`
	EventTwoDayStayByConversion                      int     `json:"event_two_day_stay_by_conversion"`
	EventTwoDayStayByConversionCost                  float64 `json:"event_two_day_stay_by_conversion_cost"`
	EventTwoDayStayByConversionRatio                 float64 `json:"event_two_day_stay_by_conversion_ratio"`
	EventWechatQrCodeLinkClick                       int     `json:"event_wechat_qr_code_link_click"`
	EventWeekStay                                    int     `json:"event_week_stay"`
	EventWeekStayByConversion                        int     `json:"event_week_stay_by_conversion"`
	EventWeekStayByConversionCost                    float64 `json:"event_week_stay_by_conversion_cost"`
	EventWeekStayByConversionRatio                   float64 `json:"event_week_stay_by_conversion_ratio"`
	EventWeekStayCost                                float64 `json:"event_week_stay_cost"`
	EventWeekStayRatio                               float64 `json:"event_week_stay_ratio"`
	LiveEventGoodsView                               int     `json:"live_event_goods_view"`
	LivePlayed3S                                     int     `json:"live_played_3s"`
	PlayedEnd                                        int     `json:"played_end"`
	PlayedFiveSeconds                                int     `json:"played_five_seconds"`
	PlayedThreeSeconds                               int     `json:"played_three_seconds"`
	AdScene                                          string  `json:"ad_scene"`
	PlacementType                                    string  `json:"placement_type"`
	CancelFollow                                     int     `json:"cancel_follow"`
	StatDate                                         string  `json:"stat_date"`
	PhotoClick                                       int     `json:"photo_click"`
	PhotoClickRatio                                  float64 `json:"photo_click_ratio"`
	ActionRatio                                      float64 `json:"action_ratio"`
	Impression1KCost                                 float64 `json:"impression_1k_cost"`
	PhotoClickCost                                   float64 `json:"photo_click_cost"`
	Click1KCost                                      float64 `json:"click_1k_cost"`
	ActionCost                                       float64 `json:"action_cost"`
	EventPayFirstDay                                 int     `json:"event_pay_first_day"`
	EventPayPurchaseAmountFirstDay                   float64 `json:"event_pay_purchase_amount_first_day"`
	EventPayFirstDayRoi                              float64 `json:"event_pay_first_day_roi"`
	EventPay                                         int     `json:"event_pay"`
	EventPayPurchaseAmount                           float64 `json:"event_pay_purchase_amount"`
	EventPayPurchaseAmount30DayByConversion          float64 `json:"event_pay_purchase_amount_30_day_by_conversion"`
	EventPayPurchaseAmount30DayByConversionRoi       float64 `json:"event_pay_purchase_amount_30_day_by_conversion_roi"`
	EventPayRoi                                      float64 `json:"event_pay_roi"`
	EventRegister                                    int     `json:"event_register"`
	EventRegisterCost                                float64 `json:"event_register_cost"`
	EventRegisterRatio                               float64 `json:"event_register_ratio"`
	EventJinJianApp                                  int     `json:"event_jin_jian_app"`
	EventJinJianAppCost                              float64 `json:"event_jin_jian_app_cost"`
	EventCreditGrantApp                              int     `json:"event_credit_grant_app"`
	EventCreditGrantAppCost                          float64 `json:"event_credit_grant_app_cost"`
	EventCreditGrantAppRatio                         float64 `json:"event_credit_grant_app_ratio"`
	EventOrderPaid                                   int     `json:"event_order_paid"`
	EventOrderPaidPurchaseAmount                     float64 `json:"event_order_paid_purchase_amount"`
	EventOrderPaidCost                               float64 `json:"event_order_paid_cost"`
	FormCount                                        int     `json:"form_count"`
	FormCost                                         float64 `json:"form_cost"`
	FormActionRatio                                  float64 `json:"form_action_ratio"`
	EventJinJianLandingPage                          int     `json:"event_jin_jian_landing_page"`
	EventJinJianLandingPageCost                      float64 `json:"event_jin_jian_landing_page_cost"`
	EventCreditGrantLandingPage                      int     `json:"event_credit_grant_landing_page"`
	EventCreditGrantLandingPageCost                  float64 `json:"event_credit_grant_landing_page_cost"`
	EventCreditGrantLandingRatio                     float64 `json:"event_credit_grant_landing_ratio"`
	EventNextDayStayCost                             float64 `json:"event_next_day_stay_cost"`
	EventNextDayStayRatio                            float64 `json:"event_next_day_stay_ratio"`
	EventNextDayStay                                 int     `json:"event_next_day_stay"`
	Play3SRatio                                      float64 `json:"play_3s_ratio"`
	EventValidClues                                  int     `json:"event_valid_clues"`
	EventValidCluesCost                              float64 `json:"event_valid_clues_cost"`
	AdProductCnt                                     int     `json:"ad_product_cnt"`
	EventGoodsView                                   int     `json:"event_goods_view"`
	MerchantRecoFans                                 int     `json:"merchant_reco_fans"`
	EventOrderAmountRoi                              float64 `json:"event_order_amount_roi"`
	EventGoodsViewCost                               float64 `json:"event_goods_view_cost"`
	MerchantRecoFansCost                             float64 `json:"merchant_reco_fans_cost"`
	EventNewUserPay                                  int     `json:"event_new_user_pay"`
	EventNewUserPayCost                              float64 `json:"event_new_user_pay_cost"`
	EventNewUserPayRatio                             float64 `json:"event_new_user_pay_ratio"`
	EventButtonClick                                 int     `json:"event_button_click"`
	EventButtonClickCost                             float64 `json:"event_button_click_cost"`
	EventButtonClickRatio                            float64 `json:"event_button_click_ratio"`
	Play5SRatio                                      float64 `json:"play_5s_ratio"`
	PlayEndRatio                                     float64 `json:"play_end_ratio"`
	EventOrderPaidRoi                                float64 `json:"event_order_paid_roi"`
	EventNewUserJinjianApp                           int     `json:"event_new_user_jinjian_app"`
	EventNewUserJinjianAppCost                       float64 `json:"event_new_user_jinjian_app_cost"`
	EventNewUserJinjianAppRoi                        float64 `json:"event_new_user_jinjian_app_roi"`
	EventNewUserCreditGrantApp                       int     `json:"event_new_user_credit_grant_app"`
	EventNewUserCreditGrantAppCost                   float64 `json:"event_new_user_credit_grant_app_cost"`
	EventNewUserCreditGrantAppRoi                    float64 `json:"event_new_user_credit_grant_app_roi"`
	EventNewUserJinjianPage                          int     `json:"event_new_user_jinjian_page"`
	EventNewUserJinjianPageCost                      float64 `json:"event_new_user_jinjian_page_cost"`
	EventNewUserJinjianPageRoi                       float64 `json:"event_new_user_jinjian_page_roi"`
	EventNewUserCreditGrantPage                      int     `json:"event_new_user_credit_grant_page"`
	EventNewUserCreditGrantPageCost                  float64 `json:"event_new_user_credit_grant_page_cost"`
	EventNewUserCreditGrantPageRoi                   float64 `json:"event_new_user_credit_grant_page_roi"`
	EventAppointForm                                 int     `json:"event_appoint_form"`
	EventAppointFormCost                             float64 `json:"event_appoint_form_cost"`
	EventAppointFormRatio                            float64 `json:"event_appoint_form_ratio"`
	EventAppointJumpClick                            int     `json:"event_appoint_jump_click"`
	EventAppointJumpClickCost                        float64 `json:"event_appoint_jump_click_cost"`
	EventAppointJumpClickRatio                       float64 `json:"event_appoint_jump_click_ratio"`
	EventDspGiftForm                                 int     `json:"event_dsp_gift_form"`
	EventAppInvoked                                  int     `json:"event_app_invoked"`
	EventAppInvokedCost                              float64 `json:"event_app_invoked_cost"`
	EventAppInvokedRatio                             float64 `json:"event_app_invoked_ratio"`
	EventAddWechat                                   int     `json:"event_add_wechat"`
	EventAddWechatCost                               float64 `json:"event_add_wechat_cost"`
	EventAddWechatRatio                              float64 `json:"event_add_wechat_ratio"`
	EventMultiConversion                             int     `json:"event_multi_conversion"`
	EventMultiConversionRatio                        float64 `json:"event_multi_conversion_ratio"`
	EventMultiConversionCost                         float64 `json:"event_multi_conversion_cost"`
	EventWatchAppAd                                  int     `json:"event_watch_app_ad"`
	EventAdWatchTimes                                int     `json:"event_ad_watch_times"`
	EventAdWatchTimesRatio                           float64 `json:"event_ad_watch_times_ratio"`
	EventAdWatchTimesCost                            float64 `json:"event_ad_watch_times_cost"`
	EventAddShoppingCart                             int     `json:"event_add_shopping_cart"`
	EventAddShoppingCartCost                         float64 `json:"event_add_shopping_cart_cost"`
	EventGetThrough                                  int     `json:"event_get_through"`
	EventGetThroughCost                              float64 `json:"event_get_through_cost"`
	EventGetThroughRatio                             float64 `json:"event_get_through_ratio"`
	AdPhotoPlayed75PercentRatio                      float64 `json:"ad_photo_played_75percent_ratio"`
	AdPhotoPlayed10SRatio                            float64 `json:"ad_photo_played_10s_ratio"`
	AdPhotoPlayed2SRatio                             float64 `json:"ad_photo_played_2s_ratio"`
	EventPhoneGetThrough                             int     `json:"event_phone_get_through"`
	EventIntentionConfirmed                          int     `json:"event_intention_confirmed"`
	EventWechatConnected                             int     `json:"event_wechat_connected"`
	EventOrderSuccessed                              int     `json:"event_order_successed"`
	EventPhoneCardActivate                           int     `json:"event_phone_card_activate"`
	EventMeasurementHouse                            int     `json:"event_measurement_house"`
	EventNextDayStayNew                              int     `json:"event_next_day_stay_new"`
	EventNextDayStayNewCost                          float64 `json:"event_next_day_stay_new_cost"`
	EventNextDayStayNewRatio                         float64 `json:"event_next_day_stay_new_ratio"`
	AdShow                                           float64 `json:"ad_show"`
	ActionNewRatio                                   float64 `json:"action_new_ratio"`
	EventOutboundCall                                int     `json:"event_outbound_call"`
	EventOutboundCallCost                            float64 `json:"event_outbound_call_cost"`
	EventOutboundCallRatio                           float64 `json:"event_outbound_call_ratio"`
	KeyAction                                        int     `json:"key_action"`
	KeyActionCost                                    float64 `json:"key_action_cost"`
	KeyActionRatio                                   float64 `json:"key_action_ratio"`
	EventCreditCardRecheck                           int     `json:"event_credit_card_recheck"`
	EventCreditCardRecheckFirstDay                   int     `json:"event_credit_card_recheck_first_day"`
	EventNoIntention                                 int     `json:"event_no_intention"`
	EventMultiPaySevenDayByConversion                float64 `json:"event_multi_pay_seven_day_by_conversion"`
	EventMultiPaySevenDayByConversionCost            float64 `json:"event_multi_pay_seven_day_by_conversion_cost"`
	LiveRoomAvgPlayedSeconds                         float64 `json:"live_room_avg_played_seconds"`
	AdLiveShare                                      int     `json:"ad_live_share"`
	AdLiveComment                                    int     `json:"ad_live_comment"`
	LivePlayedStarted                                float64 `json:"live_played_started"`
	LivePlayedStartedCost                            float64 `json:"live_played_started_cost"`
	AdLiveFollow                                     int     `json:"ad_live_follow"`
	AdLiveFollowCost                                 float64 `json:"ad_live_follow_cost"`
	SimpleLivePlayedStarted                          int     `json:"simple_live_played_started"`
	StandardLivePlayedStarted                        int     `json:"standard_live_played_started"`
	ConversionComponentImpression                    int     `json:"conversion_component_impression"`
	ConversionComponentClick                         int     `json:"conversion_component_click"`
	ConversionComponentRate                          float64 `json:"conversion_component_rate"`
	AdLandingPageImpression                          int     `json:"ad_landing_page_impression"`
	AdAppDownloadHalfImpression                      int     `json:"ad_app_download_half_impression"`
	EventDrawCreditLine                              int     `json:"event_draw_credit_line"`
	EventActive                                      int     `json:"event_active"`
	EventOrderSubmitCost                             float64 `json:"event_order_submit_cost"`
	EventOrderSubmit                                 int     `json:"event_order_submit"`
	DirectSubmit1DCost                               float64 `json:"direct_submit_1d_cost"`
	OrderSubmitAmt                                   float64 `json:"order_submit_amt"`
	OrderSubmitRoi                                   float64 `json:"order_submit_roi"`
	EventWeekTotalStayByConversion                   int     `json:"event_week_total_stay_by_conversion"`
	EventWeekTotalStayNew                            int     `json:"event_week_total_stay_new"`
	EventWeekTotalStayByConversionCost               float64 `json:"event_week_total_stay_by_conversion_cost"`
	EventWeekTotalStayNewCost                        float64 `json:"event_week_total_stay_new_cost"`
	EventWeekTotalStayByConversionRatio              float64 `json:"event_week_total_stay_by_conversion_ratio"`
	EventWeekTotalStayNewRatio                       float64 `json:"event_week_total_stay_new_ratio"`
	MinigameIaaPurchaseAmountFirstDay                float64 `json:"minigame_iaa_purchase_amount_first_day"`
	MinigameIaaPurchaseAmountThreeDayByConversion    float64 `json:"minigame_iaa_purchase_amount_three_day_by_conversion"`
	MinigameIaaPurchaseAmountWeekByConversion        float64 `json:"minigame_iaa_purchase_amount_week_by_conversion"`
	MinigameIaaPurchaseAmountFirstDayRoi             float64 `json:"minigame_iaa_purchase_amount_first_day_roi"`
	MinigameIaaPurchaseAmountThreeDayByConversionRoi float64 `json:"minigame_iaa_purchase_amount_three_day_by_conversion_roi"`
	MinigameIaaPurchaseAmountWeekByConversionRoi     float64 `json:"minigame_iaa_purchase_amount_week_by_conversion_roi"`
	MinigameIaaPurchaseAmount                        float64 `json:"minigame_iaa_purchase_amount"`
	MinigameIaaPurchaseRoi                           float64 `json:"minigame_iaa_purchase_roi"`
	T7PaiedAmt                                       float64 `json:"t7_paied_amt"`
	EventEffectiveCustomerAcquisitionCnt             int     `json:"event_effective_customer_acquisition_cnt"`
	EventEffectiveCustomerAcquisitionCost            float64 `json:"event_effective_customer_acquisition_cost"`
	EventEffectiveCustomerAcquisitionRatio           float64 `json:"event_effective_customer_acquisition_ratio"`
	SimpleLiveRoomPlayedSeconds                      int     `json:"simple_live_room_played_seconds"`
	StandardLiveRoomPlayedSeconds                    int     `json:"standard_live_room_played_seconds"`
	Likes                                            int     `json:"likes"`
	Jinjian0DCnt                                     int     `json:"jinjian_0d_cnt"`
	Jinjian3DCnt                                     int     `json:"jinjian_3d_cnt"`
	Jinjian0DCntCost                                 float64 `json:"jinjian_0d_cnt_cost"`
	Jinjian3DCntCost                                 float64 `json:"jinjian_3d_cnt_cost"`
	CreditGrant0DCnt                                 int     `json:"credit_grant_0d_cnt"`
	CreditGrant3DCnt                                 int     `json:"credit_grant_3d_cnt"`
	CreditGrant0DCntCost                             float64 `json:"credit_grant_0d_cnt_cost"`
	CreditGrant3DCntCost                             float64 `json:"credit_grant_3d_cnt_cost"`
	CreditGrant0DCntRatio                            float64 `json:"credit_grant_0d_cnt_ratio"`
	CreditGrant3DCntRatio                            float64 `json:"credit_grant_3d_cnt_ratio"`
	KeyInappAction0DCnt                              int     `json:"key_inapp_action_0d_cnt"`
	KeyInappAction3DCnt                              int     `json:"key_inapp_action_3d_cnt"`
	KeyInappAction0DCntCost                          float64 `json:"key_inapp_action_0d_cnt_cost"`
	KeyInappAction3DCntCost                          float64 `json:"key_inapp_action_3d_cnt_cost"`
	KeyInappAction0DCntRatio                         float64 `json:"key_inapp_action_0d_cnt_ratio"`
	KeyInappAction3DCntRatio                         float64 `json:"key_inapp_action_3d_cnt_ratio"`
	DrawCreditLine0DCnt                              int     `json:"draw_credit_line_0d_cnt"`
	DrawCreditLine0DCntCost                          float64 `json:"draw_credit_line_0d_cnt_cost"`
	DrawCreditLine0DCntRatio                         float64 `json:"draw_credit_line_0d_cnt_ratio"`
	MinigameIaaPurchaseAmount15DayByConversion       float64 `json:"minigame_iaa_purchase_amount_15_day_by_conversion"`
	MinigameIaaPurchaseAmount30DayByConversion       float64 `json:"minigame_iaa_purchase_amount_30_day_by_conversion"`
	EventPayPurchaseAmount15DayByConversion          float64 `json:"event_pay_purchase_amount_15_day_by_conversion"`
	MinigameIaaPurchaseAmount15DayByConversionRoi    float64 `json:"minigame_iaa_purchase_amount_15_day_by_conversion_roi"`
	MinigameIaaPurchaseAmount30DayByConversionRoi    float64 `json:"minigame_iaa_purchase_amount_30_day_by_conversion_roi"`
	EventPay15DayOverallRoi                          float64 `json:"event_pay_15_day_overall_roi"`
	EventPay30DayOverallRoi                          float64 `json:"event_pay_30_day_overall_roi"`
	EffectiveCustomerAcquisition7DCnt                int     `json:"effective_customer_acquisition_7d_cnt"`
	EffectiveCustomerAcquisition7DCost               float64 `json:"effective_customer_acquisition_7d_cost"`
	EffectiveCustomerAcquisition7DRatio              float64 `json:"effective_customer_acquisition_7d_ratio"`
	MmuEffectiveCustomerAcquisitionCnt               int     `json:"mmu_effective_customer_acquisition_cnt"`
	MmuEffectiveCustomerAcquisition7DCnt             int     `json:"mmu_effective_customer_acquisition_7d_cnt"`
	PlayedNum                                        int     `json:"played_num"`
	LeadsSubmitCnt                                   int     `json:"leads_submit_cnt"`
	LeadsSubmitCntRatio                              float64 `json:"leads_submit_cnt_ratio"`
	LeadsSubmitCost                                  float64 `json:"leads_submit_cost"`
	PrivateMessageSentCnt                            int     `json:"private_message_sent_cnt"`
	PrivateMessageSentRatio                          float64 `json:"private_message_sent_ratio"`
	PrivateMessageSentCost                           float64 `json:"private_message_sent_cost"`
	EventFormSubmit                                  int     `json:"event_form_submit"`
	DirectSubmit1DCnt                                int     `json:"direct_submit_1d_cnt"`
	EventFormSubmitRatio                             float64 `json:"event_form_submit_ratio"`
	EventFormSubmitCost                              float64 `json:"event_form_submit_cost"`
	AllLessonFinishCnt                               int     `json:"all_lesson_finish_cnt"`
	AllLessonFinish30DCnt                            int     `json:"all_lesson_finish_30d_cnt"`
	HighPriceClassPayCnt                             int     `json:"high_price_class_pay_cnt"`
	HighPriceClassPay30DCnt                          int     `json:"high_price_class_pay_30d_cnt"`
	CreativeId                                       int64   `json:"creative_id"`
	CampaignId                                       int64   `json:"campaign_id"`
	CampaignName                                     string  `json:"campaign_name"`
	UnitId                                           int64   `json:"unit_id"`
	UnitName                                         string  `json:"unit_name"`
	PhotoId                                          string  `json:"photo_id"`
	PhotoUrl                                         string  `json:"photo_url"`
	CoverUrl                                         string  `json:"cover_url"`
	ImageToken                                       string  `json:"image_token"`
	Description                                      string  `json:"description"`
	PicId                                            int     `json:"pic_id"`
	PhotoMd5                                         string  `json:"photo_md5"`
	UnitType                                         int     `json:"unit_type"`
}

func (r *ProgramCreativeReportService) SetCfg(cfg *Configuration) *ProgramCreativeReportService {
	r.cfg = cfg
	return r
}

func (r *ProgramCreativeReportService) AccessToken(accessToken string) *ProgramCreativeReportService {
	r.token = accessToken
	return r
}

func (r *ProgramCreativeReportService) SetReq(req ProgramCreativeReportReq) *ProgramCreativeReportService {
	r.Request = &req
	return r
}

func (r *ProgramCreativeReportService) Do() (data *ProgramCreativeReportResp, err error) {
	localBasePath := r.cfg.BasePath
	localVarPath := localBasePath + "/rest/openapi/v1/report/program_creative_report"
	response, err := r.cfg.HTTPClient.R().
		SetHeader("Content-Type", "application/json").
		SetBody(r.Request).
		SetHeader("Access-Token", r.token).
		SetResult(&ProgramCreativeReportResp{}).
		Post(localVarPath)
	if err != nil {
		return nil, err
	}
	resp := new(ProgramCreativeReportResp)
	// 将 JSON 响应解码到结构体中
	err = json.Unmarshal(response.Body(), &resp)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("/rest/openapi/v1/report/program_creative_report解析响应出错: %v\n", err))
	}
	if resp.Code == 0 && resp.Data != nil {
		return resp, nil
	} else {
		return resp, errors.New(resp.Message)
	}
}
