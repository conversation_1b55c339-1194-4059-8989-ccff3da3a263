// ==========================================================================
// GFast自动生成controller操作代码。
// 生成日期：2025-08-12 10:45:00
// 生成路径: internal/app/ad/controller/ks_advertiser_campaign_report_data.go
// 生成人：cq
// desc:快手广告计划报表数据
// company:云南奇讯科技有限公司
// ==========================================================================

package controller

import (
	"context"

	"github.com/tiger1103/gfast/v3/api/v1/ad"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	systemController "github.com/tiger1103/gfast/v3/internal/app/system/controller"
)

type ksAdvertiserCampaignReportDataController struct {
	systemController.BaseController
}

var KsAdvertiserCampaignReportData = new(ksAdvertiserCampaignReportDataController)

// List 列表
func (c *ksAdvertiserCampaignReportDataController) List(ctx context.Context, req *ad.KsAdvertiserCampaignReportDataSearchReq) (res *ad.KsAdvertiserCampaignReportDataSearchRes, err error) {
	res = new(ad.KsAdvertiserCampaignReportDataSearchRes)
	res.KsAdvertiserCampaignReportDataSearchRes, err = service.KsAdvertiserCampaignReportData().List(ctx, &req.KsAdvertiserCampaignReportDataSearchReq)
	return
}

// Add 添加快手广告计划报表数据
func (c *ksAdvertiserCampaignReportDataController) Add(ctx context.Context, req *ad.KsAdvertiserCampaignReportDataAddReq) (res *ad.KsAdvertiserCampaignReportDataAddRes, err error) {
	err = service.KsAdvertiserCampaignReportData().Add(ctx, req.KsAdvertiserCampaignReportDataAddReq)
	return
}

// RunSyncKsCampaignReportData 快手广告计划报表数据任务
func (c *ksAdvertiserCampaignReportDataController) RunSyncKsCampaignReportData(ctx context.Context, req *ad.KsAdvertiserCampaignReportDataTaskReq) (res *ad.KsAdvertiserCampaignReportDataTaskRes, err error) {
	err = service.KsAdvertiserCampaignReportData().RunSyncKsCampaignReportData(ctx, &req.KsAdvertiserCampaignReportDataSearchReq)
	return
}
