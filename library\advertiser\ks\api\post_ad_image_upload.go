package api

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/tiger1103/gfast/v3/library/libUtils"
	"strings"
	"time"
)

// AdImageUploadService ad-上传图片 v2 接口
type AdImageUploadService struct {
	ctx     context.Context
	cfg     *Configuration
	token   string
	Request *AdImageUploadReq
}

// AdImageUploadReq 请求结构体
type AdImageUploadReq struct {
	AdvertiserId int64 `json:"advertiser_id"` // 账号id
	//upload_type
	UploadType int `json:"upload_type"`
	//url
	Url string `json:"url"`
}

// AdImageUploadResp API响应结构体
type AdImageUploadData struct {
	Url        string      `json:"url"`
	Width      int         `json:"width"`
	Height     int         `json:"height"`
	Size       int         `json:"size"`
	Format     string      `json:"format"`
	Signature  string      `json:"signature"`
	Name       interface{} `json:"name"`
	PicType    int         `json:"pic_type"`
	ImageToken string      `json:"image_token"`
	PicId      string      `json:"pic_id"`
}

func (r *AdImageUploadService) SetCfg(cfg *Configuration) *AdImageUploadService {
	r.cfg = cfg
	return r
}

func (r *AdImageUploadService) SetReq(req AdImageUploadReq) *AdImageUploadService {
	r.Request = &req
	return r
}

func (r *AdImageUploadService) AccessToken(accessToken string) *AdImageUploadService {
	r.token = accessToken
	return r
}

func (r *AdImageUploadService) Do() (data *KsBaseResp[AdImageUploadData], err error) {
	localBasePath := r.cfg.BasePath
	localVarPath := localBasePath + "/rest/openapi/v2/file/ad/image/upload"
	localVarQueryParams := map[string]string{}
	if r.Request.AdvertiserId > 0 {
		libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "advertiser_id", r.Request.AdvertiserId)
	}
	if r.Request.UploadType > 0 {
		libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "upload_type", r.Request.UploadType)
	}

	if len(r.Request.Url) > 0 {
		libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "url", r.Request.Url)
	}

	response, err := r.cfg.HTTPClient.R().
		SetHeader("Content-Type", "multipart/form-data").
		SetHeader("Access-Token", r.token).
		SetMultipartFormData(localVarQueryParams).
		//SetHeader("Content-Type", "application/json").
		//SetHeader("Access-Token", r.token).
		//SetBody(r.Request).
		SetResult(&KsBaseResp[AdImageUploadData]{}).
		Post(localVarPath)
	if err != nil {
		return nil, err
	}
	resp := new(KsBaseResp[AdImageUploadData])
	// 将 JSON 响应解码到结构体中
	err = json.Unmarshal(response.Body(), &resp)
	if err != nil {
		if strings.Contains(err.Error(), "文件操作过于频繁") {
			time.Sleep(200 * time.Millisecond)
			return r.Do()
		}

		return nil, errors.New(fmt.Sprintf("/rest/openapi/v2/file/ad/image/upload解析响应出错: %v\n", err))
	}
	if resp.Code == 0 && resp.Data != nil {
		return resp, nil
	} else {
		return resp, errors.New(resp.Message)
	}
}
