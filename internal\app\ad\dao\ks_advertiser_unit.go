// ==========================================================================
// GFast自动生成dao操作代码。
// 生成日期：2025-08-13 16:33:56
// 生成路径: internal/app/ad/dao/ks_advertiser_unit.go
// 生成人：cyao
// desc:快手广告组
// company:云南奇讯科技有限公司
// ==========================================================================

package dao

import (
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao/internal"
)

// ksAdvertiserUnitDao is the manager for logic model data accessing and custom defined data operations functions management.
// You can define custom methods on it to extend its functionality as you wish.
type ksAdvertiserUnitDao struct {
	*internal.KsAdvertiserUnitDao
}

var (
	// KsAdvertiserUnit is globally public accessible object for table tools_gen_table operations.
	KsAdvertiserUnit = ksAdvertiserUnitDao{
		internal.NewKsAdvertiserUnitDao(),
	}
)

// Fill with you ideas below.
