// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2025-08-12 15:45:06
// 生成路径: internal/app/ad/logic/ks_advertiser_agent_info.go
// 生成人：cyao
// desc:快手代理商信息表
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"
	comModel "github.com/tiger1103/gfast/v3/internal/app/common/model"
	sysService "github.com/tiger1103/gfast/v3/internal/app/system/service"
	"github.com/tiger1103/gfast/v3/library/libUtils"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	"github.com/tiger1103/gfast/v3/library/liberr"
)

func init() {
	service.RegisterKsAdvertiserAgentInfo(New())
}

func New() service.IKsAdvertiserAgentInfo {
	return &sKsAdvertiserAgentInfo{}
}

type sKsAdvertiserAgentInfo struct{}

func (s *sKsAdvertiserAgentInfo) List(ctx context.Context, req *model.KsAdvertiserAgentInfoSearchReq) (listRes *model.KsAdvertiserAgentInfoSearchRes, err error) {
	listRes = new(model.KsAdvertiserAgentInfoSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.KsAdvertiserAgentInfo.Ctx(ctx).WithAll()
		if req.Id != "" {
			m = m.Where(dao.KsAdvertiserAgentInfo.Columns().Id+" = ?", req.Id)
		}
		if req.AgentAccountId != "" {
			m = m.Where(dao.KsAdvertiserAgentInfo.Columns().AgentAccountId+" = ?", gconv.Int64(req.AgentAccountId))
		}
		if req.AuthorizeKsAccount != "" {
			m = m.Where(dao.KsAdvertiserAgentInfo.Columns().AuthorizeKsAccount+" = ?", gconv.Int(req.AuthorizeKsAccount))
		}
		if req.Owner != "" {
			m = m.Where(dao.KsAdvertiserAgentInfo.Columns().Owner+" = ?", gconv.Int(req.Owner))
		}
		if len(req.UserIds) > 0 {
			m = m.WhereIn(dao.KsAdvertiserAgentInfo.Columns().Owner, req.UserIds)
		}

		if len(req.DateRange) != 0 {
			m = m.Where(dao.KsAdvertiserAgentInfo.Columns().CreatedAt+" >=? AND "+dao.KsAdvertiserAgentInfo.Columns().CreatedAt+" <=?", req.DateRange[0], req.DateRange[1])
		}
		if req.IsShare != "" {
			m = m.Where(dao.KsAdvertiserAgentInfo.Columns().IsShare+" = ?", gconv.Int(req.IsShare))
		}
		if req.StartTime != "" {
			m = m.Where(dao.KsAdvertiserAgentInfo.Columns().CreatedAt+" >=?", req.StartTime)
		}

		if req.EndTime != "" {
			m = m.Where(dao.KsAdvertiserAgentInfo.Columns().CreatedAt+" <=?", libUtils.StringTimeAddDay(req.EndTime, 1))
		}

		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "id asc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		var res []*model.KsAdvertiserAgentInfoListRes
		err = m.Page(req.PageNum, req.PageSize).Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		listRes.List = make([]*model.KsAdvertiserAgentInfoListRes, len(res))
		userIds := make([]uint64, len(res))
		for k, v := range res {
			userIds = append(userIds, gconv.Uint64(v.Owner))
			listRes.List[k] = &model.KsAdvertiserAgentInfoListRes{
				Id:                 v.Id,
				AgentAccountId:     v.AgentAccountId,
				AuthorizeKsAccount: v.AuthorizeKsAccount,
				Owner:              v.Owner,
				CreatedAt:          v.CreatedAt,
				IsShare:            v.IsShare,
				AppId:              v.AppId,
			}
		}

		// 获取归属人员名称
		userList, _ := sysService.SysUser().GetUserByIds(ctx, userIds)
		for k, v := range listRes.List {
			for _, user := range userList {
				if v.Owner == int(user.Id) {
					listRes.List[k].OwnerUserName = user.UserName
				}
			}
		}

	})
	return
}

func (s *sKsAdvertiserAgentInfo) GetById(ctx context.Context, id uint64) (res *model.KsAdvertiserAgentInfoInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.KsAdvertiserAgentInfo.Ctx(ctx).WithAll().Where(dao.KsAdvertiserAgentInfo.Columns().Id, id).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sKsAdvertiserAgentInfo) GetByAgentIdAndKsUserId(ctx context.Context, agentId, userId int64) (res *model.KsAdvertiserAgentInfoInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.KsAdvertiserAgentInfo.Ctx(ctx).WithAll().Where(dao.KsAdvertiserAgentInfo.Columns().AgentAccountId, agentId).Where(dao.KsAdvertiserAgentInfo.Columns().AuthorizeKsAccount, userId).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

// GetAPPConfig
func (s *sKsAdvertiserAgentInfo) GetAPPConfig(ctx context.Context, agentId, userId int64) (res *model.AdAppConfigInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		agentInfo, err := service.KsAdvertiserAgentInfo().GetByAgentIdAndKsUserId(ctx, agentId, userId)
		if err != nil || agentInfo == nil {
			g.Log().Errorf(ctx, "获取token失败err:%v", err.Error())
		}

		res, _ = service.AdAppConfig().GetByAppId(ctx, gconv.String(agentInfo.AppId))
	})
	return
}

// Share
func (s *sKsAdvertiserAgentInfo) Share(ctx context.Context, req *model.KsAdvertiserAgentInfoShareReq) (res *model.KsAdvertiserAgentInfoShareRes, err error) {
	res = new(model.KsAdvertiserAgentInfoShareRes)
	err = g.Try(ctx, func(ctx context.Context) {
		agentInfo, _ := s.GetById(ctx, req.AgentId)
		var list []*model.KsAdvertiserAgentInfoListRes
		listRes, err := s.List(ctx, &model.KsAdvertiserAgentInfoSearchReq{
			UserIds:            req.UserIds,
			AgentAccountId:     gconv.String(agentInfo.AgentAccountId),
			AuthorizeKsAccount: gconv.String(agentInfo.AuthorizeKsAccount),
			PageReq: comModel.PageReq{
				PageNum:  1,
				PageSize: 999999,
			},
		})
		list = listRes.List

		if agentInfo != nil && agentInfo.Id > 0 {
			// 复制一份数据
			for _, id := range req.UserIds {
				var have = false
				// 判断当前用户是否有该代理商的数据
				for _, infoListRes := range list {
					if infoListRes.AgentAccountId == agentInfo.AgentAccountId && infoListRes.AuthorizeKsAccount == agentInfo.AuthorizeKsAccount && infoListRes.Owner == id {
						have = true
						// 已经存在的账户
						res.FailCount++
						res.FailUserNames = append(res.FailUserNames, infoListRes.OwnerUserName)
						break
					}
				}
				if !have {
					err = s.Add(ctx, &model.KsAdvertiserAgentInfoAddReq{
						AgentAccountId:     agentInfo.AgentAccountId,
						AuthorizeKsAccount: agentInfo.AuthorizeKsAccount,
						Owner:              id,
						IsShare:            1,
						AppId:              agentInfo.AppId,
					})
					if err != nil {
						res.FailCount++
					} else {
						res.SuccessCount++
					}

				}
			}
		} else {
			res.FailCount = len(req.UserIds)
		}
		liberr.ErrIsNil(ctx, err, "分享失败")
	})
	return
}

func (s *sKsAdvertiserAgentInfo) Add(ctx context.Context, req *model.KsAdvertiserAgentInfoAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.KsAdvertiserAgentInfo.Ctx(ctx).Insert(do.KsAdvertiserAgentInfo{
			AgentAccountId:     req.AgentAccountId,
			AuthorizeKsAccount: req.AuthorizeKsAccount,
			Owner:              req.Owner,
			IsShare:            req.IsShare,
			AppId:              req.AppId,
		})
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sKsAdvertiserAgentInfo) Edit(ctx context.Context, req *model.KsAdvertiserAgentInfoEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.KsAdvertiserAgentInfo.Ctx(ctx).WherePri(req.Id).Update(do.KsAdvertiserAgentInfo{
			AgentAccountId:     req.AgentAccountId,
			AuthorizeKsAccount: req.AuthorizeKsAccount,
			Owner:              req.Owner,
			IsShare:            req.IsShare,
			AppId:              req.AppId,
		})
		liberr.ErrIsNil(ctx, err, "修改失败")
	})
	return
}

func (s *sKsAdvertiserAgentInfo) Delete(ctx context.Context, ids []uint64) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.KsAdvertiserAgentInfo.Ctx(ctx).Delete(dao.KsAdvertiserAgentInfo.Columns().Id+" in (?)", ids)
		liberr.ErrIsNil(ctx, err, "删除失败")
	})
	return
}
