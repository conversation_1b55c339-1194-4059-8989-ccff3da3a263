// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2025-08-23 17:40:11
// 生成路径: internal/app/ad/logic/ks_advertiser_strategy_task.go
// 生成人：cyao
// desc:快手广告搭建-任务
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"
	"github.com/gogf/gf/v2/os/gtime"
	sysService "github.com/tiger1103/gfast/v3/internal/app/system/service"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	"github.com/tiger1103/gfast/v3/library/liberr"
)

func init() {
	service.RegisterKsAdvertiserStrategyTask(New())
}

func New() service.IKsAdvertiserStrategyTask {
	return &sKsAdvertiserStrategyTask{}
}

type sKsAdvertiserStrategyTask struct{}

func (s *sKsAdvertiserStrategyTask) List(ctx context.Context, req *model.KsAdvertiserStrategyTaskSearchReq) (listRes *model.KsAdvertiserStrategyTaskSearchRes, err error) {
	listRes = new(model.KsAdvertiserStrategyTaskSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.KsAdvertiserStrategyTask.Ctx(ctx).WithAll()
		if req.TaskId != "" {
			m = m.Where(dao.KsAdvertiserStrategyTask.Columns().TaskId+" = ?", req.TaskId)
		}
		if req.TaskName != "" {
			m = m.Where(dao.KsAdvertiserStrategyTask.Columns().TaskName+" like ?", "%"+req.TaskName+"%")
		}
		if req.TaskStatus != "" {
			m = m.Where(dao.KsAdvertiserStrategyTask.Columns().TaskStatus+" = ?", req.TaskStatus)
		}
		if req.CampaignNum != "" {
			m = m.Where(dao.KsAdvertiserStrategyTask.Columns().CampaignNum+" = ?", gconv.Int(req.CampaignNum))
		}
		if req.UnitNum != "" {
			m = m.Where(dao.KsAdvertiserStrategyTask.Columns().UnitNum+" = ?", gconv.Int(req.UnitNum))
		}
		if req.RuleType != "" {
			m = m.Where(dao.KsAdvertiserStrategyTask.Columns().RuleType+" = ?", gconv.Int(req.RuleType))
		}
		if req.UserId != "" {
			m = m.Where(dao.KsAdvertiserStrategyTask.Columns().UserId+" = ?", gconv.Int(req.UserId))
		}
		if len(req.DateRange) != 0 {
			m = m.Where(dao.KsAdvertiserStrategyTask.Columns().CreatedAt+" >=? AND "+dao.KsAdvertiserStrategyTask.Columns().CreatedAt+" <=?", req.DateRange[0], req.DateRange[1])
		}
		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "task_id asc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		var res []*model.KsAdvertiserStrategyTaskListRes
		err = m.Page(req.PageNum, req.PageSize).Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		listRes.List = make([]*model.KsAdvertiserStrategyTaskListRes, len(res))
		for k, v := range res {
			listRes.List[k] = &model.KsAdvertiserStrategyTaskListRes{
				TaskId:        v.TaskId,
				TaskName:      v.TaskName,
				TaskStatus:    v.TaskStatus,
				AdvertiserIds: v.AdvertiserIds,
				CampaignNum:   v.CampaignNum,
				UnitNum:       v.UnitNum,
				RuleType:      v.RuleType,
				UserId:        v.UserId,
				CreatedAt:     v.CreatedAt,
			}
		}
	})
	return
}

func (s *sKsAdvertiserStrategyTask) GetByTaskId(ctx context.Context, taskId string) (res *model.KsAdvertiserStrategyTaskInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.KsAdvertiserStrategyTask.Ctx(ctx).WithAll().Where(dao.KsAdvertiserStrategyTask.Columns().TaskId, taskId).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sKsAdvertiserStrategyTask) Add(ctx context.Context, req *model.KsAdvertiserStrategyTaskAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.KsAdvertiserStrategyTask.Ctx(ctx).Insert(do.KsAdvertiserStrategyTask{
			TaskId:        req.TaskId,
			TaskName:      req.TaskName,
			TaskStatus:    req.TaskStatus,
			AdvertiserIds: req.AdvertiserIds,
			CampaignNum:   req.CampaignNum,
			UnitNum:       req.UnitNum,
			RuleType:      req.RuleType,
			UserId:        req.UserId,
		})
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sKsAdvertiserStrategyTask) UpdateStatus(ctx context.Context, taskId string, status string) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.KsAdvertiserStrategyTask.Ctx(ctx).Where(dao.KsAdvertiserStrategyTask.Columns().TaskId, taskId).Update(do.KsAdvertiserStrategyTask{
			TaskStatus: status,
		})
		liberr.ErrIsNil(ctx, err, "更新状态失败")
		_, err = dao.KsAdvertiserStrategyTaskCampaign.Ctx(ctx).Where(dao.KsAdvertiserStrategyTaskCampaign.Columns().TaskId, taskId).Update(do.KsAdvertiserStrategyTaskCampaign{
			Status: status,
		})
		liberr.ErrIsNil(ctx, err, "更新项目状态失败")
		_, err = dao.KsAdvertiserStrategyTaskUnit.Ctx(ctx).Where(dao.KsAdvertiserStrategyTaskUnit.Columns().TaskId, taskId).Update(do.KsAdvertiserStrategyTaskUnit{
			Status: status,
		})
		_, err = dao.KsAdvertiserStrategyTaskCreative.Ctx(ctx).Where(dao.KsAdvertiserStrategyTaskCreative.Columns().TaskId, taskId).Update(do.KsAdvertiserStrategyTaskCreative{
			Status: status,
		})
		liberr.ErrIsNil(ctx, err, "更新广告状态失败")
	})
	return
}

// AddTask
func (s *sKsAdvertiserStrategyTask) AddTask(ctx context.Context, req *model.AdExecuteTaskReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		user := sysService.Context().GetLoginUser(ctx)
		adIds := make([]string, 0)
		campaignNum, unitNum := 0, 0
		for _, item := range req.Generate.AdvertiserList {
			adIds = append(adIds, item.AdvertiserId)
			for _, campaignInfo := range item.CampaignList {
				campaignNum++
				unitNum += len(campaignInfo.UnitList)
			}
		}
		if req.TaskName == "" {
			// 默认任务名称为日期加时分秒
			req.TaskName = gtime.Datetime()
		}

		_, err = dao.KsAdvertiserStrategyTask.Ctx(ctx).Insert(do.KsAdvertiserStrategyTask{
			TaskId:        req.StrategyConfig.KsAdvertiserStrategyRuleReq.TaskId,
			TaskName:      req.TaskName,
			TaskStatus:    "INIT",
			AdvertiserIds: adIds,
			CampaignNum:   campaignNum,
			UnitNum:       unitNum,
			RuleType:      req.RuleType,
			UserId:        user.Id,
		})
	})
	return
}

func (s *sKsAdvertiserStrategyTask) Edit(ctx context.Context, req *model.KsAdvertiserStrategyTaskEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.KsAdvertiserStrategyTask.Ctx(ctx).WherePri(req.TaskId).Update(do.KsAdvertiserStrategyTask{
			TaskName:      req.TaskName,
			TaskStatus:    req.TaskStatus,
			AdvertiserIds: req.AdvertiserIds,
			CampaignNum:   req.CampaignNum,
			UnitNum:       req.UnitNum,
			RuleType:      req.RuleType,
			UserId:        req.UserId,
		})
		liberr.ErrIsNil(ctx, err, "修改失败")
	})
	return
}

func (s *sKsAdvertiserStrategyTask) Delete(ctx context.Context, taskIds []string) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.KsAdvertiserStrategyTask.Ctx(ctx).Delete(dao.KsAdvertiserStrategyTask.Columns().TaskId+" in (?)", taskIds)
		liberr.ErrIsNil(ctx, err, "删除失败")
	})
	return
}
