// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2025-08-13 16:33:57
// 生成路径: internal/app/ad/logic/ks_advertiser_unit.go
// 生成人：cyao
// desc:快手广告组
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"
	"errors"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	commonConsts "github.com/tiger1103/gfast/v3/internal/app/common/consts"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	ksApi "github.com/tiger1103/gfast/v3/library/advertiser/ks/api"
	"github.com/tiger1103/gfast/v3/library/libUtils"
	"github.com/tiger1103/gfast/v3/library/liberr"
)

func init() {
	service.RegisterKsAdvertiserUnit(New())
}

func New() service.IKsAdvertiserUnit {
	return &sKsAdvertiserUnit{}
}

type sKsAdvertiserUnit struct{}

func (s *sKsAdvertiserUnit) List(ctx context.Context, req *model.KsAdvertiserUnitSearchReq) (listRes *model.KsAdvertiserUnitSearchRes, err error) {
	listRes = new(model.KsAdvertiserUnitSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.KsAdvertiserUnit.Ctx(ctx).WithAll()
		if req.UnitId != "" {
			m = m.Where(dao.KsAdvertiserUnit.Columns().UnitId+" = ?", req.UnitId)
		}
		if req.AdvertiserId != "" {
			m = m.Where(dao.KsAdvertiserUnit.Columns().AdvertiserId+" = ?", gconv.Int(req.AdvertiserId))
		}
		if req.CampaignId != "" {
			m = m.Where(dao.KsAdvertiserUnit.Columns().CampaignId+" = ?", gconv.Int64(req.CampaignId))
		}
		if req.LinkIntegrationType != "" {
			m = m.Where(dao.KsAdvertiserUnit.Columns().LinkIntegrationType+" = ?", gconv.Int(req.LinkIntegrationType))
		}
		if req.AssetMining != "" {
			m = m.Where(dao.KsAdvertiserUnit.Columns().AssetMining+" = ?", gconv.Int(req.AssetMining))
		}
		if req.SiteType != "" {
			m = m.Where(dao.KsAdvertiserUnit.Columns().SiteType+" = ?", gconv.Int(req.SiteType))
		}
		if req.AdType != "" {
			m = m.Where(dao.KsAdvertiserUnit.Columns().AdType+" = ?", gconv.Int(req.AdType))
		}
		if req.DpaDynamicParamsForUri != "" {
			m = m.Where(dao.KsAdvertiserUnit.Columns().DpaDynamicParamsForUri+" = ?", req.DpaDynamicParamsForUri)
		}
		if req.SchemaUri != "" {
			m = m.Where(dao.KsAdvertiserUnit.Columns().SchemaUri+" = ?", req.SchemaUri)
		}
		if req.ProductImage != "" {
			m = m.Where(dao.KsAdvertiserUnit.Columns().ProductImage+" = ?", req.ProductImage)
		}
		if req.PackageId != "" {
			m = m.Where(dao.KsAdvertiserUnit.Columns().PackageId+" = ?", gconv.Int(req.PackageId))
		}
		if req.BidType != "" {
			m = m.Where(dao.KsAdvertiserUnit.Columns().BidType+" = ?", gconv.Int(req.BidType))
		}
		if req.ProductPrice != "" {
			m = m.Where(dao.KsAdvertiserUnit.Columns().ProductPrice+" = ?", req.ProductPrice)
		}
		if req.PutStatus != "" {
			m = m.Where(dao.KsAdvertiserUnit.Columns().PutStatus+" = ?", gconv.Int(req.PutStatus))
		}
		if req.SmartCover != "" {
			m = m.Where(dao.KsAdvertiserUnit.Columns().SmartCover+" = ?", gconv.Int(req.SmartCover))
		}
		if req.DpaOuterIds != "" {
			m = m.Where(dao.KsAdvertiserUnit.Columns().DpaOuterIds+" = ?", req.DpaOuterIds)
		}
		if req.DpaUnitSubType != "" {
			m = m.Where(dao.KsAdvertiserUnit.Columns().DpaUnitSubType+" = ?", gconv.Int(req.DpaUnitSubType))
		}
		if req.ULink != "" {
			m = m.Where(dao.KsAdvertiserUnit.Columns().ULink+" = ?", req.ULink)
		}
		if req.AppStore != "" {
			m = m.Where(dao.KsAdvertiserUnit.Columns().AppStore+" = ?", req.AppStore)
		}
		if req.AppDownloadType != "" {
			m = m.Where(dao.KsAdvertiserUnit.Columns().AppDownloadType+" = ?", gconv.Int(req.AppDownloadType))
		}
		if req.DspVersion != "" {
			m = m.Where(dao.KsAdvertiserUnit.Columns().DspVersion+" = ?", gconv.Int(req.DspVersion))
		}
		if req.PlayableId != "" {
			m = m.Where(dao.KsAdvertiserUnit.Columns().PlayableId+" = ?", gconv.Int64(req.PlayableId))
		}
		if req.EpisodeId != "" {
			m = m.Where(dao.KsAdvertiserUnit.Columns().EpisodeId+" = ?", gconv.Int64(req.EpisodeId))
		}
		if req.PlayableSwitch != "" {
			m = m.Where(dao.KsAdvertiserUnit.Columns().PlayableSwitch+" = ?", gconv.Int(req.PlayableSwitch))
		}
		if req.ProductId != "" {
			m = m.Where(dao.KsAdvertiserUnit.Columns().ProductId+" = ?", req.ProductId)
		}
		if req.SchemaId != "" {
			m = m.Where(dao.KsAdvertiserUnit.Columns().SchemaId+" = ?", req.SchemaId)
		}
		if req.UnitSource != "" {
			m = m.Where(dao.KsAdvertiserUnit.Columns().UnitSource+" = ?", gconv.Int(req.UnitSource))
		}
		if req.SeriesPayTemplateId != "" {
			m = m.Where(dao.KsAdvertiserUnit.Columns().SeriesPayTemplateId+" = ?", gconv.Int64(req.SeriesPayTemplateId))
		}
		if req.DayBudget != "" {
			m = m.Where(dao.KsAdvertiserUnit.Columns().DayBudget+" = ?", gconv.Int64(req.DayBudget))
		}
		if req.OcpxActionType != "" {
			m = m.Where(dao.KsAdvertiserUnit.Columns().OcpxActionType+" = ?", gconv.Int(req.OcpxActionType))
		}
		if req.UseAppMarket != "" {
			m = m.Where(dao.KsAdvertiserUnit.Columns().UseAppMarket+" = ?", gconv.Int(req.UseAppMarket))
		}
		if req.TargetExplore != "" {
			m = m.Where(dao.KsAdvertiserUnit.Columns().TargetExplore+" = ?", gconv.Int(req.TargetExplore))
		}
		if req.ComponentId != "" {
			m = m.Where(dao.KsAdvertiserUnit.Columns().ComponentId+" = ?", gconv.Int64(req.ComponentId))
		}
		if req.DpaDynamicParams != "" {
			m = m.Where(dao.KsAdvertiserUnit.Columns().DpaDynamicParams+" = ?", gconv.Int(req.DpaDynamicParams))
		}
		if req.CreateTime != "" {
			m = m.Where(dao.KsAdvertiserUnit.Columns().CreateTime+" = ?", gconv.Time(req.CreateTime))
		}
		if req.PlayButton != "" {
			m = m.Where(dao.KsAdvertiserUnit.Columns().PlayButton+" = ?", req.PlayButton)
		}
		if req.UrlType != "" {
			m = m.Where(dao.KsAdvertiserUnit.Columns().UrlType+" = ?", gconv.Int(req.UrlType))
		}

		if req.DpaCategories != "" {
			m = m.Where(dao.KsAdvertiserUnit.Columns().DpaCategories+" = ?", req.DpaCategories)
		}
		if req.ProductName != "" {
			m = m.Where(dao.KsAdvertiserUnit.Columns().ProductName+" like ?", "%"+req.ProductName+"%")
		}
		if req.SeriesPayMode != "" {
			m = m.Where(dao.KsAdvertiserUnit.Columns().SeriesPayMode+" = ?", gconv.Int(req.SeriesPayMode))
		}
		if req.ShowMode != "" {
			m = m.Where(dao.KsAdvertiserUnit.Columns().ShowMode+" = ?", gconv.Int(req.ShowMode))
		}
		if req.UnitName != "" {
			m = m.Where(dao.KsAdvertiserUnit.Columns().UnitName+" like ?", "%"+req.UnitName+"%")
		}
		if req.ExtendSearch != "" {
			m = m.Where(dao.KsAdvertiserUnit.Columns().ExtendSearch+" = ?", gconv.Int(req.ExtendSearch))
		}
		if req.QuickSearch != "" {
			m = m.Where(dao.KsAdvertiserUnit.Columns().QuickSearch+" = ?", gconv.Int(req.QuickSearch))
		}
		if req.SiteId != "" {
			m = m.Where(dao.KsAdvertiserUnit.Columns().SiteId+" = ?", gconv.Int64(req.SiteId))
		}
		if req.SeriesCardInfo != "" {
			m = m.Where(dao.KsAdvertiserUnit.Columns().SeriesCardInfo+" = ?", req.SeriesCardInfo)
		}
		if req.Status != "" {
			m = m.Where(dao.KsAdvertiserUnit.Columns().Status+" = ?", gconv.Int(req.Status))
		}
		if req.ConsultId != "" {
			m = m.Where(dao.KsAdvertiserUnit.Columns().ConsultId+" = ?", gconv.Int64(req.ConsultId))
		}
		if req.LiveComponentType != "" {
			m = m.Where(dao.KsAdvertiserUnit.Columns().LiveComponentType+" = ?", gconv.Int(req.LiveComponentType))
		}
		if req.SearchPopulationRetargeting != "" {
			m = m.Where(dao.KsAdvertiserUnit.Columns().SearchPopulationRetargeting+" = ?", gconv.Int(req.SearchPopulationRetargeting))
		}
		if req.EnhanceConversionType != "" {
			m = m.Where(dao.KsAdvertiserUnit.Columns().EnhanceConversionType+" = ?", gconv.Int(req.EnhanceConversionType))
		}
		if req.UnitType != "" {
			m = m.Where(dao.KsAdvertiserUnit.Columns().UnitType+" = ?", gconv.Int(req.UnitType))
		}
		if req.OuterId != "" {
			m = m.Where(dao.KsAdvertiserUnit.Columns().OuterId+" = ?", req.OuterId)
		}
		if req.StudyStatus != "" {
			m = m.Where(dao.KsAdvertiserUnit.Columns().StudyStatus+" = ?", gconv.Int(req.StudyStatus))
		}
		if req.SeriesCardType != "" {
			m = m.Where(dao.KsAdvertiserUnit.Columns().SeriesCardType+" = ?", gconv.Int(req.SeriesCardType))
		}
		if req.CustomMiniAppData != "" {
			m = m.Where(dao.KsAdvertiserUnit.Columns().CustomMiniAppData+" = ?", req.CustomMiniAppData)
		}
		if req.UpdateTime != "" {
			m = m.Where(dao.KsAdvertiserUnit.Columns().UpdateTime+" = ?", gconv.Time(req.UpdateTime))
		}
		if req.ImMessageMount != "" {
			m = m.Where(dao.KsAdvertiserUnit.Columns().ImMessageMount+" = ?", gconv.Int(req.ImMessageMount))
		}
		if req.LibraryId != "" {
			m = m.Where(dao.KsAdvertiserUnit.Columns().LibraryId+" = ?", gconv.Int64(req.LibraryId))
		}
		if req.DeepConversionType != "" {
			m = m.Where(dao.KsAdvertiserUnit.Columns().DeepConversionType+" = ?", gconv.Int(req.DeepConversionType))
		}
		if req.DeepConversionBid != "" {
			m = m.Where(dao.KsAdvertiserUnit.Columns().DeepConversionBid+" = ?", gconv.Int(req.DeepConversionBid))
		}
		if req.KwaiBookId != "" {
			m = m.Where(dao.KsAdvertiserUnit.Columns().KwaiBookId+" = ?", gconv.Int64(req.KwaiBookId))
		}
		if req.DpaDynamicParamsForDp != "" {
			m = m.Where(dao.KsAdvertiserUnit.Columns().DpaDynamicParamsForDp+" = ?", req.DpaDynamicParamsForDp)
		}
		if req.PageAuditStatus != "" {
			m = m.Where(dao.KsAdvertiserUnit.Columns().PageAuditStatus+" = ?", req.PageAuditStatus)
		}
		if req.JingleBellId != "" {
			m = m.Where(dao.KsAdvertiserUnit.Columns().JingleBellId+" = ?", gconv.Int64(req.JingleBellId))
		}
		if req.LiveUserId != "" {
			m = m.Where(dao.KsAdvertiserUnit.Columns().LiveUserId+" = ?", gconv.Int64(req.LiveUserId))
		}
		if req.PlayableUrl != "" {
			m = m.Where(dao.KsAdvertiserUnit.Columns().PlayableUrl+" = ?", req.PlayableUrl)
		}
		if req.PlayableFileName != "" {
			m = m.Where(dao.KsAdvertiserUnit.Columns().PlayableFileName+" like ?", "%"+req.PlayableFileName+"%")
		}
		if req.WebUriType != "" {
			m = m.Where(dao.KsAdvertiserUnit.Columns().WebUriType+" = ?", gconv.Int(req.WebUriType))
		}
		if req.ReviewDetail != "" {
			m = m.Where(dao.KsAdvertiserUnit.Columns().ReviewDetail+" = ?", req.ReviewDetail)
		}
		if req.EndTime != "" {
			m = m.Where(dao.KsAdvertiserUnit.Columns().EndTime+" = ?", gconv.Time(req.EndTime))
		}
		if req.CompensateStatus != "" {
			m = m.Where(dao.KsAdvertiserUnit.Columns().CompensateStatus+" = ?", gconv.Int(req.CompensateStatus))
		}
		if req.SceneId != "" {
			m = m.Where(dao.KsAdvertiserUnit.Columns().SceneId+" = ?", req.SceneId)
		}
		if req.BeginTime != "" {
			m = m.Where(dao.KsAdvertiserUnit.Columns().BeginTime+" = ?", gconv.Time(req.BeginTime))
		}
		if req.UnitMaterialType != "" {
			m = m.Where(dao.KsAdvertiserUnit.Columns().UnitMaterialType+" = ?", gconv.Int(req.UnitMaterialType))
		}
		if req.ConvertId != "" {
			m = m.Where(dao.KsAdvertiserUnit.Columns().ConvertId+" = ?", gconv.Int64(req.ConvertId))
		}
		if req.Url != "" {
			m = m.Where(dao.KsAdvertiserUnit.Columns().Url+" = ?", req.Url)
		}
		if req.SeriesId != "" {
			m = m.Where(dao.KsAdvertiserUnit.Columns().SeriesId+" = ?", gconv.Int64(req.SeriesId))
		}
		if req.ScheduleTime != "" {
			m = m.Where(dao.KsAdvertiserUnit.Columns().ScheduleTime+" = ?", req.ScheduleTime)
		}
		if req.OuterLoopNative != "" {
			m = m.Where(dao.KsAdvertiserUnit.Columns().OuterLoopNative+" = ?", gconv.Int(req.OuterLoopNative))
		}
		if req.CpaBid != "" {
			m = m.Where(dao.KsAdvertiserUnit.Columns().CpaBid+" = ?", gconv.Int(req.CpaBid))
		}
		if req.PlayableOrientation != "" {
			m = m.Where(dao.KsAdvertiserUnit.Columns().PlayableOrientation+" = ?", gconv.Int(req.PlayableOrientation))
		}
		if req.TemplateId != "" {
			m = m.Where(dao.KsAdvertiserUnit.Columns().TemplateId+" = ?", gconv.Int(req.TemplateId))
		}
		if req.Bid != "" {
			m = m.Where(dao.KsAdvertiserUnit.Columns().Bid+" = ?", gconv.Int(req.Bid))
		}
		if req.AdvCardOption != "" {
			m = m.Where(dao.KsAdvertiserUnit.Columns().AdvCardOption+" = ?", gconv.Int(req.AdvCardOption))
		}
		if len(req.DateRange) != 0 {
			m = m.Where(dao.KsAdvertiserUnit.Columns().CreatedAt+" >=? AND "+dao.KsAdvertiserUnit.Columns().CreatedAt+" <=?", req.DateRange[0], req.DateRange[1])
		}
		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "unit_id asc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		var res []*model.KsAdvertiserUnitListRes
		err = m.Page(req.PageNum, req.PageSize).Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		listRes.List = make([]*model.KsAdvertiserUnitListRes, len(res))
		for k, v := range res {
			listRes.List[k] = &model.KsAdvertiserUnitListRes{
				UnitId:                   v.UnitId,
				AdvertiserId:             v.AdvertiserId,
				CampaignId:               v.CampaignId,
				LinkIntegrationType:      v.LinkIntegrationType,
				AssetMining:              v.AssetMining,
				SiteType:                 v.SiteType,
				AdType:                   v.AdType,
				DpaDynamicParamsForUri:   v.DpaDynamicParamsForUri,
				SchemaUri:                v.SchemaUri,
				ProductImage:             v.ProductImage,
				PackageId:                v.PackageId,
				BidType:                  v.BidType,
				ProductPrice:             v.ProductPrice,
				PutStatus:                v.PutStatus,
				SmartCover:               v.SmartCover,
				DpaOuterIds:              v.DpaOuterIds,
				SeriesPayTemplateIdMulti: v.SeriesPayTemplateIdMulti,
				DpaUnitSubType:           v.DpaUnitSubType,
				ULink:                    v.ULink,
				AppStore:                 v.AppStore,
				AppDownloadType:          v.AppDownloadType,
				DspVersion:               v.DspVersion,
				PlayableId:               v.PlayableId,
				EpisodeId:                v.EpisodeId,
				PlayableSwitch:           v.PlayableSwitch,
				ProductId:                v.ProductId,
				SchemaId:                 v.SchemaId,
				UnitSource:               v.UnitSource,
				SeriesPayTemplateId:      v.SeriesPayTemplateId,
				DayBudget:                v.DayBudget,
				OcpxActionType:           v.OcpxActionType,
				UseAppMarket:             v.UseAppMarket,
				TargetExplore:            v.TargetExplore,
				ComponentId:              v.ComponentId,
				DpaDynamicParams:         v.DpaDynamicParams,
				CreateTime:               v.CreateTime,
				PlayButton:               v.PlayButton,
				UrlType:                  v.UrlType,

				DpaCategories:               v.DpaCategories,
				ProductName:                 v.ProductName,
				SeriesPayMode:               v.SeriesPayMode,
				ShowMode:                    v.ShowMode,
				UnitName:                    v.UnitName,
				ExtendSearch:                v.ExtendSearch,
				QuickSearch:                 v.QuickSearch,
				SiteId:                      v.SiteId,
				SeriesCardInfo:              v.SeriesCardInfo,
				Status:                      v.Status,
				ConsultId:                   v.ConsultId,
				RoiRatio:                    v.RoiRatio,
				LiveComponentType:           v.LiveComponentType,
				SearchPopulationRetargeting: v.SearchPopulationRetargeting,
				EnhanceConversionType:       v.EnhanceConversionType,
				UnitType:                    v.UnitType,
				OuterId:                     v.OuterId,
				StudyStatus:                 v.StudyStatus,
				SeriesCardType:              v.SeriesCardType,
				CustomMiniAppData:           v.CustomMiniAppData,
				UpdateTime:                  v.UpdateTime,
				ImMessageMount:              v.ImMessageMount,
				LibraryId:                   v.LibraryId,
				DeepConversionType:          v.DeepConversionType,
				DeepConversionBid:           v.DeepConversionBid,
				KwaiBookId:                  v.KwaiBookId,
				DpaDynamicParamsForDp:       v.DpaDynamicParamsForDp,
				PageAuditStatus:             v.PageAuditStatus,
				JingleBellId:                v.JingleBellId,
				LiveUserId:                  v.LiveUserId,
				PlayableUrl:                 v.PlayableUrl,
				PlayableFileName:            v.PlayableFileName,
				WebUriType:                  v.WebUriType,
				ReviewDetail:                v.ReviewDetail,
				EndTime:                     v.EndTime,
				CompensateStatus:            v.CompensateStatus,
				SceneId:                     v.SceneId,
				BeginTime:                   v.BeginTime,
				UnitMaterialType:            v.UnitMaterialType,
				ConvertId:                   v.ConvertId,
				Url:                         v.Url,
				SeriesId:                    v.SeriesId,
				ScheduleTime:                v.ScheduleTime,
				OuterLoopNative:             v.OuterLoopNative,
				CpaBid:                      v.CpaBid,
				PlayableOrientation:         v.PlayableOrientation,
				TemplateId:                  v.TemplateId,
				Bid:                         v.Bid,
				AdvCardOption:               v.AdvCardOption,
				CreatedAt:                   v.CreatedAt,
			}
		}
	})
	return
}

func (s *sKsAdvertiserUnit) GetByUnitId(ctx context.Context, unitId int64) (res *model.KsAdvertiserUnitInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.KsAdvertiserUnit.Ctx(ctx).WithAll().Where(dao.KsAdvertiserUnit.Columns().UnitId, unitId).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sKsAdvertiserUnit) GetUnitInfo(ctx context.Context, unitId int64) (res *model.KsAdvertiserUnitInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.KsAdvertiserUnit.Ctx(ctx).WithAll().Where(dao.KsAdvertiserUnit.Columns().UnitId, unitId).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
		// 获取广告计划名称
		campaignInfo, _ := service.KsAdvertiserCampaign().GetByCampaignId(ctx, res.CampaignId)
		if campaignInfo != nil {
			res.CampaignName = campaignInfo.CampaignName
		}
	})
	return
}

// PullUnitByAdId 拉取广告组
func (s *sKsAdvertiserUnit) PullUnitByAdId(ctx context.Context, accessToken string, adId int64) (err error) {
	return g.Try(ctx, func(ctx context.Context) {
		page, pageSize := 1, 200
		for {
			unitListRes, err := ksApi.GetKSApiClient().GetDspUnitListService.AccessToken(accessToken).SetReq(ksApi.GetDspUnitListReq{
				AdvertiserID: adId,
				PageSize:     pageSize,
				Page:         page,
			}).Do()

			if err != nil {
				g.Log().Errorf(ctx, "拉取广告计划失败: %v", err)
				break
			}
			if unitListRes == nil || unitListRes.Data == nil || unitListRes.Data.TotalCount == 0 {
				break
			}

			// 循环添加广告计划
			batchReq := make([]*model.KsAdvertiserUnitAddReq, 0)
			for _, detail := range unitListRes.Data.Details {
				req := &model.KsAdvertiserUnitAddReq{
					UnitId:                   detail.UnitID,
					AdvertiserId:             gconv.Int(adId),
					CampaignId:               detail.CampaignID,
					LinkIntegrationType:      detail.LinkIntegrationType,
					AssetMining:              libUtils.BoolParsInt(detail.AssetMining),
					SiteType:                 detail.SiteType,
					AdType:                   detail.AdType,
					DpaDynamicParamsForUri:   detail.DpaDynamicParamsForURI,
					SchemaUri:                detail.SchemaURI,
					ProductImage:             detail.ProductImage,
					PackageId:                detail.PackageID,
					BidType:                  detail.BidType,
					ProductPrice:             detail.ProductPrice,
					PutStatus:                detail.PutStatus,
					SmartCover:               libUtils.BoolParsInt(detail.SmartCover),
					DpaOuterIds:              gconv.String(detail.DpaOuterIDs),
					SeriesPayTemplateIdMulti: libUtils.AnyMarshalToString(detail.SeriesPayTemplateIDMulti),
					DpaUnitSubType:           detail.DpaUnitSubType,
					ULink:                    detail.ULink,
					AppStore:                 libUtils.AnyMarshalToString(detail.AppStore),
					AppDownloadType:          detail.AppDownloadType,
					DspVersion:               detail.DspVersion,
					PlayableId:               detail.PlayableID,
					EpisodeId:                detail.EpisodeID,
					PlayableSwitch:           libUtils.BoolParsInt(detail.PlayableSwitch),
					ProductId:                detail.ProductID,
					SchemaId:                 detail.SchemaID,
					UnitSource:               detail.UnitSource,
					SeriesPayTemplateId:      detail.SeriesPayTemplateID,
					DayBudget:                detail.DayBudget,
					OcpxActionType:           detail.OcpxActionType,
					UseAppMarket:             detail.UseAppMarket,
					TargetExplore:            detail.TargetExplore,
					ComponentId:              detail.ComponentID,
					DpaDynamicParams:         detail.DpaDynamicParams,
					CreateTime:               gconv.GTime(detail.CreateTime),
					PlayButton:               detail.PlayButton,
					UrlType:                  detail.URLType,
					//SupportUnitIds:              libUtils.AnyMarshalToString(detail.SupportUnitIDs),
					DpaCategories:               libUtils.AnyMarshalToString(detail.DpaCategories),
					ProductName:                 detail.ProductName,
					SeriesPayMode:               detail.SeriesPayMode,
					ShowMode:                    detail.ShowMode,
					UnitName:                    detail.UnitName,
					ExtendSearch:                libUtils.BoolParsInt(detail.ExtendSearch),
					QuickSearch:                 detail.QuickSearch,
					SiteId:                      detail.SiteID,
					SeriesCardInfo:              libUtils.AnyMarshalToString(detail.SeriesCardInfo),
					Status:                      detail.Status,
					ConsultId:                   detail.ConsultID,
					RoiRatio:                    detail.RoiRatio,
					LiveComponentType:           detail.LiveComponentType,
					SearchPopulationRetargeting: detail.SearchPopulationRetargeting,
					EnhanceConversionType:       detail.EnhanceConversionType,
					UnitType:                    detail.UnitType,
					OuterId:                     detail.OuterID,
					StudyStatus:                 detail.StudyStatus,
					SeriesCardType:              detail.SeriesCardType,
					CustomMiniAppData:           detail.CustomMiniAppData,
					UpdateTime:                  gconv.GTime(detail.UpdateTime),
					ImMessageMount:              libUtils.BoolParsInt(detail.ImMessageMount),
					LibraryId:                   detail.LibraryID,
					DeepConversionType:          detail.DeepConversionType,
					DeepConversionBid:           detail.DeepConversionBid,
					KwaiBookId:                  detail.KwaiBookID,
					DpaDynamicParamsForDp:       detail.DpaDynamicParamsForDP,
					PageAuditStatus:             detail.PageAuditStatus,
					JingleBellId:                detail.JingleBellID,
					LiveUserId:                  detail.LiveUserID,
					PlayableUrl:                 detail.PlayableURL,
					PlayableFileName:            detail.PlayableFileName,
					WebUriType:                  detail.WebURIType,
					ReviewDetail:                detail.ReviewDetail,
					EndTime:                     gconv.GTime(detail.EndTime),
					CompensateStatus:            detail.CompensateStatus,
					SceneId:                     libUtils.AnyMarshalToString(detail.SceneID),
					BeginTime:                   gconv.GTime(detail.BeginTime),
					UnitMaterialType:            detail.UnitMaterialType,
					ConvertId:                   detail.ConvertID,
					Url:                         detail.URL,
					SeriesId:                    detail.SeriesID,
					ScheduleTime:                detail.ScheduleTime,
					OuterLoopNative:             detail.OuterLoopNative,
					CpaBid:                      detail.CpaBid,
					PlayableOrientation:         detail.PlayableOrientation,
					TemplateId:                  detail.TemplateID,
					Bid:                         detail.Bid,
					AdvCardOption:               detail.AdvCardOption,
				}

				batchReq = append(batchReq, req)
			}
			err = s.BatchAdd(ctx, batchReq)
			if err != nil {
				g.Log().Errorf(ctx, "批量新增失败%v", err.Error())
			}
			if unitListRes.Data.TotalCount < pageSize {
				break
			} else {
				page++
			}

		}

	})

}

// BatchAdd
func (s *sKsAdvertiserUnit) BatchAdd(ctx context.Context, req []*model.KsAdvertiserUnitAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.KsAdvertiserUnit.Ctx(ctx).Save(req)
		liberr.ErrIsNil(ctx, err, "添加失败")

	})
	return
}

func (s *sKsAdvertiserUnit) Add(ctx context.Context, req *model.KsAdvertiserUnitAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.KsAdvertiserUnit.Ctx(ctx).Insert(do.KsAdvertiserUnit{
			UnitId:                   req.UnitId,
			AdvertiserId:             req.AdvertiserId,
			CampaignId:               req.CampaignId,
			LinkIntegrationType:      req.LinkIntegrationType,
			AssetMining:              req.AssetMining,
			SiteType:                 req.SiteType,
			AdType:                   req.AdType,
			DpaDynamicParamsForUri:   req.DpaDynamicParamsForUri,
			SchemaUri:                req.SchemaUri,
			ProductImage:             req.ProductImage,
			PackageId:                req.PackageId,
			BidType:                  req.BidType,
			ProductPrice:             req.ProductPrice,
			PutStatus:                req.PutStatus,
			SmartCover:               req.SmartCover,
			DpaOuterIds:              req.DpaOuterIds,
			SeriesPayTemplateIdMulti: req.SeriesPayTemplateIdMulti,
			DpaUnitSubType:           req.DpaUnitSubType,
			ULink:                    req.ULink,
			AppStore:                 req.AppStore,
			AppDownloadType:          req.AppDownloadType,
			DspVersion:               req.DspVersion,
			PlayableId:               req.PlayableId,
			EpisodeId:                req.EpisodeId,
			PlayableSwitch:           req.PlayableSwitch,
			ProductId:                req.ProductId,
			SchemaId:                 req.SchemaId,
			UnitSource:               req.UnitSource,
			SeriesPayTemplateId:      req.SeriesPayTemplateId,
			DayBudget:                req.DayBudget,
			OcpxActionType:           req.OcpxActionType,
			UseAppMarket:             req.UseAppMarket,
			TargetExplore:            req.TargetExplore,
			ComponentId:              req.ComponentId,
			DpaDynamicParams:         req.DpaDynamicParams,
			CreateTime:               req.CreateTime,
			PlayButton:               req.PlayButton,
			UrlType:                  req.UrlType,

			DpaCategories:               req.DpaCategories,
			ProductName:                 req.ProductName,
			SeriesPayMode:               req.SeriesPayMode,
			ShowMode:                    req.ShowMode,
			UnitName:                    req.UnitName,
			ExtendSearch:                req.ExtendSearch,
			QuickSearch:                 req.QuickSearch,
			SiteId:                      req.SiteId,
			SeriesCardInfo:              req.SeriesCardInfo,
			Status:                      req.Status,
			ConsultId:                   req.ConsultId,
			RoiRatio:                    req.RoiRatio,
			LiveComponentType:           req.LiveComponentType,
			SearchPopulationRetargeting: req.SearchPopulationRetargeting,
			EnhanceConversionType:       req.EnhanceConversionType,
			UnitType:                    req.UnitType,
			OuterId:                     req.OuterId,
			StudyStatus:                 req.StudyStatus,
			SeriesCardType:              req.SeriesCardType,
			CustomMiniAppData:           req.CustomMiniAppData,
			UpdateTime:                  req.UpdateTime,
			ImMessageMount:              req.ImMessageMount,
			LibraryId:                   req.LibraryId,
			DeepConversionType:          req.DeepConversionType,
			DeepConversionBid:           req.DeepConversionBid,
			KwaiBookId:                  req.KwaiBookId,
			DpaDynamicParamsForDp:       req.DpaDynamicParamsForDp,
			PageAuditStatus:             req.PageAuditStatus,
			JingleBellId:                req.JingleBellId,
			LiveUserId:                  req.LiveUserId,
			PlayableUrl:                 req.PlayableUrl,
			PlayableFileName:            req.PlayableFileName,
			WebUriType:                  req.WebUriType,
			ReviewDetail:                req.ReviewDetail,
			EndTime:                     req.EndTime,
			CompensateStatus:            req.CompensateStatus,
			SceneId:                     req.SceneId,
			BeginTime:                   req.BeginTime,
			UnitMaterialType:            req.UnitMaterialType,
			ConvertId:                   req.ConvertId,
			Url:                         req.Url,
			SeriesId:                    req.SeriesId,
			ScheduleTime:                req.ScheduleTime,
			OuterLoopNative:             req.OuterLoopNative,
			CpaBid:                      req.CpaBid,
			PlayableOrientation:         req.PlayableOrientation,
			TemplateId:                  req.TemplateId,
			Bid:                         req.Bid,
			AdvCardOption:               req.AdvCardOption,
		})
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sKsAdvertiserUnit) Edit(ctx context.Context, req *model.KsAdvertiserUnitEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.KsAdvertiserUnit.Ctx(ctx).WherePri(req.UnitId).Update(do.KsAdvertiserUnit{
			AdvertiserId:                req.AdvertiserId,
			CampaignId:                  req.CampaignId,
			LinkIntegrationType:         req.LinkIntegrationType,
			AssetMining:                 req.AssetMining,
			SiteType:                    req.SiteType,
			AdType:                      req.AdType,
			DpaDynamicParamsForUri:      req.DpaDynamicParamsForUri,
			SchemaUri:                   req.SchemaUri,
			ProductImage:                req.ProductImage,
			PackageId:                   req.PackageId,
			BidType:                     req.BidType,
			ProductPrice:                req.ProductPrice,
			PutStatus:                   req.PutStatus,
			SmartCover:                  req.SmartCover,
			DpaOuterIds:                 req.DpaOuterIds,
			SeriesPayTemplateIdMulti:    req.SeriesPayTemplateIdMulti,
			DpaUnitSubType:              req.DpaUnitSubType,
			ULink:                       req.ULink,
			AppStore:                    req.AppStore,
			AppDownloadType:             req.AppDownloadType,
			DspVersion:                  req.DspVersion,
			PlayableId:                  req.PlayableId,
			EpisodeId:                   req.EpisodeId,
			PlayableSwitch:              req.PlayableSwitch,
			ProductId:                   req.ProductId,
			SchemaId:                    req.SchemaId,
			UnitSource:                  req.UnitSource,
			SeriesPayTemplateId:         req.SeriesPayTemplateId,
			DayBudget:                   req.DayBudget,
			OcpxActionType:              req.OcpxActionType,
			UseAppMarket:                req.UseAppMarket,
			TargetExplore:               req.TargetExplore,
			ComponentId:                 req.ComponentId,
			DpaDynamicParams:            req.DpaDynamicParams,
			CreateTime:                  req.CreateTime,
			PlayButton:                  req.PlayButton,
			UrlType:                     req.UrlType,
			DpaCategories:               req.DpaCategories,
			ProductName:                 req.ProductName,
			SeriesPayMode:               req.SeriesPayMode,
			ShowMode:                    req.ShowMode,
			UnitName:                    req.UnitName,
			ExtendSearch:                req.ExtendSearch,
			QuickSearch:                 req.QuickSearch,
			SiteId:                      req.SiteId,
			SeriesCardInfo:              req.SeriesCardInfo,
			Status:                      req.Status,
			ConsultId:                   req.ConsultId,
			RoiRatio:                    req.RoiRatio,
			LiveComponentType:           req.LiveComponentType,
			SearchPopulationRetargeting: req.SearchPopulationRetargeting,
			EnhanceConversionType:       req.EnhanceConversionType,
			UnitType:                    req.UnitType,
			OuterId:                     req.OuterId,
			StudyStatus:                 req.StudyStatus,
			SeriesCardType:              req.SeriesCardType,
			CustomMiniAppData:           req.CustomMiniAppData,
			UpdateTime:                  req.UpdateTime,
			ImMessageMount:              req.ImMessageMount,
			LibraryId:                   req.LibraryId,
			DeepConversionType:          req.DeepConversionType,
			DeepConversionBid:           req.DeepConversionBid,
			KwaiBookId:                  req.KwaiBookId,
			DpaDynamicParamsForDp:       req.DpaDynamicParamsForDp,
			PageAuditStatus:             req.PageAuditStatus,
			JingleBellId:                req.JingleBellId,
			LiveUserId:                  req.LiveUserId,
			PlayableUrl:                 req.PlayableUrl,
			PlayableFileName:            req.PlayableFileName,
			WebUriType:                  req.WebUriType,
			ReviewDetail:                req.ReviewDetail,
			EndTime:                     req.EndTime,
			CompensateStatus:            req.CompensateStatus,
			SceneId:                     req.SceneId,
			BeginTime:                   req.BeginTime,
			UnitMaterialType:            req.UnitMaterialType,
			ConvertId:                   req.ConvertId,
			Url:                         req.Url,
			SeriesId:                    req.SeriesId,
			ScheduleTime:                req.ScheduleTime,
			OuterLoopNative:             req.OuterLoopNative,
			CpaBid:                      req.CpaBid,
			PlayableOrientation:         req.PlayableOrientation,
			TemplateId:                  req.TemplateId,
			Bid:                         req.Bid,
			AdvCardOption:               req.AdvCardOption,
		})
		liberr.ErrIsNil(ctx, err, "修改失败")
	})
	return
}

func (s *sKsAdvertiserUnit) Delete(ctx context.Context, unitIds []int64) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.KsAdvertiserUnit.Ctx(ctx).Delete(dao.KsAdvertiserUnit.Columns().UnitId+" in (?)", unitIds)
		liberr.ErrIsNil(ctx, err, "删除失败")
	})
	return
}

func (s *sKsAdvertiserUnit) UpdateUnitInfo(ctx context.Context, req *model.KsAdvertiserUnitUpdateReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		unitInfo := do.KsAdvertiserUnit{}
		if req.UnitName != "" {
			unitInfo.UnitName = req.UnitName
		}
		if req.PutStatus != nil {
			unitInfo.PutStatus = req.PutStatus
		}
		if req.DayBudget != nil {
			unitInfo.DayBudget = req.DayBudget
		}
		if req.RoiRatio != nil {
			unitInfo.RoiRatio = req.RoiRatio
		}
		if req.BeginTime != "" {
			unitInfo.BeginTime = gtime.NewFromStr(req.BeginTime)
		}
		if req.EndTime != "" {
			unitInfo.EndTime = gtime.NewFromStr(req.EndTime)
		}
		if req.ScheduleTime != "" {
			unitInfo.ScheduleTime = req.ScheduleTime
		}
		_, err = dao.KsAdvertiserUnit.Ctx(ctx).WherePri(req.UnitId).Update(unitInfo)
		liberr.ErrIsNil(ctx, err, "修改失败")
	})
	return
}

// ManualSyncUnit 手动同步广告组
func (s *sKsAdvertiserUnit) ManualSyncUnit(ctx context.Context, advertiserIds []int64, startTime, endTime string) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		for _, advertiserId := range advertiserIds {
			accessToken, _ := service.KsAdvertiserAccountInfo().GetAccessToken(ctx, advertiserId)
			if accessToken == "" {
				continue
			}
			var page = 1
			var pageSize = 200
			for {
				unitListRes, err1 := ksApi.GetKSApiClient().GetDspUnitListService.
					AccessToken(accessToken).SetReq(ksApi.GetDspUnitListReq{
					Page:         page,
					PageSize:     pageSize,
					StartDate:    startTime,
					EndDate:      endTime,
					AdvertiserID: advertiserId,
				}).Do()
				if err1 != nil {
					g.Log().Errorf(ctx, "同步广告主%v广告组列表失败：%v", advertiserId, err1)
					break
				}
				if unitListRes.Data == nil || len(unitListRes.Data.Details) == 0 {
					break
				}
				var unitListAdd []*model.KsAdvertiserUnitAddReq
				for _, unit := range unitListRes.Data.Details {
					unitReq := &model.KsAdvertiserUnitAddReq{}
					_ = gconv.Struct(unit, unitReq)
					unitReq.AdvertiserId = gconv.Int(advertiserId)
					unitListAdd = append(unitListAdd, unitReq)
				}
				_ = s.BatchAdd(ctx, unitListAdd)
				if page*pageSize >= unitListRes.Data.TotalCount {
					break
				}
				page++
			}
		}
	})
	return
}

// EditUnitName 修改广告组名称
func (s *sKsAdvertiserUnit) EditUnitName(ctx context.Context, unitId int64, unitName string) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		unitInfo, _ := s.GetByUnitId(ctx, unitId)
		if unitInfo == nil {
			liberr.ErrIsNil(ctx, errors.New("广告组不存在"))
		}
		advertiserId := gconv.Int64(unitInfo.AdvertiserId)
		accessToken, _ := service.KsAdvertiserAccountInfo().GetAccessToken(ctx, advertiserId)
		if accessToken == "" {
			liberr.ErrIsNil(ctx, errors.New(commonConsts.ErrMsgGetAccessToken))
		}
		_, err = ksApi.GetKSApiClient().UpdateUnitService.
			AccessToken(accessToken).SetReq(ksApi.UpdateUnitReq{
			UnitId:       unitId,
			AdvertiserId: advertiserId,
			UnitName:     unitName,
		}).Do()
		liberr.ErrIsNil(ctx, err, "修改广告组名称失败")
	})
	return
}
