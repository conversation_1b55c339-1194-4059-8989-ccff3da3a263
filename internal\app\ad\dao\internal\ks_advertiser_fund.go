// ==========================================================================
// GFast自动生成dao internal操作代码。
// 生成日期：2025-08-12 15:45:08
// 生成路径: internal/app/ad/dao/internal/ks_advertiser_fund.go
// 生成人：cyao
// desc:广告主资金信息
// company:云南奇讯科技有限公司
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// KsAdvertiserFundDao is the manager for logic model data accessing and custom defined data operations functions management.
type KsAdvertiserFundDao struct {
	table   string                  // Table is the underlying table name of the DAO.
	group   string                  // Group is the database configuration group name of current DAO.
	columns KsAdvertiserFundColumns // Columns is the short type for Columns, which contains all the column names of Table for convenient usage.
}

// KsAdvertiserFundColumns defines and stores column names for table ks_advertiser_fund.
type KsAdvertiserFundColumns struct {
	AdvertiserId    string // 快手账户ID（广告账户ID）
	DirectRebate    string // 激励余额
	ContractRebate  string // 框返余额
	RechargeBalance string // 充值余额
	Balance         string // 账户总余额
	CreatedAt       string // 创建时间
	UpdatedAt       string // 更新时间
}

var ksAdvertiserFundColumns = KsAdvertiserFundColumns{
	AdvertiserId:    "advertiser_id",
	DirectRebate:    "direct_rebate",
	ContractRebate:  "contract_rebate",
	RechargeBalance: "recharge_balance",
	Balance:         "balance",
	CreatedAt:       "created_at",
	UpdatedAt:       "updated_at",
}

// NewKsAdvertiserFundDao creates and returns a new DAO object for table data access.
func NewKsAdvertiserFundDao() *KsAdvertiserFundDao {
	return &KsAdvertiserFundDao{
		group:   "default",
		table:   "ks_advertiser_fund",
		columns: ksAdvertiserFundColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *KsAdvertiserFundDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *KsAdvertiserFundDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *KsAdvertiserFundDao) Columns() KsAdvertiserFundColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *KsAdvertiserFundDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *KsAdvertiserFundDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *KsAdvertiserFundDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
