// ==========================================================================
// GFast自动生成model操作代码。
// 生成日期：2025-08-12 15:45:06
// 生成路径: internal/app/ad/model/ks_advertiser_agent_info.go
// 生成人：cyao
// desc:快手代理商信息表
// company:云南奇讯科技有限公司
// ==========================================================================

package model

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
	comModel "github.com/tiger1103/gfast/v3/internal/app/common/model"
)

// KsAdvertiserAgentInfoInfoRes is the golang structure for table ks_advertiser_agent_info.
type KsAdvertiserAgentInfoInfoRes struct {
	gmeta.Meta         `orm:"table:ks_advertiser_agent_info"`
	Id                 uint64      `orm:"id,primary" json:"id" dc:"自增主键"`                             // 自增主键
	AgentAccountId     int64       `orm:"agent_account_id" json:"agentAccountId" dc:"代理商账户id"`        // 代理商账户id
	AuthorizeKsAccount int64       `orm:"authorize_ks_account" json:"authorizeKsAccount" dc:"授权快手账号"` // 授权快手账号
	Owner              int         `orm:"owner" json:"owner" dc:"归属人员id"`                             // 归属人员id
	CreatedAt          *gtime.Time `orm:"created_at" json:"createdAt" dc:"创建时间"`                      // 创建时间
	UpdatedAt          *gtime.Time `orm:"updated_at" json:"updatedAt" dc:"更新时间"`                      // 更新时间
	IsShare            int         `orm:"is_share" json:"isShare" dc:"0非共享1共享"`                       // 0非共享1共享
	AppId              int         `orm:"app_id" json:"appId" dc:"app_id"`
}

type KsAdvertiserAgentInfoListRes struct {
	Id                 uint64      `json:"id" dc:"自增主键"`
	AgentAccountId     int64       `json:"agentAccountId" dc:"代理商账户id"`
	AuthorizeKsAccount int64       `json:"authorizeKsAccount" dc:"授权快手账号"`
	Owner              int         `json:"owner" dc:"归属人员id"`
	OwnerUserName      string      `json:"ownerUserName" dc:"归属人员名称"`
	CreatedAt          *gtime.Time `json:"createdAt" dc:"创建时间"`
	IsShare            int         `json:"isShare" dc:"0非共享1共享"`
	AppId              int         `json:"appId" dc:"app_id"`
}

// KsAdvertiserAgentInfoSearchReq 分页请求参数
type KsAdvertiserAgentInfoSearchReq struct {
	comModel.PageReq
	Id                 string `p:"id" dc:"自增主键"`                                                             //自增主键
	AgentAccountId     string `p:"agentAccountId" v:"agentAccountId@integer#代理商账户id需为整数" dc:"代理商账户id"`       //代理商账户id
	AuthorizeKsAccount string `p:"authorizeKsAccount" v:"authorizeKsAccount@integer#授权快手账号需为整数" dc:"授权快手账号"` //授权快手账号
	Owner              string `p:"owner" v:"owner@integer#归属人员id需为整数" dc:"归属人员id"`                           //归属人员id
	UserIds            []int  `p:"userIds"   dc:"用户IDs"`
	CreatedAt          string `p:"createdAt" v:"createdAt@datetime#创建时间需为YYYY-MM-DD hh:mm:ss格式" dc:"创建时间"` //创建时间
	IsShare            string `p:"isShare" v:"isShare@integer#0非共享1共享需为整数" dc:"0非共享1共享"`                   //0非共享1共享
	StartTime          string `p:"startTime" v:"startTime@datetime#开始时间需为YYYY-MM-DD hh:mm:ss格式" dc:"开始时间"`
	EndTime            string `p:"endTime" v:"endTime@datetime#结束时间需为YYYY-MM-DD hh:mm:ss格式" dc:"结束时间"`
}

// KsAdvertiserAgentInfoSearchRes 列表返回结果
type KsAdvertiserAgentInfoSearchRes struct {
	comModel.ListRes
	List []*KsAdvertiserAgentInfoListRes `json:"list"`
}
type KsAdvertiserAgentInfoShareReq struct {
	UserIds []int  `p:"userIds" v:"required#用户ID不能为空" dc:"用户ID"`
	AgentId uint64 `p:"agentId" v:"required#代理商ID不能为空" dc:"代理商ID"`
}

type KsAdvertiserAgentInfoShareRes struct {
	SuccessCount  int      `json:"successCount" dc:"成功数量"`
	FailCount     int      `json:"failCount" dc:"失败数量"`
	FailUserNames []string `json:"failUserNames" dc:"失败用户ID"`
}

// KsAdvertiserAgentInfoAddReq 添加操作请求参数
type KsAdvertiserAgentInfoAddReq struct {
	AgentAccountId     int64 `p:"agentAccountId" v:"required#代理商账户id不能为空" dc:"代理商账户id"`
	AuthorizeKsAccount int64 `p:"authorizeKsAccount"  dc:"授权快手账号"`
	Owner              int   `p:"owner"  dc:"归属人员id"`
	IsShare            int   `p:"isShare"  dc:"0非共享1共享"`
	AppId              int   `p:"appId"  dc:"app_id"`
}

// KsAdvertiserAgentInfoEditReq 修改操作请求参数
type KsAdvertiserAgentInfoEditReq struct {
	Id                 uint64 `p:"id" v:"required#主键ID不能为空" dc:"自增主键"`
	AgentAccountId     int64  `p:"agentAccountId" v:"required#代理商账户id不能为空" dc:"代理商账户id"`
	AuthorizeKsAccount int64  `p:"authorizeKsAccount"  dc:"授权快手账号"`
	Owner              int    `p:"owner"  dc:"归属人员id"`
	IsShare            int    `p:"isShare"  dc:"0非共享1共享"`
	AppId              int    `p:"appId"  dc:"app_id"`
}
