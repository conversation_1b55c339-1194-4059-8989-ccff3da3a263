// ==========================================================================
// GFast自动生成model entity操作代码。
// 生成日期：2025-08-22 11:52:42
// 生成路径: internal/app/ad/model/entity/ks_advertiser_strategy_title.go
// 生成人：cq
// desc:快手策略组-文案
// company:云南奇讯科技有限公司
// ==========================================================================

package do

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
)

// KsAdvertiserStrategyTitle is the golang structure for table ks_advertiser_strategy_title.
type KsAdvertiserStrategyTitle struct {
	gmeta.Meta            `orm:"table:ks_advertiser_strategy_title, do:true"`
	Id                    interface{} `orm:"id,primary" json:"id"`                                 // 主键ID
	StrategyId            interface{} `orm:"strategy_id" json:"strategyId"`                        // 策略组ID
	TaskId                interface{} `orm:"task_id" json:"taskId"`                                // 任务ID
	TitleAllocationMethod interface{} `orm:"title_allocation_method" json:"titleAllocationMethod"` // 标题分配方式 AUTO TEST
	TitleData             interface{} `orm:"title_data" json:"titleData"`                          // 标题数据，包含标题详细信息、分类、性能数据等
	CreatedAt             *gtime.Time `orm:"created_at" json:"createdAt"`                          // 创建时间
	UpdatedAt             *gtime.Time `orm:"updated_at" json:"updatedAt"`                          // 更新时间
	DeletedAt             *gtime.Time `orm:"deleted_at" json:"deletedAt"`                          // 删除时间
}
