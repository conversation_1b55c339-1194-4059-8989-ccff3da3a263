// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2025-08-19 10:36:58
// 生成路径: api/v1/ad/ks_advertiser_common_asset_title.go
// 生成人：cq
// desc:快手通用资产-标题库相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package ad

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
)

// KsAdvertiserCommonAssetTitleSearchReq 分页请求参数
type KsAdvertiserCommonAssetTitleSearchReq struct {
	g.Meta `path:"/list" tags:"快手通用资产-标题库" method:"post" summary:"快手通用资产-标题库列表"`
	commonApi.Author
	model.KsAdvertiserCommonAssetTitleSearchReq
}

// KsAdvertiserCommonAssetTitleSearchRes 列表返回结果
type KsAdvertiserCommonAssetTitleSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.KsAdvertiserCommonAssetTitleSearchRes
}

// KsAdvertiserCommonAssetTitleAddReq 添加操作请求参数
type KsAdvertiserCommonAssetTitleAddReq struct {
	g.Meta `path:"/add" tags:"快手通用资产-标题库" method:"post" summary:"快手通用资产-标题库添加"`
	commonApi.Author
	*model.KsAdvertiserCommonAssetTitleBatchAddReq
}

// KsAdvertiserCommonAssetTitleAddRes 添加操作返回结果
type KsAdvertiserCommonAssetTitleAddRes struct {
	commonApi.EmptyRes
}

// KsAdvertiserCommonAssetTitleEditReq 修改操作请求参数
type KsAdvertiserCommonAssetTitleEditReq struct {
	g.Meta `path:"/edit" tags:"快手通用资产-标题库" method:"put" summary:"快手通用资产-标题库修改"`
	commonApi.Author
	*model.KsAdvertiserCommonAssetTitleEditReq
}

// KsAdvertiserCommonAssetTitleEditRes 修改操作返回结果
type KsAdvertiserCommonAssetTitleEditRes struct {
	commonApi.EmptyRes
}

// KsAdvertiserCommonAssetTitleBatchEditReq 修改操作请求参数
type KsAdvertiserCommonAssetTitleBatchEditReq struct {
	g.Meta `path:"/batchEditCategory" tags:"快手通用资产-标题库" method:"put" summary:"快手通用资产-标题库批量修改分类"`
	commonApi.Author
	*model.KsAdvertiserCommonAssetTitleBatchEditReq
}

// KsAdvertiserCommonAssetTitleBatchEditRes 修改操作返回结果
type KsAdvertiserCommonAssetTitleBatchEditRes struct {
	commonApi.EmptyRes
}

// KsAdvertiserCommonAssetTitleGetReq 获取一条数据请求
type KsAdvertiserCommonAssetTitleGetReq struct {
	g.Meta `path:"/get" tags:"快手通用资产-标题库" method:"get" summary:"获取快手通用资产-标题库信息"`
	commonApi.Author
	Id int `p:"id" v:"required#主键必须"` //通过主键获取
}

// KsAdvertiserCommonAssetTitleGetRes 获取一条数据结果
type KsAdvertiserCommonAssetTitleGetRes struct {
	g.Meta `mime:"application/json"`
	*model.KsAdvertiserCommonAssetTitleInfoRes
}

// KsAdvertiserCommonAssetTitleDeleteReq 删除数据请求
type KsAdvertiserCommonAssetTitleDeleteReq struct {
	g.Meta `path:"/delete" tags:"快手通用资产-标题库" method:"post" summary:"删除快手通用资产-标题库"`
	commonApi.Author
	Ids []int `p:"ids" v:"required#主键必须"` //通过主键删除
}

// KsAdvertiserCommonAssetTitleDeleteRes 删除数据返回
type KsAdvertiserCommonAssetTitleDeleteRes struct {
	commonApi.EmptyRes
}
