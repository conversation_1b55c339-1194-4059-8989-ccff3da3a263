// ==========================================================================
// GFast自动生成dao internal操作代码。
// 生成日期：2025-08-13 16:33:56
// 生成路径: internal/app/ad/dao/internal/ks_advertiser_unit.go
// 生成人：cyao
// desc:快手广告组
// company:云南奇讯科技有限公司
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// KsAdvertiserUnitDao is the manager for logic model data accessing and custom defined data operations functions management.
type KsAdvertiserUnitDao struct {
	table   string                  // Table is the underlying table name of the DAO.
	group   string                  // Group is the database configuration group name of current DAO.
	columns KsAdvertiserUnitColumns // Columns is the short type for Columns, which contains all the column names of Table for convenient usage.
}

// KsAdvertiserUnitColumns defines and stores column names for table ks_advertiser_unit.
type KsAdvertiserUnitColumns struct {
	UnitId                   string // 单元 ID
	AdvertiserId             string // 广告id
	CampaignId               string // 广告计划 ID
	LinkIntegrationType      string // 链接整合类型
	AssetMining              string // 资产挖掘
	SiteType                 string // 预约广告 1:IOS 预约 缺省为不传或传 0
	AdType                   string // 广告计划类型 0:信息流，1:搜索
	DpaDynamicParamsForUri   string // 落地页链接动态参数
	SchemaUri                string // 调起链接、提升应用活跃营销目标的调起链接
	ProductImage             string // 商品主图
	PackageId                string // 新版应用中心应用ID
	BidType                  string // 出价类型 1：CPM，2：CPC，6：OCPC(使用 OCPC 代表 OCPX)，10：OCPM，20：eCPC
	ProductPrice             string // 商品价格 元
	PutStatus                string // 投放状态 1：投放中；2：暂停 3：删除
	SmartCover               string // 智能封面 是否开启智能抽帧
	DpaOuterIds              string // DPA外部商品id集合
	SeriesPayTemplateIdMulti string // 短剧付费模版列表
	DpaUnitSubType           string // 商品广告类型：1-DPA，2-SDPA，3-动态商品卡
	ULink                    string // ios系统的ulink链接
	AppStore                 string // 应用商店列表
	AppDownloadType          string // 应用下载类型 0：直接下载1：落地页下载（仅在提升应用安装计划下生效）
	DspVersion               string // DSP版本
	PlayableId               string // 可试玩 ID 可选字段，开启试玩时存在，否则不存在
	EpisodeId                string // 剧集 ID
	PlayableSwitch           string // 可试玩开关
	ProductId                string // 产品 ID
	SchemaId                 string // 微信小程序外部调起链接（部分场景有效）
	UnitSource               string // 广告组来源 0:常规（非托管）、1:托管
	SeriesPayTemplateId      string // 付费模板（短剧推广时支持）
	DayBudget                string // 日预算 单位：厘
	OcpxActionType           string // 优化目标（枚举值见文档）
	UseAppMarket             string // 是否使用应用市场 0：未设置 1：优先从系统应用商店下载
	TargetExplore            string // 是否开启搜索人群探索
	ComponentId              string // 组件 ID
	DpaDynamicParams         string // DPA 动态参数 开关
	CreateTime               string // 创建时间
	PlayButton               string // 试玩按钮文字内容
	UrlType                  string // URL 类型（特定计划下返回）
	DpaCategories            string // DPA 类别
	ProductName              string // 产品名称
	SeriesPayMode            string // 付费模式（短剧推广时返回）
	ShowMode                 string // 展示模式 0：未知，1：轮播，2：优选
	UnitName                 string // 广告组名称
	ExtendSearch             string // 智能扩词开启状态
	QuickSearch              string // 是否开启快投
	SiteId                   string // 建站ID / 安卓下载中间页ID
	SeriesCardInfo           string // 剧集卡片信息

	Status                      string // 广告组状态
	ConsultId                   string // 咨询组件使用情况
	RoiRatio                    string // 付费 ROI 系数
	LiveComponentType           string // 直播组件类型
	SearchPopulationRetargeting string // 是否开启人群追投
	EnhanceConversionType       string // 增强目标
	UnitType                    string // 创意制作方式
	OuterId                     string // 外部 ID
	StudyStatus                 string // 学习状态
	SeriesCardType              string // 剧集卡片类型
	CustomMiniAppData           string // 自定义小程序数据
	UpdateTime                  string // 更新时间
	ImMessageMount              string // IM 消息挂载
	LibraryId                   string // 素材库 ID
	DeepConversionType          string // 深度转化类型
	DeepConversionBid           string // 深度转化出价
	KwaiBookId                  string // 快手书籍 ID
	DpaDynamicParamsForDp       string // DP 动态参数
	PageAuditStatus             string // 页面审核状态
	JingleBellId                string // 铃铛 ID
	LiveUserId                  string // 直播用户 ID
	PlayableUrl                 string // 可试玩 URL
	PlayableFileName            string // 可试玩文件名
	WebUriType                  string // Web URI 类型
	ReviewDetail                string // 审核详情
	EndTime                     string // 结束时间
	CompensateStatus            string // 补偿状态
	SceneId                     string // 场景 ID
	BeginTime                   string // 开始时间
	UnitMaterialType            string // 单元素材类型
	ConvertId                   string // 转化 ID
	Url                         string // 链接
	SeriesId                    string // 系列 ID
	ScheduleTime                string // 日程时间
	OuterLoopNative             string // 外循环原生
	CpaBid                      string // CPA 出价
	PlayableOrientation         string // 可试玩方向
	TemplateId                  string // 模板 ID
	Bid                         string // 出价
	AdvCardOption               string // 广告卡片选项
	CreatedAt                   string // 创建时间
	DeletedAt                   string // 删除时间
}

var ksAdvertiserUnitColumns = KsAdvertiserUnitColumns{
	UnitId:                      "unit_id",
	AdvertiserId:                "advertiser_id",
	CampaignId:                  "campaign_id",
	LinkIntegrationType:         "link_integration_type",
	AssetMining:                 "asset_mining",
	SiteType:                    "site_type",
	AdType:                      "ad_type",
	DpaDynamicParamsForUri:      "dpa_dynamic_params_for_uri",
	SchemaUri:                   "schema_uri",
	ProductImage:                "product_image",
	PackageId:                   "package_id",
	BidType:                     "bid_type",
	ProductPrice:                "product_price",
	PutStatus:                   "put_status",
	SmartCover:                  "smart_cover",
	DpaOuterIds:                 "dpa_outer_ids",
	SeriesPayTemplateIdMulti:    "series_pay_template_id_multi",
	DpaUnitSubType:              "dpa_unit_sub_type",
	ULink:                       "u_link",
	AppStore:                    "app_store",
	AppDownloadType:             "app_download_type",
	DspVersion:                  "dsp_version",
	PlayableId:                  "playable_id",
	EpisodeId:                   "episode_id",
	PlayableSwitch:              "playable_switch",
	ProductId:                   "product_id",
	SchemaId:                    "schema_id",
	UnitSource:                  "unit_source",
	SeriesPayTemplateId:         "series_pay_template_id",
	DayBudget:                   "day_budget",
	OcpxActionType:              "ocpx_action_type",
	UseAppMarket:                "use_app_market",
	TargetExplore:               "target_explore",
	ComponentId:                 "component_id",
	DpaDynamicParams:            "dpa_dynamic_params",
	CreateTime:                  "create_time",
	PlayButton:                  "play_button",
	UrlType:                     "url_type",
	DpaCategories:               "dpa_categories",
	ProductName:                 "product_name",
	SeriesPayMode:               "series_pay_mode",
	ShowMode:                    "show_mode",
	UnitName:                    "unit_name",
	ExtendSearch:                "extend_search",
	QuickSearch:                 "quick_search",
	SiteId:                      "site_id",
	SeriesCardInfo:              "series_card_info",
	Status:                      "status",
	ConsultId:                   "consult_id",
	RoiRatio:                    "roi_ratio",
	LiveComponentType:           "live_component_type",
	SearchPopulationRetargeting: "search_population_retargeting",
	EnhanceConversionType:       "enhance_conversion_type",
	UnitType:                    "unit_type",
	OuterId:                     "outer_id",
	StudyStatus:                 "study_status",
	SeriesCardType:              "series_card_type",
	CustomMiniAppData:           "custom_mini_app_data",
	UpdateTime:                  "update_time",
	ImMessageMount:              "im_message_mount",
	LibraryId:                   "library_id",
	DeepConversionType:          "deep_conversion_type",
	DeepConversionBid:           "deep_conversion_bid",
	KwaiBookId:                  "kwai_book_id",
	DpaDynamicParamsForDp:       "dpa_dynamic_params_for_dp",
	PageAuditStatus:             "page_audit_status",
	JingleBellId:                "jingle_bell_id",
	LiveUserId:                  "live_user_id",
	PlayableUrl:                 "playable_url",
	PlayableFileName:            "playable_file_name",
	WebUriType:                  "web_uri_type",
	ReviewDetail:                "review_detail",
	EndTime:                     "end_time",
	CompensateStatus:            "compensate_status",
	SceneId:                     "scene_id",
	BeginTime:                   "begin_time",
	UnitMaterialType:            "unit_material_type",
	ConvertId:                   "convert_id",
	Url:                         "url",
	SeriesId:                    "series_id",
	ScheduleTime:                "schedule_time",
	OuterLoopNative:             "outer_loop_native",
	CpaBid:                      "cpa_bid",
	PlayableOrientation:         "playable_orientation",
	TemplateId:                  "template_id",
	Bid:                         "bid",
	AdvCardOption:               "adv_card_option",
	CreatedAt:                   "created_at",
	DeletedAt:                   "deleted_at",
}

// NewKsAdvertiserUnitDao creates and returns a new DAO object for table data access.
func NewKsAdvertiserUnitDao() *KsAdvertiserUnitDao {
	return &KsAdvertiserUnitDao{
		group:   "default",
		table:   "ks_advertiser_unit",
		columns: ksAdvertiserUnitColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *KsAdvertiserUnitDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *KsAdvertiserUnitDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *KsAdvertiserUnitDao) Columns() KsAdvertiserUnitColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *KsAdvertiserUnitDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *KsAdvertiserUnitDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *KsAdvertiserUnitDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
