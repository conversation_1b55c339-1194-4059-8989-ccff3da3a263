// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2025-08-15
// 生成路径: internal/app/ad/logic/adBatchTask/ks_batch_update_campaign_processor.go
// 生成人：AI Assistant
// desc:快手广告计划批量任务处理器
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	commonConsts "github.com/tiger1103/gfast/v3/internal/app/common/consts"
	"github.com/tiger1103/gfast/v3/internal/app/common/logic/channelStream"
	ksApi "github.com/tiger1103/gfast/v3/library/advertiser/ks/api"
)

var campaignStreamConsumer *channelStream.StreamConsumer

func init() {
	go StartKsBatchUpdateCampaignTaskConsumer()
}

// StartKsBatchUpdateCampaignTaskConsumer 启动快手广告计划批量任务消费者
func StartKsBatchUpdateCampaignTaskConsumer() {
	dev := g.Cfg().MustGet(context.Background(), "redis.channel.stream.env").String()
	config := &channelStream.StreamConsumerConfig{
		StreamName:    fmt.Sprintf(commonConsts.KsBatchUpdateCampaignChannelStream, dev),
		GroupName:     fmt.Sprintf(commonConsts.KsBatchUpdateCampaignChannelGroup1, dev),
		ConsumerName:  fmt.Sprintf(commonConsts.KsBatchUpdateCampaignChannelConsumer1, dev),
		BlockTime:     5 * time.Second,
		BatchSize:     10,
		LockTimeout:   10 * time.Second,
		MessageField:  commonConsts.KsBatchUpdateCampaignMessageField,
		CleanupCron:   "0 0 1 * * ?", // 每天凌晨1点执行清理
		MaxStreamSize: 1000,
	}
	processor := NewKsBatchUpdateCampaignProcessor()
	campaignStreamConsumer = channelStream.NewStreamConsumer(config, processor)
	campaignStreamConsumer.Start()
}

// KsBatchUpdateCampaignProcessor 快手广告计划批量任务处理器
type KsBatchUpdateCampaignProcessor struct{}

// NewKsBatchUpdateCampaignProcessor 创建快手广告计划批量任务处理器
func NewKsBatchUpdateCampaignProcessor() *KsBatchUpdateCampaignProcessor {
	return &KsBatchUpdateCampaignProcessor{}
}

// ProcessTask 处理任务
func (s *KsBatchUpdateCampaignProcessor) ProcessTask(ctx context.Context, taskData []byte) (success bool, err error) {
	var task model.KsBatchUpdateCampaignTaskInfo
	if err = json.Unmarshal(taskData, &task); err != nil {
		return false, err
	}

	optResult, err := s.processTask(task)
	if err != nil {
		return false, err
	}

	return optResult == commonConsts.OptResultSuccess, nil
}

// UpdateTaskStatus 更新任务状态
func (s *KsBatchUpdateCampaignProcessor) UpdateTaskStatus(ctx context.Context, taskId string, success bool) error {
	taskEditReq := &model.AdBatchTaskEditReq{
		TaskId: taskId,
	}
	if success {
		taskEditReq.SuccessNum = 1
	} else {
		taskEditReq.FailNum = 1
	}
	return service.AdBatchTask().EditOptStatus(ctx, taskEditReq)
}

// GetTaskId 获取任务ID
func (s *KsBatchUpdateCampaignProcessor) GetTaskId(taskData []byte) (string, error) {
	var task model.KsBatchUpdateCampaignTaskInfo
	if err := json.Unmarshal(taskData, &task); err != nil {
		return "", err
	}
	return task.TaskId, nil
}

// processTask 任务处理逻辑
func (s *KsBatchUpdateCampaignProcessor) processTask(task model.KsBatchUpdateCampaignTaskInfo) (optResult string, err error) {
	ctx := context.Background()
	err = g.Try(ctx, func(ctx context.Context) {
		var errMsg string
		accessToken, _ := service.KsAdvertiserAccountInfo().GetAccessToken(ctx, task.AdvertiserId)
		if accessToken == "" {
			errMsg = commonConsts.ErrMsgGetAccessToken
			optResult = commonConsts.OptResultFail
		} else {
			err = s.executeOperation(ctx, accessToken, task)
			if err != nil {
				errMsg = err.Error()
				optResult = commonConsts.OptResultFail
			} else {
				optResult = commonConsts.OptResultSuccess
			}
		}
		// 记录任务详情
		detail := &model.AdBatchTaskDetailAddReq{
			TaskId:       task.TaskId,
			SerialNumber: task.SerialNumber,
			AdvertiserId: gconv.String(task.AdvertiserId),
			CampaignId:   task.CampaignId,
			OptResult:    optResult,
			ErrMsg:       errMsg,
		}
		err = service.AdBatchTaskDetail().Add(ctx, detail)
	})
	return
}

// executeOperation 执行具体的操作
func (s *KsBatchUpdateCampaignProcessor) executeOperation(ctx context.Context, accessToken string, task model.KsBatchUpdateCampaignTaskInfo) error {
	switch task.OptType {
	case commonConsts.OptTypePlacement, commonConsts.OptTypePause, commonConsts.OptTypeDelete:
		return s.updateCampaignStatus(ctx, accessToken, task)
	case commonConsts.OptTypeDayBudget:
		return s.updateDayBudget(ctx, accessToken, task)
	default:
		return errors.New(commonConsts.ErrMsgUnSupportOptType)
	}
}

// updateCampaignStatus 更新广告计划状态
func (s *KsBatchUpdateCampaignProcessor) updateCampaignStatus(ctx context.Context, accessToken string, task model.KsBatchUpdateCampaignTaskInfo) error {
	var putStatus int64
	switch task.OptType {
	case commonConsts.OptTypePlacement:
		putStatus = 1
	case commonConsts.OptTypePause:
		putStatus = 2
	case commonConsts.OptTypeDelete:
		putStatus = 3
	}
	_, err := ksApi.GetKSApiClient().UpdateCampaignStatusService.AccessToken(accessToken).
		SetReq(ksApi.UpdateCampaignStatusReq{
			AdvertiserId: task.AdvertiserId,
			CampaignId:   task.CampaignId,
			PutStatus:    putStatus,
		}).Do()
	if err == nil {
		_ = service.KsAdvertiserCampaign().UpdateCampaignInfo(ctx, &model.KsAdvertiserCampaignUpdateReq{
			CampaignId: task.CampaignId,
			PutStatus:  &putStatus,
		})
	}
	return err
}

// updateDayBudget 更新日预算
func (s *KsBatchUpdateCampaignProcessor) updateDayBudget(ctx context.Context, accessToken string, task model.KsBatchUpdateCampaignTaskInfo) error {
	dayBudget := task.DayBudget
	_, err := ksApi.GetKSApiClient().UpdateCampaignService.AccessToken(accessToken).
		SetReq(ksApi.UpdateCampaignReq{
			AdvertiserId: task.AdvertiserId,
			CampaignId:   task.CampaignId,
			DayBudget:    &dayBudget,
		}).Do()
	if err == nil {
		_ = service.KsAdvertiserCampaign().UpdateCampaignInfo(ctx, &model.KsAdvertiserCampaignUpdateReq{
			CampaignId: task.CampaignId,
			DayBudget:  &dayBudget,
		})
	}
	return err
}
