// ==========================================================================
// GFast自动生成dao internal操作代码。
// 生成日期：2025-08-22 11:52:27
// 生成路径: internal/app/ad/dao/internal/ks_advertiser_strategy_material.go
// 生成人：cq
// desc:快手策略组-素材
// company:云南奇讯科技有限公司
// ==========================================================================

package internal

import (
	"context"
	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// KsAdvertiserStrategyMaterialDao is the manager for logic model data accessing and custom defined data operations functions management.
type KsAdvertiserStrategyMaterialDao struct {
	table   string                              // Table is the underlying table name of the DAO.
	group   string                              // Group is the database configuration group name of current DAO.
	columns KsAdvertiserStrategyMaterialColumns // Columns is the short type for Columns, which contains all the column names of Table for convenient usage.
}

// KsAdvertiserStrategyMaterialColumns defines and stores column names for table ks_advertiser_strategy_material.
type KsAdvertiserStrategyMaterialColumns struct {
	Id                       string // 主键ID
	StrategyId               string // 策略组ID
	TaskId                   string // 任务ID
	MaterialAllocationMethod string // 素材分配方式 平均分配：AVERAGE 全账户服用：SAME 分账户选择：ADVERTISER
	VideoCount               string // 视频数量
	VideoLocked              string // 视频锁定状态 0：未锁定 1：锁定
	ImageCount               string // 图片数量
	ImageLocked              string // 图片锁定状态 0：未锁定 1：锁定
	DescriptionMatchMethod   string // 文案匹配方式 MANUAL MATERIAL CREATIVE
	CreativeMaterialData     string // 创意素材数据，包含广告主ID、素材列表等信息
	CreatedAt                string // 创建时间
	UpdatedAt                string // 更新时间
	DeletedAt                string // 删除时间
}

var ksAdvertiserStrategyMaterialColumns = KsAdvertiserStrategyMaterialColumns{
	Id:                       "id",
	StrategyId:               "strategy_id",
	TaskId:                   "task_id",
	MaterialAllocationMethod: "material_allocation_method",
	VideoCount:               "video_count",
	VideoLocked:              "video_locked",
	ImageCount:               "image_count",
	ImageLocked:              "image_locked",
	DescriptionMatchMethod:   "description_match_method",
	CreativeMaterialData:     "creative_material_data",
	CreatedAt:                "created_at",
	UpdatedAt:                "updated_at",
	DeletedAt:                "deleted_at",
}

// NewKsAdvertiserStrategyMaterialDao creates and returns a new DAO object for table data access.
func NewKsAdvertiserStrategyMaterialDao() *KsAdvertiserStrategyMaterialDao {
	return &KsAdvertiserStrategyMaterialDao{
		group:   "default",
		table:   "ks_advertiser_strategy_material",
		columns: ksAdvertiserStrategyMaterialColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *KsAdvertiserStrategyMaterialDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *KsAdvertiserStrategyMaterialDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *KsAdvertiserStrategyMaterialDao) Columns() KsAdvertiserStrategyMaterialColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *KsAdvertiserStrategyMaterialDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *KsAdvertiserStrategyMaterialDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *KsAdvertiserStrategyMaterialDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
