// ==========================================================================
// GFast自动生成model entity操作代码。
// 生成日期：2025-08-23 17:40:21
// 生成路径: internal/app/ad/model/entity/ks_advertiser_strategy_task_creative.go
// 生成人：cyao
// desc:快手广告搭建-任务-广告创意
// company:云南奇讯科技有限公司
// ==========================================================================

package do

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
)

// KsAdvertiserStrategyTaskCreative is the golang structure for table ks_advertiser_strategy_task_creative.
type KsAdvertiserStrategyTaskCreative struct {
	gmeta.Meta     `orm:"table:ks_advertiser_strategy_task_creative, do:true"`
	TaskCreativeId interface{} `orm:"task_creative_id,primary" json:"taskCreativeId"` // 任务创意ID
	CreativeId     interface{} `orm:"creative_id" json:"creativeId"`                  // 创意 id
	CreativeName   interface{} `orm:"creative_name" json:"creativeName"`              // 创意名称
	TaskId         interface{} `orm:"task_id" json:"taskId"`                          // 任务ID
	TaskCampaignId interface{} `orm:"task_campaign_id" json:"taskCampaignId"`         // 任务计划ID
	TaskUnitId     interface{} `orm:"task_unit_id" json:"taskUnitId"`                 // 任务广告组ID
	AdvertiserId   interface{} `orm:"advertiser_id" json:"advertiserId"`              // 广告主ID
	AdvertiserNick interface{} `orm:"advertiser_nick" json:"advertiserNick"`          // 广告主名称
	ErrMsg         interface{} `orm:"err_msg" json:"errMsg"`                          // 失败原因
	Status         interface{} `orm:"status" json:"status"`                           // 项目状态 等待提交：INIT 已终止：TERMINATED 成功：SUCCESS 失败：ERROR
	CreativeData   interface{} `orm:"creative_data" json:"creativeData"`              // 创建创意数据
	CreatedAt      *gtime.Time `orm:"created_at" json:"createdAt"`                    // 创建时间
	UpdatedAt      *gtime.Time `orm:"updated_at" json:"updatedAt"`                    // 更新时间
	DeletedAt      *gtime.Time `orm:"deleted_at" json:"deletedAt"`                    // 删除时间
}
