// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2025-08-20 11:00:00
// 生成路径: api/v1/ad/ks_advertiser_program_creative_report.go
// 生成人：cyao
// desc:创意数据相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package ad

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
)

// KsAdvertiserProgramCreativeReportSearchReq 分页请求参数
type KsAdvertiserProgramCreativeReportSearchReq struct {
	g.Meta `path:"/list" tags:"快手创意数据" method:"get" summary:"快手创意数据列表"`
	commonApi.Author
	model.KsAdvertiserProgramCreativeReportSearchReq
}

// KsAdvertiserProgramCreativeReportSearchRes 列表返回结果
type KsAdvertiserProgramCreativeReportSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.KsAdvertiserProgramCreativeReportSearchRes
}

type KsAdvertiserProgramCreativeReportPullReq struct {
	g.Meta `path:"/pull" tags:"快手创意数据" method:"post" summary:"拉取快手创意数据"`
	commonApi.Author
	*model.KsAdvertiserProgramCreativeReportPullReq
}

type KsAdvertiserProgramCreativeReportPullRes struct {
	commonApi.EmptyRes
}

// KsAdvertiserProgramCreativeReportAddReq 添加操作请求参数
type KsAdvertiserProgramCreativeReportAddReq struct {
	g.Meta `path:"/add" tags:"快手创意数据" method:"post" summary:"快手创意数据添加"`
	commonApi.Author
	*model.KsAdvertiserProgramCreativeReportAddReq
}

// KsAdvertiserProgramCreativeReportAddRes 添加操作返回结果
type KsAdvertiserProgramCreativeReportAddRes struct {
	commonApi.EmptyRes
}

// KsAdvertiserProgramCreativeReportEditReq 修改操作请求参数
type KsAdvertiserProgramCreativeReportEditReq struct {
	g.Meta `path:"/edit" tags:"快手创意数据" method:"put" summary:"快手创意数据修改"`
	commonApi.Author
	*model.KsAdvertiserProgramCreativeReportEditReq
}

// KsAdvertiserProgramCreativeReportEditRes 修改操作返回结果
type KsAdvertiserProgramCreativeReportEditRes struct {
	commonApi.EmptyRes
}

// KsAdvertiserProgramCreativeReportGetReq 获取一条数据请求
type KsAdvertiserProgramCreativeReportGetReq struct {
	g.Meta `path:"/get" tags:"快手创意数据" method:"get" summary:"获取快手创意数据信息"`
	commonApi.Author
	CreativeId int64  `p:"creativeId" v:"required#主键必须"` //通过主键获取
	StateDate  string `p:"stateDate"  v:"required#主键必须"`
}

// KsAdvertiserProgramCreativeReportGetRes 获取一条数据结果
type KsAdvertiserProgramCreativeReportGetRes struct {
	g.Meta `mime:"application/json"`
	*model.KsAdvertiserProgramCreativeReportInfoRes
}

// KsAdvertiserProgramCreativeReportDeleteReq 删除数据请求
type KsAdvertiserProgramCreativeReportDeleteReq struct {
	g.Meta `path:"/delete" tags:"快手创意数据" method:"delete" summary:"删除快手创意数据"`
	commonApi.Author
	CreativeIds []int64 `p:"creativeIds" v:"required#主键必须"` //通过主键删除
}

// KsAdvertiserProgramCreativeReportDeleteRes 删除数据返回
type KsAdvertiserProgramCreativeReportDeleteRes struct {
	commonApi.EmptyRes
}
