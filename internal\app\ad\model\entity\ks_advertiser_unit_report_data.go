// ==========================================================================
// GFast自动生成model entity操作代码。
// 生成日期：2025-08-12 10:45:10
// 生成路径: internal/app/ad/model/entity/ks_advertiser_unit_report_data.go
// 生成人：cq
// desc:快手广告组报表数据
// company:云南奇讯科技有限公司
// ==========================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
)

// KsAdvertiserUnitReportData is the golang structure for table ks_advertiser_unit_report_data.
type KsAdvertiserUnitReportData struct {
	gmeta.Meta                                       `orm:"table:ks_advertiser_unit_report_data"`
	UnitId                                           int64       `orm:"unit_id,primary" json:"unitId"`                                                                                    // 广告组ID
	StatDate                                         string      `orm:"stat_date,primary" json:"statDate"`                                                                                // 数据日期，格式：YYYY-MM-DD
	AdvertiserId                                     int64       `orm:"advertiser_id" json:"advertiserId"`                                                                                // 广告主ID
	CampaignId                                       int64       `orm:"campaign_id" json:"campaignId"`                                                                                    // 广告计划ID
	Status                                           int64       `orm:"status" json:"status"`                                                                                             // 1-投放中；2-已暂停；3-已删除
	UnitSource                                       int         `orm:"unit_source" json:"unitSource"`                                                                                    // 0:非托管 1:托管
	PrivateMessageSentCost                           float64     `orm:"private_message_sent_cost" json:"privateMessageSentCost"`                                                          // 私信消息转化成本
	PrivateMessageSentRatio                          float64     `orm:"private_message_sent_ratio" json:"privateMessageSentRatio"`                                                        // 私信消息转化率
	PrivateMessageSentCnt                            int64       `orm:"private_message_sent_cnt" json:"privateMessageSentCnt"`                                                            // 私信消息数
	LeadsSubmitCost                                  float64     `orm:"leads_submit_cost" json:"leadsSubmitCost"`                                                                         // 直接私信留资成本
	LeadsSubmitCntRatio                              float64     `orm:"leads_submit_cnt_ratio" json:"leadsSubmitCntRatio"`                                                                // 直接私信留资率
	LeadsSubmitCnt                                   int64       `orm:"leads_submit_cnt" json:"leadsSubmitCnt"`                                                                           // 直接私信留资数
	PlayedNum                                        int64       `orm:"played_num" json:"playedNum"`                                                                                      // 播放数
	PlayedEnd                                        int64       `orm:"played_end" json:"playedEnd"`                                                                                      // 播放完成
	PlayedFiveSeconds                                int64       `orm:"played_five_seconds" json:"playedFiveSeconds"`                                                                     // 播放5s
	PlayedThreeSeconds                               int64       `orm:"played_three_seconds" json:"playedThreeSeconds"`                                                                   // 播放3s
	Play3SRatio                                      float64     `orm:"play_3s_ratio" json:"play3SRatio"`                                                                                 // 3s播放率
	Play5SRatio                                      float64     `orm:"play_5s_ratio" json:"play5SRatio"`                                                                                 // 5s播放率
	PlayEndRatio                                     float64     `orm:"play_end_ratio" json:"playEndRatio"`                                                                               // 完播率
	AdPhotoPlayed10S                                 int64       `orm:"ad_photo_played_10s" json:"adPhotoPlayed10S"`                                                                      // 10s播放数
	AdPhotoPlayed2S                                  int64       `orm:"ad_photo_played_2s" json:"adPhotoPlayed2S"`                                                                        // 2s播放数
	AdPhotoPlayed75Percent                           int64       `orm:"ad_photo_played_75percent" json:"adPhotoPlayed75Percent"`                                                          // 75%进度播放数
	AdPhotoPlayed75PercentRatio                      float64     `orm:"ad_photo_played_75_percent_ratio" json:"adPhotoPlayed75PercentRatio"`                                              // 75%进度播放率
	AdPhotoPlayed10SRatio                            float64     `orm:"ad_photo_played_10s_ratio" json:"adPhotoPlayed10SRatio"`                                                           // 10s播放率
	AdPhotoPlayed2SRatio                             float64     `orm:"ad_photo_played_2s_ratio" json:"adPhotoPlayed2SRatio"`                                                             // 2s播放率
	MinigameIaaPurchaseAmountWeekByConversionRoi     float64     `orm:"minigame_iaa_purchase_amount_week_by_conversion_roi" json:"minigameIaaPurchaseAmountWeekByConversionRoi"`          // 激活后七日广告变现ROI
	MinigameIaaPurchaseAmountThreeDayByConversionRoi float64     `orm:"minigame_iaa_purchase_amount_three_day_by_conversion_roi" json:"minigameIaaPurchaseAmountThreeDayByConversionRoi"` // 激活后三日广告变现ROI
	MinigameIaaPurchaseAmountFirstDayRoi             float64     `orm:"minigame_iaa_purchase_amount_first_day_roi" json:"minigameIaaPurchaseAmountFirstDayRoi"`                           // 当日广告变现ROI
	MinigameIaaPurchaseAmountWeekByConversion        float64     `orm:"minigame_iaa_purchase_amount_week_by_conversion" json:"minigameIaaPurchaseAmountWeekByConversion"`                 // 激活后七日广告LTV
	MinigameIaaPurchaseAmountThreeDayByConversion    float64     `orm:"minigame_iaa_purchase_amount_three_day_by_conversion" json:"minigameIaaPurchaseAmountThreeDayByConversion"`        // 激活后三日广告LTV
	MinigameIaaPurchaseAmountFirstDay                float64     `orm:"minigame_iaa_purchase_amount_first_day" json:"minigameIaaPurchaseAmountFirstDay"`                                  // 当日广告LTV
	MinigameIaaPurchaseRoi                           float64     `orm:"minigame_iaa_purchase_roi" json:"minigameIaaPurchaseRoi"`                                                          // IAA广告变现ROI
	MinigameIaaPurchaseAmount                        float64     `orm:"minigame_iaa_purchase_amount" json:"minigameIaaPurchaseAmount"`                                                    // IAA广告变现LTV
	MinigameIaaPurchaseAmount30DayByConversionRoi    float64     `orm:"minigame_iaa_purchase_amount_30_day_by_conversion_roi" json:"minigameIaaPurchaseAmount30DayByConversionRoi"`       // 激活后30日广告变现ROI
	MinigameIaaPurchaseAmount15DayByConversionRoi    float64     `orm:"minigame_iaa_purchase_amount_15_day_by_conversion_roi" json:"minigameIaaPurchaseAmount15DayByConversionRoi"`       // 激活后15日广告变现ROI
	MinigameIaaPurchaseAmount30DayByConversion       float64     `orm:"minigame_iaa_purchase_amount_30_day_by_conversion" json:"minigameIaaPurchaseAmount30DayByConversion"`              // 激活后30日广告LTV
	MinigameIaaPurchaseAmount15DayByConversion       float64     `orm:"minigame_iaa_purchase_amount_15_day_by_conversion" json:"minigameIaaPurchaseAmount15DayByConversion"`              // 激活后15日广告LTV
	MmuEffectiveCustomerAcquisition7DCnt             int64       `orm:"mmu_effective_customer_acquisition_7d_cnt" json:"mmuEffectiveCustomerAcquisition7DCnt"`                            // MMU识别产生的有效获客数（计费）
	MmuEffectiveCustomerAcquisitionCnt               int64       `orm:"mmu_effective_customer_acquisition_cnt" json:"mmuEffectiveCustomerAcquisitionCnt"`                                 // MMU识别产生的有效获客数（回传）
	EffectiveCustomerAcquisition7DRatio              float64     `orm:"effective_customer_acquisition_7d_ratio" json:"effectiveCustomerAcquisition7DRatio"`                               // 有效获客率（计费）
	EffectiveCustomerAcquisition7DCost               float64     `orm:"effective_customer_acquisition_7d_cost" json:"effectiveCustomerAcquisition7DCost"`                                 // 有效获客成本（计费）
	EffectiveCustomerAcquisition7DCnt                int64       `orm:"effective_customer_acquisition_7d_cnt" json:"effectiveCustomerAcquisition7DCnt"`                                   // 有效获客数（计费）
	EventPay30DayOverallRoi                          float64     `orm:"event_pay_30_day_overall_roi" json:"eventPay30DayOverallRoi"`                                                      // 激活后30日整体ROI
	EventPay15DayOverallRoi                          float64     `orm:"event_pay_15_day_overall_roi" json:"eventPay15DayOverallRoi"`                                                      // 激活后15日整体ROI
	EventPayPurchaseAmount15DayByConversion          float64     `orm:"event_pay_purchase_amount_15_day_by_conversion" json:"eventPayPurchaseAmount15DayByConversion"`                    // 激活后15日付费金额
	EventPayPurchaseAmount30DayByConversion          float64     `orm:"event_pay_purchase_amount_30_day_by_conversion" json:"eventPayPurchaseAmount30DayByConversion"`                    // 激活后30日付费金额
	EventPayFirstDay                                 int64       `orm:"event_pay_first_day" json:"eventPayFirstDay"`                                                                      // 应用下载数据-首日付费次数
	EventPayPurchaseAmountFirstDay                   float64     `orm:"event_pay_purchase_amount_first_day" json:"eventPayPurchaseAmountFirstDay"`                                        // 应用下载数据-首日付费金额
	EventPayFirstDayRoi                              float64     `orm:"event_pay_first_day_roi" json:"eventPayFirstDayRoi"`                                                               // 应用下载数据-首日ROI
	EventPay                                         int64       `orm:"event_pay" json:"eventPay"`                                                                                        // 应用下载数据-付费次数
	EventPayPurchaseAmount                           float64     `orm:"event_pay_purchase_amount" json:"eventPayPurchaseAmount"`                                                          // 应用下载数据-付费金额
	EventPayRoi                                      float64     `orm:"event_pay_roi" json:"eventPayRoi"`                                                                                 // 应用下载数据-ROI
	EventPayPurchaseAmountOneDay                     float64     `orm:"event_pay_purchase_amount_one_day" json:"eventPayPurchaseAmountOneDay"`                                            // 激活后24h付费金额(回传时间)
	EventPayPurchaseAmountOneDayByConversion         float64     `orm:"event_pay_purchase_amount_one_day_by_conversion" json:"eventPayPurchaseAmountOneDayByConversion"`                  // 激活后24h付费金额(激活时间)
	EventPayPurchaseAmountOneDayByConversionRoi      float64     `orm:"event_pay_purchase_amount_one_day_by_conversion_roi" json:"eventPayPurchaseAmountOneDayByConversionRoi"`           // 激活后24小时付费ROI
	EventPayPurchaseAmountOneDayRoi                  float64     `orm:"event_pay_purchase_amount_one_day_roi" json:"eventPayPurchaseAmountOneDayRoi"`                                     // 激活后24h-ROI(回传时间)
	EventPayWeightedPurchaseAmount                   float64     `orm:"event_pay_weighted_purchase_amount" json:"eventPayWeightedPurchaseAmount"`                                         // 加权付费金额
	EventPayWeightedPurchaseAmountFirstDay           float64     `orm:"event_pay_weighted_purchase_amount_first_day" json:"eventPayWeightedPurchaseAmountFirstDay"`                       // 首日加权付费金额
	Charge                                           float64     `orm:"charge" json:"charge"`                                                                                             // 花费（元）
	Show                                             int64       `orm:"show" json:"show"`                                                                                                 // 封面曝光数
	Aclick                                           int64       `orm:"aclick" json:"aclick"`                                                                                             // 素材曝光数
	Bclick                                           int64       `orm:"bclick" json:"bclick"`                                                                                             // 行为数
	AdShow                                           float64     `orm:"ad_show" json:"adShow"`                                                                                            // 广告曝光
	Share                                            int64       `orm:"share" json:"share"`                                                                                               // 分享数
	Comment                                          int64       `orm:"comment" json:"comment"`                                                                                           // 评论数
	Like                                             int64       `orm:"like" json:"like"`                                                                                                 // 点赞数
	Follow                                           int64       `orm:"follow" json:"follow"`                                                                                             // 新增粉丝数
	CancelLike                                       int64       `orm:"cancel_like" json:"cancelLike"`                                                                                    // 取消点赞数
	CancelFollow                                     int64       `orm:"cancel_follow" json:"cancelFollow"`                                                                                // 取消关注数
	Report                                           int64       `orm:"report" json:"report"`                                                                                             // 举报数
	Block                                            int64       `orm:"block" json:"block"`                                                                                               // 拉黑数
	Negative                                         int64       `orm:"negative" json:"negative"`                                                                                         // 减少此类作品数
	Activation                                       int64       `orm:"activation" json:"activation"`                                                                                     // 应用下载数据-激活数
	DownloadStarted                                  int64       `orm:"download_started" json:"downloadStarted"`                                                                          // 应用下载数据-安卓下载开始数
	DownloadCompleted                                int64       `orm:"download_completed" json:"downloadCompleted"`                                                                      // 应用下载数据-安卓下载完成数
	DownloadInstalled                                int64       `orm:"download_installed" json:"downloadInstalled"`                                                                      // 安卓安装完成数
	ClickConversionRatio                             float64     `orm:"click_conversion_ratio" json:"clickConversionRatio"`                                                               // 点击激活成本
	ConversionCost                                   float64     `orm:"conversion_cost" json:"conversionCost"`                                                                            // 激活单价
	DownloadCompletedCost                            float64     `orm:"download_completed_cost" json:"downloadCompletedCost"`                                                             // 安卓下载完成单价（元）
	DownloadCompletedRatio                           float64     `orm:"download_completed_ratio" json:"downloadCompletedRatio"`                                                           // 安卓下载完成率
	DownloadConversionRatio                          float64     `orm:"download_conversion_ratio" json:"downloadConversionRatio"`                                                         // 下载完成激活率
	DownloadStartedCost                              float64     `orm:"download_started_cost" json:"downloadStartedCost"`                                                                 // 安卓下载开始单价（元）
	DownloadStartedRatio                             float64     `orm:"download_started_ratio" json:"downloadStartedRatio"`                                                               // 安卓下载开始率
	EventRegister                                    int64       `orm:"event_register" json:"eventRegister"`                                                                              // 应用下载数据-注册数
	EventRegisterCost                                float64     `orm:"event_register_cost" json:"eventRegisterCost"`                                                                     // 应用下载数据-注册成本
	EventRegisterRatio                               float64     `orm:"event_register_ratio" json:"eventRegisterRatio"`                                                                   // 应用下载数据-注册率
	EventJinJianApp                                  int64       `orm:"event_jin_jian_app" json:"eventJinJianApp"`                                                                        // 应用下载数据-完件数
	EventJinJianAppCost                              float64     `orm:"event_jin_jian_app_cost" json:"eventJinJianAppCost"`                                                               // 应用下载数据-完件成本
	EventJinJianLandingPage                          int64       `orm:"event_jin_jian_landing_page" json:"eventJinJianLandingPage"`                                                       // 落地页数据-落地页完件数
	EventJinJianLandingPageCost                      float64     `orm:"event_jin_jian_landing_page_cost" json:"eventJinJianLandingPageCost"`                                              // 落地页数据-落地页完件成本
	Jinjian0DCnt                                     int64       `orm:"jinjian_0d_cnt" json:"jinjian0DCnt"`                                                                               // T0完件数
	Jinjian3DCnt                                     int64       `orm:"jinjian_3d_cnt" json:"jinjian3DCnt"`                                                                               // T3完件数
	Jinjian0DCntCost                                 float64     `orm:"jinjian_0d_cnt_cost" json:"jinjian0DCntCost"`                                                                      // T0完件成本
	Jinjian3DCntCost                                 float64     `orm:"jinjian_3d_cnt_cost" json:"jinjian3DCntCost"`                                                                      // T3完件成本
	EventCreditGrantApp                              int64       `orm:"event_credit_grant_app" json:"eventCreditGrantApp"`                                                                // 应用下载数据-授信数
	EventCreditGrantAppCost                          float64     `orm:"event_credit_grant_app_cost" json:"eventCreditGrantAppCost"`                                                       // 应用下载数据-授信成本
	EventCreditGrantAppRatio                         float64     `orm:"event_credit_grant_app_ratio" json:"eventCreditGrantAppRatio"`                                                     // 应用下载数据-授信率
	EventCreditGrantLandingPage                      int64       `orm:"event_credit_grant_landing_page" json:"eventCreditGrantLandingPage"`                                               // 落地页数据-落地页授信数
	EventCreditGrantLandingPageCost                  float64     `orm:"event_credit_grant_landing_page_cost" json:"eventCreditGrantLandingPageCost"`                                      // 落地页数据-落地页授信成本
	EventCreditGrantLandingRatio                     float64     `orm:"event_credit_grant_landing_ratio" json:"eventCreditGrantLandingRatio"`                                             // 落地页数据-落地页授信率
	EventCreditGrantFirstDayApp                      int64       `orm:"event_credit_grant_first_day_app" json:"eventCreditGrantFirstDayApp"`                                              // app首日授信数
	EventCreditGrantFirstDayAppCost                  float64     `orm:"event_credit_grant_first_day_app_cost" json:"eventCreditGrantFirstDayAppCost"`                                     // 首日授信成本
	EventCreditGrantFirstDayAppRatio                 float64     `orm:"event_credit_grant_first_day_app_ratio" json:"eventCreditGrantFirstDayAppRatio"`                                   // 首日授信率
	EventCreditGrantFirstDayLandingPage              int64       `orm:"event_credit_grant_first_day_landing_page" json:"eventCreditGrantFirstDayLandingPage"`                             // 落地页首日授信数
	EventCreditGrantFirstDayLandingPageCost          float64     `orm:"event_credit_grant_first_day_landing_page_cost" json:"eventCreditGrantFirstDayLandingPageCost"`                    // 落地页首日授信成本
	EventCreditGrantFirstDayLandingPageRatio         float64     `orm:"event_credit_grant_first_day_landing_page_ratio" json:"eventCreditGrantFirstDayLandingPageRatio"`                  // 落地页首日授信率
	CreditGrant0DCnt                                 int64       `orm:"credit_grant_0d_cnt" json:"creditGrant0DCnt"`                                                                      // T0授信数
	CreditGrant3DCnt                                 int64       `orm:"credit_grant_3d_cnt" json:"creditGrant3DCnt"`                                                                      // T3授信数
	CreditGrant0DCntCost                             float64     `orm:"credit_grant_0d_cnt_cost" json:"creditGrant0DCntCost"`                                                             // T0授信成本
	CreditGrant3DCntCost                             float64     `orm:"credit_grant_3d_cnt_cost" json:"creditGrant3DCntCost"`                                                             // T3授信成本
	CreditGrant0DCntRatio                            float64     `orm:"credit_grant_0d_cnt_ratio" json:"creditGrant0DCntRatio"`                                                           // T0完件授信率
	CreditGrant3DCntRatio                            float64     `orm:"credit_grant_3d_cnt_ratio" json:"creditGrant3DCntRatio"`                                                           // T3完件授信通过率
	EventOrderSubmit                                 int64       `orm:"event_order_submit" json:"eventOrderSubmit"`                                                                       // 提交订单数
	EventOrderPaid                                   int64       `orm:"event_order_paid" json:"eventOrderPaid"`                                                                           // 应用下载数据-付款成功数
	EventOrderPaidPurchaseAmount                     float64     `orm:"event_order_paid_purchase_amount" json:"eventOrderPaidPurchaseAmount"`                                             // 应用下载数据-付款成功金额
	EventOrderPaidCost                               float64     `orm:"event_order_paid_cost" json:"eventOrderPaidCost"`                                                                  // 应用下载数据-单次付款成本
	EventOrderPaidRoi                                float64     `orm:"event_order_paid_roi" json:"eventOrderPaidRoi"`                                                                    // 订单支付率
	Submit                                           int64       `orm:"submit" json:"submit"`                                                                                             // 提交按钮点击数（历史字段）
	FormCount                                        int64       `orm:"form_count" json:"formCount"`                                                                                      // 落地页数据-线索提交个数
	FormCost                                         float64     `orm:"form_cost" json:"formCost"`                                                                                        // 落地页数据-单个线索成本
	FormActionRatio                                  float64     `orm:"form_action_ratio" json:"formActionRatio"`                                                                         // 落地页数据-表单提交点击率
	EventValidClues                                  int64       `orm:"event_valid_clues" json:"eventValidClues"`                                                                         // 落地页数据-有效线索数
	EventValidCluesCost                              float64     `orm:"event_valid_clues_cost" json:"eventValidCluesCost"`                                                                // 落地页数据-有效线索成本
	EventAudition                                    int64       `orm:"event_audition" json:"eventAudition"`                                                                              // 首次试听到课数
	EventConsultationValidRetained                   int64       `orm:"event_consultation_valid_retained" json:"eventConsultationValidRetained"`                                          // 留咨咨询数
	EventConsultationValidRetainedCost               float64     `orm:"event_consultation_valid_retained_cost" json:"eventConsultationValidRetainedCost"`                                 // 留咨咨询成本
	EventConsultationValidRetainedRatio              float64     `orm:"event_consultation_valid_retained_ratio" json:"eventConsultationValidRetainedRatio"`                               // 留咨咨询率
	EventConversionClickCost                         float64     `orm:"event_conversion_click_cost" json:"eventConversionClickCost"`                                                      // 有效咨询成本
	EventConversionClickRatio                        float64     `orm:"event_conversion_click_ratio" json:"eventConversionClickRatio"`                                                    // 有效咨询率
	EventPreComponentConsultationValidRetained       int64       `orm:"event_pre_component_consultation_valid_retained" json:"eventPreComponentConsultationValidRetained"`                // 附加咨询组件留资咨询数
	EventNextDayStayCost                             float64     `orm:"event_next_day_stay_cost" json:"eventNextDayStayCost"`                                                             // 应用下载数据-次留成本（仅支持分日查询）
	EventNextDayStayRatio                            float64     `orm:"event_next_day_stay_ratio" json:"eventNextDayStayRatio"`                                                           // 应用下载数据-次留率（仅支持分日查询）
	EventNextDayStay                                 int64       `orm:"event_next_day_stay" json:"eventNextDayStay"`                                                                      // 应用下载数据-次留数（仅支持分日查询）
	EventAdWatch10Times                              int64       `orm:"event_ad_watch_10_times" json:"eventAdWatch10Times"`                                                               // 10次广告观看数
	EventAdWatch10TimesCost                          float64     `orm:"event_ad_watch_10_times_cost" json:"eventAdWatch10TimesCost"`                                                      // 10次广告观看成本
	EventAdWatch10TimesRatio                         float64     `orm:"event_ad_watch_10_times_ratio" json:"eventAdWatch10TimesRatio"`                                                    // 10次广告观看转化率
	EventAdWatch20Times                              int64       `orm:"event_ad_watch_20_times" json:"eventAdWatch20Times"`                                                               // 20次广告观看数
	EventAdWatch20TimesCost                          float64     `orm:"event_ad_watch_20_times_cost" json:"eventAdWatch20TimesCost"`                                                      // 20次广告观看成本
	EventAdWatch20TimesRatio                         float64     `orm:"event_ad_watch_20_times_ratio" json:"eventAdWatch20TimesRatio"`                                                    // 20次广告观看转化率
	EventAdWatch5Times                               int64       `orm:"event_ad_watch_5_times" json:"eventAdWatch5Times"`                                                                 // 5次广告观看数
	EventAdWatch5TimesCost                           float64     `orm:"event_ad_watch_5_times_cost" json:"eventAdWatch5TimesCost"`                                                        // 5次广告观看成本
	EventAdWatch5TimesRatio                          float64     `orm:"event_ad_watch_5_times_ratio" json:"eventAdWatch5TimesRatio"`                                                      // 5次广告观看转化率
	EventWatchAppAd                                  int64       `orm:"event_watch_app_ad" json:"eventWatchAppAd"`                                                                        // 广告观看
	EventAdWatchTimes                                int64       `orm:"event_ad_watch_times" json:"eventAdWatchTimes"`                                                                    // 广告观看次数
	EventAdWatchTimesRatio                           float64     `orm:"event_ad_watch_times_ratio" json:"eventAdWatchTimesRatio"`                                                         // 广告观看次数转化率
	EventAdWatchTimesCost                            float64     `orm:"event_ad_watch_times_cost" json:"eventAdWatchTimesCost"`                                                           // 广告观看次数成本
	EventMakingCalls                                 int64       `orm:"event_making_calls" json:"eventMakingCalls"`                                                                       // 电话拨打数-用户点击电话按钮的次数
	EventMakingCallsCost                             float64     `orm:"event_making_calls_cost" json:"eventMakingCallsCost"`                                                              // 电话拨打成本
	EventMakingCallsRatio                            float64     `orm:"event_making_calls_ratio" json:"eventMakingCallsRatio"`                                                            // 电话拨打率
	EventGetThrough                                  int64       `orm:"event_get_through" json:"eventGetThrough"`                                                                         // 智能电话-确认接通数
	EventGetThroughCost                              float64     `orm:"event_get_through_cost" json:"eventGetThroughCost"`                                                                // 智能电话-确认接通成本
	EventGetThroughRatio                             float64     `orm:"event_get_through_ratio" json:"eventGetThroughRatio"`                                                              // 智能电话-确认接通率
	EventPhoneGetThrough                             int64       `orm:"event_phone_get_through" json:"eventPhoneGetThrough"`                                                              // 电话建联数
	EventOutboundCall                                int64       `orm:"event_outbound_call" json:"eventOutboundCall"`                                                                     // 电话拨打数
	EventOutboundCallCost                            float64     `orm:"event_outbound_call_cost" json:"eventOutboundCallCost"`                                                            // 电话拨打成本
	EventOutboundCallRatio                           float64     `orm:"event_outbound_call_ratio" json:"eventOutboundCallRatio"`                                                          // 电话拨打率
	EventWechatQrCodeLinkClick                       int64       `orm:"event_wechat_qr_code_link_click" json:"eventWechatQrCodeLinkClick"`                                                // 微信小程序深度加粉数
	EventAddWechat                                   int64       `orm:"event_add_wechat" json:"eventAddWechat"`                                                                           // 微信复制数
	EventAddWechatCost                               float64     `orm:"event_add_wechat_cost" json:"eventAddWechatCost"`                                                                  // 微信复制成本
	EventAddWechatRatio                              float64     `orm:"event_add_wechat_ratio" json:"eventAddWechatRatio"`                                                                // 微信复制率
	EventWechatConnected                             int64       `orm:"event_wechat_connected" json:"eventWechatConnected"`                                                               // 微信加粉数
	PhotoClick                                       int64       `orm:"photo_click" json:"photoClick"`                                                                                    // 封面点击数
	PhotoClickRatio                                  float64     `orm:"photo_click_ratio" json:"photoClickRatio"`                                                                         // 封面点击率
	PhotoClickCost                                   float64     `orm:"photo_click_cost" json:"photoClickCost"`                                                                           // 平均点击单价（元）
	ActionRatio                                      float64     `orm:"action_ratio" json:"actionRatio"`                                                                                  // 行为率
	ActionNewRatio                                   float64     `orm:"action_new_ratio" json:"actionNewRatio"`                                                                           // 行为率新
	ActionCost                                       float64     `orm:"action_cost" json:"actionCost"`                                                                                    // 平均行为单价（元）
	Impression1KCost                                 float64     `orm:"impression_1k_cost" json:"impression1KCost"`                                                                       // 平均千次曝光花费（元）
	Click1KCost                                      float64     `orm:"click_1k_cost" json:"click1KCost"`                                                                                 // 平均千次素材曝光花费(元)
	ApproxPayCost                                    float64     `orm:"approx_pay_cost" json:"approxPayCost"`                                                                             // 淘系近似购买成本
	ApproxPayCount                                   int64       `orm:"approx_pay_count" json:"approxPayCount"`                                                                           // 近似购买数
	ApproxPayRatio                                   float64     `orm:"approx_pay_ratio" json:"approxPayRatio"`                                                                           // 淘系近似购买率
	LiveEventGoodsView                               int64       `orm:"live_event_goods_view" json:"liveEventGoodsView"`                                                                  // 直播查看商品数
	LivePlayed3S                                     int64       `orm:"live_played_3s" json:"livePlayed3S"`                                                                               // 直播观看数
	AdProductCnt                                     int64       `orm:"ad_product_cnt" json:"adProductCnt"`                                                                               // 商品成交数
	EventGoodsView                                   int64       `orm:"event_goods_view" json:"eventGoodsView"`                                                                           // 商品访问数
	EventGoodsViewCost                               float64     `orm:"event_goods_view_cost" json:"eventGoodsViewCost"`                                                                  // 商品访问成本
	MerchantRecoFans                                 int64       `orm:"merchant_reco_fans" json:"merchantRecoFans"`                                                                       // 涨粉量
	MerchantRecoFansCost                             float64     `orm:"merchant_reco_fans_cost" json:"merchantRecoFansCost"`                                                              // 涨粉成本
	EventOrderAmountRoi                              float64     `orm:"event_order_amount_roi" json:"eventOrderAmountRoi"`                                                                // 小店推广roi
	EventNewUserPay                                  int64       `orm:"event_new_user_pay" json:"eventNewUserPay"`                                                                        // 新增付费人数
	EventNewUserPayCost                              float64     `orm:"event_new_user_pay_cost" json:"eventNewUserPayCost"`                                                               // 新增付费人数成本
	EventNewUserPayRatio                             float64     `orm:"event_new_user_pay_ratio" json:"eventNewUserPayRatio"`                                                             // 新增付费人数率
	EventNewUserJinjianApp                           int64       `orm:"event_new_user_jinjian_app" json:"eventNewUserJinjianApp"`                                                         // 新增完件人数
	EventNewUserJinjianAppCost                       float64     `orm:"event_new_user_jinjian_app_cost" json:"eventNewUserJinjianAppCost"`                                                // 新增完件人数成本
	EventNewUserJinjianAppRoi                        float64     `orm:"event_new_user_jinjian_app_roi" json:"eventNewUserJinjianAppRoi"`                                                  // 新增完件人数率
	EventNewUserCreditGrantApp                       int64       `orm:"event_new_user_credit_grant_app" json:"eventNewUserCreditGrantApp"`                                                // 新增授信人数
	EventNewUserCreditGrantAppCost                   float64     `orm:"event_new_user_credit_grant_app_cost" json:"eventNewUserCreditGrantAppCost"`                                       // 新增授信人数成本
	EventNewUserCreditGrantAppRoi                    float64     `orm:"event_new_user_credit_grant_app_roi" json:"eventNewUserCreditGrantAppRoi"`                                         // 新增授信人数率
	EventNewUserJinjianPage                          int64       `orm:"event_new_user_jinjian_page" json:"eventNewUserJinjianPage"`                                                       // 落地页新增完件人数
	EventNewUserJinjianPageCost                      float64     `orm:"event_new_user_jinjian_page_cost" json:"eventNewUserJinjianPageCost"`                                              // 落地页新增完件人数成本
	EventNewUserJinjianPageRoi                       float64     `orm:"event_new_user_jinjian_page_roi" json:"eventNewUserJinjianPageRoi"`                                                // 落地页新增完件人数率
	EventNewUserCreditGrantPage                      int64       `orm:"event_new_user_credit_grant_page" json:"eventNewUserCreditGrantPage"`                                              // 落地页新增授信人数
	EventNewUserCreditGrantPageCost                  float64     `orm:"event_new_user_credit_grant_page_cost" json:"eventNewUserCreditGrantPageCost"`                                     // 落地页新增授信人数成本
	EventNewUserCreditGrantPageRoi                   float64     `orm:"event_new_user_credit_grant_page_roi" json:"eventNewUserCreditGrantPageRoi"`                                       // 落地页新增授信人数率
	EventAppointForm                                 int64       `orm:"event_appoint_form" json:"eventAppointForm"`                                                                       // 预约表单数
	EventAppointFormCost                             float64     `orm:"event_appoint_form_cost" json:"eventAppointFormCost"`                                                              // 预约表单点击成本
	EventAppointFormRatio                            float64     `orm:"event_appoint_form_ratio" json:"eventAppointFormRatio"`                                                            // 预约表单点击率
	EventAppointJumpClick                            int64       `orm:"event_appoint_jump_click" json:"eventAppointJumpClick"`                                                            // 预约跳转点击数
	EventAppointJumpClickCost                        float64     `orm:"event_appoint_jump_click_cost" json:"eventAppointJumpClickCost"`                                                   // 预约跳转点击成本
	EventAppointJumpClickRatio                       float64     `orm:"event_appoint_jump_click_ratio" json:"eventAppointJumpClickRatio"`                                                 // 预约跳转点击率
	UnionEventPayPurchaseAmount7D                    float64     `orm:"union_event_pay_purchase_amount_7d" json:"unionEventPayPurchaseAmount7D"`                                          // 联盟广告收入
	UnionEventPayPurchaseAmount7DRoi                 float64     `orm:"union_event_pay_purchase_amount_7d_roi" json:"unionEventPayPurchaseAmount7DRoi"`                                   // 联盟变现ROI
	EventDspGiftForm                                 int64       `orm:"event_dsp_gift_form" json:"eventDspGiftForm"`                                                                      // 附加组件表单提交
	EventAppInvoked                                  int64       `orm:"event_app_invoked" json:"eventAppInvoked"`                                                                         // 唤醒应用数
	EventAppInvokedCost                              float64     `orm:"event_app_invoked_cost" json:"eventAppInvokedCost"`                                                                // 唤醒应用成本
	EventAppInvokedRatio                             float64     `orm:"event_app_invoked_ratio" json:"eventAppInvokedRatio"`                                                              // 唤醒应用率
	EventButtonClick                                 int64       `orm:"event_button_click" json:"eventButtonClick"`                                                                       // 按钮点击数
	EventButtonClickCost                             float64     `orm:"event_button_click_cost" json:"eventButtonClickCost"`                                                              // 按钮点击成本
	EventButtonClickRatio                            float64     `orm:"event_button_click_ratio" json:"eventButtonClickRatio"`                                                            // 按钮点击率
	EventMultiConversion                             int64       `orm:"event_multi_conversion" json:"eventMultiConversion"`                                                               // 落地页多转化次数
	EventMultiConversionRatio                        float64     `orm:"event_multi_conversion_ratio" json:"eventMultiConversionRatio"`                                                    // 落地页多转化率
	EventMultiConversionCost                         float64     `orm:"event_multi_conversion_cost" json:"eventMultiConversionCost"`                                                      // 落地页多转化成本
	EventAddShoppingCart                             int64       `orm:"event_add_shopping_cart" json:"eventAddShoppingCart"`                                                              // 添加购物车数
	EventAddShoppingCartCost                         float64     `orm:"event_add_shopping_cart_cost" json:"eventAddShoppingCartCost"`                                                     // 添加购物车成本
	EventIntentionConfirmed                          int64       `orm:"event_intention_confirmed" json:"eventIntentionConfirmed"`                                                         // 意向确认数
	EventOrderSuccessed                              int64       `orm:"event_order_successed" json:"eventOrderSuccessed"`                                                                 // 有效线索成交数
	EventPhoneCardActivate                           int64       `orm:"event_phone_card_activate" json:"eventPhoneCardActivate"`                                                          // 电话卡激活数
	EventMeasurementHouse                            int64       `orm:"event_measurement_house" json:"eventMeasurementHouse"`                                                             // 量房数
	KeyAction                                        int64       `orm:"key_action" json:"keyAction"`                                                                                      // 关键行为数
	KeyActionCost                                    float64     `orm:"key_action_cost" json:"keyActionCost"`                                                                             // 关键行为成本
	KeyActionRatio                                   float64     `orm:"key_action_ratio" json:"keyActionRatio"`                                                                           // 关键行为率
	KeyInappAction0DCnt                              int64       `orm:"key_inapp_action_0d_cnt" json:"keyInappAction0DCnt"`                                                               // T0全量授信数
	KeyInappAction3DCnt                              int64       `orm:"key_inapp_action_3d_cnt" json:"keyInappAction3DCnt"`                                                               // T3全量授信数
	KeyInappAction0DCntCost                          float64     `orm:"key_inapp_action_0d_cnt_cost" json:"keyInappAction0DCntCost"`                                                      // T0全量授信成本
	KeyInappAction3DCntCost                          float64     `orm:"key_inapp_action_3d_cnt_cost" json:"keyInappAction3DCntCost"`                                                      // T3全量授信成本
	KeyInappAction0DCntRatio                         float64     `orm:"key_inapp_action_0d_cnt_ratio" json:"keyInappAction0DCntRatio"`                                                    // T0全量授信通过率
	KeyInappAction3DCntRatio                         float64     `orm:"key_inapp_action_3d_cnt_ratio" json:"keyInappAction3DCntRatio"`                                                    // T3全量授信通过率
	DrawCreditLine0DCnt                              int64       `orm:"draw_credit_line_0d_cnt" json:"drawCreditLine0DCnt"`                                                               // T0用信数
	DrawCreditLine0DCntCost                          float64     `orm:"draw_credit_line_0d_cnt_cost" json:"drawCreditLine0DCntCost"`                                                      // T0用信成本
	DrawCreditLine0DCntRatio                         float64     `orm:"draw_credit_line_0d_cnt_ratio" json:"drawCreditLine0DCntRatio"`                                                    // T0授信用信率
	EventCreditCardRecheck                           int64       `orm:"event_credit_card_recheck" json:"eventCreditCardRecheck"`                                                          // 信用卡核卡数
	EventCreditCardRecheckFirstDay                   int64       `orm:"event_credit_card_recheck_first_day" json:"eventCreditCardRecheckFirstDay"`                                        // 信用卡首日核卡数
	EventNoIntention                                 int64       `orm:"event_no_intention" json:"eventNoIntention"`                                                                       // 用户无意向数
	AdScene                                          string      `orm:"ad_scene" json:"adScene"`                                                                                          // 广告场景
	AdSceneField                                     string      `orm:"ad_scene_field" json:"adSceneField"`                                                                               // 广告场景字段
	PlacementType                                    string      `orm:"placement_type" json:"placementType"`                                                                              // 投放范围
	CreatedAt                                        *gtime.Time `orm:"created_at" json:"createdAt"`                                                                                      // 创建时间
	UpdatedAt                                        *gtime.Time `orm:"updated_at" json:"updatedAt"`                                                                                      // 更新时间
}
