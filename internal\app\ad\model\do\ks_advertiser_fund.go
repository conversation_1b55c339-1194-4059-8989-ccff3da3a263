// ==========================================================================
// GFast自动生成model entity操作代码。
// 生成日期：2025-08-12 15:45:08
// 生成路径: internal/app/ad/model/entity/ks_advertiser_fund.go
// 生成人：cyao
// desc:广告主资金信息
// company:云南奇讯科技有限公司
// ==========================================================================

package do

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
)

// KsAdvertiserFund is the golang structure for table ks_advertiser_fund.
type KsAdvertiserFund struct {
	gmeta.Meta      `orm:"table:ks_advertiser_fund, do:true"`
	AdvertiserId    interface{} `orm:"advertiser_id,primary" json:"advertiserId"` // 快手账户ID（广告账户ID）
	DirectRebate    interface{} `orm:"direct_rebate" json:"directRebate"`         // 激励余额
	ContractRebate  interface{} `orm:"contract_rebate" json:"contractRebate"`     // 框返余额
	RechargeBalance interface{} `orm:"recharge_balance" json:"rechargeBalance"`   // 充值余额
	Balance         interface{} `orm:"balance" json:"balance"`                    // 账户总余额
	CreatedAt       *gtime.Time `orm:"created_at" json:"createdAt"`               // 创建时间
	UpdatedAt       *gtime.Time `orm:"updated_at" json:"updatedAt"`               // 更新时间
}
