package api

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
)

// AddAccountIncExploreService 添加增量探索配置
type AddAccountIncExploreService struct {
	ctx     context.Context
	cfg     *Configuration
	token   string
	Request *AddAccountIncExploreReq
}

// AddAccountIncExploreReq 请求结构体
type AddAccountIncExploreReq struct {
	IncExploreInfo []GwIncExploreDetailDto `json:"inc_explore_info" dc:"增量探索配置列表"`
	AdvertiserId   int64                   `json:"advertiser_id" dc:"账号id"`
}

// GwIncExploreDetailDto 增量探索配置详情DTO结构体
type GwIncExploreDetailDto struct {
	OcpxActionTypeName     string  `json:"ocpx_action_type_name" dc:"转化目标名称"`
	OcpxActionType         int64   `json:"ocpx_action_type" dc:"转化目标类型"`
	DeepConversionTypeName string  `json:"deep_conversion_type_name" dc:"深度转化目标名称"`
	DeepConversionType     int64   `json:"deep_conversion_type" dc:"深度转化目标类型"`
	AutoMode               int     `json:"auto_mode" dc:"是否开启uax: 1-开启, 2-未开启"`
	ExploreBudget          float64 `json:"explore_budget" dc:"探索预算，单位（元）"`
	FirstStartTime         int64   `json:"first_start_time" dc:"第一次开始时间（长期生效会多次开启）(Unix毫秒时间戳)"`
	IncExploreTimeType     int     `json:"inc_explore_time_type" dc:"增量时间类型: 1-长期生效, 2-当日生效, 3-立即生效6小时"`
}

func (r *AddAccountIncExploreService) SetCfg(cfg *Configuration) *AddAccountIncExploreService {
	r.cfg = cfg
	return r
}

func (r *AddAccountIncExploreService) SetReq(req AddAccountIncExploreReq) *AddAccountIncExploreService {
	r.Request = &req
	return r
}

func (r *AddAccountIncExploreService) AccessToken(accessToken string) *AddAccountIncExploreService {
	r.token = accessToken
	return r
}

func (r *AddAccountIncExploreService) Do() (data *KsBaseResp[[]GwIncExploreDetailDto], err error) {
	localBasePath := r.cfg.BasePath
	localVarPath := localBasePath + "/rest/openapi/gw/dsp/account/incExplore/add"
	response, err := r.cfg.HTTPClient.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("Access-Token", r.token).
		SetBody(r.Request).
		SetResult(&KsBaseResp[[]GwIncExploreDetailDto]{}).
		Post(localVarPath)
	if err != nil {
		return nil, err
	}
	resp := new(KsBaseResp[[]GwIncExploreDetailDto])
	// 将 JSON 响应解码到结构体中
	err = json.Unmarshal(response.Body(), &resp)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("/rest/openapi/gw/dsp/account/incExplore/add解析响应出错: %v\n", err))
	}
	if resp.Code == 0 && resp.Data != nil {
		return resp, nil
	} else {
		return resp, errors.New(resp.Message)
	}
}
