// ==========================================================================
// GFast自动生成controller操作代码。
// 生成日期：2025-08-13 16:34:00
// 生成路径: internal/app/ad/controller/ks_advertiser_campaign.go
// 生成人：cyao
// desc:快手广告计划
// company:云南奇讯科技有限公司
// ==========================================================================

package controller

import (
	"context"

	"github.com/tiger1103/gfast/v3/api/v1/ad"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	systemController "github.com/tiger1103/gfast/v3/internal/app/system/controller"
)

type ksAdvertiserCampaignController struct {
	systemController.BaseController
}

var KsAdvertiserCampaign = new(ksAdvertiserCampaignController)

// List 列表
func (c *ksAdvertiserCampaignController) List(ctx context.Context, req *ad.KsAdvertiserCampaignSearchReq) (res *ad.KsAdvertiserCampaignSearchRes, err error) {
	res = new(ad.KsAdvertiserCampaignSearchRes)
	res.KsAdvertiserCampaignSearchRes, err = service.KsAdvertiserCampaign().List(ctx, &req.KsAdvertiserCampaignSearchReq)
	return
}

// Get 获取快手广告计划
func (c *ksAdvertiserCampaignController) Get(ctx context.Context, req *ad.KsAdvertiserCampaignGetReq) (res *ad.KsAdvertiserCampaignGetRes, err error) {
	res = new(ad.KsAdvertiserCampaignGetRes)
	res.KsAdvertiserCampaignInfoRes, err = service.KsAdvertiserCampaign().GetByCampaignId(ctx, req.CampaignId)
	return
}

// Add 添加快手广告计划
func (c *ksAdvertiserCampaignController) Add(ctx context.Context, req *ad.KsAdvertiserCampaignAddReq) (res *ad.KsAdvertiserCampaignAddRes, err error) {
	err = service.KsAdvertiserCampaign().Add(ctx, req.KsAdvertiserCampaignAddReq)
	return
}

// Edit 修改快手广告计划
func (c *ksAdvertiserCampaignController) Edit(ctx context.Context, req *ad.KsAdvertiserCampaignEditReq) (res *ad.KsAdvertiserCampaignEditRes, err error) {
	err = service.KsAdvertiserCampaign().Edit(ctx, req.KsAdvertiserCampaignEditReq)
	return
}

// Delete 删除快手广告计划
func (c *ksAdvertiserCampaignController) Delete(ctx context.Context, req *ad.KsAdvertiserCampaignDeleteReq) (res *ad.KsAdvertiserCampaignDeleteRes, err error) {
	err = service.KsAdvertiserCampaign().Delete(ctx, req.CampaignIds)
	return
}

// ManualSyncCampaign 手动同步广告计划
func (c *ksAdvertiserCampaignController) ManualSyncCampaign(ctx context.Context, req *ad.KsAdvertiserCampaignManualSyncReq) (res *ad.KsAdvertiserCampaignManualSyncRes, err error) {
	err = service.KsAdvertiserCampaign().ManualSyncCampaign(ctx, req.AdvertiserIds, req.StartTime, req.EndTime)
	return
}

// EditCampaignName 修改广告计划名称
func (c *ksAdvertiserCampaignController) EditCampaignName(ctx context.Context, req *ad.KsAdvertiserCampaignEditNameReq) (res *ad.KsAdvertiserCampaignEditNameRes, err error) {
	err = service.KsAdvertiserCampaign().EditCampaignName(ctx, req.CampaignId, req.CampaignName)
	return
}
