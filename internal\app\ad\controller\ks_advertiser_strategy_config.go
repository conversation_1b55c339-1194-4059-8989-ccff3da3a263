// ==========================================================================
// GFast自动生成controller操作代码。
// 生成日期：2025-08-22 11:51:48
// 生成路径: internal/app/ad/controller/ks_advertiser_strategy_config.go
// 生成人：cq
// desc:快手广告搭建-策略配置
// company:云南奇讯科技有限公司
// ==========================================================================

package controller

import (
	"context"
	"github.com/tiger1103/gfast/v3/api/v1/ad"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	systemController "github.com/tiger1103/gfast/v3/internal/app/system/controller"
)

type ksAdvertiserStrategyConfigController struct {
	systemController.BaseController
}

var KsAdvertiserStrategyConfig = new(ksAdvertiserStrategyConfigController)

// List 列表
func (c *ksAdvertiserStrategyConfigController) List(ctx context.Context, req *ad.KsAdvertiserStrategyConfigSearchReq) (res *ad.KsAdvertiserStrategyConfigSearchRes, err error) {
	res = new(ad.KsAdvertiserStrategyConfigSearchRes)
	res.KsAdvertiserStrategyConfigSearchRes, err = service.KsAdvertiserStrategyConfig().List(ctx, &req.KsAdvertiserStrategyConfigSearchReq)
	return
}

// Get 获取快手广告搭建-策略配置
func (c *ksAdvertiserStrategyConfigController) Get(ctx context.Context, req *ad.KsAdvertiserStrategyConfigGetReq) (res *ad.KsAdvertiserStrategyConfigGetRes, err error) {
	res = new(ad.KsAdvertiserStrategyConfigGetRes)
	res.KsAdvertiserStrategyRes, err = service.KsAdvertiserStrategyConfig().GetInfoById(ctx, req.StrategyId, req.TaskId)
	return
}

// Add 添加快手广告搭建-策略配置
func (c *ksAdvertiserStrategyConfigController) Add(ctx context.Context, req *ad.KsAdvertiserStrategyConfigAddReq) (res *ad.KsAdvertiserStrategyConfigAddRes, err error) {
	err = service.KsAdvertiserStrategyConfig().Add(ctx, req.KsAdvertiserStrategyAddReq)
	return
}

// Edit 修改快手广告搭建-策略配置
func (c *ksAdvertiserStrategyConfigController) Edit(ctx context.Context, req *ad.KsAdvertiserStrategyConfigEditReq) (res *ad.KsAdvertiserStrategyConfigEditRes, err error) {
	err = service.KsAdvertiserStrategyConfig().Edit(ctx, req.KsAdvertiserStrategyEditReq)
	return
}

// Delete 删除快手广告搭建-策略配置
func (c *ksAdvertiserStrategyConfigController) Delete(ctx context.Context, req *ad.KsAdvertiserStrategyConfigDeleteReq) (res *ad.KsAdvertiserStrategyConfigDeleteRes, err error) {
	err = service.KsAdvertiserStrategyConfig().Delete(ctx, req.StrategyIds)
	return
}

// Copy 获取巨量广告搭建-复制策略组
func (c *ksAdvertiserStrategyConfigController) Copy(ctx context.Context, req *ad.KsAdvertiserStrategyConfigCopyReq) (res *ad.KsAdvertiserStrategyConfigCopyRes, err error) {
	err = service.KsAdvertiserStrategyConfig().Copy(ctx, req.StrategyId)
	return
}
