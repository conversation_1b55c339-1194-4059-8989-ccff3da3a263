// ==========================================================================
// GFast自动生成dao操作代码。
// 生成日期：2025-08-22 11:51:48
// 生成路径: internal/app/ad/dao/ks_advertiser_strategy_config.go
// 生成人：cq
// desc:快手广告搭建-策略配置
// company:云南奇讯科技有限公司
// ==========================================================================

package dao

import (
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao/internal"
)

// ksAdvertiserStrategyConfigDao is the manager for logic model data accessing and custom defined data operations functions management.
// You can define custom methods on it to extend its functionality as you wish.
type ksAdvertiserStrategyConfigDao struct {
	*internal.KsAdvertiserStrategyConfigDao
}

var (
	// KsAdvertiserStrategyConfig is globally public accessible object for table tools_gen_table operations.
	KsAdvertiserStrategyConfig = ksAdvertiserStrategyConfigDao{
		internal.NewKsAdvertiserStrategyConfigDao(),
	}
)

// Fill with you ideas below.
