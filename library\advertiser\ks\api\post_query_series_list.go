package api

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
)

// QuerySeriesListService 查询授权短剧列表（新）
type QuerySeriesListService struct {
	ctx     context.Context
	cfg     *Configuration
	token   string
	Request *QuerySeriesListReq
}

// QuerySeriesListReq 请求结构体
type QuerySeriesListReq struct {
	AdvertiserId int64  `json:"advertiser_id"` // 广告主ID，必填
	PageSize     int    `json:"page_size"`     // page_size最大 20
	Cursor       string `json:"cursor"`        // 游标
	SeriesTitle  string `json:"series_title"`  // 短剧标题
	UserId       int64  `json:"user_id"`       // 快手号id 必填
}

type MapiSeriesQueryResV2Snake struct {
	Series     []MapiSeriesInfoSnake `json:"series"`      // 短剧列表
	TotalCount int                   `json:"total_count"` // 返回短剧的数量
	Cursor     string                `json:"cursor"`      // 游标
}

// MapiSeriesInfoSnake 短剧信息
type MapiSeriesInfoSnake struct {
	CoverImg      string `json:"cover_img,omitempty"`      // 短剧封面
	Description   string `json:"description,omitempty"`    // 短剧描述
	EpisodeAmount int    `json:"episode_amount,omitempty"` // 剧集数量
	Id            int64  `json:"id,omitempty"`             // 短剧id
	Title         string `json:"title,omitempty"`          // 短剧标题
	SeriesId      int64  `json:"series_id,omitempty"`      // 短剧id 接口没有这个字段 给前端用的
}

func (r *QuerySeriesListService) SetCfg(cfg *Configuration) *QuerySeriesListService {
	r.cfg = cfg
	return r
}

func (r *QuerySeriesListService) SetReq(req QuerySeriesListReq) *QuerySeriesListService {
	r.Request = &req
	return r
}

func (r *QuerySeriesListService) AccessToken(accessToken string) *QuerySeriesListService {
	r.token = accessToken
	return r
}

func (r *QuerySeriesListService) Do() (data *KsBaseResp[MapiSeriesQueryResV2Snake], err error) {
	localBasePath := r.cfg.BasePath
	localVarPath := localBasePath + "/rest/openapi/gw/dsp/v2/series/list"
	response, err := r.cfg.HTTPClient.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("Access-Token", r.token).
		SetBody(r.Request).
		SetResult(&KsBaseResp[MapiSeriesQueryResV2Snake]{}).
		Post(localVarPath)
	if err != nil {
		return nil, err
	}
	resp := new(KsBaseResp[MapiSeriesQueryResV2Snake])
	// 将 JSON 响应解码到结构体中
	err = json.Unmarshal(response.Body(), &resp)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("/rest/openapi/gw/dsp/v2/series/list解析响应出错: %v\n", err))
	}
	if resp.Code == 0 && resp.Data != nil {
		return resp, nil
	} else {
		return resp, errors.New(resp.Message)
	}
}
