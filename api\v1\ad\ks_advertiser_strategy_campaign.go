// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2025-08-22 11:52:02
// 生成路径: api/v1/ad/ks_advertiser_strategy_campaign.go
// 生成人：cq
// desc:快手策略组-广告计划相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package ad

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
)

// KsAdvertiserStrategyCampaignSearchReq 分页请求参数
type KsAdvertiserStrategyCampaignSearchReq struct {
	g.Meta `path:"/list" tags:"快手策略组-广告计划" method:"get" summary:"快手策略组-广告计划列表"`
	commonApi.Author
	model.KsAdvertiserStrategyCampaignSearchReq
}

// KsAdvertiserStrategyCampaignSearchRes 列表返回结果
type KsAdvertiserStrategyCampaignSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.KsAdvertiserStrategyCampaignSearchRes
}

// KsAdvertiserStrategyCampaignAddReq 添加操作请求参数
type KsAdvertiserStrategyCampaignAddReq struct {
	g.Meta `path:"/add" tags:"快手策略组-广告计划" method:"post" summary:"快手策略组-广告计划添加"`
	commonApi.Author
	*model.KsAdvertiserStrategyCampaignAddReq
}

// KsAdvertiserStrategyCampaignAddRes 添加操作返回结果
type KsAdvertiserStrategyCampaignAddRes struct {
	commonApi.EmptyRes
}

// KsAdvertiserStrategyCampaignEditReq 修改操作请求参数
type KsAdvertiserStrategyCampaignEditReq struct {
	g.Meta `path:"/edit" tags:"快手策略组-广告计划" method:"put" summary:"快手策略组-广告计划修改"`
	commonApi.Author
	*model.KsAdvertiserStrategyCampaignEditReq
}

// KsAdvertiserStrategyCampaignEditRes 修改操作返回结果
type KsAdvertiserStrategyCampaignEditRes struct {
	commonApi.EmptyRes
}

// KsAdvertiserStrategyCampaignGetReq 获取一条数据请求
type KsAdvertiserStrategyCampaignGetReq struct {
	g.Meta `path:"/get" tags:"快手策略组-广告计划" method:"get" summary:"获取快手策略组-广告计划信息"`
	commonApi.Author
	Id uint64 `p:"id" v:"required#主键必须"` //通过主键获取
}

// KsAdvertiserStrategyCampaignGetRes 获取一条数据结果
type KsAdvertiserStrategyCampaignGetRes struct {
	g.Meta `mime:"application/json"`
	*model.KsAdvertiserStrategyCampaignInfoRes
}

// KsAdvertiserStrategyCampaignDeleteReq 删除数据请求
type KsAdvertiserStrategyCampaignDeleteReq struct {
	g.Meta `path:"/delete" tags:"快手策略组-广告计划" method:"delete" summary:"删除快手策略组-广告计划"`
	commonApi.Author
	Ids []uint64 `p:"ids" v:"required#主键必须"` //通过主键删除
}

// KsAdvertiserStrategyCampaignDeleteRes 删除数据返回
type KsAdvertiserStrategyCampaignDeleteRes struct {
	commonApi.EmptyRes
}
