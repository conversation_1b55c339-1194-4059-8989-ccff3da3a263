package api

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
)

// ModAccountAutoInfoService 更新账户智投配置信息
type ModAccountAutoInfoService struct {
	ctx     context.Context
	cfg     *Configuration
	token   string
	Request *ModAccountAutoInfoReq
}

type ModAccountAutoInfoReq struct {
	//advertiser_id
	AdvertiserId             int64                      `json:"advertiser_id"`
	AccountAutoManage        int                        `json:"account_auto_manage"`         // 账户智投开关
	OcpxActionTypeConstraint []OcpxActionTypeConstraint `json:"ocpx_action_type_constraint"` // 账户智投目标成本配置
	AutoCampaignNameRule     *string                    `json:"auto_campaign_name_rule"`     // 广告计划命名规则
}

type ModAccountAutoInfoResp struct {
	Code    int                             `json:"code"`    // 状态码
	Message string                          `json:"message"` // 响应消息
	Data    *AccountSimpleQueryResp863Snake `json:"data"`    // 优化目标列表视图
}

func (r *ModAccountAutoInfoService) SetCfg(cfg *Configuration) *ModAccountAutoInfoService {
	r.cfg = cfg
	return r
}

func (r *ModAccountAutoInfoService) SetReq(req ModAccountAutoInfoReq) *ModAccountAutoInfoService {
	r.Request = &req
	return r
}

func (r *ModAccountAutoInfoService) AccessToken(accessToken string) *ModAccountAutoInfoService {
	r.token = accessToken
	return r
}

func (r *ModAccountAutoInfoService) Do() (data *KsBaseResp[ModAccountAutoInfoResp], err error) {
	localBasePath := r.cfg.BasePath
	localVarPath := localBasePath + "/rest/openapi/gw/dsp/account/mod/auto/info"
	response, err := r.cfg.HTTPClient.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("Access-Token", r.token).
		SetBody(r.Request).
		SetResult(&KsBaseResp[ModAccountAutoInfoResp]{}).
		Post(localVarPath)
	if err != nil {
		return nil, err
	}
	resp := new(KsBaseResp[ModAccountAutoInfoResp])
	// 将 JSON 响应解码到结构体中
	err = json.Unmarshal(response.Body(), &resp)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("/rest/openapi/gw/dsp/account/mod/auto/info解析响应出错: %v\n", err))
	}
	if resp.Code == 0 && resp.Data != nil {
		return resp, nil
	} else {
		return resp, errors.New(resp.Message)
	}
}
