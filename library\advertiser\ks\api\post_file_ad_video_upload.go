package api

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"time"
)

// FileAdVideoUploadService 上传视频接口 v2
type FileAdVideoUploadService struct {
	ctx     context.Context
	cfg     *Configuration
	token   string
	Request *FileAdVideoUploadReq
}

type FileAdVideoUploadReq struct {
	AdvertiserId int64 `json:"advertiser_id"` // 广告主id
	//blob_store_key
	BlobStoreKey string `json:"blob_store_key"`
	// signature
	Signature string `json:"signature"`
}

type FileAdVideoUploadResp struct {
	Code    int                `json:"code"`    // 状态码
	Message string             `json:"message"` // 响应消息
	Data    *FileAdVideoUpload `json:"data"`    // 优化目标列表视图
}
type FileAdVideoUpload struct {
	PhotoId   string `json:"photo_id"`
	Signature string `json:"signature"`
}

func (r *FileAdVideoUploadService) SetCfg(cfg *Configuration) *FileAdVideoUploadService {
	r.cfg = cfg
	return r
}

func (r *FileAdVideoUploadService) SetReq(req FileAdVideoUploadReq) *FileAdVideoUploadService {
	r.Request = &req
	return r
}

func (r *FileAdVideoUploadService) AccessToken(accessToken string) *FileAdVideoUploadService {
	r.token = accessToken
	return r
}

func (r *FileAdVideoUploadService) Do() (data *FileAdVideoUploadResp, err error) {
	localBasePath := r.cfg.BasePath
	localVarPath := localBasePath + "/rest/openapi/v2/file/ad/video/upload"
	response, err := r.cfg.HTTPClient.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("Access-Token", r.token).
		SetBody(r.Request).
		SetResult(&FileAdVideoUploadResp{}).
		Post(localVarPath)
	if err != nil {
		return nil, err
	}
	resp := new(FileAdVideoUploadResp)
	// 将 JSON 响应解码到结构体中
	err = json.Unmarshal(response.Body(), &resp)
	if err != nil {
		if strings.Contains(err.Error(), "文件操作过于频繁") {
			time.Sleep(200 * time.Millisecond)
			return r.Do()
		}
		return nil, errors.New(fmt.Sprintf("/rest/openapi/v2/file/ad/video/upload解析响应出错: %v\n", err))
	}
	if resp.Code == 0 && resp.Data != nil {
		return resp, nil
	} else {
		return resp, errors.New(resp.Message)
	}
}
