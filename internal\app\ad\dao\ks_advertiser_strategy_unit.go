// ==========================================================================
// GFast自动生成dao操作代码。
// 生成日期：2025-08-22 11:52:52
// 生成路径: internal/app/ad/dao/ks_advertiser_strategy_unit.go
// 生成人：cq
// desc:快手策略组-广告组
// company:云南奇讯科技有限公司
// ==========================================================================

package dao

import (
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao/internal"
)

// ksAdvertiserStrategyUnitDao is the manager for logic model data accessing and custom defined data operations functions management.
// You can define custom methods on it to extend its functionality as you wish.
type ksAdvertiserStrategyUnitDao struct {
	*internal.KsAdvertiserStrategyUnitDao
}

var (
	// KsAdvertiserStrategyUnit is globally public accessible object for table tools_gen_table operations.
	KsAdvertiserStrategyUnit = ksAdvertiserStrategyUnitDao{
		internal.NewKsAdvertiserStrategyUnitDao(),
	}
)

// Fill with you ideas below.
