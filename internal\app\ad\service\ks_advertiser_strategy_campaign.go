// ==========================================================================
// GFast自动生成service操作代码。
// 生成日期：2025-08-22 11:52:02
// 生成路径: internal/app/ad/service/ks_advertiser_strategy_campaign.go
// 生成人：cq
// desc:快手策略组-广告计划
// company:云南奇讯科技有限公司
// ==========================================================================

package service

import (
	"context"

	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
)

type IKsAdvertiserStrategyCampaign interface {
	List(ctx context.Context, req *model.KsAdvertiserStrategyCampaignSearchReq) (res *model.KsAdvertiserStrategyCampaignSearchRes, err error)
	GetById(ctx context.Context, Id uint64) (res *model.KsAdvertiserStrategyCampaignInfoRes, err error)
	GetInfoById(ctx context.Context, strategyId string, taskId string) (res *model.KsAdvertiserStrategyCampaignInfoRes, err error)
	Add(ctx context.Context, req *model.KsAdvertiserStrategyCampaignAddReq) (err error)
	Edit(ctx context.Context, req *model.KsAdvertiserStrategyCampaignEditReq) (err error)
	Delete(ctx context.Context, Id []uint64) (err error)
}

var localKsAdvertiserStrategyCampaign IKsAdvertiserStrategyCampaign

func KsAdvertiserStrategyCampaign() IKsAdvertiserStrategyCampaign {
	if localKsAdvertiserStrategyCampaign == nil {
		panic("implement not found for interface IKsAdvertiserStrategyCampaign, forgot register?")
	}
	return localKsAdvertiserStrategyCampaign
}

func RegisterKsAdvertiserStrategyCampaign(i IKsAdvertiserStrategyCampaign) {
	localKsAdvertiserStrategyCampaign = i
}
