// ==========================================================================
// GFast自动生成dao操作代码。
// 生成日期：2025-08-21 00:00:00
// 生成路径: internal/app/ad/dao/ks_advertiser_strategy_generate.go
// 生成人：gfast
// desc:快手广告策略生成
// company:云南奇讯科技有限公司
// ==========================================================================

package dao

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// ksAdvertiserStrategyGenerate<PERSON><PERSON> is the manager for logic model data accessing and custom defined data operations functions management.
// You can define custom methods on it to extend its functionality as you wish.
type ksAdvertiserStrategyGenerateDao struct {
}

var (
	// KsAdvertiserStrategyGenerate is globally public accessible object for table operations.
	KsAdvertiserStrategyGenerate = ksAdvertiserStrategyGenerateDao{}
)

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *ksAdvertiserStrategyGenerateDao) DB() gdb.DB {
	return g.DB()
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *ksAdvertiserStrategyGenerateDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model("").Ctx(ctx)
}

// Fill with you ideas below.
