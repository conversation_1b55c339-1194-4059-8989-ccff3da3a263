// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2025-08-23 17:40:24
// 生成路径: api/v1/ad/ks_advertiser_strategy_task_unit.go
// 生成人：cyao
// desc:快手广告搭建-任务-广告组相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package ad

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
)

// KsAdvertiserStrategyTaskUnitSearchReq 分页请求参数
type KsAdvertiserStrategyTaskUnitSearchReq struct {
	g.Meta `path:"/list" tags:"快手广告搭建-任务-广告组" method:"get" summary:"快手广告搭建-任务-广告组列表"`
	commonApi.Author
	model.KsAdvertiserStrategyTaskUnitSearchReq
}

// KsAdvertiserStrategyTaskUnitSearchRes 列表返回结果
type KsAdvertiserStrategyTaskUnitSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.KsAdvertiserStrategyTaskUnitSearchRes
}

// KsAdvertiserStrategyTaskUnitAddReq 添加操作请求参数
type KsAdvertiserStrategyTaskUnitAddReq struct {
	g.Meta `path:"/add" tags:"快手广告搭建-任务-广告组" method:"post" summary:"快手广告搭建-任务-广告组添加"`
	commonApi.Author
	*model.KsAdvertiserStrategyTaskUnitAddReq
}

// KsAdvertiserStrategyTaskUnitAddRes 添加操作返回结果
type KsAdvertiserStrategyTaskUnitAddRes struct {
	commonApi.EmptyRes
}

// KsAdvertiserStrategyTaskUnitEditReq 修改操作请求参数
type KsAdvertiserStrategyTaskUnitEditReq struct {
	g.Meta `path:"/edit" tags:"快手广告搭建-任务-广告组" method:"put" summary:"快手广告搭建-任务-广告组修改"`
	commonApi.Author
	*model.KsAdvertiserStrategyTaskUnitEditReq
}

// KsAdvertiserStrategyTaskUnitEditRes 修改操作返回结果
type KsAdvertiserStrategyTaskUnitEditRes struct {
	commonApi.EmptyRes
}

// KsAdvertiserStrategyTaskUnitGetReq 获取一条数据请求
type KsAdvertiserStrategyTaskUnitGetReq struct {
	g.Meta `path:"/get" tags:"快手广告搭建-任务-广告组" method:"get" summary:"获取快手广告搭建-任务-广告组信息"`
	commonApi.Author
	TaskUnitId string `p:"taskUnitId" v:"required#主键必须"` //通过主键获取
}

// KsAdvertiserStrategyTaskUnitGetRes 获取一条数据结果
type KsAdvertiserStrategyTaskUnitGetRes struct {
	g.Meta `mime:"application/json"`
	*model.KsAdvertiserStrategyTaskUnitInfoRes
}

// KsAdvertiserStrategyTaskUnitDeleteReq 删除数据请求
type KsAdvertiserStrategyTaskUnitDeleteReq struct {
	g.Meta `path:"/delete" tags:"快手广告搭建-任务-广告组" method:"delete" summary:"删除快手广告搭建-任务-广告组"`
	commonApi.Author
	TaskUnitIds []string `p:"taskUnitIds" v:"required#主键必须"` //通过主键删除
}

// KsAdvertiserStrategyTaskUnitDeleteRes 删除数据返回
type KsAdvertiserStrategyTaskUnitDeleteRes struct {
	commonApi.EmptyRes
}
