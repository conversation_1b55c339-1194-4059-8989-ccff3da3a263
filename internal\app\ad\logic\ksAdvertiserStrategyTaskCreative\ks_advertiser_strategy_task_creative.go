// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2025-08-23 17:40:22
// 生成路径: internal/app/ad/logic/ks_advertiser_strategy_task_creative.go
// 生成人：cyao
// desc:快手广告搭建-任务-广告创意
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	"github.com/tiger1103/gfast/v3/library/liberr"
)

func init() {
	service.RegisterKsAdvertiserStrategyTaskCreative(New())
}

func New() service.IKsAdvertiserStrategyTaskCreative {
	return &sKsAdvertiserStrategyTaskCreative{}
}

type sKsAdvertiserStrategyTaskCreative struct{}

func (s *sKsAdvertiserStrategyTaskCreative) List(ctx context.Context, req *model.KsAdvertiserStrategyTaskCreativeSearchReq) (listRes *model.KsAdvertiserStrategyTaskCreativeSearchRes, err error) {
	listRes = new(model.KsAdvertiserStrategyTaskCreativeSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.KsAdvertiserStrategyTaskCreative.Ctx(ctx).WithAll()
		if req.TaskCreativeId != "" {
			m = m.Where(dao.KsAdvertiserStrategyTaskCreative.Columns().TaskCreativeId+" = ?", req.TaskCreativeId)
		}
		if req.CreativeId != "" {
			m = m.Where(dao.KsAdvertiserStrategyTaskCreative.Columns().CreativeId+" = ?", gconv.Int64(req.CreativeId))
		}
		if req.CreativeName != "" {
			m = m.Where(dao.KsAdvertiserStrategyTaskCreative.Columns().CreativeName+" like ?", "%"+req.CreativeName+"%")
		}
		if req.TaskId != "" {
			m = m.Where(dao.KsAdvertiserStrategyTaskCreative.Columns().TaskId+" = ?", req.TaskId)
		}
		if req.TaskCampaignId != "" {
			m = m.Where(dao.KsAdvertiserStrategyTaskCreative.Columns().TaskCampaignId+" = ?", req.TaskCampaignId)
		}
		if req.TaskUnitId != "" {
			m = m.Where(dao.KsAdvertiserStrategyTaskCreative.Columns().TaskUnitId+" = ?", req.TaskUnitId)
		}
		if req.AdvertiserId != "" {
			m = m.Where(dao.KsAdvertiserStrategyTaskCreative.Columns().AdvertiserId+" = ?", req.AdvertiserId)
		}
		if req.AdvertiserNick != "" {
			m = m.Where(dao.KsAdvertiserStrategyTaskCreative.Columns().AdvertiserNick+" = ?", req.AdvertiserNick)
		}
		if req.ErrMsg != "" {
			m = m.Where(dao.KsAdvertiserStrategyTaskCreative.Columns().ErrMsg+" = ?", req.ErrMsg)
		}
		if req.Status != "" {
			m = m.Where(dao.KsAdvertiserStrategyTaskCreative.Columns().Status+" = ?", req.Status)
		}
		if len(req.DateRange) != 0 {
			m = m.Where(dao.KsAdvertiserStrategyTaskCreative.Columns().CreatedAt+" >=? AND "+dao.KsAdvertiserStrategyTaskCreative.Columns().CreatedAt+" <=?", req.DateRange[0], req.DateRange[1])
		}
		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "task_creative_id asc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		var res []*model.KsAdvertiserStrategyTaskCreativeListRes
		err = m.Page(req.PageNum, req.PageSize).Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		listRes.List = make([]*model.KsAdvertiserStrategyTaskCreativeListRes, len(res))
		for k, v := range res {
			listRes.List[k] = &model.KsAdvertiserStrategyTaskCreativeListRes{
				TaskCreativeId: v.TaskCreativeId,
				CreativeId:     v.CreativeId,
				CreativeName:   v.CreativeName,
				TaskId:         v.TaskId,
				TaskCampaignId: v.TaskCampaignId,
				TaskUnitId:     v.TaskUnitId,
				AdvertiserId:   v.AdvertiserId,
				AdvertiserNick: v.AdvertiserNick,
				ErrMsg:         v.ErrMsg,
				Status:         v.Status,
				CreativeData:   v.CreativeData,
				CreatedAt:      v.CreatedAt,
			}
		}
	})
	return
}

func (s *sKsAdvertiserStrategyTaskCreative) GetByTaskCreativeId(ctx context.Context, taskCreativeId string) (res *model.KsAdvertiserStrategyTaskCreativeInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.KsAdvertiserStrategyTaskCreative.Ctx(ctx).WithAll().Where(dao.KsAdvertiserStrategyTaskCreative.Columns().TaskCreativeId, taskCreativeId).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sKsAdvertiserStrategyTaskCreative) Add(ctx context.Context, req *model.KsAdvertiserStrategyTaskCreativeAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.KsAdvertiserStrategyTaskCreative.Ctx(ctx).Insert(do.KsAdvertiserStrategyTaskCreative{
			TaskCreativeId: req.TaskCreativeId,
			CreativeId:     req.CreativeId,
			CreativeName:   req.CreativeName,
			TaskId:         req.TaskId,
			TaskCampaignId: req.TaskCampaignId,
			TaskUnitId:     req.TaskUnitId,
			AdvertiserId:   req.AdvertiserId,
			AdvertiserNick: req.AdvertiserNick,
			ErrMsg:         req.ErrMsg,
			Status:         req.Status,
			CreativeData:   req.CreativeData,
		})
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sKsAdvertiserStrategyTaskCreative) Edit(ctx context.Context, req *model.KsAdvertiserStrategyTaskCreativeEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.KsAdvertiserStrategyTaskCreative.Ctx(ctx).WherePri(req.TaskCreativeId).Update(do.KsAdvertiserStrategyTaskCreative{
			CreativeId:     req.CreativeId,
			CreativeName:   req.CreativeName,
			TaskId:         req.TaskId,
			TaskCampaignId: req.TaskCampaignId,
			TaskUnitId:     req.TaskUnitId,
			AdvertiserId:   req.AdvertiserId,
			AdvertiserNick: req.AdvertiserNick,
			ErrMsg:         req.ErrMsg,
			Status:         req.Status,
			CreativeData:   req.CreativeData,
		})
		liberr.ErrIsNil(ctx, err, "修改失败")
	})
	return
}

func (s *sKsAdvertiserStrategyTaskCreative) Delete(ctx context.Context, taskCreativeIds []string) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.KsAdvertiserStrategyTaskCreative.Ctx(ctx).Delete(dao.KsAdvertiserStrategyTaskCreative.Columns().TaskCreativeId+" in (?)", taskCreativeIds)
		liberr.ErrIsNil(ctx, err, "删除失败")
	})
	return
}
