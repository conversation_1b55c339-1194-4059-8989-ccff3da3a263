// ==========================================================================
// GFast自动生成dao internal操作代码。
// 生成日期：2025-08-22 11:52:02
// 生成路径: internal/app/ad/dao/internal/ks_advertiser_strategy_campaign.go
// 生成人：cq
// desc:快手策略组-广告计划
// company:云南奇讯科技有限公司
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// KsAdvertiserStrategyCampaignDao is the manager for logic model data accessing and custom defined data operations functions management.
type KsAdvertiserStrategyCampaignDao struct {
	table   string                              // Table is the underlying table name of the DAO.
	group   string                              // Group is the database configuration group name of current DAO.
	columns KsAdvertiserStrategyCampaignColumns // Columns is the short type for Columns, which contains all the column names of Table for convenient usage.
}

// KsAdvertiserStrategyCampaignColumns defines and stores column names for table ks_advertiser_strategy_campaign.
type KsAdvertiserStrategyCampaignColumns struct {
	Id               string // 主键ID
	StrategyId       string // 策略组ID
	TaskId           string // 任务ID
	CampaignType     string // 营销类型 30：快手号-短剧推广
	AdType           string // 广告计划类型 0:信息流（展示广告） 1:搜索（搜索广告）；不填默认信息流
	AutoAdjust       string // 自动调控开关 0：关闭，1：开启
	AutoBuild        string // 自动基建 0：关闭，1：开启
	UnitNameRule     string // 单元名称规则
	CreativeNameRule string // 创意名称规则
	AutoManage       string // 智能投放开关 0：关闭，1：开启
	BidType          string // 竞价策略 0：成本优先 1：最大转化
	DayBudget        string // 日预算 不限传0
	CampaignName     string // 广告计划名称
	AdUnitLimit      string // 广告计划内广告组上线
	PutStatus        string // 广告计划默认状态 1-投放、2-暂停
	CreatedAt        string // 创建时间
	UpdatedAt        string // 更新时间
	DeletedAt        string // 删除时间
}

var ksAdvertiserStrategyCampaignColumns = KsAdvertiserStrategyCampaignColumns{
	Id:               "id",
	StrategyId:       "strategy_id",
	TaskId:           "task_id",
	CampaignType:     "campaign_type",
	AdType:           "ad_type",
	AutoAdjust:       "auto_adjust",
	AutoBuild:        "auto_build",
	UnitNameRule:     "unit_name_rule",
	CreativeNameRule: "creative_name_rule",
	AutoManage:       "auto_manage",
	BidType:          "bid_type",
	DayBudget:        "day_budget",
	CampaignName:     "campaign_name",
	AdUnitLimit:      "ad_unit_limit",
	PutStatus:        "put_status",
	CreatedAt:        "created_at",
	UpdatedAt:        "updated_at",
	DeletedAt:        "deleted_at",
}

// NewKsAdvertiserStrategyCampaignDao creates and returns a new DAO object for table data access.
func NewKsAdvertiserStrategyCampaignDao() *KsAdvertiserStrategyCampaignDao {
	return &KsAdvertiserStrategyCampaignDao{
		group:   "default",
		table:   "ks_advertiser_strategy_campaign",
		columns: ksAdvertiserStrategyCampaignColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *KsAdvertiserStrategyCampaignDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *KsAdvertiserStrategyCampaignDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *KsAdvertiserStrategyCampaignDao) Columns() KsAdvertiserStrategyCampaignColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *KsAdvertiserStrategyCampaignDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *KsAdvertiserStrategyCampaignDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *KsAdvertiserStrategyCampaignDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
