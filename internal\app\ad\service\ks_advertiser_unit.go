// ==========================================================================
// GFast自动生成service操作代码。
// 生成日期：2025-08-13 16:33:57
// 生成路径: internal/app/ad/service/ks_advertiser_unit.go
// 生成人：cyao
// desc:快手广告组
// company:云南奇讯科技有限公司
// ==========================================================================

package service

import (
	"context"

	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
)

type IKsAdvertiserUnit interface {
	List(ctx context.Context, req *model.KsAdvertiserUnitSearchReq) (res *model.KsAdvertiserUnitSearchRes, err error)
	GetByUnitId(ctx context.Context, UnitId int64) (res *model.KsAdvertiserUnitInfoRes, err error)
	GetUnitInfo(ctx context.Context, unitId int64) (res *model.KsAdvertiserUnitInfoRes, err error)
	PullUnitByAdId(ctx context.Context, accessToken string, adId int64) (err error)
	Add(ctx context.Context, req *model.KsAdvertiserUnitAddReq) (err error)
	Edit(ctx context.Context, req *model.KsAdvertiserUnitEditReq) (err error)
	Delete(ctx context.Context, UnitId []int64) (err error)
	UpdateUnitInfo(ctx context.Context, req *model.KsAdvertiserUnitUpdateReq) (err error)
	ManualSyncUnit(ctx context.Context, advertiserIds []int64, startTime, endTime string) (err error)
	EditUnitName(ctx context.Context, unitId int64, unitName string) (err error)
}

var localKsAdvertiserUnit IKsAdvertiserUnit

func KsAdvertiserUnit() IKsAdvertiserUnit {
	if localKsAdvertiserUnit == nil {
		panic("implement not found for interface IKsAdvertiserUnit, forgot register?")
	}
	return localKsAdvertiserUnit
}

func RegisterKsAdvertiserUnit(i IKsAdvertiserUnit) {
	localKsAdvertiserUnit = i
}
