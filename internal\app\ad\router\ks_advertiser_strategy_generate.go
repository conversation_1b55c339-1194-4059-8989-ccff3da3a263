// ==========================================================================
// GFast自动生成router操作代码。
// 生成日期：2025-08-21 00:00:00
// 生成路径: internal/app/ad/router/ks_advertiser_strategy_generate.go
// 生成人：gfast
// desc:快手广告策略生成
// company:云南奇讯科技有限公司
// ==========================================================================

package router

import (
	"context"

	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/tiger1103/gfast/v3/internal/app/ad/controller"
)

func (router *Router) BindKsAdvertiserStrategyGenerateController(ctx context.Context, group *ghttp.RouterGroup) {
	group.Group("/ksAdvertiserStrategyGenerate", func(group *ghttp.RouterGroup) {
		group.Bind(
			controller.KsAdvertiserStrategyGenerate,
		)
	})
}
