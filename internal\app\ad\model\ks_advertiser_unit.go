// ==========================================================================
// GFast自动生成model操作代码。
// 生成日期：2025-08-13 16:33:56
// 生成路径: internal/app/ad/model/ks_advertiser_unit.go
// 生成人：cyao
// desc:快手广告组
// company:云南奇讯科技有限公司
// ==========================================================================

package model

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
	comModel "github.com/tiger1103/gfast/v3/internal/app/common/model"
)

// KsAdvertiserUnitInfoRes is the golang structure for table ks_advertiser_unit.
type KsAdvertiserUnitInfoRes struct {
	gmeta.Meta                  `orm:"table:ks_advertiser_unit"`
	UnitId                      int64       `orm:"unit_id,primary" json:"unitId" dc:"单元 ID"`    // 单元 ID
	AdvertiserId                int         `orm:"advertiser_id" json:"advertiserId" dc:"广告id"` // 广告id
	CampaignId                  int64       `orm:"campaign_id" json:"campaignId" dc:"广告计划 ID"`  // 广告计划 ID
	CampaignName                string      `orm:"-" json:"campaignName" dc:"广告计划名称"`
	LinkIntegrationType         int         `orm:"link_integration_type" json:"linkIntegrationType" dc:"链接整合类型"`                          // 链接整合类型
	AssetMining                 int         `orm:"asset_mining" json:"assetMining" dc:"资产挖掘"`                                             // 资产挖掘
	SiteType                    int         `orm:"site_type" json:"siteType" dc:"预约广告 1:IOS 预约 缺省为不传或传 0"`                                // 预约广告 1:IOS 预约 缺省为不传或传 0
	AdType                      int         `orm:"ad_type" json:"adType" dc:"广告计划类型 0:信息流，1:搜索"`                                          // 广告计划类型 0:信息流，1:搜索
	DpaDynamicParamsForUri      string      `orm:"dpa_dynamic_params_for_uri" json:"dpaDynamicParamsForUri" dc:"落地页链接动态参数"`               // 落地页链接动态参数
	SchemaUri                   string      `orm:"schema_uri" json:"schemaUri" dc:"调起链接、提升应用活跃营销目标的调起链接"`                                 // 调起链接、提升应用活跃营销目标的调起链接
	ProductImage                string      `orm:"product_image" json:"productImage" dc:"商品主图"`                                           // 商品主图
	PackageId                   int         `orm:"package_id" json:"packageId" dc:"新版应用中心应用ID"`                                           // 新版应用中心应用ID
	BidType                     int         `orm:"bid_type" json:"bidType" dc:"出价类型 1：CPM，2：CPC，6：OCPC(使用 OCPC 代表 OCPX)，10：OCPM，20：eCPC"` // 出价类型 1：CPM，2：CPC，6：OCPC(使用 OCPC 代表 OCPX)，10：OCPM，20：eCPC
	ProductPrice                string      `orm:"product_price" json:"productPrice" dc:"商品价格 元"`                                         // 商品价格 元
	PutStatus                   int         `orm:"put_status" json:"putStatus" dc:"投放状态 1：投放中；2：暂停 3：删除"`                                 // 投放状态 1：投放中；2：暂停 3：删除
	SmartCover                  int         `orm:"smart_cover" json:"smartCover" dc:"智能封面 是否开启智能抽帧"`                                      // 智能封面 是否开启智能抽帧
	DpaOuterIds                 string      `orm:"dpa_outer_ids" json:"dpaOuterIds" dc:"DPA外部商品id集合"`                                     // DPA外部商品id集合
	SeriesPayTemplateIdMulti    string      `orm:"series_pay_template_id_multi" json:"seriesPayTemplateIdMulti" dc:"短剧付费模版列表"`            // 短剧付费模版列表
	DpaUnitSubType              int         `orm:"dpa_unit_sub_type" json:"dpaUnitSubType" dc:"商品广告类型：1-DPA，2-SDPA，3-动态商品卡"`              // 商品广告类型：1-DPA，2-SDPA，3-动态商品卡
	ULink                       string      `orm:"u_link" json:"uLink" dc:"ios系统的ulink链接"`                                                // ios系统的ulink链接
	AppStore                    string      `orm:"app_store" json:"appStore" dc:"应用商店列表"`                                                 // 应用商店列表
	AppDownloadType             int         `orm:"app_download_type" json:"appDownloadType" dc:"应用下载类型 0：直接下载1：落地页下载（仅在提升应用安装计划下生效）"`     // 应用下载类型 0：直接下载1：落地页下载（仅在提升应用安装计划下生效）
	DspVersion                  int         `orm:"dsp_version" json:"dspVersion" dc:"DSP版本"`                                              // DSP版本
	PlayableId                  int64       `orm:"playable_id" json:"playableId" dc:"可试玩 ID 可选字段，开启试玩时存在，否则不存在"`                          // 可试玩 ID 可选字段，开启试玩时存在，否则不存在
	EpisodeId                   int64       `orm:"episode_id" json:"episodeId" dc:"剧集 ID"`                                                // 剧集 ID
	PlayableSwitch              int         `orm:"playable_switch" json:"playableSwitch" dc:"可试玩开关"`                                      // 可试玩开关
	ProductId                   string      `orm:"product_id" json:"productId" dc:"产品 ID"`                                                // 产品 ID
	SchemaId                    string      `orm:"schema_id" json:"schemaId" dc:"微信小程序外部调起链接（部分场景有效）"`                                    // 微信小程序外部调起链接（部分场景有效）
	UnitSource                  int         `orm:"unit_source" json:"unitSource" dc:"广告组来源 0:常规（非托管）、1:托管"`                               // 广告组来源 0:常规（非托管）、1:托管
	SeriesPayTemplateId         int64       `orm:"series_pay_template_id" json:"seriesPayTemplateId" dc:"付费模板（短剧推广时支持）"`                  // 付费模板（短剧推广时支持）
	DayBudget                   int64       `orm:"day_budget" json:"dayBudget" dc:"日预算 单位：厘"`                                             // 日预算 单位：厘
	OcpxActionType              int         `orm:"ocpx_action_type" json:"ocpxActionType" dc:"优化目标（枚举值见文档）"`                              // 优化目标（枚举值见文档）
	UseAppMarket                int         `orm:"use_app_market" json:"useAppMarket" dc:"是否使用应用市场 0：未设置 1：优先从系统应用商店下载"`                  // 是否使用应用市场 0：未设置 1：优先从系统应用商店下载
	TargetExplore               int         `orm:"target_explore" json:"targetExplore" dc:"是否开启搜索人群探索"`                                   // 是否开启搜索人群探索
	ComponentId                 int64       `orm:"component_id" json:"componentId" dc:"组件 ID"`                                            // 组件 ID
	DpaDynamicParams            int         `orm:"dpa_dynamic_params" json:"dpaDynamicParams" dc:"DPA 动态参数 开关"`                           // DPA 动态参数 开关
	CreateTime                  *gtime.Time `orm:"create_time" json:"createTime" dc:"创建时间"`                                               // 创建时间
	PlayButton                  string      `orm:"play_button" json:"playButton" dc:"试玩按钮文字内容"`                                           // 试玩按钮文字内容
	UrlType                     int         `orm:"url_type" json:"urlType" dc:"URL 类型（特定计划下返回）"`                                          // URL 类型（特定计划下返回）
	DpaCategories               string      `orm:"dpa_categories" json:"dpaCategories" dc:"DPA 类别"`                                       // DPA 类别
	ProductName                 string      `orm:"product_name" json:"productName" dc:"产品名称"`                                             // 产品名称
	SeriesPayMode               int         `orm:"series_pay_mode" json:"seriesPayMode" dc:"付费模式（短剧推广时返回）"`                               // 付费模式（短剧推广时返回）
	ShowMode                    int         `orm:"show_mode" json:"showMode" dc:"展示模式 0：未知，1：轮播，2：优选"`                                    // 展示模式 0：未知，1：轮播，2：优选
	UnitName                    string      `orm:"unit_name" json:"unitName" dc:"广告组名称"`                                                  // 广告组名称
	ExtendSearch                int         `orm:"extend_search" json:"extendSearch" dc:"智能扩词开启状态"`                                       // 智能扩词开启状态
	QuickSearch                 int         `orm:"quick_search" json:"quickSearch" dc:"是否开启快投"`                                           // 是否开启快投
	SiteId                      int64       `orm:"site_id" json:"siteId" dc:"建站ID / 安卓下载中间页ID"`                                           // 建站ID / 安卓下载中间页ID
	SeriesCardInfo              string      `orm:"series_card_info" json:"seriesCardInfo" dc:"剧集卡片信息"`                                    // 剧集卡片信息
	Status                      int         `orm:"status" json:"status" dc:"广告组状态"`                                                       // 广告组状态
	ConsultId                   int64       `orm:"consult_id" json:"consultId" dc:"咨询组件使用情况"`                                             // 咨询组件使用情况
	RoiRatio                    float64     `orm:"roi_ratio" json:"roiRatio" dc:"付费 ROI 系数"`                                              // 付费 ROI 系数
	LiveComponentType           int         `orm:"live_component_type" json:"liveComponentType" dc:"直播组件类型"`                              // 直播组件类型
	SearchPopulationRetargeting int         `orm:"search_population_retargeting" json:"searchPopulationRetargeting" dc:"是否开启人群追投"`        // 是否开启人群追投
	EnhanceConversionType       int         `orm:"enhance_conversion_type" json:"enhanceConversionType" dc:"增强目标"`                        // 增强目标
	UnitType                    int         `orm:"unit_type" json:"unitType" dc:"创意制作方式"`                                                 // 创意制作方式
	OuterId                     string      `orm:"outer_id" json:"outerId" dc:"外部 ID"`                                                    // 外部 ID
	StudyStatus                 int         `orm:"study_status" json:"studyStatus" dc:"学习状态"`                                             // 学习状态
	SeriesCardType              int         `orm:"series_card_type" json:"seriesCardType" dc:"剧集卡片类型"`                                    // 剧集卡片类型
	CustomMiniAppData           string      `orm:"custom_mini_app_data" json:"customMiniAppData" dc:"自定义小程序数据"`                           // 自定义小程序数据
	UpdateTime                  *gtime.Time `orm:"update_time" json:"updateTime" dc:"更新时间"`                                               // 更新时间
	ImMessageMount              int         `orm:"im_message_mount" json:"imMessageMount" dc:"IM 消息挂载"`                                   // IM 消息挂载
	LibraryId                   int64       `orm:"library_id" json:"libraryId" dc:"素材库 ID"`                                               // 素材库 ID
	DeepConversionType          int         `orm:"deep_conversion_type" json:"deepConversionType" dc:"深度转化类型"`                            // 深度转化类型
	DeepConversionBid           int         `orm:"deep_conversion_bid" json:"deepConversionBid" dc:"深度转化出价"`                              // 深度转化出价
	KwaiBookId                  int64       `orm:"kwai_book_id" json:"kwaiBookId" dc:"快手书籍 ID"`                                           // 快手书籍 ID
	DpaDynamicParamsForDp       string      `orm:"dpa_dynamic_params_for_dp" json:"dpaDynamicParamsForDp" dc:"DP 动态参数"`                   // DP 动态参数
	PageAuditStatus             string      `orm:"page_audit_status" json:"pageAuditStatus" dc:"页面审核状态"`                                  // 页面审核状态
	JingleBellId                int64       `orm:"jingle_bell_id" json:"jingleBellId" dc:"铃铛 ID"`                                         // 铃铛 ID
	LiveUserId                  int64       `orm:"live_user_id" json:"liveUserId" dc:"直播用户 ID"`                                           // 直播用户 ID
	PlayableUrl                 string      `orm:"playable_url" json:"playableUrl" dc:"可试玩 URL"`                                          // 可试玩 URL
	PlayableFileName            string      `orm:"playable_file_name" json:"playableFileName" dc:"可试玩文件名"`                                // 可试玩文件名
	WebUriType                  int         `orm:"web_uri_type" json:"webUriType" dc:"Web URI 类型"`                                        // Web URI 类型
	ReviewDetail                string      `orm:"review_detail" json:"reviewDetail" dc:"审核详情"`                                           // 审核详情
	EndTime                     *gtime.Time `orm:"end_time" json:"endTime" dc:"结束时间"`                                                     // 结束时间
	CompensateStatus            int         `orm:"compensate_status" json:"compensateStatus" dc:"补偿状态"`                                   // 补偿状态
	SceneId                     string      `orm:"scene_id" json:"sceneId" dc:"场景 ID"`                                                    // 场景 ID
	BeginTime                   *gtime.Time `orm:"begin_time" json:"beginTime" dc:"开始时间"`                                                 // 开始时间
	UnitMaterialType            int         `orm:"unit_material_type" json:"unitMaterialType" dc:"单元素材类型"`                                // 单元素材类型
	ConvertId                   int64       `orm:"convert_id" json:"convertId" dc:"转化 ID"`                                                // 转化 ID
	Url                         string      `orm:"url" json:"url" dc:"链接"`                                                                // 链接
	SeriesId                    int64       `orm:"series_id" json:"seriesId" dc:"系列 ID"`                                                  // 系列 ID
	ScheduleTime                string      `orm:"schedule_time" json:"scheduleTime" dc:"日程时间"`                                           // 日程时间
	OuterLoopNative             int         `orm:"outer_loop_native" json:"outerLoopNative" dc:"外循环原生"`                                   // 外循环原生
	CpaBid                      int         `orm:"cpa_bid" json:"cpaBid" dc:"CPA 出价"`                                                     // CPA 出价
	PlayableOrientation         int         `orm:"playable_orientation" json:"playableOrientation" dc:"可试玩方向"`                            // 可试玩方向
	TemplateId                  int         `orm:"template_id" json:"templateId" dc:"模板 ID"`                                              // 模板 ID
	Bid                         int         `orm:"bid" json:"bid" dc:"出价"`                                                                // 出价
	AdvCardOption               int         `orm:"adv_card_option" json:"advCardOption" dc:"广告卡片选项"`                                      // 广告卡片选项
	CreatedAt                   *gtime.Time `orm:"created_at" json:"createdAt" dc:"创建时间"`                                                 // 创建时间
	DeletedAt                   *gtime.Time `orm:"deleted_at" json:"deletedAt" dc:"删除时间"`                                                 // 删除时间
}

type KsAdvertiserUnitListRes struct {
	UnitId                      int64       `json:"unitId" dc:"单元 ID"`
	AdvertiserId                int         `json:"advertiserId" dc:"广告id"`
	CampaignId                  int64       `json:"campaignId" dc:"广告计划 ID"`
	LinkIntegrationType         int         `json:"linkIntegrationType" dc:"链接整合类型"`
	AssetMining                 int         `json:"assetMining" dc:"资产挖掘"`
	SiteType                    int         `json:"siteType" dc:"预约广告 1:IOS 预约 缺省为不传或传 0"`
	AdType                      int         `json:"adType" dc:"广告计划类型 0:信息流，1:搜索"`
	DpaDynamicParamsForUri      string      `json:"dpaDynamicParamsForUri" dc:"落地页链接动态参数"`
	SchemaUri                   string      `json:"schemaUri" dc:"调起链接、提升应用活跃营销目标的调起链接"`
	ProductImage                string      `json:"productImage" dc:"商品主图"`
	PackageId                   int         `json:"packageId" dc:"新版应用中心应用ID"`
	BidType                     int         `json:"bidType" dc:"出价类型 1：CPM，2：CPC，6：OCPC(使用 OCPC 代表 OCPX)，10：OCPM，20：eCPC"`
	ProductPrice                string      `json:"productPrice" dc:"商品价格 元"`
	PutStatus                   int         `json:"putStatus" dc:"投放状态 1：投放中；2：暂停 3：删除"`
	SmartCover                  int         `json:"smartCover" dc:"智能封面 是否开启智能抽帧"`
	DpaOuterIds                 string      `json:"dpaOuterIds" dc:"DPA外部商品id集合"`
	SeriesPayTemplateIdMulti    float64     `json:"seriesPayTemplateIdMulti" dc:"短剧付费模版列表"`
	DpaUnitSubType              int         `json:"dpaUnitSubType" dc:"商品广告类型：1-DPA，2-SDPA，3-动态商品卡"`
	ULink                       string      `json:"uLink" dc:"ios系统的ulink链接"`
	AppStore                    string      `json:"appStore" dc:"应用商店列表"`
	AppDownloadType             int         `json:"appDownloadType" dc:"应用下载类型 0：直接下载1：落地页下载（仅在提升应用安装计划下生效）"`
	DspVersion                  int         `json:"dspVersion" dc:"DSP版本"`
	PlayableId                  int64       `json:"playableId" dc:"可试玩 ID 可选字段，开启试玩时存在，否则不存在"`
	EpisodeId                   int64       `json:"episodeId" dc:"剧集 ID"`
	PlayableSwitch              int         `json:"playableSwitch" dc:"可试玩开关"`
	ProductId                   string      `json:"productId" dc:"产品 ID"`
	SchemaId                    string      `json:"schemaId" dc:"微信小程序外部调起链接（部分场景有效）"`
	UnitSource                  int         `json:"unitSource" dc:"广告组来源 0:常规（非托管）、1:托管"`
	SeriesPayTemplateId         int64       `json:"seriesPayTemplateId" dc:"付费模板（短剧推广时支持）"`
	DayBudget                   int64       `json:"dayBudget" dc:"日预算 单位：厘"`
	OcpxActionType              int         `json:"ocpxActionType" dc:"优化目标（枚举值见文档）"`
	UseAppMarket                int         `json:"useAppMarket" dc:"是否使用应用市场 0：未设置 1：优先从系统应用商店下载"`
	TargetExplore               int         `json:"targetExplore" dc:"是否开启搜索人群探索"`
	ComponentId                 int64       `json:"componentId" dc:"组件 ID"`
	DpaDynamicParams            int         `json:"dpaDynamicParams" dc:"DPA 动态参数 开关"`
	CreateTime                  *gtime.Time `json:"createTime" dc:"创建时间"`
	PlayButton                  string      `json:"playButton" dc:"试玩按钮文字内容"`
	UrlType                     int         `json:"urlType" dc:"URL 类型（特定计划下返回）"`
	DpaCategories               string      `json:"dpaCategories" dc:"DPA 类别"`
	ProductName                 string      `json:"productName" dc:"产品名称"`
	SeriesPayMode               int         `json:"seriesPayMode" dc:"付费模式（短剧推广时返回）"`
	ShowMode                    int         `json:"showMode" dc:"展示模式 0：未知，1：轮播，2：优选"`
	UnitName                    string      `json:"unitName" dc:"广告组名称"`
	ExtendSearch                int         `json:"extendSearch" dc:"智能扩词开启状态"`
	QuickSearch                 int         `json:"quickSearch" dc:"是否开启快投"`
	SiteId                      int64       `json:"siteId" dc:"建站ID / 安卓下载中间页ID"`
	SeriesCardInfo              string      `json:"seriesCardInfo" dc:"剧集卡片信息"`
	Status                      int         `json:"status" dc:"广告组状态"`
	ConsultId                   int64       `json:"consultId" dc:"咨询组件使用情况"`
	RoiRatio                    float64     `json:"roiRatio" dc:"付费 ROI 系数"`
	LiveComponentType           int         `json:"liveComponentType" dc:"直播组件类型"`
	SearchPopulationRetargeting int         `json:"searchPopulationRetargeting" dc:"是否开启人群追投"`
	EnhanceConversionType       int         `json:"enhanceConversionType" dc:"增强目标"`
	UnitType                    int         `json:"unitType" dc:"创意制作方式"`
	OuterId                     string      `json:"outerId" dc:"外部 ID"`
	StudyStatus                 int         `json:"studyStatus" dc:"学习状态"`
	SeriesCardType              int         `json:"seriesCardType" dc:"剧集卡片类型"`
	CustomMiniAppData           string      `json:"customMiniAppData" dc:"自定义小程序数据"`
	UpdateTime                  *gtime.Time `json:"updateTime" dc:"更新时间"`
	ImMessageMount              int         `json:"imMessageMount" dc:"IM 消息挂载"`
	LibraryId                   int64       `json:"libraryId" dc:"素材库 ID"`
	DeepConversionType          int         `json:"deepConversionType" dc:"深度转化类型"`
	DeepConversionBid           int         `json:"deepConversionBid" dc:"深度转化出价"`
	KwaiBookId                  int64       `json:"kwaiBookId" dc:"快手书籍 ID"`
	DpaDynamicParamsForDp       string      `json:"dpaDynamicParamsForDp" dc:"DP 动态参数"`
	PageAuditStatus             string      `json:"pageAuditStatus" dc:"页面审核状态"`
	JingleBellId                int64       `json:"jingleBellId" dc:"铃铛 ID"`
	LiveUserId                  int64       `json:"liveUserId" dc:"直播用户 ID"`
	PlayableUrl                 string      `json:"playableUrl" dc:"可试玩 URL"`
	PlayableFileName            string      `json:"playableFileName" dc:"可试玩文件名"`
	WebUriType                  int         `json:"webUriType" dc:"Web URI 类型"`
	ReviewDetail                string      `json:"reviewDetail" dc:"审核详情"`
	EndTime                     *gtime.Time `json:"endTime" dc:"结束时间"`
	CompensateStatus            int         `json:"compensateStatus" dc:"补偿状态"`
	SceneId                     string      `json:"sceneId" dc:"场景 ID"`
	BeginTime                   *gtime.Time `json:"beginTime" dc:"开始时间"`
	UnitMaterialType            int         `json:"unitMaterialType" dc:"单元素材类型"`
	ConvertId                   int64       `json:"convertId" dc:"转化 ID"`
	Url                         string      `json:"url" dc:"链接"`
	SeriesId                    int64       `json:"seriesId" dc:"系列 ID"`
	ScheduleTime                string      `json:"scheduleTime" dc:"日程时间"`
	OuterLoopNative             int         `json:"outerLoopNative" dc:"外循环原生"`
	CpaBid                      int         `json:"cpaBid" dc:"CPA 出价"`
	PlayableOrientation         int         `json:"playableOrientation" dc:"可试玩方向"`
	TemplateId                  int         `json:"templateId" dc:"模板 ID"`
	Bid                         int         `json:"bid" dc:"出价"`
	AdvCardOption               int         `json:"advCardOption" dc:"广告卡片选项"`
	CreatedAt                   *gtime.Time `json:"createdAt" dc:"创建时间"`
}

// KsAdvertiserUnitSearchReq 分页请求参数
type KsAdvertiserUnitSearchReq struct {
	comModel.PageReq
	UnitId                   string `p:"unitId" dc:"单元 ID"`                                                                                                                                      //单元 ID
	AdvertiserId             string `p:"advertiserId" v:"advertiserId@integer#广告id需为整数" dc:"广告id"`                                                                                               //广告id
	CampaignId               string `p:"campaignId" v:"campaignId@integer#广告计划 ID需为整数" dc:"广告计划 ID"`                                                                                             //广告计划 ID
	LinkIntegrationType      string `p:"linkIntegrationType" v:"linkIntegrationType@integer#链接整合类型需为整数" dc:"链接整合类型"`                                                                             //链接整合类型
	AssetMining              string `p:"assetMining" v:"assetMining@integer#资产挖掘需为整数" dc:"资产挖掘"`                                                                                                 //资产挖掘
	SiteType                 string `p:"siteType" v:"siteType@integer#预约广告 1:IOS 预约 缺省为不传或传 0需为整数" dc:"预约广告 1:IOS 预约 缺省为不传或传 0"`                                                                 //预约广告 1:IOS 预约 缺省为不传或传 0
	AdType                   string `p:"adType" v:"adType@integer#广告计划类型 0:信息流，1:搜索需为整数" dc:"广告计划类型 0:信息流，1:搜索"`                                                                                 //广告计划类型 0:信息流，1:搜索
	DpaDynamicParamsForUri   string `p:"dpaDynamicParamsForUri" dc:"落地页链接动态参数"`                                                                                                                  //落地页链接动态参数
	SchemaUri                string `p:"schemaUri" dc:"调起链接、提升应用活跃营销目标的调起链接"`                                                                                                                    //调起链接、提升应用活跃营销目标的调起链接
	ProductImage             string `p:"productImage" dc:"商品主图"`                                                                                                                                 //商品主图
	PackageId                string `p:"packageId" v:"packageId@integer#新版应用中心应用ID需为整数" dc:"新版应用中心应用ID"`                                                                                         //新版应用中心应用ID
	BidType                  string `p:"bidType" v:"bidType@integer#出价类型 1：CPM，2：CPC，6：OCPC(使用 OCPC 代表 OCPX)，10：OCPM，20：eCPC需为整数" dc:"出价类型 1：CPM，2：CPC，6：OCPC(使用 OCPC 代表 OCPX)，10：OCPM，20：eCPC"` //出价类型 1：CPM，2：CPC，6：OCPC(使用 OCPC 代表 OCPX)，10：OCPM，20：eCPC
	ProductPrice             string `p:"productPrice" dc:"商品价格 元"`                                                                                                                               //商品价格 元
	PutStatus                string `p:"putStatus" v:"putStatus@integer#投放状态 1：投放中；2：暂停 3：删除需为整数" dc:"投放状态 1：投放中；2：暂停 3：删除"`                                                                     //投放状态 1：投放中；2：暂停 3：删除
	SmartCover               string `p:"smartCover" v:"smartCover@integer#智能封面 是否开启智能抽帧需为整数" dc:"智能封面 是否开启智能抽帧"`                                                                                 //智能封面 是否开启智能抽帧
	DpaOuterIds              string `p:"dpaOuterIds" dc:"DPA外部商品id集合"`                                                                                                                           //DPA外部商品id集合
	SeriesPayTemplateIdMulti string `p:"seriesPayTemplateIdMulti" v:"seriesPayTemplateIdMulti@float#短剧付费模版列表需为浮点数" dc:"短剧付费模版列表"`                                                                //短剧付费模版列表
	DpaUnitSubType           string `p:"dpaUnitSubType" v:"dpaUnitSubType@integer#商品广告类型：1-DPA，2-SDPA，3-动态商品卡需为整数" dc:"商品广告类型：1-DPA，2-SDPA，3-动态商品卡"`                                             //商品广告类型：1-DPA，2-SDPA，3-动态商品卡
	ULink                    string `p:"uLink" dc:"ios系统的ulink链接"`                                                                                                                               //ios系统的ulink链接
	AppStore                 string `p:"appStore" dc:"应用商店列表"`                                                                                                                                   //应用商店列表
	AppDownloadType          string `p:"appDownloadType" v:"appDownloadType@integer#应用下载类型 0：直接下载1：落地页下载（仅在提升应用安装计划下生效）需为整数" dc:"应用下载类型 0：直接下载1：落地页下载（仅在提升应用安装计划下生效）"`                           //应用下载类型 0：直接下载1：落地页下载（仅在提升应用安装计划下生效）
	DspVersion               string `p:"dspVersion" v:"dspVersion@integer#DSP版本需为整数" dc:"DSP版本"`                                                                                                 //DSP版本
	PlayableId               string `p:"playableId" v:"playableId@integer#可试玩 ID 可选字段，开启试玩时存在，否则不存在需为整数" dc:"可试玩 ID 可选字段，开启试玩时存在，否则不存在"`                                                         //可试玩 ID 可选字段，开启试玩时存在，否则不存在
	EpisodeId                string `p:"episodeId" v:"episodeId@integer#剧集 ID需为整数" dc:"剧集 ID"`                                                                                                   //剧集 ID
	PlayableSwitch           string `p:"playableSwitch" v:"playableSwitch@integer#可试玩开关需为整数" dc:"可试玩开关"`                                                                                         //可试玩开关
	ProductId                string `p:"productId" dc:"产品 ID"`                                                                                                                                   //产品 ID
	SchemaId                 string `p:"schemaId" dc:"微信小程序外部调起链接（部分场景有效）"`                                                                                                                      //微信小程序外部调起链接（部分场景有效）
	UnitSource               string `p:"unitSource" v:"unitSource@integer#广告组来源 0:常规（非托管）、1:托管需为整数" dc:"广告组来源 0:常规（非托管）、1:托管"`                                                                   //广告组来源 0:常规（非托管）、1:托管
	SeriesPayTemplateId      string `p:"seriesPayTemplateId" v:"seriesPayTemplateId@integer#付费模板（短剧推广时支持）需为整数" dc:"付费模板（短剧推广时支持）"`                                                               //付费模板（短剧推广时支持）
	DayBudget                string `p:"dayBudget" v:"dayBudget@integer#日预算 单位：厘需为整数" dc:"日预算 单位：厘"`                                                                                             //日预算 单位：厘
	OcpxActionType           string `p:"ocpxActionType" v:"ocpxActionType@integer#优化目标（枚举值见文档）需为整数" dc:"优化目标（枚举值见文档）"`                                                                           //优化目标（枚举值见文档）
	UseAppMarket             string `p:"useAppMarket" v:"useAppMarket@integer#是否使用应用市场 0：未设置 1：优先从系统应用商店下载需为整数" dc:"是否使用应用市场 0：未设置 1：优先从系统应用商店下载"`                                               //是否使用应用市场 0：未设置 1：优先从系统应用商店下载
	TargetExplore            string `p:"targetExplore" v:"targetExplore@integer#是否开启搜索人群探索需为整数" dc:"是否开启搜索人群探索"`                                                                                 //是否开启搜索人群探索
	ComponentId              string `p:"componentId" v:"componentId@integer#组件 ID需为整数" dc:"组件 ID"`                                                                                               //组件 ID
	DpaDynamicParams         string `p:"dpaDynamicParams" v:"dpaDynamicParams@integer#DPA 动态参数 开关需为整数" dc:"DPA 动态参数 开关"`                                                                         //DPA 动态参数 开关
	CreateTime               string `p:"createTime" v:"createTime@datetime#创建时间需为YYYY-MM-DD hh:mm:ss格式" dc:"创建时间"`                                                                               //创建时间
	PlayButton               string `p:"playButton" dc:"试玩按钮文字内容"`                                                                                                                               //试玩按钮文字内容
	UrlType                  string `p:"urlType" v:"urlType@integer#URL 类型（特定计划下返回）需为整数" dc:"URL 类型（特定计划下返回）"`                                                                                   //URL 类型（特定计划下返回）

	DpaCategories               string `p:"dpaCategories" dc:"DPA 类别"`                                                                      //DPA 类别
	ProductName                 string `p:"productName" dc:"产品名称"`                                                                          //产品名称
	SeriesPayMode               string `p:"seriesPayMode" v:"seriesPayMode@integer#付费模式（短剧推广时返回）需为整数" dc:"付费模式（短剧推广时返回）"`                   //付费模式（短剧推广时返回）
	ShowMode                    string `p:"showMode" v:"showMode@integer#展示模式 0：未知，1：轮播，2：优选需为整数" dc:"展示模式 0：未知，1：轮播，2：优选"`                 //展示模式 0：未知，1：轮播，2：优选
	UnitName                    string `p:"unitName" dc:"广告组名称"`                                                                            //广告组名称
	ExtendSearch                string `p:"extendSearch" v:"extendSearch@integer#智能扩词开启状态需为整数" dc:"智能扩词开启状态"`                               //智能扩词开启状态
	QuickSearch                 string `p:"quickSearch" v:"quickSearch@integer#是否开启快投需为整数" dc:"是否开启快投"`                                     //是否开启快投
	SiteId                      string `p:"siteId" v:"siteId@integer#建站ID / 安卓下载中间页ID需为整数" dc:"建站ID / 安卓下载中间页ID"`                           //建站ID / 安卓下载中间页ID
	SeriesCardInfo              string `p:"seriesCardInfo" dc:"剧集卡片信息"`                                                                     //剧集卡片信息
	Status                      string `p:"status" v:"status@integer#广告组状态需为整数" dc:"广告组状态"`                                                 //广告组状态
	ConsultId                   string `p:"consultId" v:"consultId@integer#咨询组件使用情况需为整数" dc:"咨询组件使用情况"`                                     //咨询组件使用情况
	RoiRatio                    string `p:"roiRatio" v:"roiRatio@float#付费 ROI 系数需为浮点数" dc:"付费 ROI 系数"`                                      //付费 ROI 系数
	LiveComponentType           string `p:"liveComponentType" v:"liveComponentType@integer#直播组件类型需为整数" dc:"直播组件类型"`                         //直播组件类型
	SearchPopulationRetargeting string `p:"searchPopulationRetargeting" v:"searchPopulationRetargeting@integer#是否开启人群追投需为整数" dc:"是否开启人群追投"` //是否开启人群追投
	EnhanceConversionType       string `p:"enhanceConversionType" v:"enhanceConversionType@integer#增强目标需为整数" dc:"增强目标"`                     //增强目标
	UnitType                    string `p:"unitType" v:"unitType@integer#创意制作方式需为整数" dc:"创意制作方式"`                                           //创意制作方式
	OuterId                     string `p:"outerId" dc:"外部 ID"`                                                                             //外部 ID
	StudyStatus                 string `p:"studyStatus" v:"studyStatus@integer#学习状态需为整数" dc:"学习状态"`                                         //学习状态
	SeriesCardType              string `p:"seriesCardType" v:"seriesCardType@integer#剧集卡片类型需为整数" dc:"剧集卡片类型"`                               //剧集卡片类型
	CustomMiniAppData           string `p:"customMiniAppData" dc:"自定义小程序数据"`                                                                //自定义小程序数据
	UpdateTime                  string `p:"updateTime" v:"updateTime@datetime#更新时间需为YYYY-MM-DD hh:mm:ss格式" dc:"更新时间"`                       //更新时间
	ImMessageMount              string `p:"imMessageMount" v:"imMessageMount@integer#IM 消息挂载需为整数" dc:"IM 消息挂载"`                             //IM 消息挂载
	LibraryId                   string `p:"libraryId" v:"libraryId@integer#素材库 ID需为整数" dc:"素材库 ID"`                                         //素材库 ID
	DeepConversionType          string `p:"deepConversionType" v:"deepConversionType@integer#深度转化类型需为整数" dc:"深度转化类型"`                       //深度转化类型
	DeepConversionBid           string `p:"deepConversionBid" v:"deepConversionBid@integer#深度转化出价需为整数" dc:"深度转化出价"`                         //深度转化出价
	KwaiBookId                  string `p:"kwaiBookId" v:"kwaiBookId@integer#快手书籍 ID需为整数" dc:"快手书籍 ID"`                                     //快手书籍 ID
	DpaDynamicParamsForDp       string `p:"dpaDynamicParamsForDp" dc:"DP 动态参数"`                                                             //DP 动态参数
	PageAuditStatus             string `p:"pageAuditStatus" dc:"页面审核状态"`                                                                    //页面审核状态
	JingleBellId                string `p:"jingleBellId" v:"jingleBellId@integer#铃铛 ID需为整数" dc:"铃铛 ID"`                                     //铃铛 ID
	LiveUserId                  string `p:"liveUserId" v:"liveUserId@integer#直播用户 ID需为整数" dc:"直播用户 ID"`                                     //直播用户 ID
	PlayableUrl                 string `p:"playableUrl" dc:"可试玩 URL"`                                                                       //可试玩 URL
	PlayableFileName            string `p:"playableFileName" dc:"可试玩文件名"`                                                                   //可试玩文件名
	WebUriType                  string `p:"webUriType" v:"webUriType@integer#Web URI 类型需为整数" dc:"Web URI 类型"`                               //Web URI 类型
	ReviewDetail                string `p:"reviewDetail" dc:"审核详情"`                                                                         //审核详情
	EndTime                     string `p:"endTime" v:"endTime@datetime#结束时间需为YYYY-MM-DD hh:mm:ss格式" dc:"结束时间"`                             //结束时间
	CompensateStatus            string `p:"compensateStatus" v:"compensateStatus@integer#补偿状态需为整数" dc:"补偿状态"`                               //补偿状态
	SceneId                     string `p:"sceneId" dc:"场景 ID"`                                                                             //场景 ID
	BeginTime                   string `p:"beginTime" v:"beginTime@datetime#开始时间需为YYYY-MM-DD hh:mm:ss格式" dc:"开始时间"`                         //开始时间
	UnitMaterialType            string `p:"unitMaterialType" v:"unitMaterialType@integer#单元素材类型需为整数" dc:"单元素材类型"`                           //单元素材类型
	ConvertId                   string `p:"convertId" v:"convertId@integer#转化 ID需为整数" dc:"转化 ID"`                                           //转化 ID
	Url                         string `p:"url" dc:"链接"`                                                                                    //链接
	SeriesId                    string `p:"seriesId" v:"seriesId@integer#系列 ID需为整数" dc:"系列 ID"`                                             //系列 ID
	ScheduleTime                string `p:"scheduleTime" dc:"日程时间"`                                                                         //日程时间
	OuterLoopNative             string `p:"outerLoopNative" v:"outerLoopNative@integer#外循环原生需为整数" dc:"外循环原生"`                               //外循环原生
	CpaBid                      string `p:"cpaBid" v:"cpaBid@integer#CPA 出价需为整数" dc:"CPA 出价"`                                               //CPA 出价
	PlayableOrientation         string `p:"playableOrientation" v:"playableOrientation@integer#可试玩方向需为整数" dc:"可试玩方向"`                       //可试玩方向
	TemplateId                  string `p:"templateId" v:"templateId@integer#模板 ID需为整数" dc:"模板 ID"`                                         //模板 ID
	Bid                         string `p:"bid" v:"bid@integer#出价需为整数" dc:"出价"`                                                             //出价
	AdvCardOption               string `p:"advCardOption" v:"advCardOption@integer#广告卡片选项需为整数" dc:"广告卡片选项"`                                 //广告卡片选项
	CreatedAt                   string `p:"createdAt" v:"createdAt@datetime#创建时间需为YYYY-MM-DD hh:mm:ss格式" dc:"创建时间"`                         //创建时间
}

// KsAdvertiserUnitSearchRes 列表返回结果
type KsAdvertiserUnitSearchRes struct {
	comModel.ListRes
	List []*KsAdvertiserUnitListRes `json:"list"`
}

// KsAdvertiserUnitAddReq 添加操作请求参数
type KsAdvertiserUnitAddReq struct {
	UnitId                      int64       `p:"unitId" v:"required#主键ID不能为空" dc:"单元 ID"`
	AdvertiserId                int         `p:"advertiserId" v:"required#广告id不能为空" dc:"广告id"`
	CampaignId                  int64       `p:"campaignId"  dc:"广告计划 ID"`
	LinkIntegrationType         int         `p:"linkIntegrationType"  dc:"链接整合类型"`
	AssetMining                 int         `p:"assetMining"  dc:"资产挖掘"`
	SiteType                    int         `p:"siteType"  dc:"预约广告 1:IOS 预约 缺省为不传或传 0"`
	AdType                      int         `p:"adType"  dc:"广告计划类型 0:信息流，1:搜索"`
	DpaDynamicParamsForUri      string      `p:"dpaDynamicParamsForUri"  dc:"落地页链接动态参数"`
	SchemaUri                   string      `p:"schemaUri"  dc:"调起链接、提升应用活跃营销目标的调起链接"`
	ProductImage                string      `p:"productImage"  dc:"商品主图"`
	PackageId                   int         `p:"packageId"  dc:"新版应用中心应用ID"`
	BidType                     int         `p:"bidType"  dc:"出价类型 1：CPM，2：CPC，6：OCPC(使用 OCPC 代表 OCPX)，10：OCPM，20：eCPC"`
	ProductPrice                string      `p:"productPrice"  dc:"商品价格 元"`
	PutStatus                   int         `p:"putStatus" v:"required#投放状态 1：投放中；2：暂停 3：删除不能为空" dc:"投放状态 1：投放中；2：暂停 3：删除"`
	SmartCover                  int         `p:"smartCover"  dc:"智能封面 是否开启智能抽帧"`
	DpaOuterIds                 string      `p:"dpaOuterIds"  dc:"DPA外部商品id集合"`
	SeriesPayTemplateIdMulti    string      `p:"seriesPayTemplateIdMulti"  dc:"短剧付费模版列表"`
	DpaUnitSubType              int         `p:"dpaUnitSubType"  dc:"商品广告类型：1-DPA，2-SDPA，3-动态商品卡"`
	ULink                       string      `p:"uLink"  dc:"ios系统的ulink链接"`
	AppStore                    string      `p:"appStore"  dc:"应用商店列表"`
	AppDownloadType             int         `p:"appDownloadType"  dc:"应用下载类型 0：直接下载1：落地页下载（仅在提升应用安装计划下生效）"`
	DspVersion                  int         `p:"dspVersion"  dc:"DSP版本"`
	PlayableId                  int64       `p:"playableId"  dc:"可试玩 ID 可选字段，开启试玩时存在，否则不存在"`
	EpisodeId                   int64       `p:"episodeId"  dc:"剧集 ID"`
	PlayableSwitch              int         `p:"playableSwitch"  dc:"可试玩开关"`
	ProductId                   string      `p:"productId"  dc:"产品 ID"`
	SchemaId                    string      `p:"schemaId"  dc:"微信小程序外部调起链接（部分场景有效）"`
	UnitSource                  int         `p:"unitSource"  dc:"广告组来源 0:常规（非托管）、1:托管"`
	SeriesPayTemplateId         int64       `p:"seriesPayTemplateId"  dc:"付费模板（短剧推广时支持）"`
	DayBudget                   int64       `p:"dayBudget"  dc:"日预算 单位：厘"`
	OcpxActionType              int         `p:"ocpxActionType"  dc:"优化目标（枚举值见文档）"`
	UseAppMarket                int         `p:"useAppMarket"  dc:"是否使用应用市场 0：未设置 1：优先从系统应用商店下载"`
	TargetExplore               int         `p:"targetExplore"  dc:"是否开启搜索人群探索"`
	ComponentId                 int64       `p:"componentId"  dc:"组件 ID"`
	DpaDynamicParams            int         `p:"dpaDynamicParams"  dc:"DPA 动态参数 开关"`
	CreateTime                  *gtime.Time `p:"createTime"  dc:"创建时间"`
	PlayButton                  string      `p:"playButton"  dc:"试玩按钮文字内容"`
	UrlType                     int         `p:"urlType"  dc:"URL 类型（特定计划下返回）"`
	DpaCategories               string      `p:"dpaCategories"  dc:"DPA 类别"`
	ProductName                 string      `p:"productName" v:"required#产品名称不能为空" dc:"产品名称"`
	SeriesPayMode               int         `p:"seriesPayMode"  dc:"付费模式（短剧推广时返回）"`
	ShowMode                    int         `p:"showMode"  dc:"展示模式 0：未知，1：轮播，2：优选"`
	UnitName                    string      `p:"unitName" v:"required#广告组名称不能为空" dc:"广告组名称"`
	ExtendSearch                int         `p:"extendSearch"  dc:"智能扩词开启状态"`
	QuickSearch                 int         `p:"quickSearch"  dc:"是否开启快投"`
	SiteId                      int64       `p:"siteId"  dc:"建站ID / 安卓下载中间页ID"`
	SeriesCardInfo              string      `p:"seriesCardInfo"  dc:"剧集卡片信息"`
	Status                      int         `p:"status" v:"required#广告组状态不能为空" dc:"广告组状态"`
	ConsultId                   int64       `p:"consultId"  dc:"咨询组件使用情况"`
	RoiRatio                    float64     `p:"roiRatio"  dc:"付费 ROI 系数"`
	LiveComponentType           int         `p:"liveComponentType"  dc:"直播组件类型"`
	SearchPopulationRetargeting int         `p:"searchPopulationRetargeting"  dc:"是否开启人群追投"`
	EnhanceConversionType       int         `p:"enhanceConversionType"  dc:"增强目标"`
	UnitType                    int         `p:"unitType"  dc:"创意制作方式"`
	OuterId                     string      `p:"outerId"  dc:"外部 ID"`
	StudyStatus                 int         `p:"studyStatus" v:"required#学习状态不能为空" dc:"学习状态"`
	SeriesCardType              int         `p:"seriesCardType"  dc:"剧集卡片类型"`
	CustomMiniAppData           string      `p:"customMiniAppData"  dc:"自定义小程序数据"`
	UpdateTime                  *gtime.Time `p:"updateTime"  dc:"更新时间"`
	ImMessageMount              int         `p:"imMessageMount"  dc:"IM 消息挂载"`
	LibraryId                   int64       `p:"libraryId"  dc:"素材库 ID"`
	DeepConversionType          int         `p:"deepConversionType"  dc:"深度转化类型"`
	DeepConversionBid           int         `p:"deepConversionBid"  dc:"深度转化出价"`
	KwaiBookId                  int64       `p:"kwaiBookId"  dc:"快手书籍 ID"`
	DpaDynamicParamsForDp       string      `p:"dpaDynamicParamsForDp"  dc:"DP 动态参数"`
	PageAuditStatus             string      `p:"pageAuditStatus" v:"required#页面审核状态不能为空" dc:"页面审核状态"`
	JingleBellId                int64       `p:"jingleBellId"  dc:"铃铛 ID"`
	LiveUserId                  int64       `p:"liveUserId"  dc:"直播用户 ID"`
	PlayableUrl                 string      `p:"playableUrl"  dc:"可试玩 URL"`
	PlayableFileName            string      `p:"playableFileName" v:"required#可试玩文件名不能为空" dc:"可试玩文件名"`
	WebUriType                  int         `p:"webUriType"  dc:"Web URI 类型"`
	ReviewDetail                string      `p:"reviewDetail"  dc:"审核详情"`
	EndTime                     *gtime.Time `p:"endTime"  dc:"结束时间"`
	CompensateStatus            int         `p:"compensateStatus" v:"required#补偿状态不能为空" dc:"补偿状态"`
	SceneId                     string      `p:"sceneId"  dc:"场景 ID"`
	BeginTime                   *gtime.Time `p:"beginTime"  dc:"开始时间"`
	UnitMaterialType            int         `p:"unitMaterialType"  dc:"单元素材类型"`
	ConvertId                   int64       `p:"convertId"  dc:"转化 ID"`
	Url                         string      `p:"url"  dc:"链接"`
	SeriesId                    int64       `p:"seriesId"  dc:"系列 ID"`
	ScheduleTime                string      `p:"scheduleTime"  dc:"日程时间"`
	OuterLoopNative             int         `p:"outerLoopNative"  dc:"外循环原生"`
	CpaBid                      int         `p:"cpaBid"  dc:"CPA 出价"`
	PlayableOrientation         int         `p:"playableOrientation"  dc:"可试玩方向"`
	TemplateId                  int         `p:"templateId"  dc:"模板 ID"`
	Bid                         int         `p:"bid"  dc:"出价"`
	AdvCardOption               int         `p:"advCardOption"  dc:"广告卡片选项"`
}

// KsAdvertiserUnitEditReq 修改操作请求参数
type KsAdvertiserUnitEditReq struct {
	UnitId                      int64       `p:"unitId" v:"required#主键ID不能为空" dc:"单元 ID"`
	AdvertiserId                int         `p:"advertiserId" v:"required#广告id不能为空" dc:"广告id"`
	CampaignId                  int64       `p:"campaignId"  dc:"广告计划 ID"`
	LinkIntegrationType         int         `p:"linkIntegrationType"  dc:"链接整合类型"`
	AssetMining                 int         `p:"assetMining"  dc:"资产挖掘"`
	SiteType                    int         `p:"siteType"  dc:"预约广告 1:IOS 预约 缺省为不传或传 0"`
	AdType                      int         `p:"adType"  dc:"广告计划类型 0:信息流，1:搜索"`
	DpaDynamicParamsForUri      string      `p:"dpaDynamicParamsForUri"  dc:"落地页链接动态参数"`
	SchemaUri                   string      `p:"schemaUri"  dc:"调起链接、提升应用活跃营销目标的调起链接"`
	ProductImage                string      `p:"productImage"  dc:"商品主图"`
	PackageId                   int         `p:"packageId"  dc:"新版应用中心应用ID"`
	BidType                     int         `p:"bidType"  dc:"出价类型 1：CPM，2：CPC，6：OCPC(使用 OCPC 代表 OCPX)，10：OCPM，20：eCPC"`
	ProductPrice                string      `p:"productPrice"  dc:"商品价格 元"`
	PutStatus                   int         `p:"putStatus" v:"required#投放状态 1：投放中；2：暂停 3：删除不能为空" dc:"投放状态 1：投放中；2：暂停 3：删除"`
	SmartCover                  int         `p:"smartCover"  dc:"智能封面 是否开启智能抽帧"`
	DpaOuterIds                 string      `p:"dpaOuterIds"  dc:"DPA外部商品id集合"`
	SeriesPayTemplateIdMulti    float64     `p:"seriesPayTemplateIdMulti"  dc:"短剧付费模版列表"`
	DpaUnitSubType              int         `p:"dpaUnitSubType"  dc:"商品广告类型：1-DPA，2-SDPA，3-动态商品卡"`
	ULink                       string      `p:"uLink"  dc:"ios系统的ulink链接"`
	AppStore                    string      `p:"appStore"  dc:"应用商店列表"`
	AppDownloadType             int         `p:"appDownloadType"  dc:"应用下载类型 0：直接下载1：落地页下载（仅在提升应用安装计划下生效）"`
	DspVersion                  int         `p:"dspVersion"  dc:"DSP版本"`
	PlayableId                  int64       `p:"playableId"  dc:"可试玩 ID 可选字段，开启试玩时存在，否则不存在"`
	EpisodeId                   int64       `p:"episodeId"  dc:"剧集 ID"`
	PlayableSwitch              int         `p:"playableSwitch"  dc:"可试玩开关"`
	ProductId                   string      `p:"productId"  dc:"产品 ID"`
	SchemaId                    string      `p:"schemaId"  dc:"微信小程序外部调起链接（部分场景有效）"`
	UnitSource                  int         `p:"unitSource"  dc:"广告组来源 0:常规（非托管）、1:托管"`
	SeriesPayTemplateId         int64       `p:"seriesPayTemplateId"  dc:"付费模板（短剧推广时支持）"`
	DayBudget                   int64       `p:"dayBudget"  dc:"日预算 单位：厘"`
	OcpxActionType              int         `p:"ocpxActionType"  dc:"优化目标（枚举值见文档）"`
	UseAppMarket                int         `p:"useAppMarket"  dc:"是否使用应用市场 0：未设置 1：优先从系统应用商店下载"`
	TargetExplore               int         `p:"targetExplore"  dc:"是否开启搜索人群探索"`
	ComponentId                 int64       `p:"componentId"  dc:"组件 ID"`
	DpaDynamicParams            int         `p:"dpaDynamicParams"  dc:"DPA 动态参数 开关"`
	CreateTime                  *gtime.Time `p:"createTime"  dc:"创建时间"`
	PlayButton                  string      `p:"playButton"  dc:"试玩按钮文字内容"`
	UrlType                     int         `p:"urlType"  dc:"URL 类型（特定计划下返回）"`
	DpaCategories               string      `p:"dpaCategories"  dc:"DPA 类别"`
	ProductName                 string      `p:"productName" v:"required#产品名称不能为空" dc:"产品名称"`
	SeriesPayMode               int         `p:"seriesPayMode"  dc:"付费模式（短剧推广时返回）"`
	ShowMode                    int         `p:"showMode"  dc:"展示模式 0：未知，1：轮播，2：优选"`
	UnitName                    string      `p:"unitName" v:"required#广告组名称不能为空" dc:"广告组名称"`
	ExtendSearch                int         `p:"extendSearch"  dc:"智能扩词开启状态"`
	QuickSearch                 int         `p:"quickSearch"  dc:"是否开启快投"`
	SiteId                      int64       `p:"siteId"  dc:"建站ID / 安卓下载中间页ID"`
	SeriesCardInfo              string      `p:"seriesCardInfo"  dc:"剧集卡片信息"`
	Status                      int         `p:"status" v:"required#广告组状态不能为空" dc:"广告组状态"`
	ConsultId                   int64       `p:"consultId"  dc:"咨询组件使用情况"`
	RoiRatio                    float64     `p:"roiRatio"  dc:"付费 ROI 系数"`
	LiveComponentType           int         `p:"liveComponentType"  dc:"直播组件类型"`
	SearchPopulationRetargeting int         `p:"searchPopulationRetargeting"  dc:"是否开启人群追投"`
	EnhanceConversionType       int         `p:"enhanceConversionType"  dc:"增强目标"`
	UnitType                    int         `p:"unitType"  dc:"创意制作方式"`
	OuterId                     string      `p:"outerId"  dc:"外部 ID"`
	StudyStatus                 int         `p:"studyStatus" v:"required#学习状态不能为空" dc:"学习状态"`
	SeriesCardType              int         `p:"seriesCardType"  dc:"剧集卡片类型"`
	CustomMiniAppData           string      `p:"customMiniAppData"  dc:"自定义小程序数据"`
	UpdateTime                  *gtime.Time `p:"updateTime"  dc:"更新时间"`
	ImMessageMount              int         `p:"imMessageMount"  dc:"IM 消息挂载"`
	LibraryId                   int64       `p:"libraryId"  dc:"素材库 ID"`
	DeepConversionType          int         `p:"deepConversionType"  dc:"深度转化类型"`
	DeepConversionBid           int         `p:"deepConversionBid"  dc:"深度转化出价"`
	KwaiBookId                  int64       `p:"kwaiBookId"  dc:"快手书籍 ID"`
	DpaDynamicParamsForDp       string      `p:"dpaDynamicParamsForDp"  dc:"DP 动态参数"`
	PageAuditStatus             string      `p:"pageAuditStatus" v:"required#页面审核状态不能为空" dc:"页面审核状态"`
	JingleBellId                int64       `p:"jingleBellId"  dc:"铃铛 ID"`
	LiveUserId                  int64       `p:"liveUserId"  dc:"直播用户 ID"`
	PlayableUrl                 string      `p:"playableUrl"  dc:"可试玩 URL"`
	PlayableFileName            string      `p:"playableFileName" v:"required#可试玩文件名不能为空" dc:"可试玩文件名"`
	WebUriType                  int         `p:"webUriType"  dc:"Web URI 类型"`
	ReviewDetail                string      `p:"reviewDetail"  dc:"审核详情"`
	EndTime                     *gtime.Time `p:"endTime"  dc:"结束时间"`
	CompensateStatus            int         `p:"compensateStatus" v:"required#补偿状态不能为空" dc:"补偿状态"`
	SceneId                     string      `p:"sceneId"  dc:"场景 ID"`
	BeginTime                   *gtime.Time `p:"beginTime"  dc:"开始时间"`
	UnitMaterialType            int         `p:"unitMaterialType"  dc:"单元素材类型"`
	ConvertId                   int64       `p:"convertId"  dc:"转化 ID"`
	Url                         string      `p:"url"  dc:"链接"`
	SeriesId                    int64       `p:"seriesId"  dc:"系列 ID"`
	ScheduleTime                string      `p:"scheduleTime"  dc:"日程时间"`
	OuterLoopNative             int         `p:"outerLoopNative"  dc:"外循环原生"`
	CpaBid                      int         `p:"cpaBid"  dc:"CPA 出价"`
	PlayableOrientation         int         `p:"playableOrientation"  dc:"可试玩方向"`
	TemplateId                  int         `p:"templateId"  dc:"模板 ID"`
	Bid                         int         `p:"bid"  dc:"出价"`
	AdvCardOption               int         `p:"advCardOption"  dc:"广告卡片选项"`
}

type KsAdvertiserUnitUpdateReq struct {
	UnitId       int64    `p:"unitId" dc:"广告组ID"`
	UnitName     string   `p:"unitName" dc:"广告组名称"`
	PutStatus    *int64   `p:"putStatus" dc:"投放状态"`
	DayBudget    *int64   `p:"dayBudget"  dc:"单日预算 单位：厘"`
	RoiRatio     *float64 `p:"roiRatio"  dc:"付费ROI系数"`
	BeginTime    string   `p:"beginTime"  dc:"开始时间"`
	EndTime      string   `p:"endTime"  dc:"结束时间"`
	ScheduleTime string   `p:"scheduleTime"  dc:"投放时间段"`
}
