// ==========================================================================
// GFast自动生成controller操作代码。
// 生成日期：2025-08-19 10:36:58
// 生成路径: internal/app/ad/controller/ks_advertiser_common_asset_title.go
// 生成人：cq
// desc:快手通用资产-标题库
// company:云南奇讯科技有限公司
// ==========================================================================

package controller

import (
	"context"

	"github.com/tiger1103/gfast/v3/api/v1/ad"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	systemController "github.com/tiger1103/gfast/v3/internal/app/system/controller"
)

type ksAdvertiserCommonAssetTitleController struct {
	systemController.BaseController
}

var KsAdvertiserCommonAssetTitle = new(ksAdvertiserCommonAssetTitleController)

// List 列表
func (c *ksAdvertiserCommonAssetTitleController) List(ctx context.Context, req *ad.KsAdvertiserCommonAssetTitleSearchReq) (res *ad.KsAdvertiserCommonAssetTitleSearchRes, err error) {
	res = new(ad.KsAdvertiserCommonAssetTitleSearchRes)
	res.KsAdvertiserCommonAssetTitleSearchRes, err = service.KsAdvertiserCommonAssetTitle().List(ctx, &req.KsAdvertiserCommonAssetTitleSearchReq)
	return
}

// Get 获取快手通用资产-标题库
func (c *ksAdvertiserCommonAssetTitleController) Get(ctx context.Context, req *ad.KsAdvertiserCommonAssetTitleGetReq) (res *ad.KsAdvertiserCommonAssetTitleGetRes, err error) {
	res = new(ad.KsAdvertiserCommonAssetTitleGetRes)
	res.KsAdvertiserCommonAssetTitleInfoRes, err = service.KsAdvertiserCommonAssetTitle().GetById(ctx, req.Id)
	return
}

// Add 添加快手通用资产-标题库
func (c *ksAdvertiserCommonAssetTitleController) Add(ctx context.Context, req *ad.KsAdvertiserCommonAssetTitleAddReq) (res *ad.KsAdvertiserCommonAssetTitleAddRes, err error) {
	err = service.KsAdvertiserCommonAssetTitle().Add(ctx, req.KsAdvertiserCommonAssetTitleBatchAddReq)
	return
}

// Edit 修改快手通用资产-标题库
func (c *ksAdvertiserCommonAssetTitleController) Edit(ctx context.Context, req *ad.KsAdvertiserCommonAssetTitleEditReq) (res *ad.KsAdvertiserCommonAssetTitleEditRes, err error) {
	err = service.KsAdvertiserCommonAssetTitle().Edit(ctx, req.KsAdvertiserCommonAssetTitleEditReq)
	return
}

// BatchEditCategory 修改快手通用资产-标题库批量修改分类
func (c *ksAdvertiserCommonAssetTitleController) BatchEditCategory(ctx context.Context, req *ad.KsAdvertiserCommonAssetTitleBatchEditReq) (res *ad.KsAdvertiserCommonAssetTitleBatchEditRes, err error) {
	err = service.KsAdvertiserCommonAssetTitle().BatchEditCategory(ctx, req.KsAdvertiserCommonAssetTitleBatchEditReq)
	return
}

// Delete 删除快手通用资产-标题库
func (c *ksAdvertiserCommonAssetTitleController) Delete(ctx context.Context, req *ad.KsAdvertiserCommonAssetTitleDeleteReq) (res *ad.KsAdvertiserCommonAssetTitleDeleteRes, err error) {
	err = service.KsAdvertiserCommonAssetTitle().Delete(ctx, req.Ids)
	return
}
