// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2025-08-22 11:52:17
// 生成路径: internal/app/ad/logic/ks_advertiser_strategy_creative.go
// 生成人：cq
// desc:快手策略组-广告创意
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	"github.com/tiger1103/gfast/v3/library/liberr"
)

func init() {
	service.RegisterKsAdvertiserStrategyCreative(New())
}

func New() service.IKsAdvertiserStrategyCreative {
	return &sKsAdvertiserStrategyCreative{}
}

type sKsAdvertiserStrategyCreative struct{}

func (s *sKsAdvertiserStrategyCreative) List(ctx context.Context, req *model.KsAdvertiserStrategyCreativeSearchReq) (listRes *model.KsAdvertiserStrategyCreativeSearchRes, err error) {
	listRes = new(model.KsAdvertiserStrategyCreativeSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.KsAdvertiserStrategyCreative.Ctx(ctx).WithAll()
		if req.Id != "" {
			m = m.Where(dao.KsAdvertiserStrategyCreative.Columns().Id+" = ?", req.Id)
		}
		if req.StrategyId != "" {
			m = m.Where(dao.KsAdvertiserStrategyCreative.Columns().StrategyId+" = ?", req.StrategyId)
		}
		if req.TaskId != "" {
			m = m.Where(dao.KsAdvertiserStrategyCreative.Columns().TaskId+" = ?", req.TaskId)
		}
		if req.CreateMode != "" {
			m = m.Where(dao.KsAdvertiserStrategyCreative.Columns().CreateMode+" = ?", gconv.Int(req.CreateMode))
		}
		if req.ActionBarText != "" {
			m = m.Where(dao.KsAdvertiserStrategyCreative.Columns().ActionBarText+" = ?", req.ActionBarText)
		}
		if req.OuterLoopNative != "" {
			m = m.Where(dao.KsAdvertiserStrategyCreative.Columns().OuterLoopNative+" = ?", gconv.Int(req.OuterLoopNative))
		}
		if req.KolUserId != "" {
			m = m.Where(dao.KsAdvertiserStrategyCreative.Columns().KolUserId+" = ?", gconv.Int64(req.KolUserId))
		}
		if req.KolUserType != "" {
			m = m.Where(dao.KsAdvertiserStrategyCreative.Columns().KolUserType+" = ?", gconv.Int(req.KolUserType))
		}
		if req.CreativeCategory != "" {
			m = m.Where(dao.KsAdvertiserStrategyCreative.Columns().CreativeCategory+" = ?", gconv.Int(req.CreativeCategory))
		}
		if req.CreativeName != "" {
			m = m.Where(dao.KsAdvertiserStrategyCreative.Columns().CreativeName+" like ?", "%"+req.CreativeName+"%")
		}
		if req.TrackUrlSwitch != "" {
			m = m.Where(dao.KsAdvertiserStrategyCreative.Columns().TrackUrlSwitch+" = ?", gconv.Int(req.TrackUrlSwitch))
		}
		if len(req.DateRange) != 0 {
			m = m.Where(dao.KsAdvertiserStrategyCreative.Columns().CreatedAt+" >=? AND "+dao.KsAdvertiserStrategyCreative.Columns().CreatedAt+" <=?", req.DateRange[0], req.DateRange[1])
		}
		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "id asc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		var res []*model.KsAdvertiserStrategyCreativeListRes
		err = m.Page(req.PageNum, req.PageSize).Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		listRes.List = make([]*model.KsAdvertiserStrategyCreativeListRes, len(res))
		for k, v := range res {
			listRes.List[k] = &model.KsAdvertiserStrategyCreativeListRes{
				Id:               v.Id,
				StrategyId:       v.StrategyId,
				TaskId:           v.TaskId,
				CreateMode:       v.CreateMode,
				ActionBarText:    v.ActionBarText,
				NewExposeTag:     v.NewExposeTag,
				OuterLoopNative:  v.OuterLoopNative,
				KolUserId:        v.KolUserId,
				KolUserType:      v.KolUserType,
				CreativeCategory: v.CreativeCategory,
				CreativeTag:      v.CreativeTag,
				CreativeName:     v.CreativeName,
				TrackUrlSwitch:   v.TrackUrlSwitch,
				CreatedAt:        v.CreatedAt,
			}
		}
	})
	return
}

func (s *sKsAdvertiserStrategyCreative) GetById(ctx context.Context, id uint64) (res *model.KsAdvertiserStrategyCreativeInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.KsAdvertiserStrategyCreative.Ctx(ctx).WithAll().Where(dao.KsAdvertiserStrategyCreative.Columns().Id, id).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sKsAdvertiserStrategyCreative) GetInfoById(ctx context.Context, strategyId string, taskId string) (res *model.KsAdvertiserStrategyCreativeInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.KsAdvertiserStrategyCreative.Ctx(ctx).WithAll()
		if strategyId != "" {
			m = m.Where(dao.KsAdvertiserStrategyCreative.Columns().StrategyId+" = ?", strategyId)
		}
		if taskId != "" {
			m = m.Where(dao.KsAdvertiserStrategyCreative.Columns().TaskId+" = ?", taskId)
		}
		err = m.Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sKsAdvertiserStrategyCreative) Add(ctx context.Context, req *model.KsAdvertiserStrategyCreativeAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.KsAdvertiserStrategyCreative.Ctx(ctx).Insert(do.KsAdvertiserStrategyCreative{
			StrategyId:       req.StrategyId,
			TaskId:           req.TaskId,
			CreateMode:       req.CreateMode,
			ActionBarText:    req.ActionBarText,
			NewExposeTag:     req.NewExposeTag,
			OuterLoopNative:  req.OuterLoopNative,
			KolUserId:        req.KolUserId,
			KolUserType:      req.KolUserType,
			CreativeCategory: req.CreativeCategory,
			CreativeTag:      req.CreativeTag,
			CreativeName:     req.CreativeName,
			TrackUrlSwitch:   req.TrackUrlSwitch,
		})
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sKsAdvertiserStrategyCreative) Edit(ctx context.Context, req *model.KsAdvertiserStrategyCreativeEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.KsAdvertiserStrategyCreative.Ctx(ctx).
			Where(dao.KsAdvertiserStrategyCreative.Columns().StrategyId, req.StrategyId).
			Update(do.KsAdvertiserStrategyCreative{
				TaskId:           req.TaskId,
				CreateMode:       req.CreateMode,
				ActionBarText:    req.ActionBarText,
				NewExposeTag:     req.NewExposeTag,
				OuterLoopNative:  req.OuterLoopNative,
				KolUserId:        req.KolUserId,
				KolUserType:      req.KolUserType,
				CreativeCategory: req.CreativeCategory,
				CreativeTag:      req.CreativeTag,
				CreativeName:     req.CreativeName,
				TrackUrlSwitch:   req.TrackUrlSwitch,
			})
		liberr.ErrIsNil(ctx, err, "修改失败")
	})
	return
}

func (s *sKsAdvertiserStrategyCreative) Delete(ctx context.Context, ids []uint64) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.KsAdvertiserStrategyCreative.Ctx(ctx).Delete(dao.KsAdvertiserStrategyCreative.Columns().Id+" in (?)", ids)
		liberr.ErrIsNil(ctx, err, "删除失败")
	})
	return
}
