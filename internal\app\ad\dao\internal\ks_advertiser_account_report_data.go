// ==========================================================================
// GFast自动生成dao internal操作代码。
// 生成日期：2025-08-12 10:44:32
// 生成路径: internal/app/ad/dao/internal/ks_advertiser_account_report_data.go
// 生成人：cq
// desc:快手账户报表数据
// company:云南奇讯科技有限公司
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// KsAdvertiserAccountReportDataDao is the manager for logic model data accessing and custom defined data operations functions management.
type KsAdvertiserAccountReportDataDao struct {
	table   string                               // Table is the underlying table name of the DAO.
	group   string                               // Group is the database configuration group name of current DAO.
	columns KsAdvertiserAccountReportDataColumns // Columns is the short type for Columns, which contains all the column names of Table for convenient usage.
}

// KsAdvertiserAccountReportDataColumns defines and stores column names for table ks_advertiser_account_report_data.
type KsAdvertiserAccountReportDataColumns struct {
	AdvertiserId                                     string // 广告主ID
	StatDate                                         string // 数据日期，格式：YYYY-MM-DD
	PrivateMessageSentCost                           string // 私信消息转化成本
	PrivateMessageSentRatio                          string // 私信消息转化率
	PrivateMessageSentCnt                            string // 私信消息数
	LeadsSubmitCost                                  string // 直接私信留资成本
	LeadsSubmitCntRatio                              string // 直接私信留资率
	LeadsSubmitCnt                                   string // 直接私信留资数
	PlayedNum                                        string // 播放数
	PlayedEnd                                        string // 播放完成
	PlayedFiveSeconds                                string // 播放5s
	PlayedThreeSeconds                               string // 有效播放数
	Play3SRatio                                      string // 3s 播放率
	Play5SRatio                                      string // 5s播放率
	PlayEndRatio                                     string // 完播率
	AdPhotoPlayed10S                                 string // 10s播放数
	AdPhotoPlayed2S                                  string // 2s播放数
	AdPhotoPlayed75Percent                           string // 75%进度播放数
	AdPhotoPlayed75PercentRatio                      string // 75%进度播放率
	AdPhotoPlayed10SRatio                            string // 10s播放率
	AdPhotoPlayed2SRatio                             string // 2s播放率
	MinigameIaaPurchaseAmountWeekByConversionRoi     string // 激活后七日广告变现ROI
	MinigameIaaPurchaseAmountThreeDayByConversionRoi string // 激活后三日广告变现ROI
	MinigameIaaPurchaseAmountFirstDayRoi             string // 当日广告变现ROI
	MinigameIaaPurchaseAmountWeekByConversion        string // 激活后七日广告LTV
	MinigameIaaPurchaseAmountThreeDayByConversion    string // 激活后三日广告LTV
	MinigameIaaPurchaseAmountFirstDay                string // 当日广告LTV
	MinigameIaaPurchaseRoi                           string // IAA广告变现ROI
	MinigameIaaPurchaseAmount                        string // IAA广告变现LTV
	MinigameIaaPurchaseAmount30DayByConversionRoi    string // 激活后30日广告变现ROI
	MinigameIaaPurchaseAmount15DayByConversionRoi    string // 激活后15日广告变现ROI
	MinigameIaaPurchaseAmount30DayByConversion       string // 激活后30日广告LTV
	MinigameIaaPurchaseAmount15DayByConversion       string // 激活后15日广告LTV
	MmuEffectiveCustomerAcquisition7DCnt             string // MMU识别产生的有效获客数（计费）
	MmuEffectiveCustomerAcquisitionCnt               string // MMU识别产生的有效获客数（回传）
	EffectiveCustomerAcquisition7DRatio              string // 有效获客率（计费）
	EffectiveCustomerAcquisition7DCost               string // 有效获客成本（计费）
	EffectiveCustomerAcquisition7DCnt                string // 有效获客数（计费）
	EventPay30DayOverallRoi                          string // 激活后30日整体ROI
	EventPay15DayOverallRoi                          string // 激活后15日整体ROI
	EventPayPurchaseAmount15DayByConversion          string // 激活后15日付费金额
	EventPayPurchaseAmount30DayByConversion          string // 激活后30日付费金额
	EventPayFirstDay                                 string // 应用下载数据-首日付费次数
	EventPayPurchaseAmountFirstDay                   string // 应用下载数据-首日付费金额
	EventPayFirstDayRoi                              string // 应用下载数据-首日 ROI
	EventPay                                         string // 应用下载数据-付费次数
	EventPayPurchaseAmount                           string // 应用下载数据-付费金额
	EventPayRoi                                      string // 应用下载数据-ROI
	EventPayPurchaseAmountOneDay                     string // 激活后24h付费金额(回传时间)
	EventPayPurchaseAmountOneDayByConversion         string // 激活后24h付费金额(激活时间)
	EventPayPurchaseAmountOneDayByConversionRoi      string // 激活后24小时付费ROI
	EventPayPurchaseAmountOneDayRoi                  string // 激活后24h-ROI(回传时间)
	EventPayWeightedPurchaseAmount                   string // 加权付费金额
	EventPayWeightedPurchaseAmountFirstDay           string // 首日加权付费金额
	Charge                                           string // 花费（元）
	Show                                             string // 封面曝光数
	Aclick                                           string // 素材曝光数
	Bclick                                           string // 行为数
	AdShow                                           string // 广告曝光
	Share                                            string // 分享数
	Comment                                          string // 评论数
	Like                                             string // 点赞数
	Follow                                           string // 新增粉丝数
	CancelLike                                       string // 取消点赞数
	CancelFollow                                     string // 取消关注数
	Report                                           string // 举报数
	Block                                            string // 拉黑数
	Negative                                         string // 减少此类作品数
	Activation                                       string // 应用下载数据-激活数
	DownloadStarted                                  string // 应用下载数据-安卓下载开始数
	DownloadCompleted                                string // 应用下载数据-安卓下载完成数
	DownloadInstalled                                string // 安卓安装完成数
	ClickConversionRatio                             string // 点击激活成本
	ConversionCost                                   string // 激活单价
	DownloadCompletedCost                            string // 安卓下载完成单价（元）
	DownloadCompletedRatio                           string // 安卓下载完成率
	DownloadConversionRatio                          string // 下载完成激活率
	DownloadStartedCost                              string // 安卓下载开始单价（元）
	DownloadStartedRatio                             string // 安卓下载开始率
	EventRegister                                    string // 应用下载数据-注册数
	EventRegisterCost                                string // 应用下载数据-注册成本
	EventRegisterRatio                               string // 应用下载数据-注册率
	EventJinJianApp                                  string // 应用下载数据-完件数
	EventJinJianAppCost                              string // 应用下载数据-完件成本
	EventJinJianLandingPage                          string // 落地页数据-落地页完件数
	EventJinJianLandingPageCost                      string // 落地页数据-落地页完件成本
	Jinjian0DCnt                                     string // T0完件数
	Jinjian3DCnt                                     string // T3完件数
	Jinjian0DCntCost                                 string // T0完件成本
	Jinjian3DCntCost                                 string // T3完件成本
	EventCreditGrantApp                              string // 应用下载数据-授信数
	EventCreditGrantAppCost                          string // 应用下载数据-授信成本
	EventCreditGrantAppRatio                         string // 应用下载数据-授信率
	EventCreditGrantLandingPage                      string // 落地页授信数
	EventCreditGrantLandingPageCost                  string // 落地页数据-落地页授信成本
	EventCreditGrantLandingRatio                     string // 落地页数据-落地页授信率
	EventCreditGrantFirstDayApp                      string // app首日授信数
	EventCreditGrantFirstDayAppCost                  string // 首日授信成本
	EventCreditGrantFirstDayAppRatio                 string // 首日授信率
	EventCreditGrantFirstDayLandingPage              string // 落地页首日授信数
	EventCreditGrantFirstDayLandingPageCost          string // 落地页首日授信成本
	EventCreditGrantFirstDayLandingPageRatio         string // 落地页首日授信率
	CreditGrant0DCnt                                 string // T0授信数
	CreditGrant3DCnt                                 string // T3授信数
	CreditGrant0DCntCost                             string // T0授信成本
	CreditGrant3DCntCost                             string // T3授信成本
	CreditGrant0DCntRatio                            string // T0完件授信率
	CreditGrant3DCntRatio                            string // T3完件授信通过率
	EventOrderSubmit                                 string // 提交订单数
	EventOrderPaid                                   string // 应用下载数据-付款成功数
	EventOrderPaidPurchaseAmount                     string // 应用下载数据-付款成功金额
	EventOrderPaidCost                               string // 应用下载数据-单次付款成本
	EventOrderPaidRoi                                string // 订单支付率
	OrderSubmitAmt                                   string // 订单提交金额
	FormCount                                        string // 落地页数据-线索提交个数
	FormCost                                         string // 落地页数据-单个线索成本
	FormActionRatio                                  string // 落地页数据-表单提交点击率
	Submit                                           string // 提交按钮点击数（历史字段）
	EventValidClues                                  string // 落地页数据-有效线索数
	EventValidCluesCost                              string // 落地页数据-有效线索成本
	EventConsultationValidRetained                   string // 留咨咨询数
	EventConsultationValidRetainedCost               string // 留咨咨询成本
	EventConsultationValidRetainedRatio              string // 留咨咨询率
	EventConversionClickCost                         string // 有效咨询成本
	EventConversionClickRatio                        string // 有效咨询率
	EventPreComponentConsultationValidRetained       string // 附加咨询组件留资咨询数
	EventAdWatch10Times                              string // 10次广告广告观看数
	EventAdWatch10TimesCost                          string // 10次广告观看成本
	EventAdWatch10TimesRatio                         string // 10次广告观看转化率
	EventAdWatch20Times                              string // 20次广告广告观看数
	EventAdWatch20TimesCost                          string // 20次广告观看成本
	EventAdWatch20TimesRatio                         string // 20次广告观看转化率
	EventAdWatch5Times                               string // 5次广告广告观看数
	EventAdWatch5TimesCost                           string // 5次广告观看成本
	EventAdWatch5TimesRatio                          string // 5次广告观看转化率
	EventWatchAppAd                                  string // 广告观看
	EventAdWatchTimes                                string // 广告观看次数
	EventAdWatchTimesRatio                           string // 广告观看次数转化率
	EventAdWatchTimesCost                            string // 广告观看次数成本
	EventMakingCalls                                 string // 电话拨打数
	EventMakingCallsCost                             string // 电话拨打成本
	EventMakingCallsRatio                            string // 电话拨打率
	EventGetThrough                                  string // 智能电话-确认接通数
	EventGetThroughCost                              string // 智能电话-确认接通成本
	EventGetThroughRatio                             string // 智能电话-确认接通率
	EventPhoneGetThrough                             string // 电话建联数
	EventOutboundCall                                string // 电话拨打数
	EventOutboundCallCost                            string // 电话拨打成本
	EventOutboundCallRatio                           string // 电话拨打率
	EventWechatQrCodeLinkClick                       string // 微信小程序深度加粉数
	EventAddWechat                                   string // 微信复制数
	EventAddWechatCost                               string // 微信复制成本
	EventAddWechatRatio                              string // 微信复制率
	EventWechatConnected                             string // 微信加粉数
	EventAudition                                    string // 首次试听到课数
	EventButtonClick                                 string // 按钮点击数
	EventButtonClickCost                             string // 按钮点击成本
	EventButtonClickRatio                            string // 按钮点击率
	EventMultiConversion                             string // 落地页多转化次数
	EventMultiConversionRatio                        string // 落地页多转化率
	EventMultiConversionCost                         string // 落地页多转化成本
	EventAddShoppingCart                             string // 添加购物车数
	EventAddShoppingCartCost                         string // 添加购物车成本
	EventIntentionConfirmed                          string // 意向确认数
	EventOrderSuccessed                              string // 有效线索成交数
	EventPhoneCardActivate                           string // 电话卡激活数
	EventMeasurementHouse                            string // 量房数
	EventAppInvoked                                  string // 唤醒应用数
	EventAppInvokedCost                              string // 唤醒应用成本
	EventAppInvokedRatio                             string // 唤醒应用率
	EventNextDayStayCost                             string // 应用下载数据-次留成本（仅支持分日查询）
	EventNextDayStayRatio                            string // 应用下载数据-次留率（仅支持分日查询）
	EventNextDayStay                                 string // 应用下载数据-次留数（仅支持分日查询）
	PhotoClick                                       string // 封面点击数
	PhotoClickRatio                                  string // 封面点击率
	PhotoClickCost                                   string // 平均点击单价（元）
	ActionRatio                                      string // 行为率
	ActionNewRatio                                   string // 行为率 新
	ActionCost                                       string // 平均行为单价（元）
	Impression1KCost                                 string // 平均千次曝光花费（元）
	Click1KCost                                      string // 平均千次素材曝光花费(元)
	ApproxPayCost                                    string // 淘系近似购买成本
	ApproxPayCount                                   string // 近似购买数
	ApproxPayRatio                                   string // 淘系近似购买率
	LiveEventGoodsView                               string // 直播间商品点击数
	LivePlayed3S                                     string // 直播观看数
	AdProductCnt                                     string // 商品成交数
	EventGoodsView                                   string // 商品访问数
	EventGoodsViewCost                               string // 商品访问成本
	MerchantRecoFans                                 string // 涨粉量
	MerchantRecoFansCost                             string // 涨粉成本
	EventOrderAmountRoi                              string // 小店推广roi
	EventNewUserPay                                  string // 新增付费人数
	EventNewUserPayCost                              string // 新增付费人数成本
	EventNewUserPayRatio                             string // 新增付费人数率
	EventNewUserJinjianApp                           string // 新增完件人数
	EventNewUserJinjianAppCost                       string // 新增完件人数成本
	EventNewUserJinjianAppRoi                        string // 新增完件人数率
	EventNewUserCreditGrantApp                       string // 新增授信人数
	EventNewUserCreditGrantAppCost                   string // 新增授信人数成本
	EventNewUserCreditGrantAppRoi                    string // 新增授信人数率
	EventNewUserJinjianPage                          string // 字段描述，需要修改
	EventNewUserJinjianPageCost                      string // 字段描述，需要修改
	EventNewUserJinjianPageRoi                       string // 字段描述，需要修改
	EventNewUserCreditGrantPage                      string // 字段描述，需要修改
	EventNewUserCreditGrantPageCost                  string // 字段描述，需要修改
	EventNewUserCreditGrantPageRoi                   string // 字段描述，需要修改
	EventAppointForm                                 string // 预约表单数
	EventAppointFormCost                             string // 预约表单点击成本
	EventAppointFormRatio                            string // 预约表单点击率
	EventAppointJumpClick                            string // 预约跳转点击数
	EventAppointJumpClickCost                        string // 预约跳转点击成本
	EventAppointJumpClickRatio                       string // 预约跳转点击率
	UnionEventPayPurchaseAmount7D                    string // 联盟广告收入
	UnionEventPayPurchaseAmount7DRoi                 string // 联盟变现ROI
	EventDspGiftForm                                 string // 附加组件表单提交
	EventCreditCardRecheck                           string // 信用卡核卡数
	EventCreditCardRecheckFirstDay                   string // 信用卡首日核卡数
	KeyAction                                        string // 关键行为数
	KeyActionCost                                    string // 关键行为成本
	KeyActionRatio                                   string // 关键行为率
	KeyInappAction0DCnt                              string // T0全量授信数
	KeyInappAction3DCnt                              string // T3全量授信数
	KeyInappAction0DCntCost                          string // T0全量授信成本
	KeyInappAction3DCntCost                          string // T3全量授信成本
	KeyInappAction0DCntRatio                         string // T0全量授信通过率
	KeyInappAction3DCntRatio                         string // T3全量授信通过率
	DrawCreditLine0DCnt                              string // T0用信数
	DrawCreditLine0DCntCost                          string // T0用信成本
	DrawCreditLine0DCntRatio                         string // T0授信用信率
	EventNoIntention                                 string // 用户无意向数
	AdScene                                          string // 广告场景
	AdScene2                                         string // 广告场景2
	PlacementType                                    string // 投放类型
	CreatedAt                                        string // 创建时间
	UpdatedAt                                        string // 更新时间
}

var ksAdvertiserAccountReportDataColumns = KsAdvertiserAccountReportDataColumns{
	AdvertiserId:                "advertiser_id",
	StatDate:                    "stat_date",
	PrivateMessageSentCost:      "private_message_sent_cost",
	PrivateMessageSentRatio:     "private_message_sent_ratio",
	PrivateMessageSentCnt:       "private_message_sent_cnt",
	LeadsSubmitCost:             "leads_submit_cost",
	LeadsSubmitCntRatio:         "leads_submit_cnt_ratio",
	LeadsSubmitCnt:              "leads_submit_cnt",
	PlayedNum:                   "played_num",
	PlayedEnd:                   "played_end",
	PlayedFiveSeconds:           "played_five_seconds",
	PlayedThreeSeconds:          "played_three_seconds",
	Play3SRatio:                 "play_3s_ratio",
	Play5SRatio:                 "play_5s_ratio",
	PlayEndRatio:                "play_end_ratio",
	AdPhotoPlayed10S:            "ad_photo_played_10s",
	AdPhotoPlayed2S:             "ad_photo_played_2s",
	AdPhotoPlayed75Percent:      "ad_photo_played_75percent",
	AdPhotoPlayed75PercentRatio: "ad_photo_played_75_percent_ratio",
	AdPhotoPlayed10SRatio:       "ad_photo_played_10s_ratio",
	AdPhotoPlayed2SRatio:        "ad_photo_played_2s_ratio",
	MinigameIaaPurchaseAmountWeekByConversionRoi:     "minigame_iaa_purchase_amount_week_by_conversion_roi",
	MinigameIaaPurchaseAmountThreeDayByConversionRoi: "minigame_iaa_purchase_amount_three_day_by_conversion_roi",
	MinigameIaaPurchaseAmountFirstDayRoi:             "minigame_iaa_purchase_amount_first_day_roi",
	MinigameIaaPurchaseAmountWeekByConversion:        "minigame_iaa_purchase_amount_week_by_conversion",
	MinigameIaaPurchaseAmountThreeDayByConversion:    "minigame_iaa_purchase_amount_three_day_by_conversion",
	MinigameIaaPurchaseAmountFirstDay:                "minigame_iaa_purchase_amount_first_day",
	MinigameIaaPurchaseRoi:                           "minigame_iaa_purchase_roi",
	MinigameIaaPurchaseAmount:                        "minigame_iaa_purchase_amount",
	MinigameIaaPurchaseAmount30DayByConversionRoi:    "minigame_iaa_purchase_amount_30_day_by_conversion_roi",
	MinigameIaaPurchaseAmount15DayByConversionRoi:    "minigame_iaa_purchase_amount_15_day_by_conversion_roi",
	MinigameIaaPurchaseAmount30DayByConversion:       "minigame_iaa_purchase_amount_30_day_by_conversion",
	MinigameIaaPurchaseAmount15DayByConversion:       "minigame_iaa_purchase_amount_15_day_by_conversion",
	MmuEffectiveCustomerAcquisition7DCnt:             "mmu_effective_customer_acquisition_7d_cnt",
	MmuEffectiveCustomerAcquisitionCnt:               "mmu_effective_customer_acquisition_cnt",
	EffectiveCustomerAcquisition7DRatio:              "effective_customer_acquisition_7d_ratio",
	EffectiveCustomerAcquisition7DCost:               "effective_customer_acquisition_7d_cost",
	EffectiveCustomerAcquisition7DCnt:                "effective_customer_acquisition_7d_cnt",
	EventPay30DayOverallRoi:                          "event_pay_30_day_overall_roi",
	EventPay15DayOverallRoi:                          "event_pay_15_day_overall_roi",
	EventPayPurchaseAmount15DayByConversion:          "event_pay_purchase_amount_15_day_by_conversion",
	EventPayPurchaseAmount30DayByConversion:          "event_pay_purchase_amount_30_day_by_conversion",
	EventPayFirstDay:                                 "event_pay_first_day",
	EventPayPurchaseAmountFirstDay:                   "event_pay_purchase_amount_first_day",
	EventPayFirstDayRoi:                              "event_pay_first_day_roi",
	EventPay:                                         "event_pay",
	EventPayPurchaseAmount:                           "event_pay_purchase_amount",
	EventPayRoi:                                      "event_pay_roi",
	EventPayPurchaseAmountOneDay:                     "event_pay_purchase_amount_one_day",
	EventPayPurchaseAmountOneDayByConversion:         "event_pay_purchase_amount_one_day_by_conversion",
	EventPayPurchaseAmountOneDayByConversionRoi:      "event_pay_purchase_amount_one_day_by_conversion_roi",
	EventPayPurchaseAmountOneDayRoi:                  "event_pay_purchase_amount_one_day_roi",
	EventPayWeightedPurchaseAmount:                   "event_pay_weighted_purchase_amount",
	EventPayWeightedPurchaseAmountFirstDay:           "event_pay_weighted_purchase_amount_first_day",
	Charge:                                           "charge",
	Show:                                             "show",
	Aclick:                                           "aclick",
	Bclick:                                           "bclick",
	AdShow:                                           "ad_show",
	Share:                                            "share",
	Comment:                                          "comment",
	Like:                                             "like",
	Follow:                                           "follow",
	CancelLike:                                       "cancel_like",
	CancelFollow:                                     "cancel_follow",
	Report:                                           "report",
	Block:                                            "block",
	Negative:                                         "negative",
	Activation:                                       "activation",
	DownloadStarted:                                  "download_started",
	DownloadCompleted:                                "download_completed",
	DownloadInstalled:                                "download_installed",
	ClickConversionRatio:                             "click_conversion_ratio",
	ConversionCost:                                   "conversion_cost",
	DownloadCompletedCost:                            "download_completed_cost",
	DownloadCompletedRatio:                           "download_completed_ratio",
	DownloadConversionRatio:                          "download_conversion_ratio",
	DownloadStartedCost:                              "download_started_cost",
	DownloadStartedRatio:                             "download_started_ratio",
	EventRegister:                                    "event_register",
	EventRegisterCost:                                "event_register_cost",
	EventRegisterRatio:                               "event_register_ratio",
	EventJinJianApp:                                  "event_jin_jian_app",
	EventJinJianAppCost:                              "event_jin_jian_app_cost",
	EventJinJianLandingPage:                          "event_jin_jian_landing_page",
	EventJinJianLandingPageCost:                      "event_jin_jian_landing_page_cost",
	Jinjian0DCnt:                                     "jinjian_0d_cnt",
	Jinjian3DCnt:                                     "jinjian_3d_cnt",
	Jinjian0DCntCost:                                 "jinjian_0d_cnt_cost",
	Jinjian3DCntCost:                                 "jinjian_3d_cnt_cost",
	EventCreditGrantApp:                              "event_credit_grant_app",
	EventCreditGrantAppCost:                          "event_credit_grant_app_cost",
	EventCreditGrantAppRatio:                         "event_credit_grant_app_ratio",
	EventCreditGrantLandingPage:                      "event_credit_grant_landing_page",
	EventCreditGrantLandingPageCost:                  "event_credit_grant_landing_page_cost",
	EventCreditGrantLandingRatio:                     "event_credit_grant_landing_ratio",
	EventCreditGrantFirstDayApp:                      "event_credit_grant_first_day_app",
	EventCreditGrantFirstDayAppCost:                  "event_credit_grant_first_day_app_cost",
	EventCreditGrantFirstDayAppRatio:                 "event_credit_grant_first_day_app_ratio",
	EventCreditGrantFirstDayLandingPage:              "event_credit_grant_first_day_landing_page",
	EventCreditGrantFirstDayLandingPageCost:          "event_credit_grant_first_day_landing_page_cost",
	EventCreditGrantFirstDayLandingPageRatio:         "event_credit_grant_first_day_landing_page_ratio",
	CreditGrant0DCnt:                                 "credit_grant_0d_cnt",
	CreditGrant3DCnt:                                 "credit_grant_3d_cnt",
	CreditGrant0DCntCost:                             "credit_grant_0d_cnt_cost",
	CreditGrant3DCntCost:                             "credit_grant_3d_cnt_cost",
	CreditGrant0DCntRatio:                            "credit_grant_0d_cnt_ratio",
	CreditGrant3DCntRatio:                            "credit_grant_3d_cnt_ratio",
	EventOrderSubmit:                                 "event_order_submit",
	EventOrderPaid:                                   "event_order_paid",
	EventOrderPaidPurchaseAmount:                     "event_order_paid_purchase_amount",
	EventOrderPaidCost:                               "event_order_paid_cost",
	EventOrderPaidRoi:                                "event_order_paid_roi",
	OrderSubmitAmt:                                   "order_submit_amt",
	FormCount:                                        "form_count",
	FormCost:                                         "form_cost",
	FormActionRatio:                                  "form_action_ratio",
	Submit:                                           "submit",
	EventValidClues:                                  "event_valid_clues",
	EventValidCluesCost:                              "event_valid_clues_cost",
	EventConsultationValidRetained:                   "event_consultation_valid_retained",
	EventConsultationValidRetainedCost:               "event_consultation_valid_retained_cost",
	EventConsultationValidRetainedRatio:              "event_consultation_valid_retained_ratio",
	EventConversionClickCost:                         "event_conversion_click_cost",
	EventConversionClickRatio:                        "event_conversion_click_ratio",
	EventPreComponentConsultationValidRetained:       "event_pre_component_consultation_valid_retained",
	EventAdWatch10Times:                              "event_ad_watch_10_times",
	EventAdWatch10TimesCost:                          "event_ad_watch_10_times_cost",
	EventAdWatch10TimesRatio:                         "event_ad_watch_10_times_ratio",
	EventAdWatch20Times:                              "event_ad_watch_20_times",
	EventAdWatch20TimesCost:                          "event_ad_watch_20_times_cost",
	EventAdWatch20TimesRatio:                         "event_ad_watch_20_times_ratio",
	EventAdWatch5Times:                               "event_ad_watch_5_times",
	EventAdWatch5TimesCost:                           "event_ad_watch_5_times_cost",
	EventAdWatch5TimesRatio:                          "event_ad_watch_5_times_ratio",
	EventWatchAppAd:                                  "event_watch_app_ad",
	EventAdWatchTimes:                                "event_ad_watch_times",
	EventAdWatchTimesRatio:                           "event_ad_watch_times_ratio",
	EventAdWatchTimesCost:                            "event_ad_watch_times_cost",
	EventMakingCalls:                                 "event_making_calls",
	EventMakingCallsCost:                             "event_making_calls_cost",
	EventMakingCallsRatio:                            "event_making_calls_ratio",
	EventGetThrough:                                  "event_get_through",
	EventGetThroughCost:                              "event_get_through_cost",
	EventGetThroughRatio:                             "event_get_through_ratio",
	EventPhoneGetThrough:                             "event_phone_get_through",
	EventOutboundCall:                                "event_outbound_call",
	EventOutboundCallCost:                            "event_outbound_call_cost",
	EventOutboundCallRatio:                           "event_outbound_call_ratio",
	EventWechatQrCodeLinkClick:                       "event_wechat_qr_code_link_click",
	EventAddWechat:                                   "event_add_wechat",
	EventAddWechatCost:                               "event_add_wechat_cost",
	EventAddWechatRatio:                              "event_add_wechat_ratio",
	EventWechatConnected:                             "event_wechat_connected",
	EventAudition:                                    "event_audition",
	EventButtonClick:                                 "event_button_click",
	EventButtonClickCost:                             "event_button_click_cost",
	EventButtonClickRatio:                            "event_button_click_ratio",
	EventMultiConversion:                             "event_multi_conversion",
	EventMultiConversionRatio:                        "event_multi_conversion_ratio",
	EventMultiConversionCost:                         "event_multi_conversion_cost",
	EventAddShoppingCart:                             "event_add_shopping_cart",
	EventAddShoppingCartCost:                         "event_add_shopping_cart_cost",
	EventIntentionConfirmed:                          "event_intention_confirmed",
	EventOrderSuccessed:                              "event_order_successed",
	EventPhoneCardActivate:                           "event_phone_card_activate",
	EventMeasurementHouse:                            "event_measurement_house",
	EventAppInvoked:                                  "event_app_invoked",
	EventAppInvokedCost:                              "event_app_invoked_cost",
	EventAppInvokedRatio:                             "event_app_invoked_ratio",
	EventNextDayStayCost:                             "event_next_day_stay_cost",
	EventNextDayStayRatio:                            "event_next_day_stay_ratio",
	EventNextDayStay:                                 "event_next_day_stay",
	PhotoClick:                                       "photo_click",
	PhotoClickRatio:                                  "photo_click_ratio",
	PhotoClickCost:                                   "photo_click_cost",
	ActionRatio:                                      "action_ratio",
	ActionNewRatio:                                   "action_new_ratio",
	ActionCost:                                       "action_cost",
	Impression1KCost:                                 "impression_1k_cost",
	Click1KCost:                                      "click_1k_cost",
	ApproxPayCost:                                    "approx_pay_cost",
	ApproxPayCount:                                   "approx_pay_count",
	ApproxPayRatio:                                   "approx_pay_ratio",
	LiveEventGoodsView:                               "live_event_goods_view",
	LivePlayed3S:                                     "live_played_3s",
	AdProductCnt:                                     "ad_product_cnt",
	EventGoodsView:                                   "event_goods_view",
	EventGoodsViewCost:                               "event_goods_view_cost",
	MerchantRecoFans:                                 "merchant_reco_fans",
	MerchantRecoFansCost:                             "merchant_reco_fans_cost",
	EventOrderAmountRoi:                              "event_order_amount_roi",
	EventNewUserPay:                                  "event_new_user_pay",
	EventNewUserPayCost:                              "event_new_user_pay_cost",
	EventNewUserPayRatio:                             "event_new_user_pay_ratio",
	EventNewUserJinjianApp:                           "event_new_user_jinjian_app",
	EventNewUserJinjianAppCost:                       "event_new_user_jinjian_app_cost",
	EventNewUserJinjianAppRoi:                        "event_new_user_jinjian_app_roi",
	EventNewUserCreditGrantApp:                       "event_new_user_credit_grant_app",
	EventNewUserCreditGrantAppCost:                   "event_new_user_credit_grant_app_cost",
	EventNewUserCreditGrantAppRoi:                    "event_new_user_credit_grant_app_roi",
	EventNewUserJinjianPage:                          "event_new_user_jinjian_page",
	EventNewUserJinjianPageCost:                      "event_new_user_jinjian_page_cost",
	EventNewUserJinjianPageRoi:                       "event_new_user_jinjian_page_roi",
	EventNewUserCreditGrantPage:                      "event_new_user_credit_grant_page",
	EventNewUserCreditGrantPageCost:                  "event_new_user_credit_grant_page_cost",
	EventNewUserCreditGrantPageRoi:                   "event_new_user_credit_grant_page_roi",
	EventAppointForm:                                 "event_appoint_form",
	EventAppointFormCost:                             "event_appoint_form_cost",
	EventAppointFormRatio:                            "event_appoint_form_ratio",
	EventAppointJumpClick:                            "event_appoint_jump_click",
	EventAppointJumpClickCost:                        "event_appoint_jump_click_cost",
	EventAppointJumpClickRatio:                       "event_appoint_jump_click_ratio",
	UnionEventPayPurchaseAmount7D:                    "union_event_pay_purchase_amount_7d",
	UnionEventPayPurchaseAmount7DRoi:                 "union_event_pay_purchase_amount_7d_roi",
	EventDspGiftForm:                                 "event_dsp_gift_form",
	EventCreditCardRecheck:                           "event_credit_card_recheck",
	EventCreditCardRecheckFirstDay:                   "event_credit_card_recheck_first_day",
	KeyAction:                                        "key_action",
	KeyActionCost:                                    "key_action_cost",
	KeyActionRatio:                                   "key_action_ratio",
	KeyInappAction0DCnt:                              "key_inapp_action_0d_cnt",
	KeyInappAction3DCnt:                              "key_inapp_action_3d_cnt",
	KeyInappAction0DCntCost:                          "key_inapp_action_0d_cnt_cost",
	KeyInappAction3DCntCost:                          "key_inapp_action_3d_cnt_cost",
	KeyInappAction0DCntRatio:                         "key_inapp_action_0d_cnt_ratio",
	KeyInappAction3DCntRatio:                         "key_inapp_action_3d_cnt_ratio",
	DrawCreditLine0DCnt:                              "draw_credit_line_0d_cnt",
	DrawCreditLine0DCntCost:                          "draw_credit_line_0d_cnt_cost",
	DrawCreditLine0DCntRatio:                         "draw_credit_line_0d_cnt_ratio",
	EventNoIntention:                                 "event_no_intention",
	AdScene:                                          "ad_scene",
	AdScene2:                                         "ad_scene_2",
	PlacementType:                                    "placement_type",
	CreatedAt:                                        "created_at",
	UpdatedAt:                                        "updated_at",
}

// NewKsAdvertiserAccountReportDataDao creates and returns a new DAO object for table data access.
func NewKsAdvertiserAccountReportDataDao() *KsAdvertiserAccountReportDataDao {
	return &KsAdvertiserAccountReportDataDao{
		group:   "default",
		table:   "ks_advertiser_account_report_data",
		columns: ksAdvertiserAccountReportDataColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *KsAdvertiserAccountReportDataDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *KsAdvertiserAccountReportDataDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *KsAdvertiserAccountReportDataDao) Columns() KsAdvertiserAccountReportDataColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *KsAdvertiserAccountReportDataDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *KsAdvertiserAccountReportDataDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *KsAdvertiserAccountReportDataDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
