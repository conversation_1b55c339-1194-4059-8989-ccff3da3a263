// ==========================================================================
// GFast自动生成model操作代码。
// 生成日期：2025-08-22 11:52:02
// 生成路径: internal/app/ad/model/ks_advertiser_strategy_campaign.go
// 生成人：cq
// desc:快手策略组-广告计划
// company:云南奇讯科技有限公司
// ==========================================================================

package model

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
	comModel "github.com/tiger1103/gfast/v3/internal/app/common/model"
)

// KsAdvertiserStrategyCampaignInfoRes is the golang structure for table ks_advertiser_strategy_campaign.
type KsAdvertiserStrategyCampaignInfoRes struct {
	gmeta.Meta       `orm:"table:ks_advertiser_strategy_campaign"`
	Id               uint64      `orm:"id,primary" json:"id" dc:"主键ID"`                                                           // 主键ID
	StrategyId       string      `orm:"strategy_id" json:"strategyId" dc:"策略组ID"`                                                // 策略组ID
	TaskId           string      `orm:"task_id" json:"taskId" dc:"任务ID"`                                                          // 任务ID
	CampaignType     int         `orm:"campaign_type" json:"campaignType" dc:"营销类型 30：快手号-短剧推广"`                         // 营销类型 30：快手号-短剧推广
	AdType           int         `orm:"ad_type" json:"adType" dc:"广告计划类型 0:信息流（展示广告） 1:搜索（搜索广告）；不填默认信息流"` // 广告计划类型 0:信息流（展示广告） 1:搜索（搜索广告）；不填默认信息流
	AutoAdjust       int         `orm:"auto_adjust" json:"autoAdjust" dc:"自动调控开关 0：关闭，1：开启"`                              // 自动调控开关 0：关闭，1：开启
	AutoBuild        int         `orm:"auto_build" json:"autoBuild" dc:"自动基建 0：关闭，1：开启"`                                    // 自动基建 0：关闭，1：开启
	UnitNameRule     string      `orm:"unit_name_rule" json:"unitNameRule" dc:"单元名称规则"`                                       // 单元名称规则
	CreativeNameRule string      `orm:"creative_name_rule" json:"creativeNameRule" dc:"创意名称规则"`                               // 创意名称规则
	AutoManage       int         `orm:"auto_manage" json:"autoManage" dc:"智能投放开关 0：关闭，1：开启"`                              // 智能投放开关 0：关闭，1：开启
	BidType          int         `orm:"bid_type" json:"bidType" dc:"竞价策略 0：成本优先 1：最大转化"`                                // 竞价策略 0：成本优先 1：最大转化
	DayBudget        float64     `orm:"day_budget" json:"dayBudget" dc:"日预算 不限传0"`                                            // 日预算 不限传0
	CampaignName     string      `orm:"campaign_name" json:"campaignName" dc:"广告计划名称"`                                        // 广告计划名称
	AdUnitLimit      int         `orm:"ad_unit_limit" json:"adUnitLimit" dc:"广告计划内广告组上线"`                                 // 广告计划内广告组上线
	PutStatus        int         `orm:"put_status" json:"putStatus" dc:"广告计划默认状态 1-投放、2-暂停"`                            // 广告计划默认状态 1-投放、2-暂停
	CreatedAt        *gtime.Time `orm:"created_at" json:"createdAt" dc:"创建时间"`                                                  // 创建时间
	UpdatedAt        *gtime.Time `orm:"updated_at" json:"updatedAt" dc:"更新时间"`                                                  // 更新时间
	DeletedAt        *gtime.Time `orm:"deleted_at" json:"deletedAt" dc:"删除时间"`                                                  // 删除时间
}

type KsAdvertiserStrategyCampaignListRes struct {
	Id               uint64      `json:"id" dc:"主键ID"`
	StrategyId       string      `json:"strategyId" dc:"策略组ID"`
	TaskId           string      `json:"taskId" dc:"任务ID"`
	CampaignType     int         `json:"campaignType" dc:"营销类型 30：快手号-短剧推广"`
	AdType           int         `json:"adType" dc:"广告计划类型 0:信息流（展示广告） 1:搜索（搜索广告）；不填默认信息流"`
	AutoAdjust       int         `json:"autoAdjust" dc:"自动调控开关 0：关闭，1：开启"`
	AutoBuild        int         `json:"autoBuild" dc:"自动基建 0：关闭，1：开启"`
	UnitNameRule     string      `json:"unitNameRule" dc:"单元名称规则"`
	CreativeNameRule string      `json:"creativeNameRule" dc:"创意名称规则"`
	AutoManage       int         `json:"autoManage" dc:"智能投放开关 0：关闭，1：开启"`
	BidType          int         `json:"bidType" dc:"竞价策略 0：成本优先 1：最大转化"`
	DayBudget        float64     `json:"dayBudget" dc:"日预算 不限传0"`
	CampaignName     string      `json:"campaignName" dc:"广告计划名称"`
	AdUnitLimit      int         `json:"adUnitLimit" dc:"广告计划内广告组上线"`
	PutStatus        int         `json:"putStatus" dc:"广告计划默认状态 1-投放、2-暂停"`
	CreatedAt        *gtime.Time `json:"createdAt" dc:"创建时间"`
}

// KsAdvertiserStrategyCampaignSearchReq 分页请求参数
type KsAdvertiserStrategyCampaignSearchReq struct {
	comModel.PageReq
	Id               string `p:"id" dc:"主键ID"`                                                                                                                                                         //主键ID
	StrategyId       string `p:"strategyId" dc:"策略组ID"`                                                                                                                                               //策略组ID
	TaskId           string `p:"taskId" dc:"任务ID"`                                                                                                                                                     //任务ID
	CampaignType     string `p:"campaignType" v:"campaignType@integer#营销类型 30：快手号-短剧推广需为整数" dc:"营销类型 30：快手号-短剧推广"`                                                             //营销类型 30：快手号-短剧推广
	AdType           string `p:"adType" v:"adType@integer#广告计划类型 0:信息流（展示广告） 1:搜索（搜索广告）；不填默认信息流需为整数" dc:"广告计划类型 0:信息流（展示广告） 1:搜索（搜索广告）；不填默认信息流"` //广告计划类型 0:信息流（展示广告） 1:搜索（搜索广告）；不填默认信息流
	AutoAdjust       string `p:"autoAdjust" v:"autoAdjust@integer#自动调控开关 0：关闭，1：开启需为整数" dc:"自动调控开关 0：关闭，1：开启"`                                                                   //自动调控开关 0：关闭，1：开启
	AutoBuild        string `p:"autoBuild" v:"autoBuild@integer#自动基建 0：关闭，1：开启需为整数" dc:"自动基建 0：关闭，1：开启"`                                                                             //自动基建 0：关闭，1：开启
	UnitNameRule     string `p:"unitNameRule" dc:"单元名称规则"`                                                                                                                                         //单元名称规则
	CreativeNameRule string `p:"creativeNameRule" dc:"创意名称规则"`                                                                                                                                     //创意名称规则
	AutoManage       string `p:"autoManage" v:"autoManage@integer#智能投放开关 0：关闭，1：开启需为整数" dc:"智能投放开关 0：关闭，1：开启"`                                                                   //智能投放开关 0：关闭，1：开启
	BidType          string `p:"bidType" v:"bidType@integer#竞价策略 0：成本优先 1：最大转化需为整数" dc:"竞价策略 0：成本优先 1：最大转化"`                                                                 //竞价策略 0：成本优先 1：最大转化
	DayBudget        string `p:"dayBudget" v:"dayBudget@float#日预算 不限传0需为浮点数" dc:"日预算 不限传0"`                                                                                             //日预算 不限传0
	CampaignName     string `p:"campaignName" dc:"广告计划名称"`                                                                                                                                         //广告计划名称
	AdUnitLimit      string `p:"adUnitLimit" v:"adUnitLimit@integer#广告计划内广告组上线需为整数" dc:"广告计划内广告组上线"`                                                                             //广告计划内广告组上线
	PutStatus        string `p:"putStatus" v:"putStatus@integer#广告计划默认状态 1-投放、2-暂停需为整数" dc:"广告计划默认状态 1-投放、2-暂停"`                                                             //广告计划默认状态 1-投放、2-暂停
	CreatedAt        string `p:"createdAt" v:"createdAt@datetime#创建时间需为YYYY-MM-DD hh:mm:ss格式" dc:"创建时间"`                                                                                     //创建时间
}

// KsAdvertiserStrategyCampaignSearchRes 列表返回结果
type KsAdvertiserStrategyCampaignSearchRes struct {
	comModel.ListRes
	List []*KsAdvertiserStrategyCampaignListRes `json:"list"`
}

// KsAdvertiserStrategyCampaignAddReq 添加操作请求参数
type KsAdvertiserStrategyCampaignAddReq struct {
	StrategyId       string  `p:"-" json:"-" dc:"策略组ID"`
	TaskId           string  `p:"-" json:"-" dc:"任务ID"`
	CampaignType     int     `p:"campaignType" json:"campaignType"  dc:"营销类型 30：快手号-短剧推广"`
	AdType           int     `p:"adType" json:"adType"  dc:"广告计划类型 0:信息流（展示广告） 1:搜索（搜索广告）；不填默认信息流"`
	AutoAdjust       int     `p:"autoAdjust" json:"autoAdjust"  dc:"自动调控开关 0：关闭，1：开启"`
	AutoBuild        int     `p:"autoBuild" json:"autoBuild"  dc:"自动基建 0：关闭，1：开启"`
	UnitNameRule     string  `p:"unitNameRule" json:"unitNameRule"  dc:"单元名称规则"`
	CreativeNameRule string  `p:"creativeNameRule" json:"creativeNameRule" dc:"创意名称规则"`
	AutoManage       int     `p:"autoManage" json:"autoManage"  dc:"智能投放开关 0：关闭，1：开启"`
	BidType          int     `p:"bidType" json:"bidType"  dc:"竞价策略 0：成本优先 1：最大转化"`
	DayBudget        float64 `p:"dayBudget" json:"dayBudget"  dc:"日预算 不限传0"`
	CampaignName     string  `p:"campaignName" json:"campaignName" dc:"广告计划名称"`
	AdUnitLimit      int     `p:"adUnitLimit" json:"adUnitLimit"  dc:"广告计划内广告组上线"`
	PutStatus        int     `p:"putStatus" json:"putStatus" dc:"广告计划默认状态 1-投放、2-暂停"`
}

// KsAdvertiserStrategyCampaignEditReq 修改操作请求参数
type KsAdvertiserStrategyCampaignEditReq struct {
	StrategyId       string  `p:"strategyId"  dc:"策略组ID"`
	TaskId           string  `p:"taskId"  dc:"任务ID"`
	CampaignType     int     `p:"campaignType"  dc:"营销类型 30：快手号-短剧推广"`
	AdType           int     `p:"adType"  dc:"广告计划类型 0:信息流（展示广告） 1:搜索（搜索广告）；不填默认信息流"`
	AutoAdjust       int     `p:"autoAdjust"  dc:"自动调控开关 0：关闭，1：开启"`
	AutoBuild        int     `p:"autoBuild"  dc:"自动基建 0：关闭，1：开启"`
	UnitNameRule     string  `p:"unitNameRule" dc:"单元名称规则"`
	CreativeNameRule string  `p:"creativeNameRule" dc:"创意名称规则"`
	AutoManage       int     `p:"autoManage"  dc:"智能投放开关 0：关闭，1：开启"`
	BidType          int     `p:"bidType"  dc:"竞价策略 0：成本优先 1：最大转化"`
	DayBudget        float64 `p:"dayBudget"  dc:"日预算 不限传0"`
	CampaignName     string  `p:"campaignName" v:"required#广告计划名称不能为空" dc:"广告计划名称"`
	AdUnitLimit      int     `p:"adUnitLimit"  dc:"广告计划内广告组上线"`
	PutStatus        int     `p:"putStatus" v:"required#广告计划默认状态 1-投放、2-暂停不能为空" dc:"广告计划默认状态 1-投放、2-暂停"`
}
