// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2025-08-20 11:00:01
// 生成路径: internal/app/ad/logic/ks_advertiser_program_creative_report.go
// 生成人：cyao
// desc:创意数据
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"
	systemModel "github.com/tiger1103/gfast/v3/internal/app/system/model"
	sysService "github.com/tiger1103/gfast/v3/internal/app/system/service"
	ksApi "github.com/tiger1103/gfast/v3/library/advertiser/ks/api"
	"github.com/tiger1103/gfast/v3/library/libUtils"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	"github.com/tiger1103/gfast/v3/library/liberr"
)

func init() {
	service.RegisterKsAdvertiserProgramCreativeReport(New())
}

func New() service.IKsAdvertiserProgramCreativeReport {
	return &sKsAdvertiserProgramCreativeReport{}
}

type sKsAdvertiserProgramCreativeReport struct{}

func (s *sKsAdvertiserProgramCreativeReport) List(ctx context.Context, req *model.KsAdvertiserProgramCreativeReportSearchReq) (listRes *model.KsAdvertiserProgramCreativeReportSearchRes, err error) {
	listRes = new(model.KsAdvertiserProgramCreativeReportSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		userInfo := sysService.Context().GetLoginUser(ctx)
		_, admin, _ := sysService.SysUser().GetContainUser(ctx, &systemModel.ContextUser{
			LoginUserRes: &systemModel.LoginUserRes{
				Id:     userInfo.Id,
				DeptId: userInfo.DeptId,
			},
		})

		m := dao.KsAdvertiserProgramCreativeReport.Ctx(ctx).As("b").WithAll()
		if !admin {
			m = m.LeftJoin(dao.KsAdvertiserAccountInfo.Table(), "a", "a."+dao.KsAdvertiserAccountInfo.Columns().AccountId+"="+"b."+dao.KsAdvertiserProgramCreativeReport.Columns().AdvertiserId).
				Where(dao.KsAdvertiserAccountInfo.Columns().Owner, userInfo.Id)
		}

		if req.CreativeId != "" {
			m = m.Where("b."+dao.KsAdvertiserProgramCreativeReport.Columns().CreativeId+" = ?", req.CreativeId)
		}
		if req.StatDate != "" {
			m = m.Where("b."+dao.KsAdvertiserProgramCreativeReport.Columns().StatDate+" = ?", req.StatDate)
		}
		if req.StartTime != "" {
			m = m.Where("b."+dao.KsAdvertiserProgramCreativeReport.Columns().StatDate+" >= ?", req.StartTime)
		}
		if req.EndTime != "" {
			m = m.Where("b."+dao.KsAdvertiserProgramCreativeReport.Columns().StatDate+" <= ?", libUtils.StringTimeAddDay(req.EndTime, 1))
		}
		if req.AdvertiserId != "" {
			m = m.Where("b."+dao.KsAdvertiserProgramCreativeReport.Columns().AdvertiserId+" = ?", gconv.Int64(req.AdvertiserId))
		}
		if req.UnitName != "" {
			m = m.Where("b."+dao.KsAdvertiserProgramCreativeReport.Columns().UnitName+" like ?", "%"+req.UnitName+"%")
		}
		if req.UnitId != "" {
			m = m.Where("b."+dao.KsAdvertiserProgramCreativeReport.Columns().UnitId+" = ?", gconv.Int64(req.UnitId))
		}
		if req.UnitType != "" {
			m = m.Where("b."+dao.KsAdvertiserProgramCreativeReport.Columns().UnitType+" = ?", gconv.Int(req.UnitType))
		}
		m = m.Group(dao.KsAdvertiserProgramCreativeReport.Columns().CreativeId)
		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "creative_id asc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		fields := []string{
			"b.creative_id as creativeId",
			"ANY_VALUE(b.advertiser_id) as advertiserId",
			"Sum(b.charge) as charge",
			// 广告曝光数
			"SUm(b.`show`) as `show`",
			// 素材曝光数
			"Sum(b.aclick) as aclick",
			// 行为数
			"Sum(b.bclick) as bclick",
			// 素材点击率
			"ROUND(SUM(b.bclick)/SUM(b.show)*100,2) as aclickRate",
			"Sum(b.`share`) as `share`",
			"Sum(b.`comment`) as `comment`",
			"Sum(b.`like`) as `like`",
			"Sum(b.follow) as follow",
			"Sum(b.report) as report",
			"Sum(b.block) as block",
			"Sum(b.negative) as negative",
			"Sum(b.activation) as activation",
			"Sum(b.submit) as submit",
			"Sum(b.ad_photo_played_10s) as adPhotoPlayed10S",
			"Sum(b.ad_photo_played_2s) as adPhotoPlayed2S",
			//ad_photo_played_75percent
			"Sum(b.ad_photo_played_75percent) as adPhotoPlayed75Percent",
			//cancel_like
			"Sum(b.cancel_like) as cancelLike",
			//click_conversion_ratio
			"Sum(b.aclick)/Sum(b.activation) as clickConversionRatio",
			//conversion_cost 单次激活成本 不知道怎么算
			"ROUND(SUM(b.conversion_cost),2) as conversionCost",
			//conversion_cost_by_impression_7d
			"ROUND(SUM(b.conversion_cost_by_impression_7d),2) as conversionCostByImpression7D",
			//conversion_num 转化数
			"SUM(b.conversion_num) as conversionNum",
			//conversion_num_by_impression_7d
			"SUM(b.conversion_num_by_impression_7d) as conversionNumByImpression7D",
			// conversion_num_cost
			"ROUND(SUM(b.conversion_num_cost),2) as conversionNumCost",
			//conversion_ratio
			"ROUND(SUM(b.activation)/SUM(b.bclick)*100,2) as conversionRatio",
			//played_three_seconds
			"SUM(b.played_three_seconds) as playedThreeSeconds",
			//play_3s_ratio
			"ROUND(SUM(b.played_three_seconds)/SUM(b.aclick)*100,2) as playThreeSecondsRatio",

			"any_value(b.campaign_id) as campaignId",
			"any_value(b.campaign_name) as campaignName",
			"any_value(b.unit_name) as unitName",
			"any_value(b.photo_id) as photoId",
			"any_value(b.photo_url) as photoUrl",
			"any_value(b.cover_url) as coverUrl",
			"any_value(b.image_token) as imageToken",
			"any_value(b.description) as description",
			"any_value(b.pic_id) as picId",
			"any_value(b.photo_md5) as photoMd5",
			"any_value(b.unit_type) as unitType",
		}

		var res []*model.KsAdvertiserProgramCreativeReportListRes
		err = m.Fields(fields).Page(req.PageNum, req.PageSize).Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		listRes.List = make([]*model.KsAdvertiserProgramCreativeReportListRes, len(res))
		for k, v := range res {
			listRes.List[k] = &model.KsAdvertiserProgramCreativeReportListRes{
				AClickRate:                        v.AClickRate,
				CreativeId:                        v.CreativeId,
				AdvertiserId:                      v.AdvertiserId,
				Charge:                            libUtils.ToRound(v.Charge, 2, libUtils.RoundHalfEven),
				Show:                              v.Show,
				Aclick:                            v.Aclick,
				Bclick:                            v.Bclick,
				Share:                             v.Share,
				Comment:                           v.Comment,
				Like:                              v.Like,
				Follow:                            v.Follow,
				Report:                            v.Report,
				Block:                             v.Block,
				Negative:                          v.Negative,
				Activation:                        v.Activation,
				Submit:                            v.Submit,
				AdPhotoPlayed10S:                  v.AdPhotoPlayed10S,
				AdPhotoPlayed2S:                   v.AdPhotoPlayed2S,
				AdPhotoPlayed75Percent:            v.AdPhotoPlayed75Percent,
				CancelLike:                        v.CancelLike,
				ClickConversionRatio:              libUtils.ToRound(v.ClickConversionRatio, 2, libUtils.RoundHalfEven),
				ConversionCost:                    libUtils.ToRound(v.ConversionCost, 2, libUtils.RoundHalfEven),
				ConversionCostByImpression7D:      libUtils.ToRound(v.ConversionCostByImpression7D, 2, libUtils.RoundHalfEven),
				ConversionNum:                     v.ConversionNum,
				ConversionNumByImpression7D:       v.ConversionNumByImpression7D,
				ConversionNumCost:                 libUtils.ToRound(v.ConversionNumCost, 2, libUtils.RoundHalfEven),
				ConversionRatio:                   libUtils.ToRound(v.ConversionRatio, 2, libUtils.RoundHalfEven),
				ConversionRatioByImpression7D:     libUtils.ToRound(v.ConversionRatioByImpression7D, 2, libUtils.RoundHalfEven),
				LivePlayed3S:                      v.LivePlayed3S,
				PlayedEnd:                         v.PlayedEnd,
				PlayedFiveSeconds:                 v.PlayedFiveSeconds,
				PlayedThreeSeconds:                v.PlayedThreeSeconds,
				AdScene:                           v.AdScene,
				PlacementType:                     v.PlacementType,
				CancelFollow:                      v.CancelFollow,
				Play3SRatio:                       libUtils.ToRound(v.Play3SRatio, 2, libUtils.RoundHalfEven),
				Play5SRatio:                       libUtils.ToRound(v.Play5SRatio, 2, libUtils.RoundHalfEven),
				PlayEndRatio:                      libUtils.ToRound(v.PlayEndRatio, 2, libUtils.RoundHalfEven),
				DirectSubmit1DCost:                v.DirectSubmit1DCost,
				MinigameIaaPurchaseAmountFirstDay: v.MinigameIaaPurchaseAmountFirstDay,
				MinigameIaaPurchaseAmountThreeDayByConversion:    v.MinigameIaaPurchaseAmountThreeDayByConversion,
				MinigameIaaPurchaseAmountWeekByConversion:        v.MinigameIaaPurchaseAmountWeekByConversion,
				MinigameIaaPurchaseAmountFirstDayRoi:             v.MinigameIaaPurchaseAmountFirstDayRoi,
				MinigameIaaPurchaseAmountThreeDayByConversionRoi: v.MinigameIaaPurchaseAmountThreeDayByConversionRoi,
				MinigameIaaPurchaseAmountWeekByConversionRoi:     v.MinigameIaaPurchaseAmountWeekByConversionRoi,
				MinigameIaaPurchaseAmount:                        v.MinigameIaaPurchaseAmount,
				MinigameIaaPurchaseRoi:                           v.MinigameIaaPurchaseRoi,
				UnitId:                                           v.UnitId,
				EffectiveCustomerAcquisition7DCnt:                v.EffectiveCustomerAcquisition7DCnt,
				EffectiveCustomerAcquisition7DCost:               v.EffectiveCustomerAcquisition7DCost,
				EffectiveCustomerAcquisition7DRatio:              v.EffectiveCustomerAcquisition7DRatio,
				MmuEffectiveCustomerAcquisitionCnt:               v.MmuEffectiveCustomerAcquisitionCnt,
				MmuEffectiveCustomerAcquisition7DCnt:             v.MmuEffectiveCustomerAcquisition7DCnt,
				PlayedNum:                                        v.PlayedNum,
				LeadsSubmitCnt:                                   v.LeadsSubmitCnt,
				LeadsSubmitCntRatio:                              v.LeadsSubmitCntRatio,
				LeadsSubmitCost:                                  v.LeadsSubmitCost,
				PrivateMessageSentCnt:                            v.PrivateMessageSentCnt,
				PrivateMessageSentRatio:                          v.PrivateMessageSentRatio,
				PrivateMessageSentCost:                           v.PrivateMessageSentCost,
				EventFormSubmit:                                  v.EventFormSubmit,
				DirectSubmit1DCnt:                                v.DirectSubmit1DCnt,
				EventFormSubmitRatio:                             v.EventFormSubmitRatio,
				EventFormSubmitCost:                              v.EventFormSubmitCost,
				EventAudition:                                    v.EventAudition,
				EventAudition30DCnt:                              v.EventAudition30DCnt,
				EventAuditionCost:                                v.EventAuditionCost,
				AllLessonFinishCnt:                               v.AllLessonFinishCnt,
				AllLessonFinish30DCnt:                            v.AllLessonFinish30DCnt,
				HighPriceClassPayCnt:                             v.HighPriceClassPayCnt,
				HighPriceClassPay30DCnt:                          v.HighPriceClassPay30DCnt,
				CampaignId:                                       v.CampaignId,
				CampaignName:                                     v.CampaignName,
				UnitName:                                         v.UnitName,
				PhotoId:                                          v.PhotoId,
				PhotoUrl:                                         v.PhotoUrl,
				CoverUrl:                                         v.CoverUrl,
				ImageToken:                                       v.ImageToken,
				Description:                                      v.Description,
				PicId:                                            v.PicId,
				PhotoMd5:                                         v.PhotoMd5,
				UnitType:                                         v.UnitType,
			}
		}
	})
	return
}

func (s *sKsAdvertiserProgramCreativeReport) GetByStatDate(ctx context.Context, creativeId int64, statDate string) (res *model.KsAdvertiserProgramCreativeReportInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.KsAdvertiserProgramCreativeReport.Ctx(ctx).WithAll().Where(dao.KsAdvertiserProgramCreativeReport.Columns().StatDate, statDate).Where(dao.KsAdvertiserProgramCreativeReport.Columns().CreativeId, creativeId).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

// Pull
func (s *sKsAdvertiserProgramCreativeReport) Pull(ctx context.Context, startTime, endTime string) (err error) {
	innerCtx, cancel := context.WithCancel(context.Background())
	defer cancel()
	err = g.Try(innerCtx, func(innerCtx context.Context) {
		if libUtils.CompareDateString(startTime, endTime) > 0 {
			return
		}
		var pageNo = 1
		var pageSize = 500
		for {
			// 获取账户列表
			advertiserList, _ := service.KsAdvertiserAccountInfo().GetAccountList(innerCtx, pageNo, pageSize)
			if len(advertiserList) == 0 {
				break
			}
			for _, req := range advertiserList {
				agentInfo, _ := service.KsAdvertiserAgentInfo().GetAPPConfig(innerCtx, req.AgentAccountId, req.AuthorizeKsAccount)
				accessToken := ksApi.GetAccessTokenByAgentCache(req.AgentAccountId, req.AuthorizeKsAccount, gconv.Int64(agentInfo.AppId), agentInfo.Secret)
				err = s.PullCreateByAdId(innerCtx, accessToken, req.AccountId, startTime, endTime)
				liberr.ErrIsNil(innerCtx, err, "拉取创意失败")
			}
			pageNo++
		}

	})
	return
}

// 根据广告id 拉取 创意
func (s *sKsAdvertiserProgramCreativeReport) PullCreateByAdId(ctx context.Context, accessToken string, adId int64, startTime, endTime string) (err error) {

	err = g.Try(ctx, func(ctx context.Context) {
		page, pageSize := 1, 200
		for {
			reportResp, err := ksApi.GetKSApiClient().ProgramCreativeReportService.AccessToken(accessToken).SetReq(ksApi.ProgramCreativeReportReq{
				AdvertiserId: adId,
				PageSize:     pageSize,
				Page:         page,
				StartDate:    startTime,
				EndDate:      endTime,
			}).Do()
			if err != nil {
				g.Log().Errorf(ctx, "拉取广告计划失败: %v", err)
				break
			}
			if reportResp == nil || reportResp.Data == nil || reportResp.Data.TotalCount == 0 {
				break
			}
			// 循环添加广告计划
			batchReq := make([]*model.KsAdvertiserProgramCreativeReportAddReq, 0)
			for _, report := range reportResp.Data.Details {
				req := &model.KsAdvertiserProgramCreativeReportAddReq{
					AdvertiserId:                      gconv.Int64(adId),
					StatDate:                          report.StatDate,
					Charge:                            report.Charge,
					Show:                              report.Show,
					Aclick:                            report.Aclick,
					Bclick:                            report.Bclick,
					Share:                             report.Share,
					Comment:                           report.Comment,
					Like:                              report.Like,
					Follow:                            report.Follow,
					Report:                            report.Report,
					Block:                             report.Block,
					Negative:                          report.Negative,
					Activation:                        report.Activation,
					Submit:                            report.Submit,
					AdPhotoPlayed10S:                  report.AdPhotoPlayed10S,
					AdPhotoPlayed2S:                   report.AdPhotoPlayed2S,
					AdPhotoPlayed75Percent:            report.AdPhotoPlayed75Percent,
					CancelLike:                        report.CancelLike,
					ClickConversionRatio:              report.ClickConversionRatio,
					ConversionCost:                    report.ConversionCost,
					ConversionCostByImpression7D:      report.ConversionCostByImpression7D,
					ConversionNum:                     report.ConversionNum,
					ConversionNumByImpression7D:       report.ConversionNumByImpression7D,
					ConversionNumCost:                 report.ConversionNumCost,
					ConversionRatio:                   report.ConversionRatio,
					ConversionRatioByImpression7D:     report.ConversionRatioByImpression7D,
					LivePlayed3S:                      report.LivePlayed3S,
					PlayedEnd:                         report.PlayedEnd,
					PlayedFiveSeconds:                 report.PlayedFiveSeconds,
					PlayedThreeSeconds:                report.PlayedThreeSeconds,
					AdScene:                           report.AdScene,
					PlacementType:                     report.PlacementType,
					CancelFollow:                      report.CancelFollow,
					Play3SRatio:                       report.Play3SRatio,
					Play5SRatio:                       report.Play5SRatio,
					PlayEndRatio:                      report.PlayEndRatio,
					MinigameIaaPurchaseAmountFirstDay: report.MinigameIaaPurchaseAmountFirstDay,
					MinigameIaaPurchaseAmountThreeDayByConversion:    report.MinigameIaaPurchaseAmountThreeDayByConversion,
					MinigameIaaPurchaseAmountWeekByConversion:        report.MinigameIaaPurchaseAmountWeekByConversion,
					MinigameIaaPurchaseAmountFirstDayRoi:             report.MinigameIaaPurchaseAmountFirstDayRoi,
					MinigameIaaPurchaseAmountThreeDayByConversionRoi: report.MinigameIaaPurchaseAmountThreeDayByConversionRoi,
					MinigameIaaPurchaseAmountWeekByConversionRoi:     report.MinigameIaaPurchaseAmountWeekByConversionRoi,
					MinigameIaaPurchaseAmount:                        report.MinigameIaaPurchaseAmount,
					MinigameIaaPurchaseRoi:                           report.MinigameIaaPurchaseRoi,
					UnitId:                                           report.UnitId,
					EffectiveCustomerAcquisition7DCnt:                report.EffectiveCustomerAcquisition7DCnt,
					EffectiveCustomerAcquisition7DCost:               report.EffectiveCustomerAcquisition7DCost,
					EffectiveCustomerAcquisition7DRatio:              report.EffectiveCustomerAcquisition7DRatio,
					MmuEffectiveCustomerAcquisitionCnt:               report.MmuEffectiveCustomerAcquisitionCnt,
					MmuEffectiveCustomerAcquisition7DCnt:             report.MmuEffectiveCustomerAcquisition7DCnt,
					PlayedNum:                                        report.PlayedNum,
					LeadsSubmitCnt:                                   report.LeadsSubmitCnt,
					LeadsSubmitCntRatio:                              report.LeadsSubmitCntRatio,
					LeadsSubmitCost:                                  report.LeadsSubmitCost,
					PrivateMessageSentCnt:                            report.PrivateMessageSentCnt,
					PrivateMessageSentRatio:                          report.PrivateMessageSentRatio,
					PrivateMessageSentCost:                           report.PrivateMessageSentCost,
					EventFormSubmit:                                  report.EventFormSubmit,
					DirectSubmit1DCnt:                                report.DirectSubmit1DCnt,
					EventFormSubmitRatio:                             report.EventFormSubmitRatio,
					EventFormSubmitCost:                              report.EventFormSubmitCost,
					AllLessonFinishCnt:                               report.AllLessonFinishCnt,
					AllLessonFinish30DCnt:                            report.AllLessonFinish30DCnt,
					HighPriceClassPayCnt:                             report.HighPriceClassPayCnt,
					HighPriceClassPay30DCnt:                          report.HighPriceClassPay30DCnt,
					CreativeId:                                       report.CreativeId,
					CampaignId:                                       report.CampaignId,
					CampaignName:                                     report.CampaignName,
					UnitName:                                         report.UnitName,
					PhotoId:                                          report.PhotoId,
					PhotoUrl:                                         report.PhotoUrl,
					CoverUrl:                                         report.CoverUrl,
					ImageToken:                                       report.ImageToken,
					Description:                                      report.Description,
					PicId:                                            report.PicId,
					PhotoMd5:                                         report.PhotoMd5,
					UnitType:                                         report.UnitType,
					DirectSubmit1DCost:                               report.DirectSubmit1DCost,
					//DeepConversionCost:                               report.DeepConversionCost,
					//DeepConversionCostByImpression7D:                 report.DeepConversionCostByImpression7D,
					//DeepConversionNum:                                report.DeepConversionNum,
					//DeepConversionNumByImpression7D:                  report.DeepConversionNumByImpression7D,
					//DeepConversionRatio:                              report.DeepConversionRatio,
					//DeepConversionRatioByImpression7D:                report.DeepConversionRatioByImpression7D,
					//Event24HStay:                                     report.Event24HStay,
					//Event24HStayByConversion:                         report.Event24HStayByConversion,
					//Event24HStayByConversionCost:                     report.Event24HStayByConversionCost,
					//Event24HStayByConversionRatio:                    report.Event24HStayByConversionRatio,
					//Event24HStayCost:                                 report.Event24HStayCost,
					//Event24HStayRatio:                                report.Event24HStayRatio,
					//EventAdWatch10Times:                              report.EventAdWatch10Times,
					//EventAdWatch10TimesCost:                          report.EventAdWatch10TimesCost,
					//EventAdWatch10TimesRatio:                         report.EventAdWatch10TimesRatio,
					//EventAdWatch20Times:                              report.EventAdWatch20Times,
					//EventAdWatch20TimesCost:                          report.EventAdWatch20TimesCost,
					//EventAdWatch20TimesRatio:                         report.EventAdWatch20TimesRatio,
					//EventAdWatch5Times:                               report.EventAdWatch5Times,
					//EventAdWatch5TimesCost:                           report.EventAdWatch5TimesCost,
					//EventAdWatch5TimesRatio:                          report.EventAdWatch5TimesRatio,
					//EventConsultationValidRetained:                   report.EventConsultationValidRetained,
					//EventConsultationValidRetainedCost:               report.EventConsultationValidRetainedCost,
					//EventConsultationValidRetainedRatio:              report.EventConsultationValidRetainedRatio,
					//EventConversionClickCost:                         report.EventConversionClickCost,
					//EventConversionClickRatio:                        report.EventConversionClickRatio,
					//EventCreditGrantFirstDayApp:                      report.EventCreditGrantFirstDayApp,
					//EventCreditGrantFirstDayAppCost:                  report.EventCreditGrantFirstDayAppCost,
					//EventCreditGrantFirstDayAppRatio:                 report.EventCreditGrantFirstDayAppRatio,
					//EventCreditGrantFirstDayLandingPage:              report.EventCreditGrantFirstDayLandingPage,
					//EventCreditGrantFirstDayLandingPageCost:          report.EventCreditGrantFirstDayLandingPageCost,
					//EventCreditGrantFirstDayLandingPageRatio:         report.EventCreditGrantFirstDayLandingPageRatio,
					//EventFiveDayStayByConversion:                     report.EventFiveDayStayByConversion,
					//EventFiveDayStayByConversionCost:                 report.EventFiveDayStayByConversionCost,
					//EventFiveDayStayByConversionRatio:                report.EventFiveDayStayByConversionRatio,
					//EventFourDayStayByConversion:                     report.EventFourDayStayByConversion,
					//EventFourDayStayByConversionCost:                 report.EventFourDayStayByConversionCost,
					//EventFourDayStayByConversionRatio:                report.EventFourDayStayByConversionRatio,
					//EventPayPurchaseAmountOneDay:                     report.EventPayPurchaseAmountOneDay,
					//EventPayPurchaseAmountOneDayByConversion:         report.EventPayPurchaseAmountOneDayByConversion,
					//EventPayPurchaseAmountOneDayByConversionRoi:      report.EventPayPurchaseAmountOneDayByConversionRoi,
					//EventPayPurchaseAmountOneDayRoi:                  report.EventPayPurchaseAmountOneDayRoi,
					//EventPayPurchaseAmountThreeDayByConversion:       report.EventPayPurchaseAmountThreeDayByConversion,
					//EventPayPurchaseAmountThreeDayByConversionRoi:    report.EventPayPurchaseAmountThreeDayByConversionRoi,
					//EventPayPurchaseAmountWeekByConversion:           report.EventPayPurchaseAmountWeekByConversion,
					//EventPayPurchaseAmountWeekByConversionRoi:        report.EventPayPurchaseAmountWeekByConversionRoi,
					//EventPayWeekByConversion:                         report.EventPayWeekByConversion,
					//EventPayWeekByConversionCost:                     report.EventPayWeekByConversionCost,
					//EventPayWeightedPurchaseAmount:                   report.EventPayWeightedPurchaseAmount,
					//EventPayWeightedPurchaseAmountFirstDay:           report.EventPayWeightedPurchaseAmountFirstDay,
					//EventPreComponentConsultationValidRetained:       report.EventPreComponentConsultationValidRetained,
					//EventSixDayStayByConversion:                      report.EventSixDayStayByConversion,
					//EventSixDayStayByConversionCost:                  report.EventSixDayStayByConversionCost,
					//EventSixDayStayByConversionRatio:                 report.EventSixDayStayByConversionRatio,
					//EventThreeDayStayByConversion:                    report.EventThreeDayStayByConversion,
					//EventThreeDayStayByConversionCost:                report.EventThreeDayStayByConversionCost,
					//EventThreeDayStayByConversionRatio:               report.EventThreeDayStayByConversionRatio,
					//EventTwoDayStayByConversion:                      report.EventTwoDayStayByConversion,
					//EventTwoDayStayByConversionCost:                  report.EventTwoDayStayByConversionCost,
					//EventTwoDayStayByConversionRatio:                 report.EventTwoDayStayByConversionRatio,
					//EventWechatQrCodeLinkClick:                       report.EventWechatQrCodeLinkClick,
					//EventWeekStay:                                    report.EventWeekStay,
					//EventWeekStayByConversion:                        report.EventWeekStayByConversion,
					//EventWeekStayByConversionCost:                    report.EventWeekStayByConversionCost,
					//EventWeekStayByConversionRatio:                   report.EventWeekStayByConversionRatio,
					//EventWeekStayCost:                                report.EventWeekStayCost,
					//EventWeekStayRatio:                               report.EventWeekStayRatio,
					//LiveEventGoodsView:                               report.LiveEventGoodsView,
					//StatDate:                                         report.StatDate,
					//PhotoClick:                                       report.PhotoClick,
					//PhotoClickRatio:                                  report.PhotoClickRatio,
					//ActionRatio:                                      report.ActionRatio,
					//Impression1KCost:                                 report.Impression1KCost,
					//PhotoClickCost:                                   report.PhotoClickCost,
					//Click1KCost:                                      report.Click1KCost,
					//ActionCost:                                       report.ActionCost,
					//EventPayFirstDay:                                 report.EventPayFirstDay,
					//EventPayPurchaseAmountFirstDay:                   report.EventPayPurchaseAmountFirstDay,
					//EventPayFirstDayRoi:                              report.EventPayFirstDayRoi,
					//EventPay:                                         report.EventPay,
					//EventPayPurchaseAmount:                           report.EventPayPurchaseAmount,
					//EventPayPurchaseAmount30DayByConversion:          report.EventPayPurchaseAmount30DayByConversion,
					//EventPayPurchaseAmount30DayByConversionRoi:       report.EventPayPurchaseAmount30DayByConversionRoi,
					//EventPayRoi:                                      report.EventPayRoi,
					//EventRegister:                                    report.EventRegister,
					//EventRegisterCost:                                report.EventRegisterCost,
					//EventRegisterRatio:                               report.EventRegisterRatio,
					//EventJinJianApp:                                  report.EventJinJianApp,
					//EventJinJianAppCost:                              report.EventJinJianAppCost,
					//EventCreditGrantApp:                              report.EventCreditGrantApp,
					//EventCreditGrantAppCost:                          report.EventCreditGrantAppCost,
					//EventCreditGrantAppRatio:                         report.EventCreditGrantAppRatio,
					//EventOrderPaid:                                   report.EventOrderPaid,
					//EventOrderPaidPurchaseAmount:                     report.EventOrderPaidPurchaseAmount,
					//EventOrderPaidCost:                               report.EventOrderPaidCost,
					//FormCount:                                        report.FormCount,
					//FormCost:                                         report.FormCost,
					//FormActionRatio:                                  report.FormActionRatio,
					//EventJinJianLandingPage:                          report.EventJinJianLandingPage,
					//EventJinJianLandingPageCost:                      report.EventJinJianLandingPageCost,
					//EventCreditGrantLandingPage:                      report.EventCreditGrantLandingPage,
					//EventCreditGrantLandingPageCost:                  report.EventCreditGrantLandingPageCost,
					//EventCreditGrantLandingRatio:                     report.EventCreditGrantLandingRatio,
					//EventNextDayStayCost:                             report.EventNextDayStayCost,
					//EventNextDayStayRatio:                            report.EventNextDayStayRatio,
					//EventNextDayStay:                                 report.EventNextDayStay,
					//EventValidClues:                                  report.EventValidClues,
					//EventValidCluesCost:                              report.EventValidCluesCost,
					//AdProductCnt:                                     report.AdProductCnt,
					//EventGoodsView:                                   report.EventGoodsView,
					//MerchantRecoFans:                                 report.MerchantRecoFans,
					//EventOrderAmountRoi:                              report.EventOrderAmountRoi,
					//EventGoodsViewCost:                               report.EventGoodsViewCost,
					//MerchantRecoFansCost:                             report.MerchantRecoFansCost,
					//EventNewUserPay:                                  report.EventNewUserPay,
					//EventNewUserPayCost:                              report.EventNewUserPayCost,
					//EventNewUserPayRatio:                             report.EventNewUserPayRatio,
					//EventButtonClick:                                 report.EventButtonClick,
					//EventButtonClickCost:                             report.EventButtonClickCost,
					//EventButtonClickRatio:                            report.EventButtonClickRatio,
					//EventOrderPaidRoi:                                report.EventOrderPaidRoi,
					//EventNewUserJinjianApp:                           report.EventNewUserJinjianApp,
					//EventNewUserJinjianAppCost:                       report.EventNewUserJinjianAppCost,
					//EventNewUserJinjianAppRoi:                        report.EventNewUserJinjianAppRoi,
					//EventNewUserCreditGrantApp:                       report.EventNewUserCreditGrantApp,
					//EventNewUserCreditGrantAppCost:                   report.EventNewUserCreditGrantAppCost,
					//EventNewUserCreditGrantAppRoi:                    report.EventNewUserCreditGrantAppRoi,
					//EventNewUserJinjianPage:                          report.EventNewUserJinjianPage,
					//EventNewUserJinjianPageCost:                      report.EventNewUserJinjianPageCost,
					//EventNewUserJinjianPageRoi:                       report.EventNewUserJinjianPageRoi,
					//EventNewUserCreditGrantPage:                      report.EventNewUserCreditGrantPage,
					//EventNewUserCreditGrantPageCost:                  report.EventNewUserCreditGrantPageCost,
					//EventNewUserCreditGrantPageRoi:                   report.EventNewUserCreditGrantPageRoi,
					//EventAppointForm:                                 report.EventAppointForm,
					//EventAppointFormCost:                             report.EventAppointFormCost,
					//EventAppointFormRatio:                            report.EventAppointFormRatio,
					//EventAppointJumpClick:                            report.EventAppointJumpClick,
					//EventAppointJumpClickCost:                        report.EventAppointJumpClickCost,
					//EventAppointJumpClickRatio:                       report.EventAppointJumpClickRatio,
					//EventDspGiftForm:                                 report.EventDspGiftForm,
					//EventAppInvoked:                                  report.EventAppInvoked,
					//EventAppInvokedCost:                              report.EventAppInvokedCost,
					//EventAppInvokedRatio:                             report.EventAppInvokedRatio,
					//EventAddWechat:                                   report.EventAddWechat,
					//EventAddWechatCost:                               report.EventAddWechatCost,
					//EventAddWechatRatio:                              report.EventAddWechatRatio,
					//EventMultiConversion:                             report.EventMultiConversion,
					//EventMultiConversionRatio:                        report.EventMultiConversionRatio,
					//EventMultiConversionCost:                         report.EventMultiConversionCost,
					//EventWatchAppAd:                                  report.EventWatchAppAd,
					//EventAdWatchTimes:                                report.EventAdWatchTimes,
					//EventAdWatchTimesRatio:                           report.EventAdWatchTimesRatio,
					//EventAdWatchTimesCost:                            report.EventAdWatchTimesCost,
					//EventAddShoppingCart:                             report.EventAddShoppingCart,
					//EventAddShoppingCartCost:                         report.EventAddShoppingCartCost,
					//EventGetThrough:                                  report.EventGetThrough,
					//EventGetThroughCost:                              report.EventGetThroughCost,
					//EventGetThroughRatio:                             report.EventGetThroughRatio,
					//AdPhotoPlayed75PercentRatio:                      report.AdPhotoPlayed75PercentRatio,
					//AdPhotoPlayed10SRatio:                            report.AdPhotoPlayed10SRatio,
					//AdPhotoPlayed2SRatio:                             report.AdPhotoPlayed2SRatio,
					//EventPhoneGetThrough:                             report.EventPhoneGetThrough,
					//EventIntentionConfirmed:                          report.EventIntentionConfirmed,
					//EventWechatConnected:                             report.EventWechatConnected,
					//EventOrderSuccessed:                              report.EventOrderSuccessed,
					//EventPhoneCardActivate:                           report.EventPhoneCardActivate,
					//EventMeasurementHouse:                            report.EventMeasurementHouse,
					//EventNextDayStayNew:                              report.EventNextDayStayNew,
					//EventNextDayStayNewCost:                          report.EventNextDayStayNewCost,
					//EventNextDayStayNewRatio:                         report.EventNextDayStayNewRatio,
					//AdShow:                                           report.AdShow,
					//ActionNewRatio:                                   report.ActionNewRatio,
					//EventOutboundCall:                                report.EventOutboundCall,
					//EventOutboundCallCost:                            report.EventOutboundCallCost,
					//EventOutboundCallRatio:                           report.EventOutboundCallRatio,
					//KeyAction:                                        report.KeyAction,
					//KeyActionCost:                                    report.KeyActionCost,
					//KeyActionRatio:                                   report.KeyActionRatio,
					//EventCreditCardRecheck:                           report.EventCreditCardRecheck,
					//EventCreditCardRecheckFirstDay:                   report.EventCreditCardRecheckFirstDay,
					//EventNoIntention:                                 report.EventNoIntention,
					//EventMultiPaySevenDayByConversion:                report.EventMultiPaySevenDayByConversion,
					//EventMultiPaySevenDayByConversionCost:            report.EventMultiPaySevenDayByConversionCost,
					//LiveRoomAvgPlayedSeconds:                         report.LiveRoomAvgPlayedSeconds,
					//AdLiveShare:                                      report.AdLiveShare,
					//AdLiveComment:                                    report.AdLiveComment,
					//LivePlayedStarted:                                report.LivePlayedStarted,
					//LivePlayedStartedCost:                            report.LivePlayedStartedCost,
					//AdLiveFollow:                                     report.AdLiveFollow,
					//AdLiveFollowCost:                                 report.AdLiveFollowCost,
					//SimpleLivePlayedStarted:                          report.SimpleLivePlayedStarted,
					//StandardLivePlayedStarted:                        report.StandardLivePlayedStarted,
					//ConversionComponentImpression:                    report.ConversionComponentImpression,
					//ConversionComponentClick:                         report.ConversionComponentClick,
					//ConversionComponentRate:                          report.ConversionComponentRate,
					//AdLandingPageImpression:                          report.AdLandingPageImpression,
					//AdAppDownloadHalfImpression:                      report.AdAppDownloadHalfImpression,
					//EventDrawCreditLine:                              report.EventDrawCreditLine,
					//EventActive:                                      report.EventActive,
					//EventOrderSubmitCost:                             report.EventOrderSubmitCost,
					//EventOrderSubmit:                                 report.EventOrderSubmit,
					//OrderSubmitAmt:                                   report.OrderSubmitAmt,
					//OrderSubmitRoi:                                   report.OrderSubmitRoi,
					//EventWeekTotalStayByConversion:                   report.EventWeekTotalStayByConversion,
					//EventWeekTotalStayNew:                            report.EventWeekTotalStayNew,
					//EventWeekTotalStayByConversionCost:               report.EventWeekTotalStayByConversionCost,
					//EventWeekTotalStayNewCost:                        report.EventWeekTotalStayNewCost,
					//EventWeekTotalStayByConversionRatio:              report.EventWeekTotalStayByConversionRatio,
					//EventWeekTotalStayNewRatio:                       report.EventWeekTotalStayNewRatio,
					//T7PaiedAmt:                                       report.T7PaiedAmt,
					//EventEffectiveCustomerAcquisitionCnt:             report.EventEffectiveCustomerAcquisitionCnt,
					//EventEffectiveCustomerAcquisitionCost:            report.EventEffectiveCustomerAcquisitionCost,
					//EventEffectiveCustomerAcquisitionRatio:           report.EventEffectiveCustomerAcquisitionRatio,
					//SimpleLiveRoomPlayedSeconds:                      report.SimpleLiveRoomPlayedSeconds,
					//StandardLiveRoomPlayedSeconds:                    report.StandardLiveRoomPlayedSeconds,
					//Likes:                                            report.Likes,
					//Jinjian0DCnt:                                     report.Jinjian0DCnt,
					//Jinjian3DCnt:                                     report.Jinjian3DCnt,
					//Jinjian0DCntCost:                                 report.Jinjian0DCntCost,
					//Jinjian3DCntCost:                                 report.Jinjian3DCntCost,
					//CreditGrant0DCnt:                                 report.CreditGrant0DCnt,
					//CreditGrant3DCnt:                                 report.CreditGrant3DCnt,
					//CreditGrant0DCntCost:                             report.CreditGrant0DCntCost,
					//CreditGrant3DCntCost:                             report.CreditGrant3DCntCost,
					//CreditGrant0DCntRatio:                            report.CreditGrant0DCntRatio,
					//CreditGrant3DCntRatio:                            report.CreditGrant3DCntRatio,
					//KeyInappAction0DCnt:                              report.KeyInappAction0DCnt,
					//KeyInappAction3DCnt:                              report.KeyInappAction3DCnt,
					//KeyInappAction0DCntCost:                          report.KeyInappAction0DCntCost,
					//KeyInappAction3DCntCost:                          report.KeyInappAction3DCntCost,
					//KeyInappAction0DCntRatio:                         report.KeyInappAction0DCntRatio,
					//KeyInappAction3DCntRatio:                         report.KeyInappAction3DCntRatio,
					//DrawCreditLine0DCnt:                              report.DrawCreditLine0DCnt,
					//DrawCreditLine0DCntCost:                          report.DrawCreditLine0DCntCost,
					//DrawCreditLine0DCntRatio:                         report.DrawCreditLine0DCntRatio,
					//MinigameIaaPurchaseAmount15DayByConversion:       report.MinigameIaaPurchaseAmount15DayByConversion,
					//MinigameIaaPurchaseAmount30DayByConversion:       report.MinigameIaaPurchaseAmount30DayByConversion,
					//EventPayPurchaseAmount15DayByConversion:          report.EventPayPurchaseAmount15DayByConversion,
					//MinigameIaaPurchaseAmount15DayByConversionRoi:    report.MinigameIaaPurchaseAmount15DayByConversionRoi,
					//MinigameIaaPurchaseAmount30DayByConversionRoi:    report.MinigameIaaPurchaseAmount30DayByConversionRoi,
					//EventPay15DayOverallRoi:                          report.EventPay15DayOverallRoi,
					//EventPay30DayOverallRoi:                          report.EventPay30DayOverallRoi,

				}

				batchReq = append(batchReq, req)
			}
			err = s.BatchAdd(ctx, batchReq)
			if err != nil {
				g.Log().Errorf(ctx, "批量新增失败%v", err.Error())
			}
			if reportResp.Data.Empty {
				break
			} else {
				page++
			}
		}

	})
	return
}

// BatchAdd
func (s *sKsAdvertiserProgramCreativeReport) BatchAdd(ctx context.Context, batchReq []*model.KsAdvertiserProgramCreativeReportAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.KsAdvertiserProgramCreativeReport.Ctx(ctx).Save(batchReq)
	})
	return
}

func (s *sKsAdvertiserProgramCreativeReport) Add(ctx context.Context, req *model.KsAdvertiserProgramCreativeReportAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.KsAdvertiserProgramCreativeReport.Ctx(ctx).Insert(do.KsAdvertiserProgramCreativeReport{
			CreativeId:                        req.CreativeId,
			StatDate:                          req.StatDate,
			AdvertiserId:                      req.AdvertiserId,
			Charge:                            req.Charge,
			Show:                              req.Show,
			Aclick:                            req.Aclick,
			Bclick:                            req.Bclick,
			Share:                             req.Share,
			Comment:                           req.Comment,
			Like:                              req.Like,
			Follow:                            req.Follow,
			Report:                            req.Report,
			Block:                             req.Block,
			Negative:                          req.Negative,
			Activation:                        req.Activation,
			Submit:                            req.Submit,
			AdPhotoPlayed10S:                  req.AdPhotoPlayed10S,
			AdPhotoPlayed2S:                   req.AdPhotoPlayed2S,
			AdPhotoPlayed75Percent:            req.AdPhotoPlayed75Percent,
			CancelLike:                        req.CancelLike,
			ClickConversionRatio:              req.ClickConversionRatio,
			ConversionCost:                    req.ConversionCost,
			ConversionCostByImpression7D:      req.ConversionCostByImpression7D,
			ConversionNum:                     req.ConversionNum,
			ConversionNumByImpression7D:       req.ConversionNumByImpression7D,
			ConversionNumCost:                 req.ConversionNumCost,
			ConversionRatio:                   req.ConversionRatio,
			ConversionRatioByImpression7D:     req.ConversionRatioByImpression7D,
			LivePlayed3S:                      req.LivePlayed3S,
			PlayedEnd:                         req.PlayedEnd,
			PlayedFiveSeconds:                 req.PlayedFiveSeconds,
			PlayedThreeSeconds:                req.PlayedThreeSeconds,
			AdScene:                           req.AdScene,
			PlacementType:                     req.PlacementType,
			CancelFollow:                      req.CancelFollow,
			Play3SRatio:                       req.Play3SRatio,
			Play5SRatio:                       req.Play5SRatio,
			PlayEndRatio:                      req.PlayEndRatio,
			DirectSubmit1DCost:                req.DirectSubmit1DCost,
			MinigameIaaPurchaseAmountFirstDay: req.MinigameIaaPurchaseAmountFirstDay,
			MinigameIaaPurchaseAmountThreeDayByConversion:    req.MinigameIaaPurchaseAmountThreeDayByConversion,
			MinigameIaaPurchaseAmountWeekByConversion:        req.MinigameIaaPurchaseAmountWeekByConversion,
			MinigameIaaPurchaseAmountFirstDayRoi:             req.MinigameIaaPurchaseAmountFirstDayRoi,
			MinigameIaaPurchaseAmountThreeDayByConversionRoi: req.MinigameIaaPurchaseAmountThreeDayByConversionRoi,
			MinigameIaaPurchaseAmountWeekByConversionRoi:     req.MinigameIaaPurchaseAmountWeekByConversionRoi,
			MinigameIaaPurchaseAmount:                        req.MinigameIaaPurchaseAmount,
			MinigameIaaPurchaseRoi:                           req.MinigameIaaPurchaseRoi,
			UnitId:                                           req.UnitId,
			EffectiveCustomerAcquisition7DCnt:                req.EffectiveCustomerAcquisition7DCnt,
			EffectiveCustomerAcquisition7DCost:               req.EffectiveCustomerAcquisition7DCost,
			EffectiveCustomerAcquisition7DRatio:              req.EffectiveCustomerAcquisition7DRatio,
			MmuEffectiveCustomerAcquisitionCnt:               req.MmuEffectiveCustomerAcquisitionCnt,
			MmuEffectiveCustomerAcquisition7DCnt:             req.MmuEffectiveCustomerAcquisition7DCnt,
			PlayedNum:                                        req.PlayedNum,
			LeadsSubmitCnt:                                   req.LeadsSubmitCnt,
			LeadsSubmitCntRatio:                              req.LeadsSubmitCntRatio,
			LeadsSubmitCost:                                  req.LeadsSubmitCost,
			PrivateMessageSentCnt:                            req.PrivateMessageSentCnt,
			PrivateMessageSentRatio:                          req.PrivateMessageSentRatio,
			PrivateMessageSentCost:                           req.PrivateMessageSentCost,
			EventFormSubmit:                                  req.EventFormSubmit,
			DirectSubmit1DCnt:                                req.DirectSubmit1DCnt,
			EventFormSubmitRatio:                             req.EventFormSubmitRatio,
			EventFormSubmitCost:                              req.EventFormSubmitCost,
			EventAudition:                                    req.EventAudition,
			EventAudition30DCnt:                              req.EventAudition30DCnt,
			EventAuditionCost:                                req.EventAuditionCost,
			AllLessonFinishCnt:                               req.AllLessonFinishCnt,
			AllLessonFinish30DCnt:                            req.AllLessonFinish30DCnt,
			HighPriceClassPayCnt:                             req.HighPriceClassPayCnt,
			HighPriceClassPay30DCnt:                          req.HighPriceClassPay30DCnt,
			CampaignId:                                       req.CampaignId,
			CampaignName:                                     req.CampaignName,
			UnitName:                                         req.UnitName,
			PhotoId:                                          req.PhotoId,
			PhotoUrl:                                         req.PhotoUrl,
			CoverUrl:                                         req.CoverUrl,
			ImageToken:                                       req.ImageToken,
			Description:                                      req.Description,
			PicId:                                            req.PicId,
			PhotoMd5:                                         req.PhotoMd5,
			UnitType:                                         req.UnitType,
		})
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sKsAdvertiserProgramCreativeReport) Edit(ctx context.Context, req *model.KsAdvertiserProgramCreativeReportEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.KsAdvertiserProgramCreativeReport.Ctx(ctx).WherePri(req.CreativeId).Update(do.KsAdvertiserProgramCreativeReport{
			AdvertiserId:                      req.AdvertiserId,
			StatDate:                          req.StatDate,
			Charge:                            req.Charge,
			Show:                              req.Show,
			Aclick:                            req.Aclick,
			Bclick:                            req.Bclick,
			Share:                             req.Share,
			Comment:                           req.Comment,
			Like:                              req.Like,
			Follow:                            req.Follow,
			Report:                            req.Report,
			Block:                             req.Block,
			Negative:                          req.Negative,
			Activation:                        req.Activation,
			Submit:                            req.Submit,
			AdPhotoPlayed10S:                  req.AdPhotoPlayed10S,
			AdPhotoPlayed2S:                   req.AdPhotoPlayed2S,
			AdPhotoPlayed75Percent:            req.AdPhotoPlayed75Percent,
			CancelLike:                        req.CancelLike,
			ClickConversionRatio:              req.ClickConversionRatio,
			ConversionCost:                    req.ConversionCost,
			ConversionCostByImpression7D:      req.ConversionCostByImpression7D,
			ConversionNum:                     req.ConversionNum,
			ConversionNumByImpression7D:       req.ConversionNumByImpression7D,
			ConversionNumCost:                 req.ConversionNumCost,
			ConversionRatio:                   req.ConversionRatio,
			ConversionRatioByImpression7D:     req.ConversionRatioByImpression7D,
			LivePlayed3S:                      req.LivePlayed3S,
			PlayedEnd:                         req.PlayedEnd,
			PlayedFiveSeconds:                 req.PlayedFiveSeconds,
			PlayedThreeSeconds:                req.PlayedThreeSeconds,
			AdScene:                           req.AdScene,
			PlacementType:                     req.PlacementType,
			CancelFollow:                      req.CancelFollow,
			Play3SRatio:                       req.Play3SRatio,
			Play5SRatio:                       req.Play5SRatio,
			PlayEndRatio:                      req.PlayEndRatio,
			DirectSubmit1DCost:                req.DirectSubmit1DCost,
			MinigameIaaPurchaseAmountFirstDay: req.MinigameIaaPurchaseAmountFirstDay,
			MinigameIaaPurchaseAmountThreeDayByConversion:    req.MinigameIaaPurchaseAmountThreeDayByConversion,
			MinigameIaaPurchaseAmountWeekByConversion:        req.MinigameIaaPurchaseAmountWeekByConversion,
			MinigameIaaPurchaseAmountFirstDayRoi:             req.MinigameIaaPurchaseAmountFirstDayRoi,
			MinigameIaaPurchaseAmountThreeDayByConversionRoi: req.MinigameIaaPurchaseAmountThreeDayByConversionRoi,
			MinigameIaaPurchaseAmountWeekByConversionRoi:     req.MinigameIaaPurchaseAmountWeekByConversionRoi,
			MinigameIaaPurchaseAmount:                        req.MinigameIaaPurchaseAmount,
			MinigameIaaPurchaseRoi:                           req.MinigameIaaPurchaseRoi,
			UnitId:                                           req.UnitId,
			EffectiveCustomerAcquisition7DCnt:                req.EffectiveCustomerAcquisition7DCnt,
			EffectiveCustomerAcquisition7DCost:               req.EffectiveCustomerAcquisition7DCost,
			EffectiveCustomerAcquisition7DRatio:              req.EffectiveCustomerAcquisition7DRatio,
			MmuEffectiveCustomerAcquisitionCnt:               req.MmuEffectiveCustomerAcquisitionCnt,
			MmuEffectiveCustomerAcquisition7DCnt:             req.MmuEffectiveCustomerAcquisition7DCnt,
			PlayedNum:                                        req.PlayedNum,
			LeadsSubmitCnt:                                   req.LeadsSubmitCnt,
			LeadsSubmitCntRatio:                              req.LeadsSubmitCntRatio,
			LeadsSubmitCost:                                  req.LeadsSubmitCost,
			PrivateMessageSentCnt:                            req.PrivateMessageSentCnt,
			PrivateMessageSentRatio:                          req.PrivateMessageSentRatio,
			PrivateMessageSentCost:                           req.PrivateMessageSentCost,
			EventFormSubmit:                                  req.EventFormSubmit,
			DirectSubmit1DCnt:                                req.DirectSubmit1DCnt,
			EventFormSubmitRatio:                             req.EventFormSubmitRatio,
			EventFormSubmitCost:                              req.EventFormSubmitCost,
			EventAudition:                                    req.EventAudition,
			EventAudition30DCnt:                              req.EventAudition30DCnt,
			EventAuditionCost:                                req.EventAuditionCost,
			AllLessonFinishCnt:                               req.AllLessonFinishCnt,
			AllLessonFinish30DCnt:                            req.AllLessonFinish30DCnt,
			HighPriceClassPayCnt:                             req.HighPriceClassPayCnt,
			HighPriceClassPay30DCnt:                          req.HighPriceClassPay30DCnt,
			CampaignId:                                       req.CampaignId,
			CampaignName:                                     req.CampaignName,
			UnitName:                                         req.UnitName,
			PhotoId:                                          req.PhotoId,
			PhotoUrl:                                         req.PhotoUrl,
			CoverUrl:                                         req.CoverUrl,
			ImageToken:                                       req.ImageToken,
			Description:                                      req.Description,
			PicId:                                            req.PicId,
			PhotoMd5:                                         req.PhotoMd5,
			UnitType:                                         req.UnitType,
		})
		liberr.ErrIsNil(ctx, err, "修改失败")
	})
	return
}

func (s *sKsAdvertiserProgramCreativeReport) Delete(ctx context.Context, creativeIds []int64) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.KsAdvertiserProgramCreativeReport.Ctx(ctx).Delete(dao.KsAdvertiserProgramCreativeReport.Columns().StatDate+" in (?)", creativeIds)
		liberr.ErrIsNil(ctx, err, "删除失败")
	})
	return
}
