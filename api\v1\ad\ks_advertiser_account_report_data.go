// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2025-08-12 10:44:32
// 生成路径: api/v1/ad/ks_advertiser_account_report_data.go
// 生成人：cq
// desc:快手账户报表数据相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package ad

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
)

// KsAdvertiserAccountReportDataSearchReq 分页请求参数
type KsAdvertiserAccountReportDataSearchReq struct {
	g.Meta `path:"/list" tags:"快手账户报表数据" method:"post" summary:"快手账户报表数据列表"`
	commonApi.Author
	model.KsAdvertiserAccountReportDataSearchReq
}

// KsAdvertiserAccountReportDataSearchRes 列表返回结果
type KsAdvertiserAccountReportDataSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.KsAdvertiserAccountReportDataSearchRes
}

// KsAdvertiserAccountReportDataAddReq 添加操作请求参数
type KsAdvertiserAccountReportDataAddReq struct {
	g.Meta `path:"/add" tags:"快手账户报表数据" method:"post" summary:"快手账户报表数据添加"`
	commonApi.Author
	*model.KsAdvertiserAccountReportDataAddReq
}

// KsAdvertiserAccountReportDataAddRes 添加操作返回结果
type KsAdvertiserAccountReportDataAddRes struct {
	commonApi.EmptyRes
}

// KsAdvertiserAccountReportDataTaskReq 分页请求参数
type KsAdvertiserAccountReportDataTaskReq struct {
	g.Meta `path:"/task" tags:"快手账户报表数据" method:"post" summary:"快手账户报表数据任务"`
	commonApi.Author
	model.KsAdvertiserAccountReportDataSearchReq
}

// KsAdvertiserAccountReportDataTaskRes 列表返回结果
type KsAdvertiserAccountReportDataTaskRes struct {
	g.Meta `mime:"application/json"`
}
