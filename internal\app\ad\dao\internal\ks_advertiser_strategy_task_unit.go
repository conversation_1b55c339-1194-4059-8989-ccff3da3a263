// ==========================================================================
// GFast自动生成dao internal操作代码。
// 生成日期：2025-08-23 17:40:24
// 生成路径: internal/app/ad/dao/internal/ks_advertiser_strategy_task_unit.go
// 生成人：cyao
// desc:快手广告搭建-任务-广告组
// company:云南奇讯科技有限公司
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// KsAdvertiserStrategyTaskUnitDao is the manager for logic model data accessing and custom defined data operations functions management.
type KsAdvertiserStrategyTaskUnitDao struct {
	table   string                              // Table is the underlying table name of the DAO.
	group   string                              // Group is the database configuration group name of current DAO.
	columns KsAdvertiserStrategyTaskUnitColumns // Columns is the short type for Columns, which contains all the column names of Table for convenient usage.
}

// KsAdvertiserStrategyTaskUnitColumns defines and stores column names for table ks_advertiser_strategy_task_unit.
type KsAdvertiserStrategyTaskUnitColumns struct {
	TaskUnitId     string // 任务广告组ID
	UnitId         string // 单元 ID
	UnitName       string // 广告组名称
	TaskId         string // 任务ID
	TaskCampaignId string // 任务计划ID
	AdvertiserId   string // 广告主ID
	AdvertiserNick string // 广告主名称
	ExternalAction string // 优化目标
	ErrMsg         string // 失败原因
	Status         string // 项目状态 等待提交：INIT 已终止：TERMINATED 成功：SUCCESS 失败：ERROR
	UnitData       string // 创建项目数据
	CreatedAt      string // 创建时间
	UpdatedAt      string // 更新时间
	DeletedAt      string // 删除时间
}

var ksAdvertiserStrategyTaskUnitColumns = KsAdvertiserStrategyTaskUnitColumns{
	TaskUnitId:     "task_unit_id",
	UnitId:         "unit_id",
	UnitName:       "unit_name",
	TaskId:         "task_id",
	TaskCampaignId: "task_campaign_id",
	AdvertiserId:   "advertiser_id",
	AdvertiserNick: "advertiser_nick",
	ExternalAction: "external_action",
	ErrMsg:         "err_msg",
	Status:         "status",
	UnitData:       "unit_data",
	CreatedAt:      "created_at",
	UpdatedAt:      "updated_at",
	DeletedAt:      "deleted_at",
}

// NewKsAdvertiserStrategyTaskUnitDao creates and returns a new DAO object for table data access.
func NewKsAdvertiserStrategyTaskUnitDao() *KsAdvertiserStrategyTaskUnitDao {
	return &KsAdvertiserStrategyTaskUnitDao{
		group:   "default",
		table:   "ks_advertiser_strategy_task_unit",
		columns: ksAdvertiserStrategyTaskUnitColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *KsAdvertiserStrategyTaskUnitDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *KsAdvertiserStrategyTaskUnitDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *KsAdvertiserStrategyTaskUnitDao) Columns() KsAdvertiserStrategyTaskUnitColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *KsAdvertiserStrategyTaskUnitDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *KsAdvertiserStrategyTaskUnitDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *KsAdvertiserStrategyTaskUnitDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
