// ==========================================================================
// GFast自动生成model操作代码。
// 生成日期：2025-08-23 17:40:24
// 生成路径: internal/app/ad/model/ks_advertiser_strategy_task_unit.go
// 生成人：cyao
// desc:快手广告搭建-任务-广告组
// company:云南奇讯科技有限公司
// ==========================================================================

package model

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
	comModel "github.com/tiger1103/gfast/v3/internal/app/common/model"
)

// KsAdvertiserStrategyTaskUnitInfoRes is the golang structure for table ks_advertiser_strategy_task_unit.
type KsAdvertiserStrategyTaskUnitInfoRes struct {
	gmeta.Meta     `orm:"table:ks_advertiser_strategy_task_unit"`
	TaskUnitId     string      `orm:"task_unit_id,primary" json:"taskUnitId" dc:"任务广告组ID"`                         // 任务广告组ID
	UnitId         int64       `orm:"unit_id" json:"unitId" dc:"单元 ID"`                                            // 单元 ID
	UnitName       string      `orm:"unit_name" json:"unitName" dc:"广告组名称"`                                        // 广告组名称
	TaskId         string      `orm:"task_id" json:"taskId" dc:"任务ID"`                                             // 任务ID
	TaskCampaignId string      `orm:"task_campaign_id" json:"taskCampaignId" dc:"任务计划ID"`                          // 任务计划ID
	AdvertiserId   string      `orm:"advertiser_id" json:"advertiserId" dc:"广告主ID"`                                // 广告主ID
	AdvertiserNick string      `orm:"advertiser_nick" json:"advertiserNick" dc:"广告主名称"`                            // 广告主名称
	ExternalAction string      `orm:"external_action" json:"externalAction" dc:"优化目标"`                             // 优化目标
	ErrMsg         string      `orm:"err_msg" json:"errMsg" dc:"失败原因"`                                             // 失败原因
	Status         string      `orm:"status" json:"status" dc:"项目状态 等待提交：INIT 已终止：TERMINATED 成功：SUCCESS 失败：ERROR"` // 项目状态 等待提交：INIT 已终止：TERMINATED 成功：SUCCESS 失败：ERROR
	UnitData       string      `orm:"unit_data" json:"unitData" dc:"创建项目数据"`                                       // 创建项目数据
	CreatedAt      *gtime.Time `orm:"created_at" json:"createdAt" dc:"创建时间"`                                       // 创建时间
	UpdatedAt      *gtime.Time `orm:"updated_at" json:"updatedAt" dc:"更新时间"`                                       // 更新时间
	DeletedAt      *gtime.Time `orm:"deleted_at" json:"deletedAt" dc:"删除时间"`                                       // 删除时间
}

type KsAdvertiserStrategyTaskUnitListRes struct {
	TaskUnitId     string      `json:"taskUnitId" dc:"任务广告组ID"`
	UnitId         int64       `json:"unitId" dc:"单元 ID"`
	UnitName       string      `json:"unitName" dc:"广告组名称"`
	TaskId         string      `json:"taskId" dc:"任务ID"`
	TaskCampaignId string      `json:"taskCampaignId" dc:"任务计划ID"`
	AdvertiserId   string      `json:"advertiserId" dc:"广告主ID"`
	AdvertiserNick string      `json:"advertiserNick" dc:"广告主名称"`
	ExternalAction string      `json:"externalAction" dc:"优化目标"`
	ErrMsg         string      `json:"errMsg" dc:"失败原因"`
	Status         string      `json:"status" dc:"项目状态 等待提交：INIT 已终止：TERMINATED 成功：SUCCESS 失败：ERROR"`
	UnitData       string      `json:"unitData" dc:"创建项目数据"`
	CreatedAt      *gtime.Time `json:"createdAt" dc:"创建时间"`
}

// KsAdvertiserStrategyTaskUnitSearchReq 分页请求参数
type KsAdvertiserStrategyTaskUnitSearchReq struct {
	comModel.PageReq
	TaskUnitId     string `p:"taskUnitId" dc:"任务广告组ID"`                                                //任务广告组ID
	UnitId         string `p:"unitId" v:"unitId@integer#单元 ID需为整数" dc:"单元 ID"`                         //单元 ID
	UnitName       string `p:"unitName" dc:"广告组名称"`                                                    //广告组名称
	TaskId         string `p:"taskId" dc:"任务ID"`                                                       //任务ID
	TaskCampaignId string `p:"taskCampaignId" dc:"任务计划ID"`                                             //任务计划ID
	AdvertiserId   string `p:"advertiserId" dc:"广告主ID"`                                                //广告主ID
	AdvertiserNick string `p:"advertiserNick" dc:"广告主名称"`                                              //广告主名称
	ExternalAction string `p:"externalAction" dc:"优化目标"`                                               //优化目标
	ErrMsg         string `p:"errMsg" dc:"失败原因"`                                                       //失败原因
	Status         string `p:"status" dc:"项目状态 等待提交：INIT 已终止：TERMINATED 成功：SUCCESS 失败：ERROR"`          //项目状态 等待提交：INIT 已终止：TERMINATED 成功：SUCCESS 失败：ERROR
	UnitData       string `p:"unitData" dc:"创建项目数据"`                                                   //创建项目数据
	CreatedAt      string `p:"createdAt" v:"createdAt@datetime#创建时间需为YYYY-MM-DD hh:mm:ss格式" dc:"创建时间"` //创建时间
}

// KsAdvertiserStrategyTaskUnitSearchRes 列表返回结果
type KsAdvertiserStrategyTaskUnitSearchRes struct {
	comModel.ListRes
	List []*KsAdvertiserStrategyTaskUnitListRes `json:"list"`
}

// KsAdvertiserStrategyTaskUnitAddReq 添加操作请求参数
type KsAdvertiserStrategyTaskUnitAddReq struct {
	TaskUnitId     string `p:"taskUnitId" v:"required#主键ID不能为空" dc:"任务广告组ID"`
	UnitId         int64  `p:"unitId"  dc:"单元 ID"`
	UnitName       string `p:"unitName" v:"required#广告组名称不能为空" dc:"广告组名称"`
	TaskId         string `p:"taskId"  dc:"任务ID"`
	TaskCampaignId string `p:"taskCampaignId" v:"required#任务计划ID不能为空" dc:"任务计划ID"`
	AdvertiserId   string `p:"advertiserId"  dc:"广告主ID"`
	AdvertiserNick string `p:"advertiserNick"  dc:"广告主名称"`
	ExternalAction string `p:"externalAction"  dc:"优化目标"`
	ErrMsg         string `p:"errMsg"  dc:"失败原因"`
	Status         string `p:"status" v:"required#项目状态 等待提交：INIT 已终止：TERMINATED 成功：SUCCESS 失败：ERROR不能为空" dc:"项目状态 等待提交：INIT 已终止：TERMINATED 成功：SUCCESS 失败：ERROR"`
	UnitData       string `p:"unitData"  dc:"创建项目数据"`
}

// KsAdvertiserStrategyTaskUnitEditReq 修改操作请求参数
type KsAdvertiserStrategyTaskUnitEditReq struct {
	TaskUnitId     string `p:"taskUnitId" v:"required#主键ID不能为空" dc:"任务广告组ID"`
	UnitId         int64  `p:"unitId"  dc:"单元 ID"`
	UnitName       string `p:"unitName" v:"required#广告组名称不能为空" dc:"广告组名称"`
	TaskId         string `p:"taskId"  dc:"任务ID"`
	TaskCampaignId string `p:"taskCampaignId" v:"required#任务计划ID不能为空" dc:"任务计划ID"`
	AdvertiserId   string `p:"advertiserId"  dc:"广告主ID"`
	AdvertiserNick string `p:"advertiserNick"  dc:"广告主名称"`
	ExternalAction string `p:"externalAction"  dc:"优化目标"`
	ErrMsg         string `p:"errMsg"  dc:"失败原因"`
	Status         string `p:"status" v:"required#项目状态 等待提交：INIT 已终止：TERMINATED 成功：SUCCESS 失败：ERROR不能为空" dc:"项目状态 等待提交：INIT 已终止：TERMINATED 成功：SUCCESS 失败：ERROR"`
	UnitData       string `p:"unitData"  dc:"创建项目数据"`
}
