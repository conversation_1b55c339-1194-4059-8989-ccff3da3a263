package generate

import (
	"context"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
)

// 广告分组规则：按定向包分组

type GroupByAudiencePackage struct{}

func (s *GroupByAudiencePackage) GetEstimateInfo(ctx context.Context, req *model.KsAdvertiserStrategyGenerateReq) (res map[int64]*model.EstimateInfo, err error) {
	err = g.Try(ctx, func(ctx context.Context) {

	})
	return
}

func (s *GroupByAudiencePackage) GeneratePreviewBaseInfo(
	ctx context.Context,
	req *model.KsAdvertiserStrategyGenerateReq,
	estimateInfoMap map[int64]*model.EstimateInfo) (res []*model.PreviewBaseInfo, err error) {
	err = g.Try(ctx, func(ctx context.Context) {

	})
	return
}
