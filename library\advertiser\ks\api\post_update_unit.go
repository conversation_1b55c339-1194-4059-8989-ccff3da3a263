package api

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
)

// UpdateUnitService 修改广告计划
type UpdateUnitService struct {
	ctx     context.Context
	cfg     *Configuration
	token   string
	Request *UpdateUnitReq
}

// UpdateUnitReq 广告组更新请求结构体
type UpdateUnitReq struct {
	SearchPopulationRetargeting *int                      `json:"search_population_retargeting,omitempty"` // 搜索人群追投开关
	AdvertiserId                int64                     `json:"advertiser_id"`                           // 广告主id
	ImMessageMount              *bool                     `json:"im_message_mount,omitempty"`              // 是否挂载私信收集组件
	SeriesPayTemplateId         *int64                    `json:"series_pay_template_id,omitempty"`        // 付费模板
	SeriesPayMode               *int                      `json:"series_pay_mode,omitempty"`               // 付费模式
	AdType                      *int                      `json:"ad_type,omitempty"`                       // (描述不详)
	AdvCardList                 []int64                   `json:"adv_card_list,omitempty"`                 // 绑定卡片 id
	AdvCardOption               *int                      `json:"adv_card_option,omitempty"`               // 高级创意开关
	AppDownloadType             *int                      `json:"app_download_type,omitempty"`             // 应用下载方式
	AppIconUrl                  *string                   `json:"app_icon_url,omitempty"`                  // (描述不详)
	AppId                       *int64                    `json:"app_id,omitempty"`                        // 应用 ID
	AppStore                    []string                  `json:"app_store,omitempty"`                     // 应用商店列表
	AssetMining                 *bool                     `json:"asset_mining,omitempty"`                  // 程序化创意 2.0 素材挖掘
	AutoCreatePhoto             *bool                     `json:"auto_create_photo,omitempty"`             // (描述不详)
	BeginTime                   *string                   `json:"begin_time,omitempty"`                    // 投放开始时间 (yyyy-MM-dd)
	EndTime                     *string                   `json:"end_time,omitempty"`                      // 投放结束时间 (yyyy-MM-dd)
	EnhanceConversionType       *int                      `json:"enhance_conversion_type,omitempty"`       // (描述不详)
	ExtendSearch                *bool                     `json:"extend_search,omitempty"`                 // 搜索广告智能扩词开启状态
	GroupId                     *int64                    `json:"group_id,omitempty"`                      // 程序化落地页ID
	JingleBellId                *int64                    `json:"jingle_bell_id,omitempty"`                // 小铃铛组件id
	LiveUserId                  *int64                    `json:"live_user_id,omitempty"`                  // 主播id
	NegativeWordParam           *MapiNegativeWordSnake    `json:"negative_word_param,omitempty"`           // 搜索否词
	OcpxActionType              *int                      `json:"ocpx_action_type,omitempty"`              // (描述不详)
	OuterLoopNative             *int                      `json:"outer_loop_native,omitempty"`             // (描述不详)
	PlayButton                  *string                   `json:"play_button,omitempty"`                   // 试玩按钮文字内容
	PlayableId                  *int64                    `json:"playable_id,omitempty"`                   // 试玩 ID
	PlayableOrientation         *int                      `json:"playable_orientation,omitempty"`          // (描述不详)
	PlayableSwitch              *int                      `json:"playable_switch,omitempty"`               // (描述不详)
	PlayableUrl                 *string                   `json:"playable_url,omitempty"`                  // (描述不详)
	PutStatus                   *int64                    `json:"put_status,omitempty"`                    // 广告组的投放状态
	QuickSearch                 *int                      `json:"quick_search,omitempty"`                  // 搜索快投开关
	RoiRatio                    *float64                  `json:"roi_ratio,omitempty"`                     // 付费 ROI 系数
	SceneId                     []string                  `json:"scene_id,omitempty"`                      // 资源位置
	ScheduleTime                *string                   `json:"schedule_time,omitempty"`                 // 投放时间段
	SchemaId                    *string                   `json:"schema_id,omitempty"`                     // 微信小程序ID
	SchemaUri                   *string                   `json:"schema_uri,omitempty"`                    // 调起链接
	ShowMode                    *int                      `json:"show_mode,omitempty"`                     // 创意展现方式
	SiteType                    *int                      `json:"site_type,omitempty"`                     // 预约广告
	SmartBid                    *int                      `json:"smart_bid,omitempty"`                     // (描述不详)
	SmartCover                  *bool                     `json:"smart_cover,omitempty"`                   // 程序化创意 2.0 智能抽帧
	Speed                       *int                      `json:"speed,omitempty"`                         // (描述不详)
	SplashAdSwitch              *bool                     `json:"splash_ad_switch,omitempty"`              // (描述不详)
	Target                      *MapiTargetDetail479Snake `json:"target,omitempty"`                        // 定向数据
	TargetExplore               *int                      `json:"target_explore,omitempty"`                // 搜索人群探索
	DistanceShow                []DistanceShowSnake       `json:"distance_show,omitempty"`                 // 新商圈
	Bid                         *int64                    `json:"bid,omitempty"`                           // 出价 (单位: 厘)
	BidType                     *int                      `json:"bid_type"`                                // 优化目标出价类型
	CampaignId                  *int64                    `json:"campaign_id,omitempty"`                   // (描述不详)
	CardType                    *int                      `json:"card_type,omitempty"`                     // (描述不详)
	ComponentId                 *int64                    `json:"component_id,omitempty"`                  // (描述不详)
	ConsultId                   *int64                    `json:"consult_id,omitempty"`                    // 咨询组件 id
	ConversionType              *int                      `json:"conversion_type,omitempty"`               // 转化途径
	ConvertId                   *int64                    `json:"convert_id,omitempty"`                    // (描述不详)
	CpaBid                      *int64                    `json:"cpa_bid,omitempty"`                       // 出价 (单位: 厘)
	CustomMiniAppData           *CustomMiniAppInfoSnake   `json:"custom_mini_app_data,omitempty"`          // 推广小程序信息
	DayBudget                   *int64                    `json:"day_budget,omitempty"`                    // 分日预算 (单位: 厘)
	DayBudgetSchedule           []string                  `json:"day_budget_schedule,omitempty"`           // 分日预算 (单位: 厘)
	DeepConversionBid           *int64                    `json:"deep_conversion_bid,omitempty"`           // 深度转化出价 (单位: 厘)
	DeepConversionType          *int64                    `json:"deep_conversion_type,omitempty"`          // 深度转化目标
	DownloadPageUrl             *string                   `json:"download_page_url,omitempty"`             // 自定义落地页URL
	DpaUnitParam                *MapiDpaUnitReqSnake      `json:"dpa_unit_param,omitempty"`                // DPA 商品信息
	TemplateId                  *int64                    `json:"template_id,omitempty"`                   // 定向模板 id
	UnitId                      int64                     `json:"unit_id"`                                 // 广告组 ID
	UnitName                    string                    `json:"unit_name"`                               // 广告组名称
	UnitType                    *int                      `json:"unit_type,omitempty"`                     // (描述不详)
	Url                         *string                   `json:"url,omitempty"`                           // 投放链接
	UrlType                     *int                      `json:"url_type,omitempty"`                      // url 类型
	UseAppMarket                *int                      `json:"use_app_market,omitempty"`                // 优先从系统应用商店下载
	VideoLandingPage            *bool                     `json:"video_landing_page,omitempty"`            // (描述不详)
	WebUriType                  *int                      `json:"web_uri_type,omitempty"`                  // url 类型
	PackageId                   *int64                    `json:"package_id,omitempty"`                    // 应用包ID
	ULink                       *string                   `json:"u_link,omitempty"`                        // ios系统的ulink链接
	SeriesPayTemplateIdMulti    []int64                   `json:"series_pay_template_id_multi,omitempty"`  // 短剧付费模版列表
}

// IntegerRange573 自定义年龄段
type IntegerRange573 struct {
	Min *int `json:"min,omitempty"` // 年龄最小限制
	Max *int `json:"max,omitempty"` // 年龄最大限制
}

// Keyword 行为/兴趣定向关键词
type Keyword struct {
	Id   int64  `json:"id"`   // 关键词 id
	Name string `json:"name"` // 关键词名称
}

// Behavior482Snake 行为定向
type Behavior482Snake struct {
	Keyword      []Keyword `json:"keyword,omitempty"`       // 行为定向关键词
	Label        []string  `json:"label,omitempty"`         // 行为定向 类目词
	SceneType    []string  `json:"scene_type,omitempty"`    // 行为场景
	StrengthType *int      `json:"strength_type,omitempty"` // 行为强度
	TimeType     *int      `json:"time_type,omitempty"`     // 在多少天内发生行为的用户
}

// Interest708Snake 兴趣定向
type Interest708Snake struct {
	Keyword      []Keyword `json:"keyword,omitempty"`       // 行为定向关键词
	Label        []string  `json:"label,omitempty"`         // 行为定向 类目词
	StrengthType *int      `json:"strength_type,omitempty"` // 兴趣标签强度
}

// BehaviorInterest765Snake 行为兴趣定向 (旧标签体系)
type BehaviorInterest765Snake struct {
	Behavior *Behavior482Snake `json:"behavior,omitempty"` // 行为定向
	Interest *Interest708Snake `json:"interest,omitempty"` // 兴趣定向
}

// CelebrityFansStar118Snake 快手网红粉丝/分类
type CelebrityFansStar118Snake struct {
	Category []string `json:"category,omitempty"` // 网红分类 (type=2时有效)
	Id       string   `json:"id"`                 // 分类ID或网红author_id
	Name     string   `json:"name"`               // 分类名称或网红kwai_id
	Type     int      `json:"type"`               // 1:网红分类, 2:快手网红
}

// MapiCelebrity280Snake 快手网红定向
type MapiCelebrity280Snake struct {
	Behaviors []string                    `json:"behaviors"`  // 行为类型 (0:关注, 1:视频互动, 2:直播互动)
	FansStars []CelebrityFansStar118Snake `json:"fans_stars"` // 网红分类或快手网红
}

// DistanceShowSnake 新商圈定向
type DistanceShowSnake struct {
	Address      *string `json:"address,omitempty"`
	Lng          *string `json:"lng,omitempty"`
	Lat          *string `json:"lat,omitempty"`
	Radius       *int64  `json:"radius,omitempty"`
	LocationName *string `json:"location_name,omitempty"`
	PoiId        *string `json:"poi_id,omitempty"`
}

// BehaviorInterestParamShowSnake 行为兴趣（升级版）
type BehaviorInterestParamShowSnake struct {
	Keywords       []string `json:"keywords,omitempty"`        // 关键词ID
	Categories     []string `json:"categories,omitempty"`      // 类目ID
	CustomBehavior *int     `json:"custom_behavior,omitempty"` // 自定义行为意向 0：关闭，1：开启
	SceneTypes     []int    `json:"scene_types,omitempty"`     // 场景类型 1：视频；2：APP；4：广告；custom_behaivor为1时必填，不能为空列表。
	TimeType       *int     `json:"time_type,omitempty"`       // 时间范围 0:7 天；1：15 天；2：30 天；3：90 天；4：180 天；5：60天；custom_behaivor为1时必填，不能null
}

// MapiTargetDetail479Snake 定向数据
type MapiTargetDetail479Snake struct {
	Age                       *IntegerRange573                `json:"age,omitempty"`                          // 自定义年龄段
	AgesRange                 []string                        `json:"ages_range,omitempty"`                   // 固定年龄段
	AndroidOsv                *int                            `json:"android_osv,omitempty"`                  // Android 版本
	AppIds                    []string                        `json:"app_ids,omitempty"`                      // APP 行为-按 APP 名称
	AppInterestIds            []string                        `json:"app_interest_ids,omitempty"`             // APP 行为-按分类 (新标签体系)
	AppNames                  []string                        `json:"app_names,omitempty"`                    // (描述不详)
	BehaviorInterest          *BehaviorInterest765Snake       `json:"behavior_interest,omitempty"`            // 行为兴趣定向 (旧)
	BehaviorInterestParamShow *BehaviorInterestParamShowSnake `json:"behavior_interest_param_show,omitempty"` // 行为兴趣（升级版）
	BehaviorType              *int                            `json:"behavior_type,omitempty"`                // 行为兴趣类型
	BusinessInterest          []string                        `json:"business_interest,omitempty"`            // (描述不详)
	Celebrity                 *MapiCelebrity280Snake          `json:"celebrity,omitempty"`                    // 快手网红
	DeviceBrand               []string                        `json:"device_brand,omitempty"`                 // (描述不详)
	DeviceBrandIds            []string                        `json:"device_brand_ids,omitempty"`             // (描述不详)
	DevicePrice               []string                        `json:"device_price,omitempty"`                 // 设备价格
	DisableInstalledAppSwitch *int                            `json:"disable_installed_app_switch,omitempty"` // 过滤已安装人群维度
	DistrictIds               []string                        `json:"district_ids,omitempty"`                 // 商圈定向
	ExcludeMedia              []string                        `json:"exclude_media,omitempty"`                // 媒体定向排除包
	ExcludePopulation         []string                        `json:"exclude_population,omitempty"`           // 人群包排除
	FilterConvertedLevel      *int                            `json:"filter_converted_level,omitempty"`       // 过滤已转化人群纬度
	FilterConvertedWechatId   []string                        `json:"filter_converted_wechat_id,omitempty"`   // 过滤已转化-企微号ID
	FilterTimeRange           *int                            `json:"filter_time_range,omitempty"`            // 用户的转化时间范围
	Gender                    *int                            `json:"gender,omitempty"`                       // 性别
	IntelliExtendOption       *int                            `json:"intelli_extend_option,omitempty"`        // 智能定向开关
	IosOsv                    *int                            `json:"ios_osv,omitempty"`                      // IOS 版本
	Media                     []string                        `json:"media,omitempty"`                        // 媒体定向包
	MediaSourceType           *int                            `json:"media_source_type,omitempty"`            // 媒体包来源
	Network                   *int                            `json:"network,omitempty"`                      // 网络环境
	PaidAudience              []string                        `json:"paid_audience,omitempty"`                // 付费人群包 id
	PlatformOs                *int                            `json:"platform_os,omitempty"`                  // 操作系统
	Population                []string                        `json:"population,omitempty"`                   // 人群包定向
	Region                    []string                        `json:"region,omitempty"`                       // 地域
	SeedPopulation            []string                        `json:"seed_population,omitempty"`              // 种子人群包
	TargetSource              *int                            `json:"target_source,omitempty"`                // (描述不详)
	UserType                  *int                            `json:"user_type,omitempty"`                    // 用户类型
	IpType                    *int                            `json:"ip_type,omitempty"`                      // 地域IP
	AutoPopulation            *int                            `json:"auto_population,omitempty"`              // 智能人群包
	Operators                 []int                           `json:"operators,omitempty"`                    // 运营商
}

// MapiNegativeWordSnake 搜索否词
type MapiNegativeWordSnake struct {
	ExactWords  []string `json:"exact_words,omitempty"`  // 精确否定词
	PhraseWords []string `json:"phrase_words,omitempty"` // 短语否定词
}

// CustomMiniAppInfoSnake 推广小程序信息
type CustomMiniAppInfoSnake struct {
	BootstrapPage     *string `json:"bootstrap_page,omitempty"`       // 小程序启动页面
	BootstrapParams   *string `json:"bootstrap_params,omitempty"`     // 小程序启动参数
	MiniAppIdPlatform *string `json:"mini_app_id_platform,omitempty"` // 小程序APPID
}

// MapiDpaUnitReqSnake DPA商品信息
type MapiDpaUnitReqSnake struct {
	DetailUnitType           *int     `json:"detail_unit_type,omitempty"`
	DpaCategoryIds           []string `json:"dpa_category_ids,omitempty"`             // DPA商品类目集合
	DpaUnitActionbarClickUrl *string  `json:"dpa_unit_actionbar_click_url,omitempty"` // (描述不详)
	DpaUnitClickUrl          *string  `json:"dpa_unit_click_url,omitempty"`           // (描述不详)
	DpaUnitSubType           *int     `json:"dpa_unit_sub_type,omitempty"`            // 商品广告类型
	LibraryId                *int64   `json:"library_id,omitempty"`                   // 商品库ID
	OuterId                  *string  `json:"outer_id,omitempty"`                     // 外部商品ID
	ProductId                *string  `json:"product_id,omitempty"`                   // 快手商品ID
	UnitImpressionUrl        *string  `json:"unit_impression_url,omitempty"`          // (描述不详)
}

// UpdateUnitData API响应结构体
type UpdateUnitData struct {
	UnitId int64 `json:"unit_id"` // 广告组id
}

func (r *UpdateUnitService) SetCfg(cfg *Configuration) *UpdateUnitService {
	r.cfg = cfg
	return r
}

func (r *UpdateUnitService) SetReq(req UpdateUnitReq) *UpdateUnitService {
	r.Request = &req
	return r
}

func (r *UpdateUnitService) AccessToken(accessToken string) *UpdateUnitService {
	r.token = accessToken
	return r
}

func (r *UpdateUnitService) Do() (data *KsBaseResp[UpdateUnitData], err error) {
	localBasePath := r.cfg.BasePath
	localVarPath := localBasePath + "/rest/openapi/gw/dsp/unit/update"
	response, err := r.cfg.HTTPClient.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("Access-Token", r.token).
		SetBody(r.Request).
		SetResult(&KsBaseResp[UpdateUnitData]{}).
		Post(localVarPath)
	if err != nil {
		return nil, err
	}
	resp := new(KsBaseResp[UpdateUnitData])
	// 将 JSON 响应解码到结构体中
	err = json.Unmarshal(response.Body(), &resp)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("/rest/openapi/gw/dsp/unit/update解析响应出错: %v\n", err))
	}
	if resp.Code == 0 && resp.Data != nil {
		return resp, nil
	} else {
		return resp, errors.New(resp.Message)
	}
}
