// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2025-08-12 10:45:00
// 生成路径: api/v1/ad/ks_advertiser_campaign_report_data.go
// 生成人：cq
// desc:快手广告计划报表数据相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package ad

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
)

// KsAdvertiserCampaignReportDataSearchReq 分页请求参数
type KsAdvertiserCampaignReportDataSearchReq struct {
	g.Meta `path:"/list" tags:"快手广告计划报表数据" method:"post" summary:"快手广告计划报表数据列表"`
	commonApi.Author
	model.KsAdvertiserCampaignReportDataSearchReq
}

// KsAdvertiserCampaignReportDataSearchRes 列表返回结果
type KsAdvertiserCampaignReportDataSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.KsAdvertiserCampaignReportDataSearchRes
}

// KsAdvertiserCampaignReportDataAddReq 添加操作请求参数
type KsAdvertiserCampaignReportDataAddReq struct {
	g.Meta `path:"/add" tags:"快手广告计划报表数据" method:"post" summary:"快手广告计划报表数据添加"`
	commonApi.Author
	*model.KsAdvertiserCampaignReportDataAddReq
}

// KsAdvertiserCampaignReportDataAddRes 添加操作返回结果
type KsAdvertiserCampaignReportDataAddRes struct {
	commonApi.EmptyRes
}

// KsAdvertiserCampaignReportDataTaskReq 分页请求参数
type KsAdvertiserCampaignReportDataTaskReq struct {
	g.Meta `path:"/task" tags:"快手广告计划报表数据" method:"post" summary:"快手广告计划报表数据任务"`
	commonApi.Author
	model.KsAdvertiserCampaignReportDataSearchReq
}

// KsAdvertiserCampaignReportDataTaskRes 列表返回结果
type KsAdvertiserCampaignReportDataTaskRes struct {
	g.Meta `mime:"application/json"`
}
