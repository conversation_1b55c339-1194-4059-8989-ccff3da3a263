// ==========================================================================
// GFast自动生成model操作代码。
// 生成日期：2025-08-22 11:52:17
// 生成路径: internal/app/ad/model/ks_advertiser_strategy_creative.go
// 生成人：cq
// desc:快手策略组-广告创意
// company:云南奇讯科技有限公司
// ==========================================================================

package model

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
	comModel "github.com/tiger1103/gfast/v3/internal/app/common/model"
)

// KsAdvertiserStrategyCreativeInfoRes is the golang structure for table ks_advertiser_strategy_creative.
type KsAdvertiserStrategyCreativeInfoRes struct {
	gmeta.Meta       `orm:"table:ks_advertiser_strategy_creative"`
	Id               uint64      `orm:"id,primary" json:"id" dc:"主键ID"`                                                     // 主键ID
	StrategyId       string      `orm:"strategy_id" json:"strategyId" dc:"策略组ID"`                                           // 策略组ID
	TaskId           string      `orm:"task_id" json:"taskId" dc:"任务ID"`                                                    // 任务ID
	CreateMode       int         `orm:"create_mode" json:"createMode" dc:"创意制作方式 1：自动化创意 2：程序化3.0"`                         // 创意制作方式 1：自动化创意 2：程序化3.0
	ActionBarText    string      `orm:"action_bar_text" json:"actionBarText" dc:"行动号召"`                                     // 行动号召
	NewExposeTag     []string    `orm:"new_expose_tag" json:"newExposeTag" dc:"推荐理由（选填）"`                                   // 推荐理由（选填）
	OuterLoopNative  int         `orm:"outer_loop_native" json:"outerLoopNative" dc:"是否开启原生 1开启、0关闭｜不填则默认为0"`               // 是否开启原生 1开启、0关闭｜不填则默认为0
	KolUserId        int64       `orm:"kol_user_id" json:"kolUserId" dc:"达人id 开启原生场景下必传，计划 campaignType=30 短剧推广时，值为短剧作者ID"` // 达人id 开启原生场景下必传，计划 campaignType=30 短剧推广时，值为短剧作者ID
	KolUserType      int         `orm:"kol_user_type" json:"kolUserType" dc:"达人类型 1普通快手号原生，2服务号原生，3聚星达人（需加白使用）"`            // 达人类型 1普通快手号原生，2服务号原生，3聚星达人（需加白使用）
	CreativeCategory int         `orm:"creative_category" json:"creativeCategory" dc:"创意分类"`                                // 创意分类
	CreativeTag      []string    `orm:"creative_tag" json:"creativeTag" dc:"创意标签"`                                          // 创意标签
	CreativeName     string      `orm:"creative_name" json:"creativeName" dc:"创意名称"`                                        // 创意名称
	TrackUrlSwitch   int         `orm:"track_url_switch" json:"trackUrlSwitch" dc:"监测链接开关 1:开启，0:关闭"`                       // 监测链接开关 1:开启，0:关闭
	CreatedAt        *gtime.Time `orm:"created_at" json:"createdAt" dc:"创建时间"`                                              // 创建时间
	UpdatedAt        *gtime.Time `orm:"updated_at" json:"updatedAt" dc:"更新时间"`                                              // 更新时间
	DeletedAt        *gtime.Time `orm:"deleted_at" json:"deletedAt" dc:"删除时间"`                                              // 删除时间
}

type KsAdvertiserStrategyCreativeListRes struct {
	Id               uint64      `json:"id" dc:"主键ID"`
	StrategyId       string      `json:"strategyId" dc:"策略组ID"`
	TaskId           string      `json:"taskId" dc:"任务ID"`
	CreateMode       int         `json:"createMode" dc:"创意制作方式 1：自动化创意 2：程序化3.0"`
	ActionBarText    string      `json:"actionBarText" dc:"行动号召"`
	NewExposeTag     []string    `json:"newExposeTag" dc:"推荐理由（选填）"`
	OuterLoopNative  int         `json:"outerLoopNative" dc:"是否开启原生 1开启、0关闭｜不填则默认为0"`
	KolUserId        int64       `json:"kolUserId" dc:"达人id 开启原生场景下必传，计划 campaignType=30 短剧推广时，值为短剧作者ID"`
	KolUserType      int         `json:"kolUserType" dc:"达人类型 1普通快手号原生，2服务号原生，3聚星达人（需加白使用）"`
	CreativeCategory int         `json:"creativeCategory" dc:"创意分类"`
	CreativeTag      []string    `json:"creativeTag" dc:"创意标签"`
	CreativeName     string      `json:"creativeName" dc:"创意名称"`
	TrackUrlSwitch   int         `json:"trackUrlSwitch" dc:"监测链接开关 1:开启，0:关闭"`
	CreatedAt        *gtime.Time `json:"createdAt" dc:"创建时间"`
}

// KsAdvertiserStrategyCreativeSearchReq 分页请求参数
type KsAdvertiserStrategyCreativeSearchReq struct {
	comModel.PageReq
	Id               string   `p:"id" dc:"主键ID"`                                                                                                                               //主键ID
	StrategyId       string   `p:"strategyId" dc:"策略组ID"`                                                                                                                      //策略组ID
	TaskId           string   `p:"taskId" dc:"任务ID"`                                                                                                                           //任务ID
	CreateMode       string   `p:"createMode" v:"createMode@integer#创意制作方式 1：自动化创意 2：程序化3.0需为整数" dc:"创意制作方式 1：自动化创意 2：程序化3.0"`                                                 //创意制作方式 1：自动化创意 2：程序化3.0
	ActionBarText    string   `p:"actionBarText" dc:"行动号召"`                                                                                                                    //行动号召
	NewExposeTag     []string `p:"newExposeTag" dc:"推荐理由（选填）"`                                                                                                                 //推荐理由（选填）
	OuterLoopNative  string   `p:"outerLoopNative" v:"outerLoopNative@integer#是否开启原生 1开启、0关闭｜不填则默认为0需为整数" dc:"是否开启原生 1开启、0关闭｜不填则默认为0"`                                         //是否开启原生 1开启、0关闭｜不填则默认为0
	KolUserId        string   `p:"kolUserId" v:"kolUserId@integer#达人id 开启原生场景下必传，计划 campaignType=30 短剧推广时，值为短剧作者ID需为整数" dc:"达人id 开启原生场景下必传，计划 campaignType=30 短剧推广时，值为短剧作者ID"` //达人id 开启原生场景下必传，计划 campaignType=30 短剧推广时，值为短剧作者ID
	KolUserType      string   `p:"kolUserType" v:"kolUserType@integer#达人类型 1普通快手号原生，2服务号原生，3聚星达人（需加白使用）需为整数" dc:"达人类型 1普通快手号原生，2服务号原生，3聚星达人（需加白使用）"`                           //达人类型 1普通快手号原生，2服务号原生，3聚星达人（需加白使用）
	CreativeCategory string   `p:"creativeCategory" v:"creativeCategory@integer#创意分类需为整数" dc:"创意分类"`                                                                           //创意分类
	CreativeTag      []string `p:"creativeTag" dc:"创意标签"`                                                                                                                      //创意标签
	CreativeName     string   `p:"creativeName" dc:"创意名称"`                                                                                                                     //创意名称
	TrackUrlSwitch   string   `p:"trackUrlSwitch" v:"trackUrlSwitch@integer#监测链接开关 1:开启，0:关闭需为整数" dc:"监测链接开关 1:开启，0:关闭"`                                                       //监测链接开关 1:开启，0:关闭
	CreatedAt        string   `p:"createdAt" v:"createdAt@datetime#创建时间需为YYYY-MM-DD hh:mm:ss格式" dc:"创建时间"`                                                                     //创建时间
}

// KsAdvertiserStrategyCreativeSearchRes 列表返回结果
type KsAdvertiserStrategyCreativeSearchRes struct {
	comModel.ListRes
	List []*KsAdvertiserStrategyCreativeListRes `json:"list"`
}

// KsAdvertiserStrategyCreativeAddReq 添加操作请求参数
type KsAdvertiserStrategyCreativeAddReq struct {
	StrategyId       string   `p:"-" json:"-"  dc:"策略组ID"`
	TaskId           string   `p:"-" json:"-"  dc:"任务ID"`
	CreateMode       int      `p:"createMode" json:"createMode"  dc:"创意制作方式 1：自动化创意 2：程序化3.0"`
	ActionBarText    string   `p:"actionBarText" json:"actionBarText"  dc:"行动号召"`
	NewExposeTag     []string `p:"newExposeTag" json:"newExposeTag"  dc:"推荐理由（选填）"`
	OuterLoopNative  int      `p:"outerLoopNative" json:"outerLoopNative"  dc:"是否开启原生 1开启、0关闭｜不填则默认为0"`
	KolUserId        int64    `p:"kolUserId" json:"kolUserId"  dc:"达人id 开启原生场景下必传，计划 campaignType=30 短剧推广时，值为短剧作者ID"`
	KolUserType      int      `p:"kolUserType" json:"kolUserType"  dc:"达人类型 1普通快手号原生，2服务号原生，3聚星达人（需加白使用）"`
	CreativeCategory int      `p:"creativeCategory" json:"creativeCategory"  dc:"创意分类"`
	CreativeTag      []string `p:"creativeTag" json:"creativeTag"  dc:"创意标签"`
	CreativeName     string   `p:"creativeName" json:"creativeName" dc:"创意名称"`
	TrackUrlSwitch   int      `p:"trackUrlSwitch" json:"trackUrlSwitch"  dc:"监测链接开关 1:开启，0:关闭"`
}

// KsAdvertiserStrategyCreativeEditReq 修改操作请求参数
type KsAdvertiserStrategyCreativeEditReq struct {
	StrategyId       string   `p:"strategyId"  dc:"策略组ID"`
	TaskId           string   `p:"taskId"  dc:"任务ID"`
	CreateMode       int      `p:"createMode"  dc:"创意制作方式 1：自动化创意 2：程序化3.0"`
	ActionBarText    string   `p:"actionBarText"  dc:"行动号召"`
	NewExposeTag     []string `p:"newExposeTag"  dc:"推荐理由（选填）"`
	OuterLoopNative  int      `p:"outerLoopNative"  dc:"是否开启原生 1开启、0关闭｜不填则默认为0"`
	KolUserId        int64    `p:"kolUserId"  dc:"达人id 开启原生场景下必传，计划 campaignType=30 短剧推广时，值为短剧作者ID"`
	KolUserType      int      `p:"kolUserType"  dc:"达人类型 1普通快手号原生，2服务号原生，3聚星达人（需加白使用）"`
	CreativeCategory int      `p:"creativeCategory"  dc:"创意分类"`
	CreativeTag      []string `p:"creativeTag"  dc:"创意标签"`
	CreativeName     string   `p:"creativeName" v:"required#创意名称不能为空" dc:"创意名称"`
	TrackUrlSwitch   int      `p:"trackUrlSwitch"  dc:"监测链接开关 1:开启，0:关闭"`
}
