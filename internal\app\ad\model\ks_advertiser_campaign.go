// ==========================================================================
// GFast自动生成model操作代码。
// 生成日期：2025-08-13 16:34:00
// 生成路径: internal/app/ad/model/ks_advertiser_campaign.go
// 生成人：cyao
// desc:快手广告计划
// company:云南奇讯科技有限公司
// ==========================================================================

package model

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
	comModel "github.com/tiger1103/gfast/v3/internal/app/common/model"
	ksApi "github.com/tiger1103/gfast/v3/library/advertiser/ks/api"
)

// KsAdvertiserCampaignInfoRes is the golang structure for table ks_advertiser_campaign.
type KsAdvertiserCampaignInfoRes struct {
	gmeta.Meta                     `orm:"table:ks_advertiser_campaign"`
	CampaignId                     int64       `orm:"campaign_id,primary" json:"campaignId" dc:"广告计划 ID"`                                         // 广告计划 ID
	AdvertiserId                   int         `orm:"advertiser_id" json:"advertiserId" dc:"广告id"`                                                // 广告id
	PhotoPackageDetails            string      `orm:"photo_package_details" json:"photoPackageDetails" dc:"图片包详情（可能为 null）"`                      // 图片包详情（可能为 null）
	AdType                         int         `orm:"ad_type" json:"adType" dc:"广告类型"`                                                            // 广告类型
	CampaignType                   int         `orm:"campaign_type" json:"campaignType" dc:"广告系列类型"`                                              // 广告系列类型
	CampaignDeepConversionType     int         `orm:"campaign_deep_conversion_type" json:"campaignDeepConversionType" dc:"深度转化类型"`                // 深度转化类型
	BidType                        int         `orm:"bid_type" json:"bidType" dc:"出价类型"`                                                          // 出价类型
	PutStatus                      int         `orm:"put_status" json:"putStatus" dc:"投放状态"`                                                      // 投放状态
	CampaignOcpxActionTypeName     string      `orm:"campaign_ocpx_action_type_name" json:"campaignOcpxActionTypeName" dc:"智投计划优化目标名称 可能为null"`   // 智投计划优化目标名称 可能为null
	CapRoiRatio                    float64     `orm:"cap_roi_ratio" json:"capRoiRatio" dc:"ROI 上限比例"`                                             // ROI 上限比例
	CampaignOcpxActionType         int         `orm:"campaign_ocpx_action_type" json:"campaignOcpxActionType" dc:"OCPX 动作类型"`                     // OCPX 动作类型
	CampaignName                   string      `orm:"campaign_name" json:"campaignName" dc:"广告系列名称"`                                              // 广告系列名称
	UpdateTime                     *gtime.Time `orm:"update_time" json:"updateTime" dc:"更新时间"`                                                    // 更新时间
	DspVersion                     int         `orm:"dsp_version" json:"dspVersion" dc:"DSP 版本"`                                                  // DSP 版本
	PeriodicDeliveryType           int         `orm:"periodic_delivery_type" json:"periodicDeliveryType" dc:"周期投放类型"`                             // 周期投放类型
	CampaignDeepConversionTypeName string      `orm:"campaign_deep_conversion_type_name" json:"campaignDeepConversionTypeName" dc:"智投计划深度优化目标名称"` // 智投计划深度优化目标名称
	CampaignSubType                int         `orm:"campaign_sub_type" json:"campaignSubType" dc:"广告系列子类型"`                                      // 广告系列子类型
	ConstraintCpa                  int         `orm:"constraint_cpa" json:"constraintCpa" dc:"CPA 限制"`                                            // CPA 限制
	AutoAdjust                     int         `orm:"auto_adjust" json:"autoAdjust" dc:"是否自动调节"`                                                  // 是否自动调节
	ContinuePeriodType             int         `orm:"continue_period_type" json:"continuePeriodType" dc:"连续周期类型"`                                 // 连续周期类型
	ConstraintActionType           int         `orm:"constraint_action_type" json:"constraintActionType" dc:"动作类型限制"`                             // 动作类型限制
	DayBudget                      int         `orm:"day_budget" json:"dayBudget" dc:"每日预算"`                                                      // 每日预算
	AutoManage                     int         `orm:"auto_manage" json:"autoManage" dc:"是否自动管理"`                                                  // 是否自动管理
	AutoPhotoScope                 int         `orm:"auto_photo_scope" json:"autoPhotoScope" dc:"自动图片范围"`                                         // 自动图片范围
	CreateTime                     *gtime.Time `orm:"create_time" json:"createTime" dc:"创建时间"`                                                    // 创建时间
	AutoBuild                      int         `orm:"auto_build" json:"autoBuild" dc:"是否自动构建"`                                                    // 是否自动构建
	PeriodicDays                   int         `orm:"periodic_days" json:"periodicDays" dc:"周期天数"`                                                // 周期天数
	CapBid                         int         `orm:"cap_bid" json:"capBid" dc:"出价上限"`                                                            // 出价上限
	RangeBudget                    int         `orm:"range_budget" json:"rangeBudget" dc:"区间预算"`                                                  // 区间预算
	DayBudgetSchedule              string      `orm:"day_budget_schedule" json:"dayBudgetSchedule" dc:"分日预算"`                                     // 分日预算
	Status                         int         `orm:"status" json:"status" dc:"状态"`                                                               // 状态
	UnitNameRule                   string      `orm:"unit_name_rule" json:"unitNameRule" dc:"单元命名规则"`                                             // 单元命名规则
	CreativeNameRule               string      `orm:"creative_name_rule" json:"creativeNameRule" dc:"创意命名规则"`                                     // 创意命名规则
	CreatedAt                      *gtime.Time `orm:"created_at" json:"createdAt" dc:"创建时间"`                                                      // 创建时间
	DeletedAt                      *gtime.Time `orm:"deleted_at" json:"deletedAt" dc:"删除时间"`                                                      // 删除时间
}

type KsAdvertiserCampaignListRes struct {
	CampaignId                     int64       `json:"campaignId" dc:"广告计划 ID"`
	AdvertiserId                   int         `json:"advertiserId" dc:"广告id"`
	PhotoPackageDetails            string      `json:"photoPackageDetails" dc:"图片包详情（可能为 null）"`
	AdType                         int         `json:"adType" dc:"广告类型"`
	CampaignType                   int         `json:"campaignType" dc:"广告系列类型"`
	CampaignDeepConversionType     int         `json:"campaignDeepConversionType" dc:"深度转化类型"`
	BidType                        int         `json:"bidType" dc:"出价类型"`
	PutStatus                      int         `json:"putStatus" dc:"投放状态"`
	CampaignOcpxActionTypeName     string      `json:"campaignOcpxActionTypeName" dc:"智投计划优化目标名称 可能为null"`
	CapRoiRatio                    float64     `json:"capRoiRatio" dc:"ROI 上限比例"`
	CampaignOcpxActionType         int         `json:"campaignOcpxActionType" dc:"OCPX 动作类型"`
	CampaignName                   string      `json:"campaignName" dc:"广告系列名称"`
	UpdateTime                     *gtime.Time `json:"updateTime" dc:"更新时间"`
	DspVersion                     int         `json:"dspVersion" dc:"DSP 版本"`
	PeriodicDeliveryType           int         `json:"periodicDeliveryType" dc:"周期投放类型"`
	CampaignDeepConversionTypeName string      `json:"campaignDeepConversionTypeName" dc:"智投计划深度优化目标名称"`
	CampaignSubType                int         `json:"campaignSubType" dc:"广告系列子类型"`
	ConstraintCpa                  int         `json:"constraintCpa" dc:"CPA 限制"`
	AutoAdjust                     int         `json:"autoAdjust" dc:"是否自动调节"`
	ContinuePeriodType             int         `json:"continuePeriodType" dc:"连续周期类型"`
	ConstraintActionType           int         `json:"constraintActionType" dc:"动作类型限制"`
	DayBudget                      int         `json:"dayBudget" dc:"每日预算"`
	AutoManage                     int         `json:"autoManage" dc:"是否自动管理"`
	AutoPhotoScope                 int         `json:"autoPhotoScope" dc:"自动图片范围"`
	CreateTime                     *gtime.Time `json:"createTime" dc:"创建时间"`
	AutoBuild                      int         `json:"autoBuild" dc:"是否自动构建"`
	PeriodicDays                   int         `json:"periodicDays" dc:"周期天数"`
	CapBid                         int         `json:"capBid" dc:"出价上限"`
	RangeBudget                    int         `json:"rangeBudget" dc:"区间预算"`
	DayBudgetSchedule              string      `json:"dayBudgetSchedule" dc:"分日预算"`
	Status                         int         `json:"status" dc:"状态"`
	UnitNameRule                   string      `json:"unitNameRule" dc:"单元命名规则"`
	CreativeNameRule               string      `json:"creativeNameRule" dc:"创意命名规则"`
	CreatedAt                      *gtime.Time `json:"createdAt" dc:"创建时间"`
}

// KsAdvertiserCampaignSearchReq 分页请求参数
type KsAdvertiserCampaignSearchReq struct {
	comModel.PageReq
	CampaignId                     string `p:"campaignId" dc:"广告计划 ID"`                                                                  //广告计划 ID
	AdvertiserId                   string `p:"advertiserId" v:"advertiserId@integer#广告id需为整数" dc:"广告id"`                                 //广告id
	PhotoPackageDetails            string `p:"photoPackageDetails" dc:"图片包详情（可能为 null）"`                                                 //图片包详情（可能为 null）
	AdType                         string `p:"adType" v:"adType@integer#广告类型需为整数" dc:"广告类型"`                                             //广告类型
	CampaignType                   string `p:"campaignType" v:"campaignType@integer#广告系列类型需为整数" dc:"广告系列类型"`                             //广告系列类型
	CampaignDeepConversionType     string `p:"campaignDeepConversionType" v:"campaignDeepConversionType@integer#深度转化类型需为整数" dc:"深度转化类型"` //深度转化类型
	BidType                        string `p:"bidType" v:"bidType@integer#出价类型需为整数" dc:"出价类型"`                                           //出价类型
	PutStatus                      string `p:"putStatus" v:"putStatus@integer#投放状态需为整数" dc:"投放状态"`                                       //投放状态
	CampaignOcpxActionTypeName     string `p:"campaignOcpxActionTypeName" dc:"智投计划优化目标名称 可能为null"`                                       //智投计划优化目标名称 可能为null
	CapRoiRatio                    string `p:"capRoiRatio" v:"capRoiRatio@float#ROI 上限比例需为浮点数" dc:"ROI 上限比例"`                            //ROI 上限比例
	CampaignOcpxActionType         string `p:"campaignOcpxActionType" v:"campaignOcpxActionType@integer#OCPX 动作类型需为整数" dc:"OCPX 动作类型"`   //OCPX 动作类型
	CampaignName                   string `p:"campaignName" dc:"广告系列名称"`                                                                 //广告系列名称
	UpdateTime                     string `p:"updateTime" v:"updateTime@datetime#更新时间需为YYYY-MM-DD hh:mm:ss格式" dc:"更新时间"`                 //更新时间
	DspVersion                     string `p:"dspVersion" v:"dspVersion@integer#DSP 版本需为整数" dc:"DSP 版本"`                                 //DSP 版本
	PeriodicDeliveryType           string `p:"periodicDeliveryType" v:"periodicDeliveryType@integer#周期投放类型需为整数" dc:"周期投放类型"`             //周期投放类型
	CampaignDeepConversionTypeName string `p:"campaignDeepConversionTypeName" dc:"智投计划深度优化目标名称"`                                         //智投计划深度优化目标名称
	CampaignSubType                string `p:"campaignSubType" v:"campaignSubType@integer#广告系列子类型需为整数" dc:"广告系列子类型"`                     //广告系列子类型
	ConstraintCpa                  string `p:"constraintCpa" v:"constraintCpa@integer#CPA 限制需为整数" dc:"CPA 限制"`                           //CPA 限制
	AutoAdjust                     string `p:"autoAdjust" v:"autoAdjust@integer#是否自动调节需为整数" dc:"是否自动调节"`                                 //是否自动调节
	ContinuePeriodType             string `p:"continuePeriodType" v:"continuePeriodType@integer#连续周期类型需为整数" dc:"连续周期类型"`                 //连续周期类型
	ConstraintActionType           string `p:"constraintActionType" v:"constraintActionType@integer#动作类型限制需为整数" dc:"动作类型限制"`             //动作类型限制
	DayBudget                      string `p:"dayBudget" v:"dayBudget@integer#每日预算需为整数" dc:"每日预算"`                                       //每日预算
	AutoManage                     string `p:"autoManage" v:"autoManage@integer#是否自动管理需为整数" dc:"是否自动管理"`                                 //是否自动管理
	AutoPhotoScope                 string `p:"autoPhotoScope" v:"autoPhotoScope@integer#自动图片范围需为整数" dc:"自动图片范围"`                         //自动图片范围
	CreateTime                     string `p:"createTime" v:"createTime@datetime#创建时间需为YYYY-MM-DD hh:mm:ss格式" dc:"创建时间"`                 //创建时间
	AutoBuild                      string `p:"autoBuild" v:"autoBuild@integer#是否自动构建需为整数" dc:"是否自动构建"`                                   //是否自动构建
	PeriodicDays                   string `p:"periodicDays" v:"periodicDays@integer#周期天数需为整数" dc:"周期天数"`                                 //周期天数
	CapBid                         string `p:"capBid" v:"capBid@integer#出价上限需为整数" dc:"出价上限"`                                             //出价上限
	RangeBudget                    string `p:"rangeBudget" v:"rangeBudget@integer#区间预算需为整数" dc:"区间预算"`                                   //区间预算
	DayBudgetSchedule              string `p:"dayBudgetSchedule" dc:"分日预算"`                                                              //分日预算
	Status                         string `p:"status" v:"status@integer#状态需为整数" dc:"状态"`                                                 //状态
	UnitNameRule                   string `p:"unitNameRule" dc:"单元命名规则"`                                                                 //单元命名规则
	CreativeNameRule               string `p:"creativeNameRule" dc:"创意命名规则"`                                                             //创意命名规则
	CreatedAt                      string `p:"createdAt" v:"createdAt@datetime#创建时间需为YYYY-MM-DD hh:mm:ss格式" dc:"创建时间"`                   //创建时间
}

// KsAdvertiserCampaignSearchRes 列表返回结果
type KsAdvertiserCampaignSearchRes struct {
	comModel.ListRes
	List []*KsAdvertiserCampaignListRes `json:"list"`
}

// KsAdvertiserCampaignAddReq 添加操作请求参数
type KsAdvertiserCampaignAddReq struct {
	CampaignId                     int64                      `p:"campaignId" v:"required#主键ID不能为空" dc:"广告计划 ID"`
	AdvertiserId                   int                        `p:"advertiserId" v:"required#广告id不能为空" dc:"广告id"`
	PhotoPackageDetails            []ksApi.PhotoPackageDetail `p:"photoPackageDetails"  dc:"图片包详情（可能为 null）"`
	AdType                         int                        `p:"adType"  dc:"广告类型"`
	CampaignType                   int                        `p:"campaignType"  dc:"广告系列类型"`
	CampaignDeepConversionType     int                        `p:"campaignDeepConversionType"  dc:"深度转化类型"`
	BidType                        int                        `p:"bidType"  dc:"出价类型"`
	PutStatus                      int                        `p:"putStatus" v:"required#投放状态不能为空" dc:"投放状态"`
	CampaignOcpxActionTypeName     string                     `p:"campaignOcpxActionTypeName" v:"required#智投计划优化目标名称 可能为null不能为空" dc:"智投计划优化目标名称 可能为null"`
	CapRoiRatio                    float64                    `p:"capRoiRatio"  dc:"ROI 上限比例"`
	CampaignOcpxActionType         int                        `p:"campaignOcpxActionType"  dc:"OCPX 动作类型"`
	CampaignName                   string                     `p:"campaignName" v:"required#广告系列名称不能为空" dc:"广告系列名称"`
	UpdateTime                     *gtime.Time                `p:"updateTime"  dc:"更新时间"`
	DspVersion                     int                        `p:"dspVersion"  dc:"DSP 版本"`
	PeriodicDeliveryType           int                        `p:"periodicDeliveryType"  dc:"周期投放类型"`
	CampaignDeepConversionTypeName string                     `p:"campaignDeepConversionTypeName" v:"required#智投计划深度优化目标名称不能为空" dc:"智投计划深度优化目标名称"`
	CampaignSubType                int                        `p:"campaignSubType"  dc:"广告系列子类型"`
	ConstraintCpa                  int                        `p:"constraintCpa"  dc:"CPA 限制"`
	AutoAdjust                     int                        `p:"autoAdjust"  dc:"是否自动调节"`
	ContinuePeriodType             int                        `p:"continuePeriodType"  dc:"连续周期类型"`
	ConstraintActionType           int                        `p:"constraintActionType"  dc:"动作类型限制"`
	DayBudget                      int                        `p:"dayBudget"  dc:"每日预算"`
	AutoManage                     int                        `p:"autoManage"  dc:"是否自动管理"`
	AutoPhotoScope                 int                        `p:"autoPhotoScope"  dc:"自动图片范围"`
	CreateTime                     *gtime.Time                `p:"createTime"  dc:"创建时间"`
	AutoBuild                      int                        `p:"autoBuild"  dc:"是否自动构建"`
	PeriodicDays                   int                        `p:"periodicDays"  dc:"周期天数"`
	CapBid                         int                        `p:"capBid"  dc:"出价上限"`
	RangeBudget                    int                        `p:"rangeBudget"  dc:"区间预算"`
	DayBudgetSchedule              []int64                    `p:"dayBudgetSchedule"  dc:"分日预算"`
	Status                         int                        `p:"status" v:"required#状态不能为空" dc:"状态"`
	UnitNameRule                   string                     `p:"unitNameRule" v:"required#单元命名规则不能为空" dc:"单元命名规则"`
	CreativeNameRule               string                     `p:"creativeNameRule" v:"required#创意命名规则不能为空" dc:"创意命名规则"`
}

// KsAdvertiserCampaignEditReq 修改操作请求参数
type KsAdvertiserCampaignEditReq struct {
	CampaignId                     int64       `p:"campaignId" v:"required#主键ID不能为空" dc:"广告计划 ID"`
	AdvertiserId                   int         `p:"advertiserId" v:"required#广告id不能为空" dc:"广告id"`
	PhotoPackageDetails            string      `p:"photoPackageDetails"  dc:"图片包详情（可能为 null）"`
	AdType                         int         `p:"adType"  dc:"广告类型"`
	CampaignType                   int         `p:"campaignType"  dc:"广告系列类型"`
	CampaignDeepConversionType     int         `p:"campaignDeepConversionType"  dc:"深度转化类型"`
	BidType                        int         `p:"bidType"  dc:"出价类型"`
	PutStatus                      int         `p:"putStatus" v:"required#投放状态不能为空" dc:"投放状态"`
	CampaignOcpxActionTypeName     string      `p:"campaignOcpxActionTypeName" v:"required#智投计划优化目标名称 可能为null不能为空" dc:"智投计划优化目标名称 可能为null"`
	CapRoiRatio                    float64     `p:"capRoiRatio"  dc:"ROI 上限比例"`
	CampaignOcpxActionType         int         `p:"campaignOcpxActionType"  dc:"OCPX 动作类型"`
	CampaignName                   string      `p:"campaignName" v:"required#广告系列名称不能为空" dc:"广告系列名称"`
	UpdateTime                     *gtime.Time `p:"updateTime"  dc:"更新时间"`
	DspVersion                     int         `p:"dspVersion"  dc:"DSP 版本"`
	PeriodicDeliveryType           int         `p:"periodicDeliveryType"  dc:"周期投放类型"`
	CampaignDeepConversionTypeName string      `p:"campaignDeepConversionTypeName" v:"required#智投计划深度优化目标名称不能为空" dc:"智投计划深度优化目标名称"`
	CampaignSubType                int         `p:"campaignSubType"  dc:"广告系列子类型"`
	ConstraintCpa                  int         `p:"constraintCpa"  dc:"CPA 限制"`
	AutoAdjust                     int         `p:"autoAdjust"  dc:"是否自动调节"`
	ContinuePeriodType             int         `p:"continuePeriodType"  dc:"连续周期类型"`
	ConstraintActionType           int         `p:"constraintActionType"  dc:"动作类型限制"`
	DayBudget                      int         `p:"dayBudget"  dc:"每日预算"`
	AutoManage                     int         `p:"autoManage"  dc:"是否自动管理"`
	AutoPhotoScope                 int         `p:"autoPhotoScope"  dc:"自动图片范围"`
	CreateTime                     *gtime.Time `p:"createTime"  dc:"创建时间"`
	AutoBuild                      int         `p:"autoBuild"  dc:"是否自动构建"`
	PeriodicDays                   int         `p:"periodicDays"  dc:"周期天数"`
	CapBid                         int         `p:"capBid"  dc:"出价上限"`
	RangeBudget                    int         `p:"rangeBudget"  dc:"区间预算"`
	DayBudgetSchedule              string      `p:"dayBudgetSchedule"  dc:"分日预算"`
	Status                         int         `p:"status" v:"required#状态不能为空" dc:"状态"`
	UnitNameRule                   string      `p:"unitNameRule" v:"required#单元命名规则不能为空" dc:"单元命名规则"`
	CreativeNameRule               string      `p:"creativeNameRule" v:"required#创意命名规则不能为空" dc:"创意命名规则"`
}

type KsAdvertiserCampaignUpdateReq struct {
	CampaignId int64  `p:"campaignId" dc:"广告计划ID"`
	PutStatus  *int64 `p:"putStatus" dc:"投放状态"`
	DayBudget  *int64 `p:"dayBudget"  dc:"单日预算 单位：厘"`
}
