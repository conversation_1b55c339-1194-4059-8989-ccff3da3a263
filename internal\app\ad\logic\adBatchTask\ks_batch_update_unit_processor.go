// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2025-08-18 17:30:45
// 生成路径: internal/app/ad/logic/adBatchTask/ks_batch_update_unit_processor.go
// 生成人：cq
// desc:快手广告组批量任务处理器
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	commonConsts "github.com/tiger1103/gfast/v3/internal/app/common/consts"
	"github.com/tiger1103/gfast/v3/internal/app/common/logic/channelStream"
	ksApi "github.com/tiger1103/gfast/v3/library/advertiser/ks/api"
)

var unitStreamConsumer *channelStream.StreamConsumer

func init() {
	go StartKsBatchUpdateUnitTaskConsumer()
}

// StartKsBatchUpdateUnitTaskConsumer 启动快手广告组批量任务消费者
func StartKsBatchUpdateUnitTaskConsumer() {
	dev := g.Cfg().MustGet(context.Background(), "redis.channel.stream.env").String()
	config := &channelStream.StreamConsumerConfig{
		StreamName:    fmt.Sprintf(commonConsts.KsBatchUpdateUnitChannelStream, dev),
		GroupName:     fmt.Sprintf(commonConsts.KsBatchUpdateUnitChannelGroup1, dev),
		ConsumerName:  fmt.Sprintf(commonConsts.KsBatchUpdateUnitChannelConsumer1, dev),
		BlockTime:     5 * time.Second,
		BatchSize:     10,
		LockTimeout:   10 * time.Second,
		MessageField:  commonConsts.KsBatchUpdateUnitMessageField,
		CleanupCron:   "0 0 1 * * ?", // 每天凌晨1点执行清理
		MaxStreamSize: 1000,
	}
	unitStreamConsumer = channelStream.NewStreamConsumer(config, &KsBatchUpdateUnitProcessor{})
	unitStreamConsumer.Start()
}

// KsBatchUpdateUnitProcessor 快手广告组批量任务处理器
type KsBatchUpdateUnitProcessor struct{}

// GetTaskId 获取任务ID
func (s *KsBatchUpdateUnitProcessor) GetTaskId(taskData []byte) (string, error) {
	var task model.KsBatchUpdateUnitTaskInfo
	if err := json.Unmarshal(taskData, &task); err != nil {
		return "", err
	}
	return task.TaskId, nil
}

// ProcessTask 处理任务
func (s *KsBatchUpdateUnitProcessor) ProcessTask(ctx context.Context, taskData []byte) (success bool, err error) {
	var task model.KsBatchUpdateUnitTaskInfo
	if err = json.Unmarshal(taskData, &task); err != nil {
		return false, err
	}

	optResult, err := s.processTask(task)
	if err != nil {
		return false, err
	}

	return optResult == commonConsts.OptResultSuccess, nil
}

// UpdateTaskStatus 更新任务状态
func (s *KsBatchUpdateUnitProcessor) UpdateTaskStatus(ctx context.Context, taskId string, success bool) error {
	taskEditReq := &model.AdBatchTaskEditReq{
		TaskId: taskId,
	}
	if success {
		taskEditReq.SuccessNum = 1
	} else {
		taskEditReq.FailNum = 1
	}
	return service.AdBatchTask().EditOptStatus(ctx, taskEditReq)
}

// processTask 任务处理逻辑
func (s *KsBatchUpdateUnitProcessor) processTask(task model.KsBatchUpdateUnitTaskInfo) (optResult string, err error) {
	ctx := context.Background()
	err = g.Try(ctx, func(ctx context.Context) {
		var errMsg string
		accessToken, _ := service.KsAdvertiserAccountInfo().GetAccessToken(ctx, task.AdvertiserId)
		if accessToken == "" {
			errMsg = commonConsts.ErrMsgGetAccessToken
			optResult = commonConsts.OptResultFail
		} else {
			err = s.executeOperation(ctx, accessToken, task)
			if err != nil {
				errMsg = err.Error()
				optResult = commonConsts.OptResultFail
			} else {
				optResult = commonConsts.OptResultSuccess
			}
		}
		// 记录任务详情
		detail := &model.AdBatchTaskDetailAddReq{
			TaskId:           task.TaskId,
			SerialNumber:     task.SerialNumber,
			AdvertiserId:     gconv.String(task.AdvertiserId),
			UnitId:           task.UnitId,
			OriginalUnitName: task.UnitName,
			NewUnitName:      task.NewUnitName,
			OptResult:        optResult,
			ErrMsg:           errMsg,
		}
		err = service.AdBatchTaskDetail().Add(ctx, detail)
	})
	return
}

// executeOperation 执行具体的操作
func (s *KsBatchUpdateUnitProcessor) executeOperation(ctx context.Context, accessToken string, task model.KsBatchUpdateUnitTaskInfo) error {
	switch task.OptType {
	case commonConsts.OptTypePlacement, commonConsts.OptTypePause, commonConsts.OptTypeDelete:
		return s.updateUnitStatus(ctx, accessToken, task)
	case commonConsts.OptTypeEditRoiRatio:
		return s.updateRoiRatio(ctx, accessToken, task)
	case commonConsts.OptTypeEditUnitName:
		return s.updateUnitName(ctx, accessToken, task)
	case commonConsts.OptTypeEditDeliveryPeriod:
		return s.updateDeliveryPeriod(ctx, accessToken, task)
	default:
		return errors.New(commonConsts.ErrMsgUnSupportOptType)
	}
}

// updateUnitStatus 更新广告组状态
func (s *KsBatchUpdateUnitProcessor) updateUnitStatus(ctx context.Context, accessToken string, task model.KsBatchUpdateUnitTaskInfo) error {
	var putStatus int64
	switch task.OptType {
	case commonConsts.OptTypePlacement:
		putStatus = 1
	case commonConsts.OptTypePause:
		putStatus = 2
	case commonConsts.OptTypeDelete:
		putStatus = 3
	}
	_, err := ksApi.GetKSApiClient().UpdateUnitStatusService.AccessToken(accessToken).
		SetReq(ksApi.UpdateUnitStatusReq{
			AdvertiserId: task.AdvertiserId,
			UnitId:       task.UnitId,
			PutStatus:    putStatus,
		}).Do()
	if err == nil {
		_ = service.KsAdvertiserUnit().UpdateUnitInfo(ctx, &model.KsAdvertiserUnitUpdateReq{
			UnitId:    task.UnitId,
			PutStatus: &putStatus,
		})
	}
	return err
}

// updateRoiRatio 更新ROI系数
func (s *KsBatchUpdateUnitProcessor) updateRoiRatio(ctx context.Context, accessToken string, task model.KsBatchUpdateUnitTaskInfo) error {
	_, err := ksApi.GetKSApiClient().UpdateUnitService.AccessToken(accessToken).
		SetReq(ksApi.UpdateUnitReq{
			AdvertiserId: task.AdvertiserId,
			UnitId:       task.UnitId,
			UnitName:     task.UnitName,
			RoiRatio:     &task.RoiRatio,
		}).Do()
	if err == nil {
		_ = service.KsAdvertiserUnit().UpdateUnitInfo(ctx, &model.KsAdvertiserUnitUpdateReq{
			UnitId:   task.UnitId,
			RoiRatio: &task.RoiRatio,
		})
	}
	return err
}

// updateUnitName 更新广告组名称
func (s *KsBatchUpdateUnitProcessor) updateUnitName(ctx context.Context, accessToken string, task model.KsBatchUpdateUnitTaskInfo) error {
	_, err := ksApi.GetKSApiClient().UpdateUnitService.AccessToken(accessToken).
		SetReq(ksApi.UpdateUnitReq{
			AdvertiserId: task.AdvertiserId,
			UnitId:       task.UnitId,
			UnitName:     task.NewUnitName,
		}).Do()
	if err == nil {
		_ = service.KsAdvertiserUnit().UpdateUnitInfo(ctx, &model.KsAdvertiserUnitUpdateReq{
			UnitId:   task.UnitId,
			UnitName: task.NewUnitName,
		})
	}
	return err
}

// updateDeliveryPeriod 更新投放时段
func (s *KsBatchUpdateUnitProcessor) updateDeliveryPeriod(ctx context.Context, accessToken string, task model.KsBatchUpdateUnitTaskInfo) error {
	updateReq := ksApi.UpdateUnitReq{
		AdvertiserId: task.AdvertiserId,
		UnitId:       task.UnitId,
		UnitName:     task.UnitName,
	}
	// 设置可选参数
	if task.BeginTime != "" {
		updateReq.BeginTime = &task.BeginTime
	}
	if task.EndTime != "" {
		updateReq.EndTime = &task.EndTime
	}
	if task.ScheduleTime != "" {
		updateReq.ScheduleTime = &task.ScheduleTime
	}
	_, err := ksApi.GetKSApiClient().UpdateUnitService.AccessToken(accessToken).SetReq(updateReq).Do()
	if err == nil {
		_ = service.KsAdvertiserUnit().UpdateUnitInfo(ctx, &model.KsAdvertiserUnitUpdateReq{
			UnitId:       task.UnitId,
			BeginTime:    task.BeginTime,
			EndTime:      task.EndTime,
			ScheduleTime: task.ScheduleTime,
		})
	}
	return err
}
