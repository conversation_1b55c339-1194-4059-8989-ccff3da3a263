// ==========================================================================
// GFast自动生成controller操作代码。
// 生成日期：2025-08-12 15:45:08
// 生成路径: internal/app/ad/controller/ks_advertiser_fund.go
// 生成人：cyao
// desc:广告主资金信息
// company:云南奇讯科技有限公司
// ==========================================================================

package controller

import (
	"context"

	"github.com/tiger1103/gfast/v3/api/v1/ad"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	systemController "github.com/tiger1103/gfast/v3/internal/app/system/controller"
)

type ksAdvertiserFundController struct {
	systemController.BaseController
}

var KsAdvertiserFund = new(ksAdvertiserFundController)

// List 列表
func (c *ksAdvertiserFundController) List(ctx context.Context, req *ad.KsAdvertiserFundSearchReq) (res *ad.KsAdvertiserFundSearchRes, err error) {
	res = new(ad.KsAdvertiserFundSearchRes)
	res.KsAdvertiserFundSearchRes, err = service.KsAdvertiserFund().List(ctx, &req.KsAdvertiserFundSearchReq)
	return
}

// Get 获取广告主资金信息
func (c *ksAdvertiserFundController) Get(ctx context.Context, req *ad.KsAdvertiserFundGetReq) (res *ad.KsAdvertiserFundGetRes, err error) {
	res = new(ad.KsAdvertiserFundGetRes)
	res.KsAdvertiserFundInfoRes, err = service.KsAdvertiserFund().GetByAdvertiserId(ctx, req.AdvertiserId)
	return
}

// Add 添加广告主资金信息
func (c *ksAdvertiserFundController) Add(ctx context.Context, req *ad.KsAdvertiserFundAddReq) (res *ad.KsAdvertiserFundAddRes, err error) {
	err = service.KsAdvertiserFund().Add(ctx, req.KsAdvertiserFundAddReq)
	return
}

// Edit 修改广告主资金信息
func (c *ksAdvertiserFundController) Edit(ctx context.Context, req *ad.KsAdvertiserFundEditReq) (res *ad.KsAdvertiserFundEditRes, err error) {
	err = service.KsAdvertiserFund().Edit(ctx, req.KsAdvertiserFundEditReq)
	return
}

// Delete 删除广告主资金信息
func (c *ksAdvertiserFundController) Delete(ctx context.Context, req *ad.KsAdvertiserFundDeleteReq) (res *ad.KsAdvertiserFundDeleteRes, err error) {
	err = service.KsAdvertiserFund().Delete(ctx, req.AdvertiserIds)
	return
}
