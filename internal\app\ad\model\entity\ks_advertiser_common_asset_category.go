// ==========================================================================
// GFast自动生成model entity操作代码。
// 生成日期：2025-08-19 10:36:49
// 生成路径: internal/app/ad/model/entity/ks_advertiser_common_asset_category.go
// 生成人：cq
// desc:快手通用资产-标题分类
// company:云南奇讯科技有限公司
// ==========================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
)

// KsAdvertiserCommonAssetCategory is the golang structure for table ks_advertiser_common_asset_category.
type KsAdvertiserCommonAssetCategory struct {
	gmeta.Meta `orm:"table:ks_advertiser_common_asset_category"`
	Id         int         `orm:"id,primary" json:"id"`        //
	Category   string      `orm:"category" json:"category"`    // 分类
	UserId     int         `orm:"user_id" json:"userId"`       // 创建者
	CreatedAt  *gtime.Time `orm:"created_at" json:"createdAt"` // 创建时间
	UpdatedAt  *gtime.Time `orm:"updated_at" json:"updatedAt"` // 更新时间
	DeletedAt  *gtime.Time `orm:"deleted_at" json:"deletedAt"` // 删除时间
}
