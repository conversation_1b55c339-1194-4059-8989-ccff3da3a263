package api

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"time"
)

// UploadTokenGenerateService 获取上传token
type UploadTokenGenerateService struct {
	ctx     context.Context
	cfg     *Configuration
	token   string
	Request *UploadTokenGenerateReq
}

type UploadTokenGenerateReq struct {
	AdvertiserId int64  `json:"advertiser_id"` // 广告主id
	FileType     string `json:"file_type"`     // 默认mp4
}

type UploadTokenGenerateResp struct {
	Code    int                  `json:"code"`    // 状态码
	Message string               `json:"message"` // 响应消息
	Data    *UploadTokenGenerate `json:"data"`    // 优化目标列表视图
}
type UploadTokenGenerate struct {
	UploadToken string   `json:"upload_token"`
	Endpoint    []string `json:"endpoint"`
}

func (r *UploadTokenGenerateService) SetCfg(cfg *Configuration) *UploadTokenGenerateService {
	r.cfg = cfg
	return r
}

func (r *UploadTokenGenerateService) SetReq(req UploadTokenGenerateReq) *UploadTokenGenerateService {
	r.Request = &req
	return r
}

func (r *UploadTokenGenerateService) AccessToken(accessToken string) *UploadTokenGenerateService {
	r.token = accessToken
	return r
}

func (r *UploadTokenGenerateService) Do() (data *UploadTokenGenerateResp, err error) {
	localBasePath := r.cfg.BasePath
	localVarPath := localBasePath + "/rest/openapi/gw/ad/common/upload/token/generate"
	response, err := r.cfg.HTTPClient.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("Access-Token", r.token).
		SetBody(r.Request).
		SetResult(&UploadTokenGenerateResp{}).
		Post(localVarPath)
	if err != nil {
		return nil, err
	}
	resp := new(UploadTokenGenerateResp)
	// 将 JSON 响应解码到结构体中
	err = json.Unmarshal(response.Body(), &resp)
	if err != nil {
		if strings.Contains(err.Error(), "文件操作过于频繁") {
			time.Sleep(200 * time.Millisecond)
			return r.Do()
		}
		return nil, errors.New(fmt.Sprintf("/rest/openapi/gw/ad/common/upload/token/generate解析响应出错: %v\n", err))
	}
	if resp.Code == 0 && resp.Data != nil {
		return resp, nil
	} else {
		return resp, errors.New(resp.Message)
	}
}
