// ==========================================================================
// GFast自动生成model entity操作代码。
// 生成日期：2025-08-13 16:33:57
// 生成路径: internal/app/ad/model/entity/ks_advertiser_unit.go
// 生成人：cyao
// desc:快手广告组
// company:云南奇讯科技有限公司
// ==========================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
)

// KsAdvertiserUnit is the golang structure for table ks_advertiser_unit.
type KsAdvertiserUnit struct {
	gmeta.Meta               `orm:"table:ks_advertiser_unit"`
	UnitId                   int64       `orm:"unit_id,primary" json:"unitId"`                                // 单元 ID
	AdvertiserId             int         `orm:"advertiser_id" json:"advertiserId"`                            // 广告id
	CampaignId               int64       `orm:"campaign_id" json:"campaignId"`                                // 广告计划 ID
	LinkIntegrationType      int         `orm:"link_integration_type" json:"linkIntegrationType"`             // 链接整合类型
	AssetMining              int         `orm:"asset_mining" json:"assetMining"`                              // 资产挖掘
	SiteType                 int         `orm:"site_type" json:"siteType"`                                    // 预约广告 1:IOS 预约 缺省为不传或传 0
	AdType                   int         `orm:"ad_type" json:"adType"`                                        // 广告计划类型 0:信息流，1:搜索
	DpaDynamicParamsForUri   string      `orm:"dpa_dynamic_params_for_uri" json:"dpaDynamicParamsForUri"`     // 落地页链接动态参数
	SchemaUri                string      `orm:"schema_uri" json:"schemaUri"`                                  // 调起链接、提升应用活跃营销目标的调起链接
	ProductImage             string      `orm:"product_image" json:"productImage"`                            // 商品主图
	PackageId                int         `orm:"package_id" json:"packageId"`                                  // 新版应用中心应用ID
	BidType                  int         `orm:"bid_type" json:"bidType"`                                      // 出价类型 1：CPM，2：CPC，6：OCPC(使用 OCPC 代表 OCPX)，10：OCPM，20：eCPC
	ProductPrice             string      `orm:"product_price" json:"productPrice"`                            // 商品价格 元
	PutStatus                int         `orm:"put_status" json:"putStatus"`                                  // 投放状态 1：投放中；2：暂停 3：删除
	SmartCover               int         `orm:"smart_cover" json:"smartCover"`                                // 智能封面 是否开启智能抽帧
	DpaOuterIds              string      `orm:"dpa_outer_ids" json:"dpaOuterIds"`                             // DPA外部商品id集合
	SeriesPayTemplateIdMulti string      `orm:"series_pay_template_id_multi" json:"seriesPayTemplateIdMulti"` // 短剧付费模版列表
	DpaUnitSubType           int         `orm:"dpa_unit_sub_type" json:"dpaUnitSubType"`                      // 商品广告类型：1-DPA，2-SDPA，3-动态商品卡
	ULink                    string      `orm:"u_link" json:"uLink"`                                          // ios系统的ulink链接
	AppStore                 string      `orm:"app_store" json:"appStore"`                                    // 应用商店列表
	AppDownloadType          int         `orm:"app_download_type" json:"appDownloadType"`                     // 应用下载类型 0：直接下载1：落地页下载（仅在提升应用安装计划下生效）
	DspVersion               int         `orm:"dsp_version" json:"dspVersion"`                                // DSP版本
	PlayableId               int64       `orm:"playable_id" json:"playableId"`                                // 可试玩 ID 可选字段，开启试玩时存在，否则不存在
	EpisodeId                int64       `orm:"episode_id" json:"episodeId"`                                  // 剧集 ID
	PlayableSwitch           int         `orm:"playable_switch" json:"playableSwitch"`                        // 可试玩开关
	ProductId                string      `orm:"product_id" json:"productId"`                                  // 产品 ID
	SchemaId                 string      `orm:"schema_id" json:"schemaId"`                                    // 微信小程序外部调起链接（部分场景有效）
	UnitSource               int         `orm:"unit_source" json:"unitSource"`                                // 广告组来源 0:常规（非托管）、1:托管
	SeriesPayTemplateId      int64       `orm:"series_pay_template_id" json:"seriesPayTemplateId"`            // 付费模板（短剧推广时支持）
	DayBudget                int64       `orm:"day_budget" json:"dayBudget"`                                  // 日预算 单位：厘
	OcpxActionType           int         `orm:"ocpx_action_type" json:"ocpxActionType"`                       // 优化目标（枚举值见文档）
	UseAppMarket             int         `orm:"use_app_market" json:"useAppMarket"`                           // 是否使用应用市场 0：未设置 1：优先从系统应用商店下载
	TargetExplore            int         `orm:"target_explore" json:"targetExplore"`                          // 是否开启搜索人群探索
	ComponentId              int64       `orm:"component_id" json:"componentId"`                              // 组件 ID
	DpaDynamicParams         int         `orm:"dpa_dynamic_params" json:"dpaDynamicParams"`                   // DPA 动态参数 开关
	CreateTime               *gtime.Time `orm:"create_time" json:"createTime"`                                // 创建时间
	PlayButton               string      `orm:"play_button" json:"playButton"`                                // 试玩按钮文字内容
	UrlType                  int         `orm:"url_type" json:"urlType"`                                      // URL 类型（特定计划下返回）

	DpaCategories               string      `orm:"dpa_categories" json:"dpaCategories"`                              // DPA 类别
	ProductName                 string      `orm:"product_name" json:"productName"`                                  // 产品名称
	SeriesPayMode               int         `orm:"series_pay_mode" json:"seriesPayMode"`                             // 付费模式（短剧推广时返回）
	ShowMode                    int         `orm:"show_mode" json:"showMode"`                                        // 展示模式 0：未知，1：轮播，2：优选
	UnitName                    string      `orm:"unit_name" json:"unitName"`                                        // 广告组名称
	ExtendSearch                int         `orm:"extend_search" json:"extendSearch"`                                // 智能扩词开启状态
	QuickSearch                 int         `orm:"quick_search" json:"quickSearch"`                                  // 是否开启快投
	SiteId                      int64       `orm:"site_id" json:"siteId"`                                            // 建站ID / 安卓下载中间页ID
	SeriesCardInfo              string      `orm:"series_card_info" json:"seriesCardInfo"`                           // 剧集卡片信息
	Status                      int         `orm:"status" json:"status"`                                             // 广告组状态
	ConsultId                   int64       `orm:"consult_id" json:"consultId"`                                      // 咨询组件使用情况
	RoiRatio                    float64     `orm:"roi_ratio" json:"roiRatio"`                                        // 付费 ROI 系数
	LiveComponentType           int         `orm:"live_component_type" json:"liveComponentType"`                     // 直播组件类型
	SearchPopulationRetargeting int         `orm:"search_population_retargeting" json:"searchPopulationRetargeting"` // 是否开启人群追投
	EnhanceConversionType       int         `orm:"enhance_conversion_type" json:"enhanceConversionType"`             // 增强目标
	UnitType                    int         `orm:"unit_type" json:"unitType"`                                        // 创意制作方式
	OuterId                     string      `orm:"outer_id" json:"outerId"`                                          // 外部 ID
	StudyStatus                 int         `orm:"study_status" json:"studyStatus"`                                  // 学习状态
	SeriesCardType              int         `orm:"series_card_type" json:"seriesCardType"`                           // 剧集卡片类型
	CustomMiniAppData           string      `orm:"custom_mini_app_data" json:"customMiniAppData"`                    // 自定义小程序数据
	UpdateTime                  *gtime.Time `orm:"update_time" json:"updateTime"`                                    // 更新时间
	ImMessageMount              int         `orm:"im_message_mount" json:"imMessageMount"`                           // IM 消息挂载
	LibraryId                   int64       `orm:"library_id" json:"libraryId"`                                      // 素材库 ID
	DeepConversionType          int         `orm:"deep_conversion_type" json:"deepConversionType"`                   // 深度转化类型
	DeepConversionBid           int         `orm:"deep_conversion_bid" json:"deepConversionBid"`                     // 深度转化出价
	KwaiBookId                  int64       `orm:"kwai_book_id" json:"kwaiBookId"`                                   // 快手书籍 ID
	DpaDynamicParamsForDp       string      `orm:"dpa_dynamic_params_for_dp" json:"dpaDynamicParamsForDp"`           // DP 动态参数
	PageAuditStatus             string      `orm:"page_audit_status" json:"pageAuditStatus"`                         // 页面审核状态
	JingleBellId                int64       `orm:"jingle_bell_id" json:"jingleBellId"`                               // 铃铛 ID
	LiveUserId                  int64       `orm:"live_user_id" json:"liveUserId"`                                   // 直播用户 ID
	PlayableUrl                 string      `orm:"playable_url" json:"playableUrl"`                                  // 可试玩 URL
	PlayableFileName            string      `orm:"playable_file_name" json:"playableFileName"`                       // 可试玩文件名
	WebUriType                  int         `orm:"web_uri_type" json:"webUriType"`                                   // Web URI 类型
	ReviewDetail                string      `orm:"review_detail" json:"reviewDetail"`                                // 审核详情
	EndTime                     *gtime.Time `orm:"end_time" json:"endTime"`                                          // 结束时间
	CompensateStatus            int         `orm:"compensate_status" json:"compensateStatus"`                        // 补偿状态
	SceneId                     string      `orm:"scene_id" json:"sceneId"`                                          // 场景 ID
	BeginTime                   *gtime.Time `orm:"begin_time" json:"beginTime"`                                      // 开始时间
	UnitMaterialType            int         `orm:"unit_material_type" json:"unitMaterialType"`                       // 单元素材类型
	ConvertId                   int64       `orm:"convert_id" json:"convertId"`                                      // 转化 ID
	Url                         string      `orm:"url" json:"url"`                                                   // 链接
	SeriesId                    int64       `orm:"series_id" json:"seriesId"`                                        // 系列 ID
	ScheduleTime                string      `orm:"schedule_time" json:"scheduleTime"`                                // 日程时间
	OuterLoopNative             int         `orm:"outer_loop_native" json:"outerLoopNative"`                         // 外循环原生
	CpaBid                      int         `orm:"cpa_bid" json:"cpaBid"`                                            // CPA 出价
	PlayableOrientation         int         `orm:"playable_orientation" json:"playableOrientation"`                  // 可试玩方向
	TemplateId                  int         `orm:"template_id" json:"templateId"`                                    // 模板 ID
	Bid                         int         `orm:"bid" json:"bid"`                                                   // 出价
	AdvCardOption               int         `orm:"adv_card_option" json:"advCardOption"`                             // 广告卡片选项
	CreatedAt                   *gtime.Time `orm:"created_at" json:"createdAt"`                                      // 创建时间
	DeletedAt                   *gtime.Time `orm:"deleted_at" json:"deletedAt"`                                      // 删除时间
}
