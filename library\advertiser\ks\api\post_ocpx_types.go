package api

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
)

// OcpxTypesService 获取可选的浅度转化目标
type OcpxTypesService struct {
	ctx     context.Context
	cfg     *Configuration
	token   string
	Request *OcpxTypesReq
}

type OcpxTypesReq struct {
	BookPayMode          *int    `json:"book_pay_mode,omitempty"`          // 小说付费模式: 1=全文付费, 2=全文付费+章节付费, 3=免费
	SeriesPayMode        *int    `json:"series_pay_mode,omitempty"`        // 短剧付费模式
	PeriodicDeliveryType *int    `json:"periodic_delivery_type,omitempty"` // 是否稳投为稳投计划
	PlcBizType           *int    `json:"plc_biz_type,omitempty"`           // 快手号营销目标-线索留资-推广目标-组件类型
	SchemaUri            *string `json:"schema_uri,omitempty"`             // -
	Uri                  *string `json:"uri,omitempty"`                    // api转化追踪增加参数
	LiveComponentType    *int    `json:"live_component_type,omitempty"`    // 直播组件类型
	OcpxActionType       *int    `json:"ocpx_action_type,omitempty"`       // -
	ImMessageMount       *bool   `json:"im_message_mount,omitempty"`       // 是否挂载私信收集组件
	SceneCategory        int     `json:"scene_category"`                   // 场景分类: 0=普通创编, 1=追踪工具, 2=常规托管（智能模式）, 3=搜索广告
	CampaignType         *int    `json:"campaign_type,omitempty"`          // 计划类型 (scene_category!=1时必填)
	SceneIds             []int   `json:"scene_ids,omitempty"`              // 资源位 (scene_category!=1时必填)
	TraceutilType        *int    `json:"traceutil_type,omitempty"`         // 追踪工具类型 (scene_category=1时必填): 1=js, 2=xpath, 3=sdk, 7=api, 12=kwai attribution
	AppId                *int64  `json:"app_id,omitempty"`                 // 应用id (追踪工具类型时不需要)
	HostingScene         *int    `json:"hosting_scene,omitempty"`          // 追踪工具类型 (scene_category=2时可用): 0=常规推广, 1=测书工具, 2=账户预热, 5=最大转化
	AdvertiserId         int64   `json:"advertiser_id"`                    // 广告主id
	BidType              *int    `json:"bid_type,omitempty"`               // 出价类型 (scene_category=0时可用): 10=ocpm, 12=mcb
	SiteId               *int64  `json:"site_id,omitempty"`                // 建站id
	UnitMaterialType     *int    `json:"unit_material_type,omitempty"`     // 单元组件类型: 1=快手小程序, 2=快手小游戏, 3=微信小程序, 4=微信小游戏
}

type OcpxActionTypeStructForGatewayView struct {
	OcpxTypes []OcpxActionTypeStructForGateway `json:"ocpx_types"` // 优化目标列表
}

// OcpxActionTypeStructForGateway 优化目标详情结构体
type OcpxActionTypeStructForGateway struct {
	OcpxActionType int    `json:"ocpx_action_type"` // 优化目标的id数字
	Name           string `json:"name"`             // ocpxActionType的中文描述
}

func (r *OcpxTypesService) SetCfg(cfg *Configuration) *OcpxTypesService {
	r.cfg = cfg
	return r
}

func (r *OcpxTypesService) SetReq(req OcpxTypesReq) *OcpxTypesService {
	r.Request = &req
	return r
}

func (r *OcpxTypesService) AccessToken(accessToken string) *OcpxTypesService {
	r.token = accessToken
	return r
}

func (r *OcpxTypesService) Do() (data *KsBaseResp[OcpxActionTypeStructForGatewayView], err error) {
	localBasePath := r.cfg.BasePath
	localVarPath := localBasePath + "/rest/openapi/gw/dsp/v1/ocpx/ocpxTypes"
	response, err := r.cfg.HTTPClient.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("Access-Token", r.token).
		SetBody(r.Request).
		SetResult(&KsBaseResp[OcpxActionTypeStructForGatewayView]{}).
		Post(localVarPath)
	if err != nil {
		return nil, err
	}
	resp := new(KsBaseResp[OcpxActionTypeStructForGatewayView])
	// 将 JSON 响应解码到结构体中
	err = json.Unmarshal(response.Body(), &resp)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("/rest/openapi/gw/dsp/v1/ocpx/ocpxTypes解析响应出错: %v\n", err))
	}
	if resp.Code == 0 && resp.Data != nil {
		return resp, nil
	} else {
		return resp, errors.New(resp.Message)
	}
}
