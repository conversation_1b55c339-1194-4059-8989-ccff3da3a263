package api

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
)

// UpdateCampaignService 修改广告计划
type UpdateCampaignService struct {
	ctx     context.Context
	cfg     *Configuration
	token   string
	Request *UpdateCampaignReq
}

// UpdateCampaignReq 请求结构体
type UpdateCampaignReq struct {
	RangeBudget                *int64                            `json:"range_budget,omitempty"`                  // 周期稳投总预算 (单位: 分)
	PutStatus                  *int                              `json:"put_status,omitempty"`                    // 计划状态: 1-投放中, 2-暂停, 3-删除
	AutoAdjust                 *int                              `json:"auto_adjust,omitempty"`                   // 自动调控开关: 0-关闭, 1-开启
	AutoBuild                  *int                              `json:"auto_build,omitempty"`                    // 自动基建开关: 0-关闭, 1-开启
	AutoBuildNameRule          *AutoBuildNameRuleParamForGateway `json:"auto_build_name_rule,omitempty"`          // 自动基建广告命名规则 (auto_build=1时生效)
	CampaignId                 int64                             `json:"campaign_id"`                             // 广告计划 ID (必填)
	CampaignName               *string                           `json:"campaign_name,omitempty"`                 // 广告计划名称
	CapBid                     *int64                            `json:"cap_bid,omitempty"`                       // cost cap的成本约束 (单位: 厘)
	CapRoiRatio                *float64                          `json:"cap_roi_ratio,omitempty"`                 // cost cap的roi约束
	ConstraintCpa              *int64                            `json:"constraint_cpa,omitempty"`                // 浅层成本约束 (单位: 厘)
	DayBudget                  *int64                            `json:"day_budget,omitempty"`                    // 单日预算金额 (单位: 厘)
	DayBudgetSchedule          []string                          `json:"day_budget_schedule,omitempty"`           // 分日预算 (单位: 厘)
	AutoManage                 *int                              `json:"auto_manage,omitempty"`                   // 智投: 0-关闭, 1-开启
	CampaignOcpxActionType     *int                              `json:"campaign_ocpx_action_type,omitempty"`     // 智投浅层优化目标
	CampaignDeepConversionType *int                              `json:"campaign_deep_conversion_type,omitempty"` // 智投深层优化目标
	AdvertiserId               int64                             `json:"advertiser_id"`                           // accountId (必填)
}

// AutoBuildNameRuleParamForGateway 自动基建广告命名规则参数
type AutoBuildNameRuleParamForGateway struct {
	UnitNameRule       *string `json:"unit_name_rule,omitempty"`       // 单元命名规则
	CreativeNamingRule *string `json:"creative_naming_rule,omitempty"` // 创意命名规则
}

// UpdateCampaignData API响应结构体
type UpdateCampaignData struct {
	CampaignId int64 `json:"campaign_id"` // 广告计划id
}

func (r *UpdateCampaignService) SetCfg(cfg *Configuration) *UpdateCampaignService {
	r.cfg = cfg
	return r
}

func (r *UpdateCampaignService) SetReq(req UpdateCampaignReq) *UpdateCampaignService {
	r.Request = &req
	return r
}

func (r *UpdateCampaignService) AccessToken(accessToken string) *UpdateCampaignService {
	r.token = accessToken
	return r
}

func (r *UpdateCampaignService) Do() (data *KsBaseResp[UpdateCampaignData], err error) {
	localBasePath := r.cfg.BasePath
	localVarPath := localBasePath + "/rest/openapi/gw/dsp/campaign/update"
	response, err := r.cfg.HTTPClient.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("Access-Token", r.token).
		SetBody(r.Request).
		SetResult(&KsBaseResp[UpdateCampaignData]{}).
		Post(localVarPath)
	if err != nil {
		return nil, err
	}
	resp := new(KsBaseResp[UpdateCampaignData])
	// 将 JSON 响应解码到结构体中
	err = json.Unmarshal(response.Body(), &resp)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("/rest/openapi/gw/dsp/campaign/update解析响应出错: %v\n", err))
	}
	if resp.Code == 0 && resp.Data != nil {
		return resp, nil
	} else {
		return resp, errors.New(resp.Message)
	}
}
