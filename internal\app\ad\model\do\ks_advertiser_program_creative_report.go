// ==========================================================================
// GFast自动生成model entity操作代码。
// 生成日期：2025-08-20 11:00:01
// 生成路径: internal/app/ad/model/entity/ks_advertiser_program_creative_report.go
// 生成人：cyao
// desc:创意数据
// company:云南奇讯科技有限公司
// ==========================================================================

package do

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
)

// KsAdvertiserProgramCreativeReport is the golang structure for table ks_advertiser_program_creative_report.
type KsAdvertiserProgramCreativeReport struct {
	gmeta.Meta                                       `orm:"table:ks_advertiser_program_creative_report, do:true"`
	CreativeId                                       interface{} `orm:"creative_id,primary" json:"creativeId"`                                                                            // 创意 id
	StatDate                                         interface{} `orm:"stat_date,primary" json:"statDate"`                                                                                // 统计日期
	AdvertiserId                                     interface{} `orm:"advertiser_id" json:"advertiserId"`                                                                                // 广告id
	Charge                                           interface{} `orm:"charge" json:"charge"`                                                                                             // 消耗
	Show                                             interface{} `orm:"show" json:"show"`                                                                                                 // 封面曝光数
	Aclick                                           interface{} `orm:"aclick" json:"aclick"`                                                                                             // 素材曝光数
	Bclick                                           interface{} `orm:"bclick" json:"bclick"`                                                                                             // 行为数
	Share                                            interface{} `orm:"share" json:"share"`                                                                                               // 分享数
	Comment                                          interface{} `orm:"comment" json:"comment"`                                                                                           // 评论数
	Like                                             interface{} `orm:"like" json:"like"`                                                                                                 // 点赞数
	Follow                                           interface{} `orm:"follow" json:"follow"`                                                                                             // 关注数
	Report                                           interface{} `orm:"report" json:"report"`                                                                                             // 举报数
	Block                                            interface{} `orm:"block" json:"block"`                                                                                               // 拉黑数
	Negative                                         interface{} `orm:"negative" json:"negative"`                                                                                         // 减少此类作品数
	Activation                                       interface{} `orm:"activation" json:"activation"`                                                                                     // 激活数
	Submit                                           interface{} `orm:"submit" json:"submit"`                                                                                             // 表单提交数
	AdPhotoPlayed10S                                 interface{} `orm:"ad_photo_played_10s" json:"adPhotoPlayed10S"`                                                                      // 10s播放数
	AdPhotoPlayed2S                                  interface{} `orm:"ad_photo_played_2s" json:"adPhotoPlayed2S"`                                                                        // 2s播放数
	AdPhotoPlayed75Percent                           interface{} `orm:"ad_photo_played_75percent" json:"adPhotoPlayed75Percent"`                                                          // 75%进度播放数
	CancelLike                                       interface{} `orm:"cancel_like" json:"cancelLike"`                                                                                    // 取消点赞
	ClickConversionRatio                             interface{} `orm:"click_conversion_ratio" json:"clickConversionRatio"`                                                               // 点击激活率 (激活数/点击数)
	ConversionCost                                   interface{} `orm:"conversion_cost" json:"conversionCost"`                                                                            // 单次激活成本
	ConversionCostByImpression7D                     interface{} `orm:"conversion_cost_by_impression_7d" json:"conversionCostByImpression7D"`                                             // 7日激活成本
	ConversionNum                                    interface{} `orm:"conversion_num" json:"conversionNum"`                                                                              // 转化数？
	ConversionNumByImpression7D                      interface{} `orm:"conversion_num_by_impression_7d" json:"conversionNumByImpression7D"`                                               // 七日转化数？
	ConversionNumCost                                interface{} `orm:"conversion_num_cost" json:"conversionNumCost"`                                                                     // 单次转换成本？
	ConversionRatio                                  interface{} `orm:"conversion_ratio" json:"conversionRatio"`                                                                          // 转化率？
	ConversionRatioByImpression7D                    interface{} `orm:"conversion_ratio_by_impression_7d" json:"conversionRatioByImpression7D"`                                           // 7日转化率？
	LivePlayed3S                                     interface{} `orm:"live_played_3s" json:"livePlayed3S"`                                                                               // 3s播放数量
	PlayedEnd                                        interface{} `orm:"played_end" json:"playedEnd"`                                                                                      // 完播数量
	PlayedFiveSeconds                                interface{} `orm:"played_five_seconds" json:"playedFiveSeconds"`                                                                     // 5s数量
	PlayedThreeSeconds                               interface{} `orm:"played_three_seconds" json:"playedThreeSeconds"`                                                                   // 3s数量
	AdScene                                          interface{} `orm:"ad_scene" json:"adScene"`                                                                                          // 广告场景
	PlacementType                                    interface{} `orm:"placement_type" json:"placementType"`                                                                              // 位置类型
	CancelFollow                                     interface{} `orm:"cancel_follow" json:"cancelFollow"`                                                                                // 取消关注
	Play3SRatio                                      interface{} `orm:"play_3s_ratio" json:"play3SRatio"`                                                                                 // 3s播放率
	Play5SRatio                                      interface{} `orm:"play_5s_ratio" json:"play5SRatio"`                                                                                 // 5s播放率
	PlayEndRatio                                     interface{} `orm:"play_end_ratio" json:"playEndRatio"`                                                                               // 完播率
	DirectSubmit1DCost                               interface{} `orm:"direct_submit_1d_cost" json:"directSubmit1DCost"`                                                                  // 表单提交成本
	MinigameIaaPurchaseAmountFirstDay                interface{} `orm:"minigame_iaa_purchase_amount_first_day" json:"minigameIaaPurchaseAmountFirstDay"`                                  // 当日广告LTV
	MinigameIaaPurchaseAmountThreeDayByConversion    interface{} `orm:"minigame_iaa_purchase_amount_three_day_by_conversion" json:"minigameIaaPurchaseAmountThreeDayByConversion"`        // 激活后三日广告LTV
	MinigameIaaPurchaseAmountWeekByConversion        interface{} `orm:"minigame_iaa_purchase_amount_week_by_conversion" json:"minigameIaaPurchaseAmountWeekByConversion"`                 // 激活后七日广告LTV
	MinigameIaaPurchaseAmountFirstDayRoi             interface{} `orm:"minigame_iaa_purchase_amount_first_day_roi" json:"minigameIaaPurchaseAmountFirstDayRoi"`                           // 当日广告变现ROI
	MinigameIaaPurchaseAmountThreeDayByConversionRoi interface{} `orm:"minigame_iaa_purchase_amount_three_day_by_conversion_roi" json:"minigameIaaPurchaseAmountThreeDayByConversionRoi"` // 激活后三日广告变现ROI
	MinigameIaaPurchaseAmountWeekByConversionRoi     interface{} `orm:"minigame_iaa_purchase_amount_week_by_conversion_roi" json:"minigameIaaPurchaseAmountWeekByConversionRoi"`          // 激活后七日广告变现ROI
	MinigameIaaPurchaseAmount                        interface{} `orm:"minigame_iaa_purchase_amount" json:"minigameIaaPurchaseAmount"`                                                    // IAA广告变现LTV
	MinigameIaaPurchaseRoi                           interface{} `orm:"minigame_iaa_purchase_roi" json:"minigameIaaPurchaseRoi"`                                                          // IAA广告变现ROI
	UnitId                                           interface{} `orm:"unit_id" json:"unitId"`                                                                                            // 广告组id
	EffectiveCustomerAcquisition7DCnt                interface{} `orm:"effective_customer_acquisition_7d_cnt" json:"effectiveCustomerAcquisition7DCnt"`                                   // 有效获客数（计费）
	EffectiveCustomerAcquisition7DCost               interface{} `orm:"effective_customer_acquisition_7d_cost" json:"effectiveCustomerAcquisition7DCost"`                                 // 有效获客成本（计费）
	EffectiveCustomerAcquisition7DRatio              interface{} `orm:"effective_customer_acquisition_7d_ratio" json:"effectiveCustomerAcquisition7DRatio"`                               // 有效获客率（计费）
	MmuEffectiveCustomerAcquisitionCnt               interface{} `orm:"mmu_effective_customer_acquisition_cnt" json:"mmuEffectiveCustomerAcquisitionCnt"`                                 // MMU识别产生的有效获客数（回传）
	MmuEffectiveCustomerAcquisition7DCnt             interface{} `orm:"mmu_effective_customer_acquisition_7d_cnt" json:"mmuEffectiveCustomerAcquisition7DCnt"`                            // MMU识别产生的有效获客数（计费）
	PlayedNum                                        interface{} `orm:"played_num" json:"playedNum"`                                                                                      // 播放数
	LeadsSubmitCnt                                   interface{} `orm:"leads_submit_cnt" json:"leadsSubmitCnt"`                                                                           // 直接私信留资数
	LeadsSubmitCntRatio                              interface{} `orm:"leads_submit_cnt_ratio" json:"leadsSubmitCntRatio"`                                                                // 直接私信留资率
	LeadsSubmitCost                                  interface{} `orm:"leads_submit_cost" json:"leadsSubmitCost"`                                                                         // 直接私信留资成本
	PrivateMessageSentCnt                            interface{} `orm:"private_message_sent_cnt" json:"privateMessageSentCnt"`                                                            // 私信消息数
	PrivateMessageSentRatio                          interface{} `orm:"private_message_sent_ratio" json:"privateMessageSentRatio"`                                                        // 私信消息转化率
	PrivateMessageSentCost                           interface{} `orm:"private_message_sent_cost" json:"privateMessageSentCost"`                                                          // 私信消息转化成本
	EventFormSubmit                                  interface{} `orm:"event_form_submit" json:"eventFormSubmit"`                                                                         // 表单提交数（回传时间）
	DirectSubmit1DCnt                                interface{} `orm:"direct_submit_1d_cnt" json:"directSubmit1DCnt"`                                                                    // 表单提交数(计费时间)
	EventFormSubmitRatio                             interface{} `orm:"event_form_submit_ratio" json:"eventFormSubmitRatio"`                                                              // 表单提交率（回传时间）
	EventFormSubmitCost                              interface{} `orm:"event_form_submit_cost" json:"eventFormSubmitCost"`                                                                // 表单提交成本（回传时间）
	EventAudition                                    interface{} `orm:"event_audition" json:"eventAudition"`                                                                              // 首次试听到课（归因）
	EventAudition30DCnt                              interface{} `orm:"event_audition_30d_cnt" json:"eventAudition30DCnt"`                                                                // 首次试听到课（归因）
	EventAuditionCost                                interface{} `orm:"event_audition_cost" json:"eventAuditionCost"`                                                                     // 首次试听到课成本
	AllLessonFinishCnt                               interface{} `orm:"all_lesson_finish_cnt" json:"allLessonFinishCnt"`                                                                  // 全部试听完课（回传）
	AllLessonFinish30DCnt                            interface{} `orm:"all_lesson_finish_30d_cnt" json:"allLessonFinish30DCnt"`                                                           // 全部试听完课（归因）
	HighPriceClassPayCnt                             interface{} `orm:"high_price_class_pay_cnt" json:"highPriceClassPayCnt"`                                                             // 成交付费（回传）
	HighPriceClassPay30DCnt                          interface{} `orm:"high_price_class_pay_30d_cnt" json:"highPriceClassPay30DCnt"`                                                      // 成交付费（归因）
	CampaignId                                       interface{} `orm:"campaign_id" json:"campaignId"`                                                                                    // 广告计划Id
	CampaignName                                     interface{} `orm:"campaign_name" json:"campaignName"`                                                                                // 计划名称
	UnitName                                         interface{} `orm:"unit_name" json:"unitName"`                                                                                        // 广告组名称
	PhotoId                                          interface{} `orm:"photo_id" json:"photoId"`                                                                                          // 视频 id
	PhotoUrl                                         interface{} `orm:"photo_url" json:"photoUrl"`                                                                                        // 视频链接
	CoverUrl                                         interface{} `orm:"cover_url" json:"coverUrl"`                                                                                        // 封面链接
	ImageToken                                       interface{} `orm:"image_token" json:"imageToken"`                                                                                    // 封面 id
	Description                                      interface{} `orm:"description" json:"description"`                                                                                   // 作品广告语
	PicId                                            interface{} `orm:"pic_id" json:"picId"`                                                                                              // 图片库图片ID
	PhotoMd5                                         interface{} `orm:"photo_md5" json:"photoMd5"`                                                                                        // 视频 md5
	UnitType                                         interface{} `orm:"unit_type" json:"unitType"`                                                                                        // 单元组的创意类型，7=程序化2.0创意，10=智能创意；当值为空时，代表不区分创意类型取全部创意； 默认为空
	CreatedAt                                        *gtime.Time `orm:"created_at" json:"createdAt"`                                                                                      // 创建时间
}
