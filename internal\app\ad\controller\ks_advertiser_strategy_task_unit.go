// ==========================================================================
// GFast自动生成controller操作代码。
// 生成日期：2025-08-23 17:40:24
// 生成路径: internal/app/ad/controller/ks_advertiser_strategy_task_unit.go
// 生成人：cyao
// desc:快手广告搭建-任务-广告组
// company:云南奇讯科技有限公司
// ==========================================================================

package controller

import (
	"context"

	"github.com/tiger1103/gfast/v3/api/v1/ad"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	systemController "github.com/tiger1103/gfast/v3/internal/app/system/controller"
)

type ksAdvertiserStrategyTaskUnitController struct {
	systemController.BaseController
}

var KsAdvertiserStrategyTaskUnit = new(ksAdvertiserStrategyTaskUnitController)

// List 列表
func (c *ksAdvertiserStrategyTaskUnitController) List(ctx context.Context, req *ad.KsAdvertiserStrategyTaskUnitSearchReq) (res *ad.KsAdvertiserStrategyTaskUnitSearchRes, err error) {
	res = new(ad.KsAdvertiserStrategyTaskUnitSearchRes)
	res.KsAdvertiserStrategyTaskUnitSearchRes, err = service.KsAdvertiserStrategyTaskUnit().List(ctx, &req.KsAdvertiserStrategyTaskUnitSearchReq)
	return
}

// Get 获取快手广告搭建-任务-广告组
func (c *ksAdvertiserStrategyTaskUnitController) Get(ctx context.Context, req *ad.KsAdvertiserStrategyTaskUnitGetReq) (res *ad.KsAdvertiserStrategyTaskUnitGetRes, err error) {
	res = new(ad.KsAdvertiserStrategyTaskUnitGetRes)
	res.KsAdvertiserStrategyTaskUnitInfoRes, err = service.KsAdvertiserStrategyTaskUnit().GetByTaskUnitId(ctx, req.TaskUnitId)
	return
}

// Add 添加快手广告搭建-任务-广告组
func (c *ksAdvertiserStrategyTaskUnitController) Add(ctx context.Context, req *ad.KsAdvertiserStrategyTaskUnitAddReq) (res *ad.KsAdvertiserStrategyTaskUnitAddRes, err error) {
	err = service.KsAdvertiserStrategyTaskUnit().Add(ctx, req.KsAdvertiserStrategyTaskUnitAddReq)
	return
}

// Edit 修改快手广告搭建-任务-广告组
func (c *ksAdvertiserStrategyTaskUnitController) Edit(ctx context.Context, req *ad.KsAdvertiserStrategyTaskUnitEditReq) (res *ad.KsAdvertiserStrategyTaskUnitEditRes, err error) {
	err = service.KsAdvertiserStrategyTaskUnit().Edit(ctx, req.KsAdvertiserStrategyTaskUnitEditReq)
	return
}

// Delete 删除快手广告搭建-任务-广告组
func (c *ksAdvertiserStrategyTaskUnitController) Delete(ctx context.Context, req *ad.KsAdvertiserStrategyTaskUnitDeleteReq) (res *ad.KsAdvertiserStrategyTaskUnitDeleteRes, err error) {
	err = service.KsAdvertiserStrategyTaskUnit().Delete(ctx, req.TaskUnitIds)
	return
}
