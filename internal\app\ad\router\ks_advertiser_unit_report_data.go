// ==========================================================================
// GFast自动生成router操作代码。
// 生成日期：2025-08-12 10:45:10
// 生成路径: internal/app/ad/router/ks_advertiser_unit_report_data.go
// 生成人：cq
// desc:快手广告组报表数据
// company:云南奇讯科技有限公司
// ==========================================================================

package router

import (
	"context"

	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/tiger1103/gfast/v3/internal/app/ad/controller"
)

func (router *Router) BindKsAdvertiserUnitReportDataController(ctx context.Context, group *ghttp.RouterGroup) {
	group.Group("/ksAdvertiserUnitReportData", func(group *ghttp.RouterGroup) {
		group.Bind(
			controller.KsAdvertiserUnitReportData,
		)
	})
}
