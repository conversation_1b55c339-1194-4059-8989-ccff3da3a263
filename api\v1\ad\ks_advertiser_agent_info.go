// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2025-08-12 15:45:06
// 生成路径: api/v1/ad/ks_advertiser_agent_info.go
// 生成人：cyao
// desc:快手代理商信息表相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package ad

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
)

// KsAdvertiserAgentInfoSearchReq 分页请求参数
type KsAdvertiserAgentInfoSearchReq struct {
	g.Meta `path:"/list" tags:"快手代理商信息表" method:"get" summary:"快手代理商信息表列表"`
	commonApi.Author
	model.KsAdvertiserAgentInfoSearchReq
}

// KsAdvertiserAgentInfoSearchRes 列表返回结果
type KsAdvertiserAgentInfoSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.KsAdvertiserAgentInfoSearchRes
}

// 共享代理商账户
type KsAdvertiserAgentShareReq struct {
	g.Meta `path:"/share" tags:"快手代理商信息表" method:"post" summary:"共享代理商账户"`
	commonApi.Author
	model.KsAdvertiserAgentInfoShareReq
}

type KsAdvertiserAgentShareRes struct {
	g.Meta `mime:"application/json"`
	*model.KsAdvertiserAgentInfoShareRes
}

// KsAdvertiserAgentInfoAddReq 添加操作请求参数
type KsAdvertiserAgentInfoAddReq struct {
	g.Meta `path:"/add" tags:"快手代理商信息表" method:"post" summary:"快手代理商信息表添加"`
	commonApi.Author
	*model.KsAdvertiserAgentInfoAddReq
}

// KsAdvertiserAgentInfoAddRes 添加操作返回结果
type KsAdvertiserAgentInfoAddRes struct {
	commonApi.EmptyRes
}

// KsAdvertiserAgentInfoEditReq 修改操作请求参数
type KsAdvertiserAgentInfoEditReq struct {
	g.Meta `path:"/edit" tags:"快手代理商信息表" method:"put" summary:"快手代理商信息表修改"`
	commonApi.Author
	*model.KsAdvertiserAgentInfoEditReq
}

// KsAdvertiserAgentInfoEditRes 修改操作返回结果
type KsAdvertiserAgentInfoEditRes struct {
	commonApi.EmptyRes
}

// KsAdvertiserAgentInfoGetReq 获取一条数据请求
type KsAdvertiserAgentInfoGetReq struct {
	g.Meta `path:"/get" tags:"快手代理商信息表" method:"get" summary:"获取快手代理商信息表信息"`
	commonApi.Author
	Id uint64 `p:"id" v:"required#主键必须"` //通过主键获取
}

// KsAdvertiserAgentInfoGetRes 获取一条数据结果
type KsAdvertiserAgentInfoGetRes struct {
	g.Meta `mime:"application/json"`
	*model.KsAdvertiserAgentInfoInfoRes
}

// KsAdvertiserAgentInfoDeleteReq 删除数据请求
type KsAdvertiserAgentInfoDeleteReq struct {
	g.Meta `path:"/delete" tags:"快手代理商信息表" method:"delete" summary:"删除快手代理商信息表"`
	commonApi.Author
	Ids []uint64 `p:"ids" v:"required#主键必须"` //通过主键删除
}

// KsAdvertiserAgentInfoDeleteRes 删除数据返回
type KsAdvertiserAgentInfoDeleteRes struct {
	commonApi.EmptyRes
}
