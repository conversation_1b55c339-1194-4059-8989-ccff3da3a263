package api

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
)

// QueryCreativeCategoryListService 获取创意分类查询接口
type QueryCreativeCategoryListService struct {
	ctx     context.Context
	cfg     *Configuration
	token   string
	Request *ActionBarTextListRequest
}

// CreativeCategoryListListRequest 获取创意分类查询接口
type CreativeCategoryListListRequest struct {
	// AdvertiserID 广告主id
	AdvertiserID uint64 `json:"advertiser_id,omitempty"`
}

// CreativeCategoryListListResponse 创意分类 API Response
type CreativeCategoryListListResponse struct {
	// ExposeTagView 创意推荐理由
	AdMarketCreativeCategoryView []AdMarketCreativeCategoryView `json:"details,omitempty"`
}

type AdMarketCreativeCategoryView struct {
	// category_id 分类id  若没有其他节点，以此节点的分类 id 为 parent_id，则此节点为叶子结点
	CategoryID int `json:"category_id,omitempty"`
	// parent_id 分类父id
	ParentID int `json:"parent_id,omitempty"`
	// category_name 分类名称
	CategoryName string `json:"category_name,omitempty"`
	// level 分类级别
	Level string `json:"level,omitempty"`
}

func (r *QueryCreativeCategoryListService) SetCfg(cfg *Configuration) *QueryCreativeCategoryListService {
	r.cfg = cfg
	return r
}

func (r *QueryCreativeCategoryListService) SetReq(req ActionBarTextListRequest) *QueryCreativeCategoryListService {
	r.Request = &req
	return r
}

func (r *QueryCreativeCategoryListService) AccessToken(accessToken string) *QueryCreativeCategoryListService {
	r.token = accessToken
	return r
}

func (r *QueryCreativeCategoryListService) Do() (data *KsBaseResp[CreativeCategoryListListResponse], err error) {
	localBasePath := r.cfg.BasePath
	localVarPath := localBasePath + "/rest/openapi/v1/creative/creative_category/list"
	response, err := r.cfg.HTTPClient.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("Access-Token", r.token).
		SetBody(r.Request).
		SetResult(&KsBaseResp[CreativeCategoryListListResponse]{}).
		Post(localVarPath)
	if err != nil {
		return nil, err
	}
	resp := new(KsBaseResp[CreativeCategoryListListResponse])
	// 将 JSON 响应解码到结构体中
	err = json.Unmarshal(response.Body(), &resp)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("/rest/openapi/v1/creative/creative_category/list解析响应出错: %v\n", err))
	}
	if resp.Code == 0 && resp.Data != nil {
		return resp, nil
	} else {
		return resp, errors.New(resp.Message)
	}
}
