// ==========================================================================
// GFast自动生成controller操作代码。
// 生成日期：2025-08-22 11:52:17
// 生成路径: internal/app/ad/controller/ks_advertiser_strategy_creative.go
// 生成人：cq
// desc:快手策略组-广告创意
// company:云南奇讯科技有限公司
// ==========================================================================

package controller

import (
	"context"

	"github.com/tiger1103/gfast/v3/api/v1/ad"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	systemController "github.com/tiger1103/gfast/v3/internal/app/system/controller"
)

type ksAdvertiserStrategyCreativeController struct {
	systemController.BaseController
}

var KsAdvertiserStrategyCreative = new(ksAdvertiserStrategyCreativeController)

// List 列表
func (c *ksAdvertiserStrategyCreativeController) List(ctx context.Context, req *ad.KsAdvertiserStrategyCreativeSearchReq) (res *ad.KsAdvertiserStrategyCreativeSearchRes, err error) {
	res = new(ad.KsAdvertiserStrategyCreativeSearchRes)
	res.KsAdvertiserStrategyCreativeSearchRes, err = service.KsAdvertiserStrategyCreative().List(ctx, &req.KsAdvertiserStrategyCreativeSearchReq)
	return
}

// Get 获取快手策略组-广告创意
func (c *ksAdvertiserStrategyCreativeController) Get(ctx context.Context, req *ad.KsAdvertiserStrategyCreativeGetReq) (res *ad.KsAdvertiserStrategyCreativeGetRes, err error) {
	res = new(ad.KsAdvertiserStrategyCreativeGetRes)
	res.KsAdvertiserStrategyCreativeInfoRes, err = service.KsAdvertiserStrategyCreative().GetById(ctx, req.Id)
	return
}

// Add 添加快手策略组-广告创意
func (c *ksAdvertiserStrategyCreativeController) Add(ctx context.Context, req *ad.KsAdvertiserStrategyCreativeAddReq) (res *ad.KsAdvertiserStrategyCreativeAddRes, err error) {
	err = service.KsAdvertiserStrategyCreative().Add(ctx, req.KsAdvertiserStrategyCreativeAddReq)
	return
}

// Edit 修改快手策略组-广告创意
func (c *ksAdvertiserStrategyCreativeController) Edit(ctx context.Context, req *ad.KsAdvertiserStrategyCreativeEditReq) (res *ad.KsAdvertiserStrategyCreativeEditRes, err error) {
	err = service.KsAdvertiserStrategyCreative().Edit(ctx, req.KsAdvertiserStrategyCreativeEditReq)
	return
}

// Delete 删除快手策略组-广告创意
func (c *ksAdvertiserStrategyCreativeController) Delete(ctx context.Context, req *ad.KsAdvertiserStrategyCreativeDeleteReq) (res *ad.KsAdvertiserStrategyCreativeDeleteRes, err error) {
	err = service.KsAdvertiserStrategyCreative().Delete(ctx, req.Ids)
	return
}
