// ==========================================================================
// GFast自动生成model entity操作代码。
// 生成日期：2025-08-22 11:52:27
// 生成路径: internal/app/ad/model/entity/ks_advertiser_strategy_material.go
// 生成人：cq
// desc:快手策略组-素材
// company:云南奇讯科技有限公司
// ==========================================================================

package do

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
)

// KsAdvertiserStrategyMaterial is the golang structure for table ks_advertiser_strategy_material.
type KsAdvertiserStrategyMaterial struct {
	gmeta.Meta               `orm:"table:ks_advertiser_strategy_material, do:true"`
	Id                       interface{} `orm:"id,primary" json:"id"`                                       // 主键ID
	StrategyId               interface{} `orm:"strategy_id" json:"strategyId"`                              // 策略组ID
	TaskId                   interface{} `orm:"task_id" json:"taskId"`                                      // 任务ID
	MaterialAllocationMethod interface{} `orm:"material_allocation_method" json:"materialAllocationMethod"` // 素材分配方式 平均分配：AVERAGE 全账户服用：SAME 分账户选择：ADVERTISER
	VideoCount               interface{} `orm:"video_count" json:"videoCount"`                              // 视频数量
	VideoLocked              interface{} `orm:"video_locked" json:"videoLocked"`                            // 视频锁定状态 0：未锁定 1：锁定
	ImageCount               interface{} `orm:"image_count" json:"imageCount"`                              // 图片数量
	ImageLocked              interface{} `orm:"image_locked" json:"imageLocked"`                            // 图片锁定状态 0：未锁定 1：锁定
	DescriptionMatchMethod   interface{} `orm:"description_match_method" json:"descriptionMatchMethod"`     // 文案匹配方式 MANUAL MATERIAL CREATIVE
	CreativeMaterialData     interface{} `orm:"creative_material_data" json:"creativeMaterialData"`         // 创意素材数据，包含广告主ID、素材列表等信息
	CreatedAt                *gtime.Time `orm:"created_at" json:"createdAt"`                                // 创建时间
	UpdatedAt                *gtime.Time `orm:"updated_at" json:"updatedAt"`                                // 更新时间
	DeletedAt                *gtime.Time `orm:"deleted_at" json:"deletedAt"`                                // 删除时间
}
