package api

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"time"
)

// GetDspCampaignListService 查询广告计划 DspCampaignList
type GetDspCampaignListService struct {
	ctx     context.Context
	cfg     *Configuration
	token   string
	Request *GetDspCampaignListReq
}

// GetDspCampaignListReq 请求结构体
type GetDspCampaignListReq struct {
	AdType         int     `json:"ad_type"`          // 当补为错误时错
	CampaignID     int64   `json:"campaign_id"`      // 广告计划ID（过滤筛选条件，空缺视为无误删除件）
	CampaignIds    []int64 `json:"campaign_ids"`     // 广告计划结果（不超过60个）
	CampaignName   string  `json:"campaign_name"`    // 计划名称（过滤筛选条件，空缺视为无误删除件）
	CampaignType   int     `json:"campaign_type"`    // 当补为错误时错
	EndDate        string  `json:"end_date"`         // 检查时间（格式：yyyy-MM-dd，与start_date同时修改）
	Page           int     `json:"page"`             // 请求的声明数（默认1）
	PageSize       int     `json:"page_size"`        // 请求的传递行数（默认20）
	PutStatusList  []int   `json:"put_status_list"`  // 计划状态筛选（1:设定 2:暂停 3:删除）
	StartDate      string  `json:"start_date"`       // 开始时间（格式：yyyy-MM-dd，与end_date同时修改）
	Status         int     `json:"status"`           // 计划状态（1:暂停 4:有效 5:删除 -2:不限）
	TimeFilterType int     `json:"time_filter_type"` // 时间筛选类型（规则见备注）
	AdvertiserID   int64   `json:"advertiser_id"`    // 广告主ID（从access_token获取）必填
}

// GetDspCampaignListResp
type GetDspCampaignListResp struct {
	Code      int                     `json:"code"`       // 返回码
	Data      *GetDspCampaignListData `json:"data"`       // 数据内容
	Message   string                  `json:"message"`    // 返回消息
	RequestID string                  `json:"request_id"` // 请求 ID
}

// GetDspCampaignListData
type GetDspCampaignListData struct {
	TotalCount int              `json:"total_count"` // 数据总数
	Details    []CampaignDetail `json:"details"`     // 详情列表
}

// CampaignDetail 表示 details 中的每一项
type CampaignDetail struct {
	PhotoPackageDetails            []PhotoPackageDetail `json:"photo_package_details"`              // 图片包详情（可能为 null）
	AdType                         int                  `json:"ad_type"`                            // 广告类型
	CampaignType                   int                  `json:"campaign_type"`                      // 广告系列类型
	CampaignDeepConversionType     int                  `json:"campaign_deep_conversion_type"`      // 深度转化类型
	BidType                        int                  `json:"bid_type"`                           // 出价类型
	PutStatus                      int                  `json:"put_status"`                         // 投放状态
	CampaignOcpxActionTypeName     string               `json:"campaign_ocpx_action_type_name"`     // 智投计划优化目标名称 可能为null
	CapRoiRatio                    float64              `json:"cap_roi_ratio"`                      // ROI 上限比例
	AutoBuildNameRule              *AutoBuildNameRule   `json:"auto_build_name_rule"`               // 自动命名规则
	CampaignOcpxActionType         int                  `json:"campaign_ocpx_action_type"`          // OCPX 动作类型
	CampaignName                   string               `json:"campaign_name"`                      // 广告系列名称
	UpdateTime                     string               `json:"update_time"`                        // 更新时间
	DspVersion                     int                  `json:"dsp_version"`                        // DSP 版本
	PeriodicDeliveryType           int                  `json:"periodic_delivery_type"`             // 周期投放类型
	CampaignDeepConversionTypeName string               `json:"campaign_deep_conversion_type_name"` // 智投计划深度优化目标名称
	CampaignSubType                int                  `json:"campaign_sub_type"`                  // 广告系列子类型
	ConstraintCpa                  int                  `json:"constraint_cpa"`                     // CPA 限制
	AutoAdjust                     int                  `json:"auto_adjust"`                        // 是否自动调节
	ContinuePeriodType             int                  `json:"continue_period_type"`               // 连续周期类型
	CampaignID                     int64                `json:"campaign_id"`                        // 广告系列 ID
	ConstraintActionType           int                  `json:"constraint_action_type"`             // 动作类型限制
	DayBudget                      int                  `json:"day_budget"`                         // 每日预算
	AutoManage                     int                  `json:"auto_manage"`                        // 是否自动管理
	AutoPhotoScope                 int                  `json:"auto_photo_scope"`                   // 自动图片范围
	CreateTime                     string               `json:"create_time"`                        // 创建时间
	AutoBuild                      int                  `json:"auto_build"`                         // 是否自动构建
	PeriodicDays                   int                  `json:"periodic_days"`                      // 周期天数
	CapBid                         int                  `json:"cap_bid"`                            // 出价上限
	RangeBudget                    int                  `json:"range_budget"`                       // 区间预算
	DayBudgetSchedule              []int64              `json:"day_budget_schedule"`                // 分日预算
	Status                         int                  `json:"status"`                             // 状态
}

type PhotoPackageDetail struct {
	PhotoPackageId int      `json:"photo_package_id"`
	Name           string   `json:"name"`
	Status         int      `json:"status"`
	PhotoIdInfo    []string `json:"photo_id_info"`
}

// AutoBuildNameRule 表示自动命名规则
type AutoBuildNameRule struct {
	CreativeNameRule string `json:"creative_name_rule"` // 创意命名规则
	UnitNameRule     string `json:"unit_name_rule"`     // 单元命名规则
}

func (r *GetDspCampaignListService) SetCfg(cfg *Configuration) *GetDspCampaignListService {
	r.cfg = cfg
	return r
}

func (r *GetDspCampaignListService) SetReq(req GetDspCampaignListReq) *GetDspCampaignListService {
	r.Request = &req
	return r
}

func (r *GetDspCampaignListService) AccessToken(accessToken string) *GetDspCampaignListService {
	r.token = accessToken
	return r
}

func (r *GetDspCampaignListService) Do() (data *GetDspCampaignListResp, err error) {
	localBasePath := r.cfg.BasePath
	localVarPath := localBasePath + "/rest/openapi/gw/dsp/campaign/list"
	response, err := r.cfg.HTTPClient.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("Access-Token", r.token).
		SetBody(r.Request).
		SetResult(&GetDspCampaignListResp{}).
		Post(localVarPath)
	if err != nil {
		return nil, err
	}
	resp := new(GetDspCampaignListResp)
	// 将 JSON 响应解码到结构体中
	err = json.Unmarshal(response.Body(), &resp)
	if err != nil {
		if strings.Contains(err.Error(), "文件操作过于频繁") {
			time.Sleep(200 * time.Millisecond)
			return r.Do()
		}
		return nil, errors.New(fmt.Sprintf("/rest/openapi/gw/dsp/campaign/list解析响应出错: %v\n", err))
	}
	if resp.Code == 0 && resp.Data != nil {
		return resp, nil
	} else {
		return resp, errors.New(resp.Message)
	}
}
