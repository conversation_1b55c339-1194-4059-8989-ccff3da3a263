package api

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
)

// GetAdvertiserInfoService  获取广告主资质信息
type GetAdvertiserInfoService struct {
	ctx     context.Context
	cfg     *Configuration
	token   string
	Request *GetAdvertiserInfoReq
}

type GetAdvertiserInfoReq struct {
	AdvertiserId int `json:"advertiser_id"` // 广告主id
}

type GetAdvertiserInfoResp struct {
	Code    int                      `json:"code"`    // 状态码
	Message string                   `json:"message"` // 返回信息
	Data    *GetAdvertiserInfoDetail `json:"data"`    // 详情
}

type GetAdvertiserInfoDetail struct {
	PrimaryIndustryName string `json:"primary_industry_name"` // 一级行业名称
	IndustryID          int64  `json:"industry_id"`           // 二级行业ID
	IndustryName        string `json:"industry_name"`         // 二级行业名称
	UserID              int64  `json:"user_id"`               // 快手账户ID
	UserName            string `json:"user_name"`             // 快手账户名称
	DeliveryType        int    `json:"delivery_type"`         // 投放方式：0 默认；1 优先效果
	PrimaryIndustryID   int64  `json:"primary_industry_id"`   // 一级行业ID
	EffectFirst         int    `json:"effect_first"`          // 优先效果策略：1 开启；其他未开启
	CorporationName     string `json:"corporation_name"`      // 公司名称
	ProductName         string `json:"product_name"`          // 账户产品名称
}

func (r *GetAdvertiserInfoService) SetCfg(cfg *Configuration) *GetAdvertiserInfoService {
	r.cfg = cfg
	return r
}

func (r *GetAdvertiserInfoService) SetReq(req GetAdvertiserInfoReq) *GetAdvertiserInfoService {
	r.Request = &req
	return r
}

func (r *GetAdvertiserInfoService) AccessToken(accessToken string) *GetAdvertiserInfoService {
	r.token = accessToken
	return r
}

func (r *GetAdvertiserInfoService) Do() (data *GetAdvertiserInfoResp, err error) {
	localBasePath := r.cfg.BasePath
	localVarPath := localBasePath + "/rest/openapi/v1/advertiser/info"
	response, err := r.cfg.HTTPClient.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("Access-Token", r.token).
		SetBody(r.Request).
		SetResult(&GetAdvertiserInfoResp{}).
		Post(localVarPath)
	if err != nil {
		return nil, err
	}
	resp := new(GetAdvertiserInfoResp)
	// 将 JSON 响应解码到结构体中
	err = json.Unmarshal(response.Body(), &resp)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("/rest/openapi/v1/advertiser/info解析响应出错: %v\n", err))
	}
	if resp.Code == 0 && resp.Data != nil {
		return resp, nil
	} else {
		return resp, errors.New(resp.Message)
	}
}
