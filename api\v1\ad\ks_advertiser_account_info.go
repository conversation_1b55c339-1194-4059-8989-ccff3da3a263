// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2025-08-14 14:09:04
// 生成路径: api/v1/ad/ks_advertiser_account_info.go
// 生成人：cyao
// desc:快手广告账户表格相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package ad

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	comModel "github.com/tiger1103/gfast/v3/internal/app/common/model"
)

// KsAdvertiserAccountInfoSearchReq 分页请求参数
type KsAdvertiserAccountInfoSearchReq struct {
	g.Meta `path:"/list" tags:"快手广告账户表格" method:"post" summary:"快手广告账户表格列表"`
	commonApi.Author
	model.KsAdvertiserAccountInfoSearchReq
}

// KsAdvertiserAccountInfoSearchRes 列表返回结果
type KsAdvertiserAccountInfoSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.KsAdvertiserAccountInfoSearchRes
}

type UpLoadVideoReq struct {
	g.Meta `path:"/uploadVideo" tags:"快手广告账户表格" method:"post" summary:"上传视频"`
	commonApi.Author
	*model.UpLoadVideoReq
}

type UpLoadVideoRes struct {
	g.Meta `mime:"application/json"`
	// 没有通过的账户ID
	*model.UpLoadVideoRes
}

// 批量设置归属人员
type KsAdvertiserAccountInfoSetOwnerReq struct {
	g.Meta `path:"/setOwner" tags:"快手广告账户表格" method:"post" summary:"批量设置归属人员"`
	commonApi.Author
	*model.KsAdvertiserAccountInfoSetOwnerReq
}

type KsAdvertiserAccountInfoSetOwnerRes struct {
	commonApi.EmptyRes
}

// 通过代理商+广告账户id导入广告主账户
type KsAdvertiserAccountInfoImportReq struct {
	g.Meta `path:"/import" tags:"快手广告账户表格" method:"post" summary:"通过广告账户ids导入广告主账户"`
	commonApi.Author
	model.KsAdvertiserAccountInfoImportReq
}

type KsAdvertiserAccountInfoImportRes struct {
	g.Meta `mime:"application/json"`
	// 没有通过的账户ID
	AccountIds []int
}

// KsAdvertiserGetImportListReq
type KsAdvertiserGetImportListReq struct {
	g.Meta `path:"/getImportList" tags:"快手广告账户表格" method:"post" summary:"通过广告账户ids获取导入列表"`
	commonApi.Author
	model.KsAdvertiserGetImportListReq
}

type KsAdvertiserGetImportListRes struct {
	g.Meta `mime:"application/json"`
	*model.KsAdvertiserGetImportListRes
}

// 通过广告账户id列表导入账户
type KsAdvertiserAccountInfoImport2Req struct {
	g.Meta `path:"/import2" tags:"快手广告账户表格" method:"post" summary:"通过广告账户列表导入广告主账户"`
	commonApi.Author
	model.KsAdvertiserAccountInfoImportReq
}

type KsAdvertiserAccountInfoImport2Res struct {
	commonApi.EmptyRes
}

// KsAdvertiserAccountInfoAddReq 添加操作请求参数
type KsAdvertiserAccountInfoAddReq struct {
	g.Meta `path:"/add" tags:"快手广告账户表格" method:"post" summary:"快手广告账户表格添加"`
	commonApi.Author
	*model.KsAdvertiserAccountInfoAddReq
}

// KsAdvertiserAccountInfoAddRes 添加操作返回结果
type KsAdvertiserAccountInfoAddRes struct {
	commonApi.EmptyRes
}

// KsAdvertiserAccountInfoEditReq 修改操作请求参数
type KsAdvertiserAccountInfoEditReq struct {
	g.Meta `path:"/edit" tags:"快手广告账户表格" method:"put" summary:"快手广告账户表格修改"`
	commonApi.Author
	*model.KsAdvertiserAccountInfoEditReq
}

// KsAdvertiserAccountInfoEditRes 修改操作返回结果
type KsAdvertiserAccountInfoEditRes struct {
	commonApi.EmptyRes
}

// KsAdvertiserAccountInfoGetReq 获取一条数据请求
type KsAdvertiserAccountInfoGetReq struct {
	g.Meta `path:"/get" tags:"快手广告账户表格" method:"get" summary:"获取快手广告账户表格信息"`
	commonApi.Author
	AccountId int64 `p:"accountId" v:"required#主键必须"` //通过主键获取
}

// KsAdvertiserAccountInfoGetRes 获取一条数据结果
type KsAdvertiserAccountInfoGetRes struct {
	g.Meta `mime:"application/json"`
	*model.KsAdvertiserAccountInfoInfoRes
}

// KsAdvertiserAccountInfoDeleteReq 删除数据请求
type KsAdvertiserAccountInfoDeleteReq struct {
	g.Meta `path:"/delete" tags:"快手广告账户表格" method:"delete" summary:"删除快手广告账户表格"`
	commonApi.Author
	AccountIds []int64 `p:"accountIds" v:"required#主键必须"` //通过主键删除
}

// KsAdvertiserAccountInfoDeleteRes 删除数据返回
type KsAdvertiserAccountInfoDeleteRes struct {
	commonApi.EmptyRes
}

// GetKsAdCorporationListReq 获取账户主体请求
type GetKsAdCorporationListReq struct {
	g.Meta `path:"/getCorporationList" tags:"快手广告账户表格" method:"post" summary:"获取账户主体"`
	commonApi.Author
	comModel.PageReq
	CorporationName string `p:"corporationName"`
}

// GetKsAdCorporationListRes 获取账户主体返回
type GetKsAdCorporationListRes struct {
	commonApi.EmptyRes
	*model.GetCorporationListRes
}

// GetKsAdAccountAutoInfoReq 查询账户智投配置请求
type GetKsAdAccountAutoInfoReq struct {
	g.Meta `path:"/getAccountAutoInfo" tags:"快手广告账户表格" method:"post" summary:"查询账户智投配置信息"`
	commonApi.Author
	AdvertiserId int64 `p:"advertiser_id"`
}

// GetKsAdAccountAutoInfoRes 查询账户智投配置返回
type GetKsAdAccountAutoInfoRes struct {
	commonApi.EmptyRes
	*model.GetAccountAutoInfoRes
}

// GetKsAdAccountIncExploreReq 查询账户增量探索配置请求
type GetKsAdAccountIncExploreReq struct {
	g.Meta `path:"/getAccountIncExplore" tags:"快手广告账户表格" method:"post" summary:"查询账户增量探索配置"`
	commonApi.Author
	AdvertiserId int64 `p:"advertiser_id"`
}

// GetKsAdAccountIncExploreRes 查询账户增量探索配置返回
type GetKsAdAccountIncExploreRes struct {
	commonApi.EmptyRes
	*model.GetAccountIncExploreRes
}

// AddKsAdAccountIncExploreReq 新增账户增量探索配置请求
type AddKsAdAccountIncExploreReq struct {
	g.Meta `path:"/addAccountIncExplore" tags:"快手广告账户表格" method:"post" summary:"新增账户增量探索配置"`
	commonApi.Author
	model.AddAccountIncExploreReq
}

// AddKsAdAccountIncExploreRes 新增账户增量探索配置返回
type AddKsAdAccountIncExploreRes struct {
	commonApi.EmptyRes
	*model.AddAccountIncExploreRes
}

// UpdateKsAdAccountIncExploreReq 编辑账户增量探索配置请求
type UpdateKsAdAccountIncExploreReq struct {
	g.Meta `path:"/updateAccountIncExplore" tags:"快手广告账户表格" method:"post" summary:"编辑账户增量探索配置"`
	commonApi.Author
	model.UpdateAccountIncExploreReq
}

// UpdateKsAdAccountIncExploreRes 编辑账户增量探索配置返回
type UpdateKsAdAccountIncExploreRes struct {
	commonApi.EmptyRes
	*model.UpdateAccountIncExploreRes
}

// DeleteKsAdAccountIncExploreReq 删除账户增量探索配置请求
type DeleteKsAdAccountIncExploreReq struct {
	g.Meta `path:"/deleteAccountIncExplore" tags:"快手广告账户表格" method:"post" summary:"删除账户增量探索配置"`
	commonApi.Author
	model.DeleteAccountIncExploreReq
}

// DeleteKsAdAccountIncExploreRes 删除账户增量探索配置返回
type DeleteKsAdAccountIncExploreRes struct {
	commonApi.EmptyRes
	*model.DeleteAccountIncExploreRes
}

// PauseKsAdAccountIncExploreReq 暂停账户增量探索配置请求
type PauseKsAdAccountIncExploreReq struct {
	g.Meta `path:"/pauseAccountIncExplore" tags:"快手广告账户表格" method:"post" summary:"暂停账户增量探索配置"`
	commonApi.Author
	model.PauseAccountIncExploreReq
}

// PauseKsAdAccountIncExploreRes 暂停账户增量探索配置返回
type PauseKsAdAccountIncExploreRes struct {
	commonApi.EmptyRes
	*model.PauseAccountIncExploreRes
}

// RebootKsAdAccountIncExploreReq 重启账户增量探索配置请求
type RebootKsAdAccountIncExploreReq struct {
	g.Meta `path:"/rebootAccountIncExplore" tags:"快手广告账户表格" method:"post" summary:"重启账户增量探索配置"`
	commonApi.Author
	model.RebootAccountIncExploreReq
}

// RebootKsAdAccountIncExploreRes 重启账户增量探索配置返回
type RebootKsAdAccountIncExploreRes struct {
	commonApi.EmptyRes
	*model.RebootAccountIncExploreRes
}

// GetAccountBalanceReq 实时查询账户余额请求
type GetAccountBalanceReq struct {
	g.Meta `path:"/getAccountBalance" tags:"快手广告账户表格" method:"post" summary:"实时查询账户余额"`
	commonApi.Author
	AdvertiserIds []int64 `p:"advertiser_ids" pc:"账户ID列表"`
}

// GetAccountBalanceRes 实时查询账户余额返回
type GetAccountBalanceRes struct {
	commonApi.EmptyRes
	*model.GetAccountBalanceRes
}
