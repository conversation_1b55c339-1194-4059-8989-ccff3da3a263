// ==========================================================================
// GFast自动生成model操作代码。
// 生成日期：2025-08-12 15:45:08
// 生成路径: internal/app/ad/model/ks_advertiser_fund.go
// 生成人：cyao
// desc:广告主资金信息
// company:云南奇讯科技有限公司
// ==========================================================================

package model

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
	comModel "github.com/tiger1103/gfast/v3/internal/app/common/model"
)

// KsAdvertiserFundInfoRes is the golang structure for table ks_advertiser_fund.
type KsAdvertiserFundInfoRes struct {
	gmeta.Meta      `orm:"table:ks_advertiser_fund"`
	AdvertiserId    int64       `orm:"advertiser_id,primary" json:"advertiserId" dc:"快手账户ID（广告账户ID）"` // 快手账户ID（广告账户ID）
	DirectRebate    float64     `orm:"direct_rebate" json:"directRebate" dc:"激励余额"`                   // 激励余额
	ContractRebate  string      `orm:"contract_rebate" json:"contractRebate" dc:"框返余额"`               // 框返余额
	RechargeBalance float64     `orm:"recharge_balance" json:"rechargeBalance" dc:"充值余额"`             // 充值余额
	Balance         float64     `orm:"balance" json:"balance" dc:"账户总余额"`                             // 账户总余额
	CreatedAt       *gtime.Time `orm:"created_at" json:"createdAt" dc:"创建时间"`                         // 创建时间
	UpdatedAt       *gtime.Time `orm:"updated_at" json:"updatedAt" dc:"更新时间"`                         // 更新时间
}

type KsAdvertiserFundListRes struct {
	AdvertiserId    int64       `json:"advertiserId" dc:"快手账户ID（广告账户ID）"`
	DirectRebate    float64     `json:"directRebate" dc:"激励余额"`
	ContractRebate  string      `json:"contractRebate" dc:"框返余额"`
	RechargeBalance float64     `json:"rechargeBalance" dc:"充值余额"`
	Balance         float64     `json:"balance" dc:"账户总余额"`
	CreatedAt       *gtime.Time `json:"createdAt" dc:"创建时间"`
}

// KsAdvertiserFundSearchReq 分页请求参数
type KsAdvertiserFundSearchReq struct {
	comModel.PageReq
	AdvertiserId    string `p:"advertiserId" dc:"快手账户ID（广告账户ID）"`                                       //快手账户ID（广告账户ID）
	DirectRebate    string `p:"directRebate" v:"directRebate@float#激励余额需为浮点数" dc:"激励余额"`                //激励余额
	ContractRebate  string `p:"contractRebate" dc:"框返余额"`                                               //框返余额
	RechargeBalance string `p:"rechargeBalance" v:"rechargeBalance@float#充值余额需为浮点数" dc:"充值余额"`          //充值余额
	Balance         string `p:"balance" v:"balance@float#账户总余额需为浮点数" dc:"账户总余额"`                        //账户总余额
	CreatedAt       string `p:"createdAt" v:"createdAt@datetime#创建时间需为YYYY-MM-DD hh:mm:ss格式" dc:"创建时间"` //创建时间
}

// KsAdvertiserFundSearchRes 列表返回结果
type KsAdvertiserFundSearchRes struct {
	comModel.ListRes
	List []*KsAdvertiserFundListRes `json:"list"`
}

// KsAdvertiserFundAddReq 添加操作请求参数
type KsAdvertiserFundAddReq struct {
	AdvertiserId    int64   `p:"advertiserId" v:"required#主键ID不能为空" dc:"快手账户ID（广告账户ID）"`
	DirectRebate    float64 `p:"directRebate" v:"required#激励余额不能为空" dc:"激励余额"`
	ContractRebate  string  `p:"contractRebate" v:"required#框返余额不能为空" dc:"框返余额"`
	RechargeBalance float64 `p:"rechargeBalance" v:"required#充值余额不能为空" dc:"充值余额"`
	Balance         float64 `p:"balance" v:"required#账户总余额不能为空" dc:"账户总余额"`
}

// KsAdvertiserFundEditReq 修改操作请求参数
type KsAdvertiserFundEditReq struct {
	AdvertiserId    int64   `p:"advertiserId" v:"required#主键ID不能为空" dc:"快手账户ID（广告账户ID）"`
	DirectRebate    float64 `p:"directRebate" v:"required#激励余额不能为空" dc:"激励余额"`
	ContractRebate  string  `p:"contractRebate" v:"required#框返余额不能为空" dc:"框返余额"`
	RechargeBalance float64 `p:"rechargeBalance" v:"required#充值余额不能为空" dc:"充值余额"`
	Balance         float64 `p:"balance" v:"required#账户总余额不能为空" dc:"账户总余额"`
}
