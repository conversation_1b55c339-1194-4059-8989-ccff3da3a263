// ==========================================================================
// GFast自动生成dao internal操作代码。
// 生成日期：2025-08-13 16:33:59
// 生成路径: internal/app/ad/dao/internal/ks_advertiser_campaign.go
// 生成人：cyao
// desc:快手广告计划
// company:云南奇讯科技有限公司
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// KsAdvertiserCampaignDao is the manager for logic model data accessing and custom defined data operations functions management.
type KsAdvertiserCampaignDao struct {
	table   string                      // Table is the underlying table name of the DAO.
	group   string                      // Group is the database configuration group name of current DAO.
	columns KsAdvertiserCampaignColumns // Columns is the short type for Columns, which contains all the column names of Table for convenient usage.
}

// KsAdvertiserCampaignColumns defines and stores column names for table ks_advertiser_campaign.
type KsAdvertiserCampaignColumns struct {
	CampaignId                     string // 广告计划 ID
	AdvertiserId                   string // 广告id
	PhotoPackageDetails            string // 图片包详情（可能为 null）
	AdType                         string // 广告类型
	CampaignType                   string // 广告系列类型
	CampaignDeepConversionType     string // 深度转化类型
	BidType                        string // 出价类型
	PutStatus                      string // 投放状态
	CampaignOcpxActionTypeName     string // 智投计划优化目标名称 可能为null
	CapRoiRatio                    string // ROI 上限比例
	CampaignOcpxActionType         string // OCPX 动作类型
	CampaignName                   string // 广告系列名称
	UpdateTime                     string // 更新时间
	DspVersion                     string // DSP 版本
	PeriodicDeliveryType           string // 周期投放类型
	CampaignDeepConversionTypeName string // 智投计划深度优化目标名称
	CampaignSubType                string // 广告系列子类型
	ConstraintCpa                  string // CPA 限制
	AutoAdjust                     string // 是否自动调节
	ContinuePeriodType             string // 连续周期类型
	ConstraintActionType           string // 动作类型限制
	DayBudget                      string // 每日预算
	AutoManage                     string // 是否自动管理
	AutoPhotoScope                 string // 自动图片范围
	CreateTime                     string // 创建时间
	AutoBuild                      string // 是否自动构建
	PeriodicDays                   string // 周期天数
	CapBid                         string // 出价上限
	RangeBudget                    string // 区间预算
	DayBudgetSchedule              string // 分日预算
	Status                         string // 状态
	UnitNameRule                   string // 单元命名规则
	CreativeNameRule               string // 创意命名规则
	CreatedAt                      string // 创建时间
	DeletedAt                      string // 删除时间
}

var ksAdvertiserCampaignColumns = KsAdvertiserCampaignColumns{
	CampaignId:                     "campaign_id",
	AdvertiserId:                   "advertiser_id",
	PhotoPackageDetails:            "photo_package_details",
	AdType:                         "ad_type",
	CampaignType:                   "campaign_type",
	CampaignDeepConversionType:     "campaign_deep_conversion_type",
	BidType:                        "bid_type",
	PutStatus:                      "put_status",
	CampaignOcpxActionTypeName:     "campaign_ocpx_action_type_name",
	CapRoiRatio:                    "cap_roi_ratio",
	CampaignOcpxActionType:         "campaign_ocpx_action_type",
	CampaignName:                   "campaign_name",
	UpdateTime:                     "update_time",
	DspVersion:                     "dsp_version",
	PeriodicDeliveryType:           "periodic_delivery_type",
	CampaignDeepConversionTypeName: "campaign_deep_conversion_type_name",
	CampaignSubType:                "campaign_sub_type",
	ConstraintCpa:                  "constraint_cpa",
	AutoAdjust:                     "auto_adjust",
	ContinuePeriodType:             "continue_period_type",
	ConstraintActionType:           "constraint_action_type",
	DayBudget:                      "day_budget",
	AutoManage:                     "auto_manage",
	AutoPhotoScope:                 "auto_photo_scope",
	CreateTime:                     "create_time",
	AutoBuild:                      "auto_build",
	PeriodicDays:                   "periodic_days",
	CapBid:                         "cap_bid",
	RangeBudget:                    "range_budget",
	DayBudgetSchedule:              "day_budget_schedule",
	Status:                         "status",
	UnitNameRule:                   "unit_name_rule",
	CreativeNameRule:               "creative_name_rule",
	CreatedAt:                      "created_at",
	DeletedAt:                      "deleted_at",
}

// NewKsAdvertiserCampaignDao creates and returns a new DAO object for table data access.
func NewKsAdvertiserCampaignDao() *KsAdvertiserCampaignDao {
	return &KsAdvertiserCampaignDao{
		group:   "default",
		table:   "ks_advertiser_campaign",
		columns: ksAdvertiserCampaignColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *KsAdvertiserCampaignDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *KsAdvertiserCampaignDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *KsAdvertiserCampaignDao) Columns() KsAdvertiserCampaignColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *KsAdvertiserCampaignDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *KsAdvertiserCampaignDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *KsAdvertiserCampaignDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
