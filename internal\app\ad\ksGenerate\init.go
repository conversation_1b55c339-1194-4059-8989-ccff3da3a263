package generate

import (
	"context"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	commonConsts "github.com/tiger1103/gfast/v3/internal/app/common/consts"
)

func init() {
	RegisterGenerate(GeneratedType1, &GroupByAudiencePackage{})
	RegisterGenerate(GeneratedType2, &GroupByCreative{})
	RegisterGenerate(GeneratedType3, &GroupByTitle{})
}

const (
	GeneratedType1 KsGeneratedType = iota + 1
	GeneratedType2
	GeneratedType3
)

type KsGeneratedType int

type IKsGenerate interface {
	// GetEstimateInfo 获取预估信息
	GetEstimateInfo(ctx context.Context, req *model.KsAdvertiserStrategyGenerateReq) (res map[int64]*model.EstimateInfo, err error)
	// GeneratePreviewBaseInfo 生成预览基本信息，根据基本信息去生成最终广告预览
	GeneratePreviewBaseInfo(ctx context.Context, req *model.KsAdvertiserStrategyGenerateReq, estimateInfoMap map[int64]*model.EstimateInfo) (res []*model.PreviewBaseInfo, err error)
}

var generateCollection map[KsGeneratedType]IKsGenerate

func RegisterGenerate(key KsGeneratedType, value IKsGenerate) {
	if generateCollection == nil {
		generateCollection = make(map[KsGeneratedType]IKsGenerate)
	}
	generateCollection[key] = value
}

// GetGenerate 根据生成方式获取具体实现
func GetGenerate(adGroupRule commonConsts.KsAdGroupRuleType) IKsGenerate {
	if adGroupRule == commonConsts.KsAdGroupRuleTypeAudiencePackage {
		return GetGenerateByType(GeneratedType1)
	} else if adGroupRule == commonConsts.KsAdGroupRuleTypeCreative {
		return GetGenerateByType(GeneratedType2)
	} else if adGroupRule == commonConsts.KsAdGroupRuleTypeTitle {
		return GetGenerateByType(GeneratedType3)
	}
	return nil
}

func GetGenerateByType(key KsGeneratedType) IKsGenerate {
	if generateCollection == nil {
		return nil
	}
	if v, ok := generateCollection[key]; ok {
		return v
	}
	return nil
}
