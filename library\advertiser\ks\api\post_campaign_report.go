package api

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
)

// CampaignReportService 广告计划数据（实时）
type CampaignReportService struct {
	ctx     context.Context
	cfg     *Configuration
	token   string
	Request *CampaignReportReq
}

// CampaignReportReq 广告主数据（实时）查询请求参数
type CampaignReportReq struct {
	AdvertiserID        int64    `json:"advertiser_id"`        // 必填，广告主 ID
	StartDate           string   `json:"start_date"`           // 查询开始日期，格式 yyyy-MM-dd（与 end_date 或 start_date_min/end_date_min 二选一）
	EndDate             string   `json:"end_date"`             // 查询结束日期，格式 yyyy-MM-dd
	Page                int      `json:"page"`                 // 页码，默认 1
	PageSize            int      `json:"page_size"`            // 每页数量，默认 20，最大 2000
	StartDateMin        *string  `json:"start_date_min"`       // 增量查询开始时间，格式 yyyy-MM-dd HH:mm（与 end_date_min 配合使用）
	EndDateMin          *string  `json:"end_date_min"`         // 增量查询结束时间，格式 yyyy-MM-dd HH:mm
	CampaignType        *int     `json:"campaign_type"`        // 计划类型，过滤筛选条件 1 - 作品推广；2 - 提升应用安装；3 - 获取电商下单；4 - 推广品牌活动；5 - 收集销售线索；6 - 保量广告；7 - 提高应用活跃。
	ReportDims          []string `json:"report_dims"`          // 维度数组，如 ["adScene"]，可选
	TemporalGranularity string   `json:"temporal_granularity"` // 时间粒度：DAILY（天）或 HOURLY（小时），默认 DAILY
	CampaignIds         []int64  `json:"campaign_ids"`         // 广告计划 ID 集，过滤筛选条件，单次查询数量不超过 5000
}

// CampaignReport 数据内容
type CampaignReport struct {
	Details    []CampaignDetails `json:"details"`     // 数据明细信息
	TotalCount int64             `json:"total_count"` // 数据的总行数
}

type CampaignDetails struct {
	PrivateMessageSentCost                     float64 `json:"private_message_sent_cost"`                                // 私信消息转化成本
	PrivateMessageSentRatio                    float64 `json:"private_message_sent_ratio"`                               // 私信消息转化率
	PrivateMessageSentCnt                      int64   `json:"private_message_sent_cnt"`                                 // 私信消息数
	LeadsSubmitCost                            float64 `json:"leads_submit_cost"`                                        // 直接私信留资成本
	LeadsSubmitCntRatio                        float64 `json:"leads_submit_cnt_ratio"`                                   // 直接私信留资率
	LeadsSubmitCnt                             int64   `json:"leads_submit_cnt"`                                         // 直接私信留资数
	PlayedNum                                  int64   `json:"played_num"`                                               // 播放数
	MinigameIaaPurchaseAmountWeekByConvRoi     float64 `json:"minigame_iaa_purchase_amount_week_by_conversion_roi"`      // 激活后七日广告变现ROI
	MinigameIaaPurchaseAmountThreeDayByConvRoi float64 `json:"minigame_iaa_purchase_amount_three_day_by_conversion_roi"` // 激活后三日广告变现ROI
	MinigameIaaPurchaseAmountFirstDayRoi       float64 `json:"minigame_iaa_purchase_amount_first_day_roi"`               // 当日广告变现ROI
	MinigameIaaPurchaseAmountWeekByConv        float64 `json:"minigame_iaa_purchase_amount_week_by_conversion"`          // 激活后七日广告LTV
	MinigameIaaPurchaseAmountThreeDayByConv    float64 `json:"minigame_iaa_purchase_amount_three_day_by_conversion"`     // 激活后三日广告LTV
	MinigameIaaPurchaseAmountFirstDay          float64 `json:"minigame_iaa_purchase_amount_first_day"`                   // 当日广告LTV
	MinigameIaaPurchaseRoi                     float64 `json:"minigame_iaa_purchase_roi"`                                // IAA广告变现ROI
	MinigameIaaPurchaseAmount                  float64 `json:"minigame_iaa_purchase_amount"`                             // IAA广告变现LTV
	MmuEffectiveCustomerAcquisition7dCnt       int64   `json:"mmu_effective_customer_acquisition_7d_cnt"`                // MMU识别产生的有效获客数（计费）
	MmuEffectiveCustomerAcquisitionCnt         int64   `json:"mmu_effective_customer_acquisition_cnt"`                   // MMU识别产生的有效获客数（回传）
	EffectiveCustomerAcquisition7dRatio        float64 `json:"effective_customer_acquisition_7d_ratio"`                  // 有效获客率（计费）
	EffectiveCustomerAcquisition7dCost         float64 `json:"effective_customer_acquisition_7d_cost"`                   // 有效获客成本（计费）
	EffectiveCustomerAcquisition7dCnt          int64   `json:"effective_customer_acquisition_7d_cnt"`                    // 有效获客数（计费）
	EventPay30DayOverallRoi                    float64 `json:"event_pay_30_day_overall_roi"`                             // 激活后30日整体ROI
	EventPay15DayOverallRoi                    float64 `json:"event_pay_15_day_overall_roi"`                             // 激活后15日整体ROI
	MinigameIaaPurchaseAmount30DayByConvRoi    float64 `json:"minigame_iaa_purchase_amount_30_day_by_conversion_roi"`    // 激活后30日广告变现ROI
	MinigameIaaPurchaseAmount15DayByConvRoi    float64 `json:"minigame_iaa_purchase_amount_15_day_by_conversion_roi"`    // 激活后15日广告变现ROI
	MinigameIaaPurchaseAmount30DayByConv       float64 `json:"minigame_iaa_purchase_amount_30_day_by_conversion"`        // 激活后30日广告LTV
	MinigameIaaPurchaseAmount15DayByConv       float64 `json:"minigame_iaa_purchase_amount_15_day_by_conversion"`        // 激活后15日广告LTV
	EventPayPurchaseAmount15DayByConv          float64 `json:"event_pay_purchase_amount_15_day_by_conversion"`           // 激活后15日付费金额
	Charge                                     float64 `json:"charge"`                                                   // 花费（元）
	Show                                       int64   `json:"show"`                                                     // 封面曝光数
	Aclick                                     int64   `json:"aclick"`                                                   // 素材曝光数
	Bclick                                     int64   `json:"bclick"`                                                   // 行为数
	Share                                      int64   `json:"share"`                                                    // 分享数
	Comment                                    int64   `json:"comment"`                                                  // 评论数
	Like                                       int64   `json:"like"`                                                     // 点赞数
	Follow                                     int64   `json:"follow"`                                                   // 新增粉丝数
	Report                                     int64   `json:"report"`                                                   // 举报数
	Block                                      int64   `json:"block"`                                                    // 拉黑数
	Negative                                   int64   `json:"negative"`                                                 // 减少此类作品数
	Activation                                 int64   `json:"activation"`                                               // 应用下载数据-激活数
	Submit                                     int64   `json:"submit"`                                                   // 提交按钮点击数（历史字段，同下方“线索提交个数”，预计年底删除该字段）
	AdPhotoPlayed10s                           int64   `json:"ad_photo_played_10s"`                                      // 10s播放数
	AdPhotoPlayed2s                            int64   `json:"ad_photo_played_2s"`                                       // 2s播放数
	AdPhotoPlayed75percent                     int64   `json:"ad_photo_played_75percent"`                                // 75%进度播放数
	ApproxPayCost                              float64 `json:"approx_pay_cost"`                                          // 淘系近似购买成本
	ApproxPayCount                             int64   `json:"approx_pay_count"`                                         // 近似购买数
	ApproxPayRatio                             float64 `json:"approx_pay_ratio"`                                         // 淘系近似购买率
	CancelLike                                 int64   `json:"cancel_like"`                                              // 取消点赞数
	ClickConversionRatio                       float64 `json:"click_conversion_ratio"`                                   // 点击激活成本
	ConversionCost                             float64 `json:"conversion_cost"`                                          // 激活单价
	DownloadCompletedCost                      float64 `json:"download_completed_cost"`                                  // 安卓下载完成单价（元）
	DownloadCompletedRatio                     float64 `json:"download_completed_ratio"`                                 // 安卓下载完成率
	DownloadConversionRatio                    float64 `json:"download_conversion_ratio"`                                // 下载完成激活率
	DownloadInstalled                          int64   `json:"download_installed"`                                       // 安卓安装完成数
	DownloadStartedCost                        float64 `json:"download_started_cost"`                                    // 安卓下载开始单价（元）
	DownloadStartedRatio                       float64 `json:"download_started_ratio"`                                   // 安卓下载开始率
	EventAdWatch10Times                        int64   `json:"event_ad_watch_10_times"`                                  // 10次广告广告观看数
	EventAdWatch10TimesCost                    float64 `json:"event_ad_watch_10_times_cost"`                             // 10次广告观看成本
	EventAdWatch10TimesRatio                   float64 `json:"event_ad_watch_10_times_ratio"`                            // 10次广告观看转化率
	EventAdWatch20Times                        int64   `json:"event_ad_watch_20_times"`                                  // 20次广告广告观看数
	EventAdWatch20TimesCost                    float64 `json:"event_ad_watch_20_times_cost"`                             // 20次广告观看成本
	EventAdWatch20TimesRatio                   float64 `json:"event_ad_watch_20_times_ratio"`                            // 20次广告观看转化率
	EventAdWatch5Times                         int64   `json:"event_ad_watch_5_times"`                                   // 5次广告广告观看数
	EventAdWatch5TimesCost                     float64 `json:"event_ad_watch_5_times_cost"`                              // 5次广告观看成本
	EventAdWatch5TimesRatio                    float64 `json:"event_ad_watch_5_times_ratio"`                             // 5次广告观看转化率
	EventAudition                              int64   `json:"event_audition"`                                           // 首次试听到课数
	EventConsultationValidRetained             int64   `json:"event_consultation_valid_retained"`                        // 留咨咨询数
	EventConsultationValidRetainedCost         float64 `json:"event_consultation_valid_retained_cost"`                   // 留咨咨询成本
	EventConsultationValidRetainedRatio        float64 `json:"event_consultation_valid_retained_ratio"`                  // 留咨咨询率
	EventConversionClickCost                   float64 `json:"event_conversion_click_cost"`                              // 有效咨询成本
	EventConversionClickRatio                  float64 `json:"event_conversion_click_ratio"`                             // 有效咨询率
	EventCreditGrantFirstDayApp                int64   `json:"event_credit_grant_first_day_app"`                         // app首日授信数
	EventCreditGrantFirstDayAppCost            float64 `json:"event_credit_grant_first_day_app_cost"`                    // 首日授信成本
	EventCreditGrantFirstDayAppRatio           float64 `json:"event_credit_grant_first_day_app_ratio"`                   // 首日授信率
	EventCreditGrantFirstDayLandingPage        int64   `json:"event_credit_grant_first_day_landing_page"`                // 落地页首日授信数
	EventCreditGrantFirstDayLandingPageCost    float64 `json:"event_credit_grant_first_day_landing_page_cost"`           // 落地页首日授信成本
	EventCreditGrantFirstDayLandingPageRatio   float64 `json:"event_credit_grant_first_day_landing_page_ratio"`          // 落地页首日授信率
	EventMakingCalls                           int64   `json:"event_making_calls"`                                       // 电话拨打数-用户点击电话按钮的次数
	EventMakingCallsCost                       float64 `json:"event_making_calls_cost"`                                  // 电话拨打成本
	EventMakingCallsRatio                      float64 `json:"event_making_calls_ratio"`                                 // 电话拨打率
	EventOrderSubmit                           int64   `json:"event_order_submit"`                                       // 提交订单数
	EventPayPurchaseAmountOneDay               float64 `json:"event_pay_purchase_amount_one_day"`                        // 激活后24h付费金额(回传时间)
	EventPayPurchaseAmountOneDayByConv         float64 `json:"event_pay_purchase_amount_one_day_by_conversion"`          // 激活后24h付费金额(激活时间)
	EventPayPurchaseAmountOneDayByConvRoi      float64 `json:"event_pay_purchase_amount_one_day_by_conversion_roi"`      // 激活后24小时付费ROI
	EventPayPurchaseAmountOneDayRoi            float64 `json:"event_pay_purchase_amount_one_day_roi"`                    // 激活后24h-ROI(回传时间)
	EventPayWeightedPurchaseAmount             float64 `json:"event_pay_weighted_purchase_amount"`                       // 加权付费金额-当日回传的付费行为所带来的加权付费金额，单位:元，当前用于保险行业
	EventPayWeightedPurchaseAmountFirstDay     float64 `json:"event_pay_weighted_purchase_amount_first_day"`             // 首日加权付费金额-当日激活的用户在当天产生的付费行为所带来的加权付费金额 单位:元，当前用于保险行业
	EventPreComponentConsultationValidRetained int64   `json:"event_pre_component_consultation_valid_retained"`          // 附加咨询组件留资咨询数
	EventWechatQrCodeLinkClick                 int64   `json:"event_wechat_qr_code_link_click"`                          // 微信小程序深度加粉数
	LiveEventGoodsView                         int64   `json:"live_event_goods_view"`                                    // 直播间商品点击数
	LivePlayed3s                               int64   `json:"live_played_3s"`                                           // 直播观看数
	PlayedEnd                                  int64   `json:"played_end"`                                               // 播放完成
	PlayedFiveSeconds                          int64   `json:"played_five_seconds"`                                      // 播放5s
	PlayedThreeSeconds                         int64   `json:"played_three_seconds"`                                     // 有效播放数
	AdScene                                    string  `json:"ad_scene"`                                                 // 字段描述，需要修改
	PlacementType                              string  `json:"placement_type"`                                           // 字段描述，需要修改
	CancelFollow                               int64   `json:"cancel_follow"`                                            // 取消关注数
	DownloadStarted                            int64   `json:"download_started"`                                         // 应用下载数据-安卓下载开始数
	DownloadCompleted                          int64   `json:"download_completed"`                                       // 应用下载数据-安卓下载完成数
	StatDate                                   string  `json:"stat_date"`                                                // 数据日期，格式：YYYY-MM-DD
	StatHour                                   int64   `json:"stat_hour"`                                                // 数据小时
	PhotoClick                                 int64   `json:"photo_click"`                                              // 封面点击数
	PhotoClickRatio                            float64 `json:"photo_click_ratio"`                                        // 封面点击率
	ActionRatio                                float64 `json:"action_ratio"`                                             // 行为率
	Impression1kCost                           float64 `json:"impression_1k_cost"`                                       // 平均千次曝光花费（元）
	PhotoClickCost                             float64 `json:"photo_click_cost"`                                         // 平均点击单价（元）
	Click1kCost                                float64 `json:"click_1k_cost"`                                            // 平均千次素材曝光花费(元)
	ActionCost                                 float64 `json:"action_cost"`                                              // 平均行为单价（元）
	EventPayFirstDay                           int64   `json:"event_pay_first_day"`                                      // 应用下载数据-首日付费次数
	EventPayPurchaseAmountFirstDay             float64 `json:"event_pay_purchase_amount_first_day"`                      // 应用下载数据-首日付费金额
	EventPayFirstDayRoi                        float64 `json:"event_pay_first_day_roi"`                                  // 应用下载数据-首日 ROI
	EventPay                                   int64   `json:"event_pay"`                                                // 应用下载数据-付费次数
	EventPayPurchaseAmount                     float64 `json:"event_pay_purchase_amount"`                                // 应用下载数据-付费金额
	EventPayRoi                                float64 `json:"event_pay_roi"`                                            // 应用下载数据-ROI
	EventRegister                              int64   `json:"event_register"`                                           // 应用下载数据-注册数
	EventRegisterCost                          float64 `json:"event_register_cost"`                                      // 应用下载数据-注册成本
	EventRegisterRatio                         float64 `json:"event_register_ratio"`                                     // 应用下载数据-注册率
	EventJinJianApp                            int64   `json:"event_jin_jian_app"`                                       // 应用下载数据-完件数
	EventJinJianAppCost                        float64 `json:"event_jin_jian_app_cost"`                                  // 应用下载数据-完件成本
	EventCreditGrantApp                        int64   `json:"event_credit_grant_app"`                                   // 应用下载数据-授信数
	EventCreditGrantAppCost                    float64 `json:"event_credit_grant_app_cost"`                              // 应用下载数据-授信成本
	EventCreditGrantAppRatio                   float64 `json:"event_credit_grant_app_ratio"`                             // 应用下载数据-授信率
	EventOrderPaid                             int64   `json:"event_order_paid"`                                         // 应用下载数据-付款成功数
	EventOrderPaidPurchaseAmount               float64 `json:"event_order_paid_purchase_amount"`                         // 应用下载数据-付款成功金额
	EventOrderPaidCost                         float64 `json:"event_order_paid_cost"`                                    // 应用下载数据-单次付款成本
	FormCount                                  int64   `json:"form_count"`                                               // 落地页数据-线索提交个数
	FormCost                                   float64 `json:"form_cost"`                                                // 落地页数据-单个线索成本
	FormActionRatio                            float64 `json:"form_action_ratio"`                                        // 落地页数据-表单提交点击率
	EventJinJianLandingPage                    int64   `json:"event_jin_jian_landing_page"`                              // 落地页数据-落地页完件数
	EventJinJianLandingPageCost                float64 `json:"event_jin_jian_landing_page_cost"`                         // 落地页数据-落地页完件成本
	EventCreditGrantLandingPage                int64   `json:"event_credit_grant_landing_page"`                          // 落地页授信数
	EventCreditGrantLandingPageCost            float64 `json:"event_credit_grant_landing_page_cost"`                     // 落地页数据-落地页授信成本
	EventCreditGrantLandingRatio               float64 `json:"event_credit_grant_landing_ratio"`                         // 落地页数据-落地页授信率
	EventNextDayStayCost                       float64 `json:"event_next_day_stay_cost"`                                 // 应用下载数据-次留成本（仅支持分日查询）
	EventNextDayStayRatio                      float64 `json:"event_next_day_stay_ratio"`                                // 应用下载数据-次留率（仅支持分日查询）
	EventNextDayStay                           int64   `json:"event_next_day_stay"`                                      // 应用下载数据-次留数（仅支持分日查询）
	Play3sRatio                                float64 `json:"play_3s_ratio"`                                            // 3s 播放率
	EventValidClues                            int64   `json:"event_valid_clues"`                                        // 落地页数据-有效线索数
	EventValidCluesCost                        float64 `json:"event_valid_clues_cost"`                                   // 落地页数据-有效线索成本
	AdProductCnt                               int64   `json:"ad_product_cnt"`                                           // 商品成交数
	EventGoodsView                             int64   `json:"event_goods_view"`                                         // 商品访问数
	MerchantRecoFans                           int64   `json:"merchant_reco_fans"`                                       // 涨粉量
	EventOrderAmountRoi                        float64 `json:"event_order_amount_roi"`                                   // 小店推广roi
	EventGoodsViewCost                         float64 `json:"event_goods_view_cost"`                                    // 商品访问成本
	MerchantRecoFansCost                       float64 `json:"merchant_reco_fans_cost"`                                  // 涨粉成本
	EventNewUserPay                            int64   `json:"event_new_user_pay"`                                       // 新增付费人数
	EventNewUserPayCost                        float64 `json:"event_new_user_pay_cost"`                                  // 新增付费人数成本
	EventNewUserPayRatio                       float64 `json:"event_new_user_pay_ratio"`                                 // 新增付费人数率
	EventButtonClick                           int64   `json:"event_button_click"`                                       // 按钮点击数
	EventButtonClickCost                       float64 `json:"event_button_click_cost"`                                  // 按钮点击成本
	EventButtonClickRatio                      float64 `json:"event_button_click_ratio"`                                 // 按钮点击率
	Play5sRatio                                float64 `json:"play_5s_ratio"`                                            // 5s播放率
	PlayEndRatio                               float64 `json:"play_end_ratio"`                                           // 完播率
	EventOrderPaidRoi                          float64 `json:"event_order_paid_roi"`                                     // 订单支付率
	EventNewUserJinjianApp                     int64   `json:"event_new_user_jinjian_app"`                               // 新增完件人数
	EventNewUserJinjianAppCost                 float64 `json:"event_new_user_jinjian_app_cost"`                          // 新增完件人数成本
	EventNewUserJinjianAppRoi                  float64 `json:"event_new_user_jinjian_app_roi"`                           // 新增完件人数率
	EventNewUserCreditGrantApp                 int64   `json:"event_new_user_credit_grant_app"`                          // 新增授信人数
	EventNewUserCreditGrantAppCost             float64 `json:"event_new_user_credit_grant_app_cost"`                     // 新增授信人数成本
	EventNewUserCreditGrantAppRoi              float64 `json:"event_new_user_credit_grant_app_roi"`                      // 新增授信人数率
	EventNewUserJinjianPage                    int64   `json:"event_new_user_jinjian_page"`                              // 字段描述，需要修改
	EventNewUserJinjianPageCost                float64 `json:"event_new_user_jinjian_page_cost"`                         // 字段描述，需要修改
	EventNewUserJinjianPageRoi                 float64 `json:"event_new_user_jinjian_page_roi"`                          // 字段描述，需要修改
	EventNewUserCreditGrantPage                int64   `json:"event_new_user_credit_grant_page"`                         // 字段描述，需要修改
	EventNewUserCreditGrantPageCost            float64 `json:"event_new_user_credit_grant_page_cost"`                    // 字段描述，需要修改
	EventNewUserCreditGrantPageRoi             float64 `json:"event_new_user_credit_grant_page_roi"`                     // 字段描述，需要修改
	EventAppointForm                           int64   `json:"event_appoint_form"`                                       // 预约表单数
	EventAppointFormCost                       float64 `json:"event_appoint_form_cost"`                                  // 预约表单点击成本
	EventAppointFormRatio                      float64 `json:"event_appoint_form_ratio"`                                 // 预约表单点击率
	EventAppointJumpClick                      int64   `json:"event_appoint_jump_click"`                                 // 预约跳转点击数
	EventAppointJumpClickCost                  float64 `json:"event_appoint_jump_click_cost"`                            // 预约跳转点击成本
	EventAppointJumpClickRatio                 float64 `json:"event_appoint_jump_click_ratio"`                           // 预约跳转点击率
	UnionEventPayPurchaseAmount7d              float64 `json:"union_event_pay_purchase_amount_7d"`                       // 联盟广告收入
	UnionEventPayPurchaseAmount7dRoi           float64 `json:"union_event_pay_purchase_amount_7d_roi"`                   // 联盟变现ROI
	EventDspGiftForm                           int64   `json:"event_dsp_gift_form"`                                      // 附加组件表单提交
	EventAppInvoked                            int64   `json:"event_app_invoked"`                                        // 唤醒应用数
	EventAppInvokedCost                        float64 `json:"event_app_invoked_cost"`                                   // 唤醒应用成本
	EventAppInvokedRatio                       float64 `json:"event_app_invoked_ratio"`                                  // 唤醒应用率
	EventAddWechat                             int64   `json:"event_add_wechat"`                                         // 微信复制数
	EventAddWechatCost                         float64 `json:"event_add_wechat_cost"`                                    // 微信复制成本
	EventAddWechatRatio                        float64 `json:"event_add_wechat_ratio"`                                   // 微信复制率
	EventMultiConversion                       int64   `json:"event_multi_conversion"`                                   // 落地页多转化次数
	EventMultiConversionRatio                  float64 `json:"event_multi_conversion_ratio"`                             // 落地页多转化率
	EventMultiConversionCost                   float64 `json:"event_multi_conversion_cost"`                              // 落地页多转化成本
	EventWatchAppAd                            int64   `json:"event_watch_app_ad"`                                       // 广告观看
	EventAdWatchTimes                          int64   `json:"event_ad_watch_times"`                                     // 广告观看次数
	EventAdWatchTimesRatio                     float64 `json:"event_ad_watch_times_ratio"`                               // 广告观看次数转化率
	EventAdWatchTimesCost                      float64 `json:"event_ad_watch_times_cost"`                                // 广告观看次数成本
	EventAddShoppingCart                       int64   `json:"event_add_shopping_cart"`                                  // 添加购物车数
	EventAddShoppingCartCost                   float64 `json:"event_add_shopping_cart_cost"`                             // 添加购物车成本
	EventGetThrough                            int64   `json:"event_get_through"`                                        // 智能电话-确认接通数
	EventGetThroughCost                        float64 `json:"event_get_through_cost"`                                   // 智能电话-确认接通成本
	EventGetThroughRatio                       float64 `json:"event_get_through_ratio"`                                  // 智能电话-确认接通率
	AdPhotoPlayed75percentRatio                float64 `json:"ad_photo_played_75percent_ratio"`                          // 75%进度播放率
	AdPhotoPlayed10sRatio                      float64 `json:"ad_photo_played_10s_ratio"`                                // 10s播放率
	AdPhotoPlayed2sRatio                       float64 `json:"ad_photo_played_2s_ratio"`                                 // 2s播放率
	EventPhoneGetThrough                       int64   `json:"event_phone_get_through"`                                  // 电话建联数
	EventIntentionConfirmed                    int64   `json:"event_intention_confirmed"`                                // 意向确认数
	EventWechatConnected                       int64   `json:"event_wechat_connected"`                                   // 微信加粉数
	EventOrderSuccessed                        int64   `json:"event_order_successed"`                                    // 有效线索成交数
	EventPhoneCardActivate                     int64   `json:"event_phone_card_activate"`                                // 电话卡激活数
	EventMeasurementHouse                      int64   `json:"event_measurement_house"`                                  // 量房数
	AdShow                                     float64 `json:"ad_show"`                                                  // 广告曝光
	ActionNewRatio                             float64 `json:"action_new_ratio"`                                         // 行为率 新
	EventOutboundCall                          int64   `json:"event_outbound_call"`                                      // 电话拨打数
	EventOutboundCallCost                      float64 `json:"event_outbound_call_cost"`                                 // 电话拨打成本
	EventOutboundCallRatio                     float64 `json:"event_outbound_call_ratio"`                                // 电话拨打率
	KeyAction                                  int64   `json:"key_action"`                                               // 关键行为数
	KeyActionCost                              float64 `json:"key_action_cost"`                                          // 关键行为成本
	KeyActionRatio                             float64 `json:"key_action_ratio"`                                         // 关键行为率
	EventCreditCardRecheck                     int64   `json:"event_credit_card_recheck"`                                // 信用卡核卡数
	EventCreditCardRecheckFirstDay             int64   `json:"event_credit_card_recheck_first_day"`                      // 信用卡首日核卡数
	EventNoIntention                           int64   `json:"event_no_intention"`                                       // 用户无意向数
	CampaignId                                 int64   `json:"campaign_id"`                                              // 广告计划 ID
	CampaignName                               string  `json:"campaign_name"`                                            // 广告计划名称
	OrderSubmitAmt                             float64 `json:"order_submit_amt"`                                         // 订单提交金额
	Jinjian0dCnt                               int64   `json:"jinjian_0d_cnt"`                                           // T0完件数
	Jinjian3dCnt                               int64   `json:"jinjian_3d_cnt"`                                           // T3完件数
	Jinjian0dCntCost                           float64 `json:"jinjian_0d_cnt_cost"`                                      // T0完件成本
	Jinjian3dCntCost                           float64 `json:"jinjian_3d_cnt_cost"`                                      // T3完件成本
	CreditGrant0dCnt                           int64   `json:"credit_grant_0d_cnt"`                                      // T0授信数
	CreditGrant3dCnt                           int64   `json:"credit_grant_3d_cnt"`                                      // T3授信数
	CreditGrant0dCntCost                       float64 `json:"credit_grant_0d_cnt_cost"`                                 // T0授信成本
	CreditGrant3dCntCost                       float64 `json:"credit_grant_3d_cnt_cost"`                                 // T3授信成本
	CreditGrant0dCntRatio                      float64 `json:"credit_grant_0d_cnt_ratio"`                                // T0完件授信率
	CreditGrant3dCntRatio                      float64 `json:"credit_grant_3d_cnt_ratio"`                                // T3完件授信通过率
	KeyInappAction0dCnt                        int64   `json:"key_inapp_action_0d_cnt"`                                  // T0全量授信数
	KeyInappAction3dCnt                        int64   `json:"key_inapp_action_3d_cnt"`                                  // T3全量授信数
	KeyInappAction0dCntCost                    float64 `json:"key_inapp_action_0d_cnt_cost"`                             // T0全量授信成本
	KeyInappAction3dCntCost                    float64 `json:"key_inapp_action_3d_cnt_cost"`                             // T3全量授信成本
	KeyInappAction0dCntRatio                   float64 `json:"key_inapp_action_0d_cnt_ratio"`                            // T0全量授信通过率
	KeyInappAction3dCntRatio                   float64 `json:"key_inapp_action_3d_cnt_ratio"`                            // T3全量授信通过率
	DrawCreditLine0dCnt                        int64   `json:"draw_credit_line_0d_cnt"`                                  // T0用信数
	DrawCreditLine0dCntCost                    float64 `json:"draw_credit_line_0d_cnt_cost"`                             // T0用信成本
	DrawCreditLine0dCntRatio                   float64 `json:"draw_credit_line_0d_cnt_ratio"`                            // T0授信用信率
}

func (r *CampaignReportService) SetCfg(cfg *Configuration) *CampaignReportService {
	r.cfg = cfg
	return r
}

func (r *CampaignReportService) SetReq(req CampaignReportReq) *CampaignReportService {
	r.Request = &req
	return r
}

func (r *CampaignReportService) AccessToken(accessToken string) *CampaignReportService {
	r.token = accessToken
	return r
}

func (r *CampaignReportService) Do() (data *KsBaseResp[CampaignReport], err error) {
	localBasePath := r.cfg.BasePath
	localVarPath := localBasePath + "/rest/openapi/v1/report/campaign_report"
	response, err := r.cfg.HTTPClient.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("Access-Token", r.token).
		SetBody(r.Request).
		SetResult(&KsBaseResp[CampaignReport]{}).
		Post(localVarPath)
	if err != nil {
		return nil, err
	}
	resp := new(KsBaseResp[CampaignReport])
	// 将 JSON 响应解码到结构体中
	err = json.Unmarshal(response.Body(), &resp)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("/rest/openapi/v1/report/campaign_report: %v\n", err))
	}
	if resp.Code == 0 && resp.Data != nil {
		return resp, nil
	} else {
		return resp, errors.New(resp.Message)
	}
}
