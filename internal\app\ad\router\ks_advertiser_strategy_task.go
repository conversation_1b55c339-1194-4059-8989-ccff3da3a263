// ==========================================================================
// GFast自动生成router操作代码。
// 生成日期：2025-08-23 17:40:11
// 生成路径: internal/app/ad/router/ks_advertiser_strategy_task.go
// 生成人：cyao
// desc:快手广告搭建-任务
// company:云南奇讯科技有限公司
// ==========================================================================

package router

import (
	"context"

	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/tiger1103/gfast/v3/internal/app/ad/controller"
)

func (router *Router) BindKsAdvertiserStrategyTaskController(ctx context.Context, group *ghttp.RouterGroup) {
	group.Group("/ksAdvertiserStrategyTask", func(group *ghttp.RouterGroup) {
		group.Bind(
			controller.KsAdvertiserStrategyTask,
		)
	})
}
