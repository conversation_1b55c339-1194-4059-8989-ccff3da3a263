// ==========================================================================
// GFast自动生成service操作代码。
// 生成日期：2025-08-12 10:44:33
// 生成路径: internal/app/ad/service/ks_advertiser_account_report_data.go
// 生成人：cq
// desc:快手账户报表数据
// company:云南奇讯科技有限公司
// ==========================================================================

package service

import (
	"context"

	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
)

type IKsAdvertiserAccountReportData interface {
	List(ctx context.Context, req *model.KsAdvertiserAccountReportDataSearchReq) (res *model.KsAdvertiserAccountReportDataSearchRes, err error)
	Add(ctx context.Context, req *model.KsAdvertiserAccountReportDataAddReq) (err error)
	BatchAdd(ctx context.Context, batchReq []*model.KsAdvertiserAccountReportDataAddReq) (err error)
	RunSyncKsAccountReportData(ctx context.Context, req *model.KsAdvertiserAccountReportDataSearchReq) (err error)
	SyncKsAccountReportDataTask(ctx context.Context)
}

var localKsAdvertiserAccountReportData IKsAdvertiserAccountReportData

func KsAdvertiserAccountReportData() IKsAdvertiserAccountReportData {
	if localKsAdvertiserAccountReportData == nil {
		panic("implement not found for interface IKsAdvertiserAccountReportData, forgot register?")
	}
	return localKsAdvertiserAccountReportData
}

func RegisterKsAdvertiserAccountReportData(i IKsAdvertiserAccountReportData) {
	localKsAdvertiserAccountReportData = i
}
