// ==========================================================================
// GFast自动生成service操作代码。
// 生成日期：2025-08-12 10:45:10
// 生成路径: internal/app/ad/service/ks_advertiser_unit_report_data.go
// 生成人：cq
// desc:快手广告组报表数据
// company:云南奇讯科技有限公司
// ==========================================================================

package service

import (
	"context"

	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
)

type IKsAdvertiserUnitReportData interface {
	List(ctx context.Context, req *model.KsAdvertiserUnitReportDataSearchReq) (res *model.KsAdvertiserUnitReportDataSearchRes, err error)
	Add(ctx context.Context, req *model.KsAdvertiserUnitReportDataAddReq) (err error)
	BatchAdd(ctx context.Context, batchReq []*model.KsAdvertiserUnitReportDataAddReq) (err error)
	RunSyncKsUnitReportData(ctx context.Context, req *model.KsAdvertiserUnitReportDataSearchReq) (err error)
	SyncKsUnitReportDataTask(ctx context.Context)
}

var localKsAdvertiserUnitReportData IKsAdvertiserUnitReportData

func KsAdvertiserUnitReportData() IKsAdvertiserUnitReportData {
	if localKsAdvertiserUnitReportData == nil {
		panic("implement not found for interface IKsAdvertiserUnitReportData, forgot register?")
	}
	return localKsAdvertiserUnitReportData
}

func RegisterKsAdvertiserUnitReportData(i IKsAdvertiserUnitReportData) {
	localKsAdvertiserUnitReportData = i
}
