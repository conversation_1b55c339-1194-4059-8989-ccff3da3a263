// ==========================================================================
// GFast自动生成controller操作代码。
// 生成日期：2025-08-13 16:33:57
// 生成路径: internal/app/ad/controller/ks_advertiser_unit.go
// 生成人：cyao
// desc:快手广告组
// company:云南奇讯科技有限公司
// ==========================================================================

package controller

import (
	"context"

	"github.com/tiger1103/gfast/v3/api/v1/ad"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	systemController "github.com/tiger1103/gfast/v3/internal/app/system/controller"
)

type ksAdvertiserUnitController struct {
	systemController.BaseController
}

var KsAdvertiserUnit = new(ksAdvertiserUnitController)

// List 列表
func (c *ksAdvertiserUnitController) List(ctx context.Context, req *ad.KsAdvertiserUnitSearchReq) (res *ad.KsAdvertiserUnitSearchRes, err error) {
	res = new(ad.KsAdvertiserUnitSearchRes)
	res.KsAdvertiserUnitSearchRes, err = service.KsAdvertiserUnit().List(ctx, &req.KsAdvertiserUnitSearchReq)
	return
}

// Get 获取快手广告组
func (c *ksAdvertiserUnitController) Get(ctx context.Context, req *ad.KsAdvertiserUnitGetReq) (res *ad.KsAdvertiserUnitGetRes, err error) {
	res = new(ad.KsAdvertiserUnitGetRes)
	res.KsAdvertiserUnitInfoRes, err = service.KsAdvertiserUnit().GetByUnitId(ctx, req.UnitId)
	return
}

// GetUnitInfo 获取快手广告组详情
func (c *ksAdvertiserUnitController) GetUnitInfo(ctx context.Context, req *ad.KsAdvertiserUnitInfoReq) (res *ad.KsAdvertiserUnitInfoRes, err error) {
	res = new(ad.KsAdvertiserUnitInfoRes)
	res.KsAdvertiserUnitInfoRes, err = service.KsAdvertiserUnit().GetUnitInfo(ctx, req.UnitId)
	return
}

// Add 添加快手广告组
func (c *ksAdvertiserUnitController) Add(ctx context.Context, req *ad.KsAdvertiserUnitAddReq) (res *ad.KsAdvertiserUnitAddRes, err error) {
	err = service.KsAdvertiserUnit().Add(ctx, req.KsAdvertiserUnitAddReq)
	return
}

// Edit 修改快手广告组
func (c *ksAdvertiserUnitController) Edit(ctx context.Context, req *ad.KsAdvertiserUnitEditReq) (res *ad.KsAdvertiserUnitEditRes, err error) {
	err = service.KsAdvertiserUnit().Edit(ctx, req.KsAdvertiserUnitEditReq)
	return
}

// Delete 删除快手广告组
func (c *ksAdvertiserUnitController) Delete(ctx context.Context, req *ad.KsAdvertiserUnitDeleteReq) (res *ad.KsAdvertiserUnitDeleteRes, err error) {
	err = service.KsAdvertiserUnit().Delete(ctx, req.UnitIds)
	return
}

// ManualSyncUnit 手动同步广告组
func (c *ksAdvertiserUnitController) ManualSyncUnit(ctx context.Context, req *ad.KsAdvertiserUnitManualSyncReq) (res *ad.KsAdvertiserUnitManualSyncRes, err error) {
	err = service.KsAdvertiserUnit().ManualSyncUnit(ctx, req.AdvertiserIds, req.StartTime, req.EndTime)
	return
}

// EditUnitName 修改广告组名称
func (c *ksAdvertiserUnitController) EditUnitName(ctx context.Context, req *ad.KsAdvertiserUnitEditNameReq) (res *ad.KsAdvertiserUnitEditNameRes, err error) {
	err = service.KsAdvertiserUnit().EditUnitName(ctx, req.UnitId, req.UnitName)
	return
}
