// ==========================================================================
// GFast自动生成dao操作代码。
// 生成日期：2025-08-19 10:36:49
// 生成路径: internal/app/ad/dao/ks_advertiser_common_asset_category.go
// 生成人：cq
// desc:快手通用资产-标题分类
// company:云南奇讯科技有限公司
// ==========================================================================

package dao

import (
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao/internal"
)

// ksAdvertiserCommonAssetCategoryDao is the manager for logic model data accessing and custom defined data operations functions management.
// You can define custom methods on it to extend its functionality as you wish.
type ksAdvertiserCommonAssetCategoryDao struct {
	*internal.KsAdvertiserCommonAssetCategoryDao
}

var (
	// KsAdvertiserCommonAssetCategory is globally public accessible object for table tools_gen_table operations.
	KsAdvertiserCommonAssetCategory = ksAdvertiserCommonAssetCategoryDao{
		internal.NewKsAdvertiserCommonAssetCategoryDao(),
	}
)

// Fill with you ideas below.
