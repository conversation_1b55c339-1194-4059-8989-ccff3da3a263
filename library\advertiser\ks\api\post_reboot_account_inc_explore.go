package api

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
)

// RebootAccountIncExploreService 重启增量探索
type RebootAccountIncExploreService struct {
	ctx     context.Context
	cfg     *Configuration
	token   string
	Request *RebootAccountIncExploreReq
}

// RebootAccountIncExploreReq 请求结构体
type RebootAccountIncExploreReq struct {
	RebootInfo   []GwIncExploreDetailRebootDto `json:"pause_info"`    // 暂停详情
	AdvertiserId int64                         `json:"advertiser_id"` // 账号id
}

type GwIncExploreDetailRebootDto struct {
	OcpxActionType     int64 `json:"ocpx_action_type" dc:"转化目标类型"`
	DeepConversionType int64 `json:"deep_conversion_type" dc:"深度转化目标类型"`
}

func (r *RebootAccountIncExploreService) SetCfg(cfg *Configuration) *RebootAccountIncExploreService {
	r.cfg = cfg
	return r
}

func (r *RebootAccountIncExploreService) SetReq(req RebootAccountIncExploreReq) *RebootAccountIncExploreService {
	r.Request = &req
	return r
}

func (r *RebootAccountIncExploreService) AccessToken(accessToken string) *RebootAccountIncExploreService {
	r.token = accessToken
	return r
}

func (r *RebootAccountIncExploreService) Do() (data *KsBaseResp[[]GwIncExploreDetailView], err error) {
	localBasePath := r.cfg.BasePath
	localVarPath := localBasePath + "/rest/openapi/gw/dsp/account/incExplore/reboot"
	response, err := r.cfg.HTTPClient.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("Access-Token", r.token).
		SetBody(r.Request).
		SetResult(&KsBaseResp[[]GwIncExploreDetailView]{}).
		Post(localVarPath)
	if err != nil {
		return nil, err
	}
	resp := new(KsBaseResp[[]GwIncExploreDetailView])
	// 将 JSON 响应解码到结构体中
	err = json.Unmarshal(response.Body(), &resp)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("/rest/openapi/gw/dsp/account/incExplore/reboot解析响应出错: %v\n", err))
	}
	if resp.Code == 0 && resp.Data != nil {
		return resp, nil
	} else {
		return resp, errors.New(resp.Message)
	}
}
