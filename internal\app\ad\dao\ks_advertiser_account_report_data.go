// ==========================================================================
// GFast自动生成dao操作代码。
// 生成日期：2025-08-12 10:44:32
// 生成路径: internal/app/ad/dao/ks_advertiser_account_report_data.go
// 生成人：cq
// desc:快手账户报表数据
// company:云南奇讯科技有限公司
// ==========================================================================

package dao

import (
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao/internal"
)

// ksAdvertiserAccountReportDataDao is the manager for logic model data accessing and custom defined data operations functions management.
// You can define custom methods on it to extend its functionality as you wish.
type ksAdvertiserAccountReportDataDao struct {
	*internal.KsAdvertiserAccountReportDataDao
}

var (
	// KsAdvertiserAccountReportData is globally public accessible object for table tools_gen_table operations.
	KsAdvertiserAccountReportData = ksAdvertiserAccountReportDataDao{
		internal.NewKsAdvertiserAccountReportDataDao(),
	}
)

// Fill with you ideas below.
