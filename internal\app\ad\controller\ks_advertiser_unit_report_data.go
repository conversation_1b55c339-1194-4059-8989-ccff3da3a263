// ==========================================================================
// GFast自动生成controller操作代码。
// 生成日期：2025-08-12 10:45:10
// 生成路径: internal/app/ad/controller/ks_advertiser_unit_report_data.go
// 生成人：cq
// desc:快手广告组报表数据
// company:云南奇讯科技有限公司
// ==========================================================================

package controller

import (
	"context"

	"github.com/tiger1103/gfast/v3/api/v1/ad"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	systemController "github.com/tiger1103/gfast/v3/internal/app/system/controller"
)

type ksAdvertiserUnitReportDataController struct {
	systemController.BaseController
}

var KsAdvertiserUnitReportData = new(ksAdvertiserUnitReportDataController)

// List 列表
func (c *ksAdvertiserUnitReportDataController) List(ctx context.Context, req *ad.KsAdvertiserUnitReportDataSearchReq) (res *ad.KsAdvertiserUnitReportDataSearchRes, err error) {
	res = new(ad.KsAdvertiserUnitReportDataSearchRes)
	res.KsAdvertiserUnitReportDataSearchRes, err = service.KsAdvertiserUnitReportData().List(ctx, &req.KsAdvertiserUnitReportDataSearchReq)
	return
}

// Add 添加快手广告组报表数据
func (c *ksAdvertiserUnitReportDataController) Add(ctx context.Context, req *ad.KsAdvertiserUnitReportDataAddReq) (res *ad.KsAdvertiserUnitReportDataAddRes, err error) {
	err = service.KsAdvertiserUnitReportData().Add(ctx, req.KsAdvertiserUnitReportDataAddReq)
	return
}

// RunSyncKsUnitReportData 快手广告组报表数据任务
func (c *ksAdvertiserUnitReportDataController) RunSyncKsUnitReportData(ctx context.Context, req *ad.KsAdvertiserUnitReportDataTaskReq) (res *ad.KsAdvertiserUnitReportDataTaskRes, err error) {
	err = service.KsAdvertiserUnitReportData().RunSyncKsUnitReportData(ctx, &req.KsAdvertiserUnitReportDataSearchReq)
	return
}
