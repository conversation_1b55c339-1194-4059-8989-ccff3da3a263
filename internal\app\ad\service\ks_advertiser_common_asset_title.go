// ==========================================================================
// GFast自动生成service操作代码。
// 生成日期：2025-08-19 10:36:58
// 生成路径: internal/app/ad/service/ks_advertiser_common_asset_title.go
// 生成人：cq
// desc:快手通用资产-标题库
// company:云南奇讯科技有限公司
// ==========================================================================

package service

import (
	"context"

	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
)

type IKsAdvertiserCommonAssetTitle interface {
	List(ctx context.Context, req *model.KsAdvertiserCommonAssetTitleSearchReq) (res *model.KsAdvertiserCommonAssetTitleSearchRes, err error)
	GetById(ctx context.Context, Id int) (res *model.KsAdvertiserCommonAssetTitleInfoRes, err error)
	Add(ctx context.Context, req *model.KsAdvertiserCommonAssetTitleBatchAddReq) (err error)
	BatchAdd(ctx context.Context, batchReq []*model.KsAdvertiserCommonAssetTitleAddReq) (err error)
	Edit(ctx context.Context, req *model.KsAdvertiserCommonAssetTitleEditReq) (err error)
	BatchEditCategory(ctx context.Context, req *model.KsAdvertiserCommonAssetTitleBatchEditReq) (err error)
	Delete(ctx context.Context, Id []int) (err error)
}

var localKsAdvertiserCommonAssetTitle IKsAdvertiserCommonAssetTitle

func KsAdvertiserCommonAssetTitle() IKsAdvertiserCommonAssetTitle {
	if localKsAdvertiserCommonAssetTitle == nil {
		panic("implement not found for interface IKsAdvertiserCommonAssetTitle, forgot register?")
	}
	return localKsAdvertiserCommonAssetTitle
}

func RegisterKsAdvertiserCommonAssetTitle(i IKsAdvertiserCommonAssetTitle) {
	localKsAdvertiserCommonAssetTitle = i
}
