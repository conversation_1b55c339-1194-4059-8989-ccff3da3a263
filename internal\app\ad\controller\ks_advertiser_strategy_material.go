// ==========================================================================
// GFast自动生成controller操作代码。
// 生成日期：2025-08-22 11:52:27
// 生成路径: internal/app/ad/controller/ks_advertiser_strategy_material.go
// 生成人：cq
// desc:快手策略组-素材
// company:云南奇讯科技有限公司
// ==========================================================================

package controller

import (
	"context"

	"github.com/tiger1103/gfast/v3/api/v1/ad"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	systemController "github.com/tiger1103/gfast/v3/internal/app/system/controller"
)

type ksAdvertiserStrategyMaterialController struct {
	systemController.BaseController
}

var KsAdvertiserStrategyMaterial = new(ksAdvertiserStrategyMaterialController)

// List 列表
func (c *ksAdvertiserStrategyMaterialController) List(ctx context.Context, req *ad.KsAdvertiserStrategyMaterialSearchReq) (res *ad.KsAdvertiserStrategyMaterialSearchRes, err error) {
	res = new(ad.KsAdvertiserStrategyMaterialSearchRes)
	res.KsAdvertiserStrategyMaterialSearchRes, err = service.KsAdvertiserStrategyMaterial().List(ctx, &req.KsAdvertiserStrategyMaterialSearchReq)
	return
}

// Get 获取快手策略组-素材
func (c *ksAdvertiserStrategyMaterialController) Get(ctx context.Context, req *ad.KsAdvertiserStrategyMaterialGetReq) (res *ad.KsAdvertiserStrategyMaterialGetRes, err error) {
	res = new(ad.KsAdvertiserStrategyMaterialGetRes)
	res.KsAdvertiserStrategyMaterialInfoRes, err = service.KsAdvertiserStrategyMaterial().GetById(ctx, req.Id)
	return
}

// Add 添加快手策略组-素材
func (c *ksAdvertiserStrategyMaterialController) Add(ctx context.Context, req *ad.KsAdvertiserStrategyMaterialAddReq) (res *ad.KsAdvertiserStrategyMaterialAddRes, err error) {
	err = service.KsAdvertiserStrategyMaterial().Add(ctx, req.KsAdvertiserStrategyMaterialAddReq)
	return
}

// Edit 修改快手策略组-素材
func (c *ksAdvertiserStrategyMaterialController) Edit(ctx context.Context, req *ad.KsAdvertiserStrategyMaterialEditReq) (res *ad.KsAdvertiserStrategyMaterialEditRes, err error) {
	err = service.KsAdvertiserStrategyMaterial().Edit(ctx, req.KsAdvertiserStrategyMaterialEditReq)
	return
}

// Delete 删除快手策略组-素材
func (c *ksAdvertiserStrategyMaterialController) Delete(ctx context.Context, req *ad.KsAdvertiserStrategyMaterialDeleteReq) (res *ad.KsAdvertiserStrategyMaterialDeleteRes, err error) {
	err = service.KsAdvertiserStrategyMaterial().Delete(ctx, req.Ids)
	return
}
