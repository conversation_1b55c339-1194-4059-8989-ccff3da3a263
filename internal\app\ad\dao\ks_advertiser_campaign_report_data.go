// ==========================================================================
// GFast自动生成dao操作代码。
// 生成日期：2025-08-12 10:45:00
// 生成路径: internal/app/ad/dao/ks_advertiser_campaign_report_data.go
// 生成人：cq
// desc:快手广告计划报表数据
// company:云南奇讯科技有限公司
// ==========================================================================

package dao

import (
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao/internal"
)

// ksAdvertiserCampaignReportDataDao is the manager for logic model data accessing and custom defined data operations functions management.
// You can define custom methods on it to extend its functionality as you wish.
type ksAdvertiserCampaignReportDataDao struct {
	*internal.KsAdvertiserCampaignReportDataDao
}

var (
	// KsAdvertiserCampaignReportData is globally public accessible object for table tools_gen_table operations.
	KsAdvertiserCampaignReportData = ksAdvertiserCampaignReportDataDao{
		internal.NewKsAdvertiserCampaignReportDataDao(),
	}
)

// Fill with you ideas below.
