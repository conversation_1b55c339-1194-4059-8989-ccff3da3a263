// ==========================================================================
// GFast自动生成dao internal操作代码。
// 生成日期：2025-08-23 17:40:21
// 生成路径: internal/app/ad/dao/internal/ks_advertiser_strategy_task_creative.go
// 生成人：cyao
// desc:快手广告搭建-任务-广告创意
// company:云南奇讯科技有限公司
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// KsAdvertiserStrategyTaskCreativeDao is the manager for logic model data accessing and custom defined data operations functions management.
type KsAdvertiserStrategyTaskCreativeDao struct {
	table   string                                  // Table is the underlying table name of the DAO.
	group   string                                  // Group is the database configuration group name of current DAO.
	columns KsAdvertiserStrategyTaskCreativeColumns // Columns is the short type for Columns, which contains all the column names of Table for convenient usage.
}

// KsAdvertiserStrategyTaskCreativeColumns defines and stores column names for table ks_advertiser_strategy_task_creative.
type KsAdvertiserStrategyTaskCreativeColumns struct {
	TaskCreativeId string // 任务创意ID
	CreativeId     string // 创意 id
	CreativeName   string // 创意名称
	TaskId         string // 任务ID
	TaskCampaignId string // 任务计划ID
	TaskUnitId     string // 任务广告组ID
	AdvertiserId   string // 广告主ID
	AdvertiserNick string // 广告主名称
	ErrMsg         string // 失败原因
	Status         string // 项目状态 等待提交：INIT 已终止：TERMINATED 成功：SUCCESS 失败：ERROR
	CreativeData   string // 创建创意数据
	CreatedAt      string // 创建时间
	UpdatedAt      string // 更新时间
	DeletedAt      string // 删除时间
}

var ksAdvertiserStrategyTaskCreativeColumns = KsAdvertiserStrategyTaskCreativeColumns{
	TaskCreativeId: "task_creative_id",
	CreativeId:     "creative_id",
	CreativeName:   "creative_name",
	TaskId:         "task_id",
	TaskCampaignId: "task_campaign_id",
	TaskUnitId:     "task_unit_id",
	AdvertiserId:   "advertiser_id",
	AdvertiserNick: "advertiser_nick",
	ErrMsg:         "err_msg",
	Status:         "status",
	CreativeData:   "creative_data",
	CreatedAt:      "created_at",
	UpdatedAt:      "updated_at",
	DeletedAt:      "deleted_at",
}

// NewKsAdvertiserStrategyTaskCreativeDao creates and returns a new DAO object for table data access.
func NewKsAdvertiserStrategyTaskCreativeDao() *KsAdvertiserStrategyTaskCreativeDao {
	return &KsAdvertiserStrategyTaskCreativeDao{
		group:   "default",
		table:   "ks_advertiser_strategy_task_creative",
		columns: ksAdvertiserStrategyTaskCreativeColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *KsAdvertiserStrategyTaskCreativeDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *KsAdvertiserStrategyTaskCreativeDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *KsAdvertiserStrategyTaskCreativeDao) Columns() KsAdvertiserStrategyTaskCreativeColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *KsAdvertiserStrategyTaskCreativeDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *KsAdvertiserStrategyTaskCreativeDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *KsAdvertiserStrategyTaskCreativeDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
